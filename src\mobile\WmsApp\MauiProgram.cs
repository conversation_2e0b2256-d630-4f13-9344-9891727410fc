using Microsoft.Extensions.Logging;
using CommunityToolkit.Maui;
using Refit;
using System.Globalization;
using System.Resources;
using System.Text.Json;
using WmsApp.Services;
using WmsApp.Services.Contracts;
using WmsApp.Services.Receives;
using WmsApp.Services.Inventory;
using WmsApp.Models;
using WmsApp.Views;
using WmsApp.Views.Receives;
using WmsApp.Views.Inventory;
using WmsApp.ViewModels;
using WmsApp.ViewModels.Receives;
using WmsApp.ViewModels.Inventory;

namespace WmsApp;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

        // Localization init (default: pl)
        var lang = Preferences.Default.Get("AppLanguage", "pl");
        var culture = CultureInfo.GetCultureInfo(lang);
        CultureInfo.DefaultThreadCurrentUICulture = culture;
        CultureInfo.DefaultThreadCurrentCulture = culture;
        var resManager = new ResourceManager("WmsApp.Resources.Strings.AppResources", typeof(MauiProgram).Assembly);
        WmsApp.Localization.LocalizationResourceManager.Current.Init(resManager);
        WmsApp.Localization.LocalizationResourceManager.Current.SetCulture(culture);

        // Register services
        ConfigureServices(builder.Services);

#if DEBUG
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // Core services
        services.AddSingleton<IPreferences>(Preferences.Default);
        services.AddSingleton<IConnectivity>(Connectivity.Current);

        // Localization service
        services.AddSingleton<ILocalizationService, LocalizationService>();
        
        // Device Detection Service (platform-specific) - moved earlier
#if ANDROID
        services.AddSingleton<IDeviceDetectionService, Platforms.Android.DeviceDetectionService>();
#else
        services.AddSingleton<IDeviceDetectionService, DeviceDetectionService>();
#endif
        
        // Authentication Service
        services.AddSingleton<IAuthService, AuthService>();
        
        // API Configuration Service  
        services.AddSingleton<IApiConfigurationService, ApiConfigurationService>();
        
        // Authorization Handler
        services.AddTransient<AuthorizationMessageHandler>();
        
        // WMS API Service - singleton to share same HttpClient instance
        services.AddSingleton<IWmsApiService>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            return apiConfig.CreateApiService();
        });
        
        // Other services
        services.AddTransient<CodeValidationService>();
        
        // Product Picker Service
        services.AddSingleton<IProductPickerService, ProductPickerService>();
        
        // Mock Scanner Service
        services.AddTransient<IMockScannerService, MockScannerService>();
        
        // Receives module services
        services.AddTransient<ReceiveCodeValidationService>();
        
        // Configure ReceiveService based on API environment
        services.AddTransient<IReceiveService>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();

            System.Diagnostics.Debug.WriteLine($"[MAUI] Configuring ReceiveService for environment: {apiConfig.CurrentEnvironment}");

            // Use MockReceiveService only for Mock environment; otherwise use real ReceiveService
            if (apiConfig.CurrentEnvironment == ApiEnvironment.Mock)
            {
                System.Diagnostics.Debug.WriteLine("[MAUI] Using MockReceiveService");
                return provider.GetRequiredService<MockReceiveService>();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[MAUI] Using real ReceiveService");
                return provider.GetRequiredService<ReceiveService>();
            }
        });
        
        // Register both implementations
        services.AddTransient<MockReceiveService>();
        services.AddTransient<ReceiveService>();

        // Inventory module services
        services.AddTransient<IInventoryService, InventoryService>();

        // Add IInventoryApiClient for inventory API calls
        services.AddSingleton<IInventoryApiClient>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            var authService = provider.GetRequiredService<IAuthService>();

            var baseUrl = apiConfig.CurrentEnvironment.GetBaseUrl();

            // Dla klienta Inventory używamy bazowego hosta BEZ sufiksu "/api/v1",
            // bo atrybuty Refit zawierają pełny prefiks "/api/v1/...".
            var inventoryBaseUrl = baseUrl.EndsWith("/api/v1", StringComparison.OrdinalIgnoreCase)
                ? baseUrl.Substring(0, baseUrl.Length - "/api/v1".Length)
                : baseUrl;

            System.Diagnostics.Debug.WriteLine($"[INVENTORY_API] BaseUrl from env: {baseUrl}, using inventoryBaseUrl: {inventoryBaseUrl}");

            // Tworzymy HttpClient z custom handler (podobnie jak w ApiConfigurationService)
            var authHandler = new AuthorizationMessageHandler(authService)
            {
                InnerHandler = new HttpClientHandler()
            };

            var httpClient = new HttpClient(authHandler)
            {
                BaseAddress = new Uri(inventoryBaseUrl),
                Timeout = TimeSpan.FromSeconds(60)
            };

            httpClient.DefaultRequestHeaders.Add("User-Agent", "WmsApp/1.0 (Inventory)");
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            var refitSettings = new RefitSettings
            {
                ContentSerializer = new SystemTextJsonContentSerializer(new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                })
            };

            return RestService.For<IInventoryApiClient>(httpClient, refitSettings);
        });
        
        // Add IReceiveApiClient for real API (only used when not Mock)
        services.AddSingleton<IReceiveApiClient>(provider =>
        {
            var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
            var authService = provider.GetRequiredService<IAuthService>();
            
            var baseUrl = apiConfig.CurrentEnvironment.GetBaseUrl();
            
            // Tworzymy HttpClient z custom handler (podobnie jak w ApiConfigurationService)
            var authHandler = new AuthorizationMessageHandler(authService)
            {
                InnerHandler = new HttpClientHandler()
            };
            
            var httpClient = new HttpClient(authHandler)
            {
                BaseAddress = new Uri(baseUrl),
                Timeout = TimeSpan.FromSeconds(60)
            };
            
            httpClient.DefaultRequestHeaders.Add("User-Agent", "WmsApp/1.0 (Receives)");
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            
            var refitSettings = new RefitSettings
            {
                ContentSerializer = new SystemTextJsonContentSerializer(new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                })
            };
            
            return RestService.For<IReceiveApiClient>(httpClient, refitSettings);
        });
        
        // Update Service with HttpClient
        services.AddHttpClient<IUpdateService, UpdateService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(60); // Longer timeout for downloads
        });
        
        // ViewModels
        services.AddTransient<LoginViewModel>();
        services.AddTransient<MainViewModel>();
        services.AddTransient<LocationsViewModel>();
        services.AddTransient<PalletsViewModel>();
        services.AddTransient<OptionsViewModel>();
        services.AddTransient<MovePalletViewModel>();
        services.AddTransient<AboutViewModel>();
        
        // Receives ViewModels
        services.AddTransient<ReceivesSelectionViewModel>();
        services.AddTransient<ReceivesRegistrationViewModel>();
        services.AddTransient<ProductPickerViewModel>();

        // Inventory ViewModels
        services.AddTransient<InventorySelectionViewModel>();
        services.AddTransient<InventoryProductViewModel>();

        // Emulator ViewModels
        services.AddTransient<EmulatorScannerViewModel>();
        
        // Views
        services.AddTransient<LoginPage>();
        services.AddTransient<MainPage>();
        services.AddTransient<LocationsPage>();
        services.AddTransient<PalletsPage>();
        services.AddTransient<OptionsPage>();
        services.AddTransient<MovePalletPage>();
        services.AddTransient<AboutPage>();
        
        // Receives Views
        services.AddTransient<ReceivesSelectionPage>();
        services.AddTransient<ReceivesRegistrationPage>();
        services.AddTransient<ProductPickerPage>();

        // Inventory Views
        services.AddTransient<InventorySelectionPage>();
        services.AddTransient<InventoryProductPage>();

        // Emulator Views
        services.AddTransient<EmulatorScannerPage>();
        
        // Shell
        services.AddTransient<AppShell>();
    }
}
