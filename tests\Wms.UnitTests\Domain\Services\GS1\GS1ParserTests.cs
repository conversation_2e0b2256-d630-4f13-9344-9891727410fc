using AutoFixture;
using FluentAssertions;
using System.Globalization;
using Wms.Domain.Services.GS1;
using Wms.Domain.ValueObjects;

namespace Wms.UnitTests.Domain.Services.GS1;

/// <summary>
/// Testy jednostkowe dla GS1Parser
/// Pokrycie: happy path, błędy parsowania, prefiks IZ, separatory FNC1
/// </summary>
public class GS1ParserTests
{
    private readonly GS1Parser _parser;
    private readonly IFixture _fixture;

    public GS1ParserTests()
    {
        _parser = new GS1Parser();
        _fixture = new Fixture();
        
        // Ustaw polską kulturę dla testów
        CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("pl-PL");
        CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo("pl-PL");
    }

    #region Happy Path Tests

    [Fact]
    public void Parse_WithValidCompleteGS1Data_ShouldParseAllFields()
    {
        // Arrange
        var gs1Code = "(00)123456789012345678(02)12345678901234(10)LOT123(17)241231(37)100";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
        result.HasGtin.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.HasExpiryDate.Should().BeTrue();
        result.HasQuantity.Should().BeTrue();
        result.HasIzPrefix.Should().BeFalse();

        result.SSCC!.Value.Should().Be("123456789012345678");
        result.Gtin!.Value.Should().Be("12345678901234");
        result.Lot!.Value.Should().Be("LOT123");
        result.ExpiryDate!.Value.Should().Be(new DateOnly(2024, 12, 31));
        result.Quantity!.Value.Should().Be(100);
        result.ParsedFieldsCount.Should().Be(5);
    }

    [Fact]
    public void Parse_WithValidGS1DataAndIzPrefix_ShouldParseAndDetectPrefix()
    {
        // Arrange
        var gs1Code = "IZ(00)123456789012345678(10)BATCH01(37)50";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasIzPrefix.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.HasQuantity.Should().BeTrue();

        result.SSCC!.Value.Should().Be("123456789012345678");
        result.Lot!.Value.Should().Be("BATCH01");
        result.Quantity!.Value.Should().Be(50);
        result.ParsedFieldsCount.Should().Be(3);
    }

    [Theory]
    [InlineData("(00)123456789012345678", 1)] // Only SSCC
    [InlineData("(02)12345678901234", 1)] // Only GTIN
    [InlineData("(10)LOT456", 1)] // Only Lot
    [InlineData("(17)250630", 1)] // Only Expiry Date
    [InlineData("(37)25", 1)] // Only Quantity
    [InlineData("(00)123456789012345678(10)MIXED", 2)] // SSCC + Lot
    [InlineData("(02)98765432109876(17)230515", 2)] // GTIN + Date
    public void Parse_WithPartialGS1Data_ShouldParseAvailableFields(string gs1Code, int expectedFieldsCount)
    {
        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.ParsedFieldsCount.Should().Be(expectedFieldsCount);
    }

    [Fact]
    public void Parse_WithEAN128StartCode_ShouldRemoveStartCodeAndParse()
    {
        // Arrange
        var gs1Code = "]C1(00)123456789012345678(10)TEST";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.SSCC!.Value.Should().Be("123456789012345678");
        result.Lot!.Value.Should().Be("TEST");
    }

    [Fact]
    public void Parse_WithFNC1Separators_ShouldHandleSeparatorsCorrectly()
    {
        // Arrange
        var groupSeparator = (char)29; // FNC1
        var gs1Code = $"(10)VARIABLE{groupSeparator}(17)241015";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.HasExpiryDate.Should().BeTrue();
        result.Lot!.Value.Should().Be("VARIABLE");
        result.ExpiryDate!.Value.Should().Be(new DateOnly(2024, 10, 15));
    }

    [Fact]
    public void Parse_WithBestBeforeDate_ShouldTreatAsBestBefore()
    {
        // Arrange
        var gs1Code = "(00)123456789012345678(15)241130"; // AI 15 = Best Before

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasExpiryDate.Should().BeTrue();
        result.ExpiryDate!.Value.Should().Be(new DateOnly(2024, 11, 30));
    }

    #endregion

    #region Error Cases Tests

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t\n\r")]
    public void Parse_WithEmptyOrNullData_ShouldReturnFailure(string? input)
    {
        // Act
        var result = _parser.Parse(input!);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().Contain("puste", "empty data should return Polish error message");
    }

    [Theory]
    [InlineData("INVALID_DATA")]
    [InlineData("123456789")]
    [InlineData("NO_PARENTHESES")]
    [InlineData("(99)UNSUPPORTED")]
    public void Parse_WithInvalidFormat_ShouldReturnFailure(string invalidData)
    {
        // Act
        var result = _parser.Parse(invalidData);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
        result.RawData.Should().Be(invalidData);
    }

    [Theory]
    [InlineData("(00)12345678901234567")] // 17 digits instead of 18
    [InlineData("(00)1234567890123456789")] // 19 digits instead of 18
    [InlineData("(00)ABC1234567890123456")] // Non-numeric SSCC
    public void Parse_WithInvalidSSCCLength_ShouldReturnFailure(string invalidSSCC)
    {
        // Act
        var result = _parser.Parse(invalidSSCC);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    [Theory]
    [InlineData("(02)1234567890123")] // 13 digits instead of 14
    [InlineData("(02)123456789012345")] // 15 digits instead of 14
    [InlineData("(02)ABC567890123456")] // Non-numeric GTIN
    public void Parse_WithInvalidGTINLength_ShouldReturnFailure(string invalidGTIN)
    {
        // Act
        var result = _parser.Parse(invalidGTIN);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    [Theory]
    [InlineData("(17)24123")] // 5 digits instead of 6
    [InlineData("(17)2412345")] // 7 digits instead of 6
    [InlineData("(17)991301")] // Invalid date (99th month)
    [InlineData("(17)240132")] // Invalid date (32nd day)
    public void Parse_WithInvalidExpiryDate_ShouldReturnFailure(string invalidDate)
    {
        // Act
        var result = _parser.Parse(invalidDate);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    [Theory]
    [InlineData("(37)ABC")] // Non-numeric quantity
    [InlineData("(37)0")] // Zero quantity
    [InlineData("(37)-5")] // Negative quantity
    public void Parse_WithInvalidQuantity_ShouldReturnFailure(string invalidQuantity)
    {
        // Act
        var result = _parser.Parse(invalidQuantity);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void Parse_WithIncompleteAI_ShouldReturnFailure()
    {
        // Arrange
        var incompleteData = "(0"; // Incomplete AI

        // Act
        var result = _parser.Parse(incompleteData);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().Contain("AI", "error should mention Application Identifier");
    }

    [Fact]
    public void Parse_WithMixedValidAndInvalidData_ShouldReturnFailure()
    {
        // Arrange - valid SSCC followed by invalid date
        var mixedData = "(00)123456789012345678(17)999999";

        // Act
        var result = _parser.Parse(mixedData);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
    }

    #endregion

    #region IZ Prefix Tests

    [Theory]
    [InlineData("IZ")]
    [InlineData("iz")]
    [InlineData("Iz")]
    [InlineData("iZ")]
    public void Parse_WithIzPrefixVariations_ShouldDetectPrefix(string prefix)
    {
        // Arrange
        var gs1Code = $"{prefix}(00)123456789012345678";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasIzPrefix.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
    }

    [Fact]
    public void Parse_WithIzPrefixAndComplexData_ShouldParseCorrectly()
    {
        // Arrange
        var complexData = "IZ]C1(00)123456789012345678(02)12345678901234(10)BATCH123(17)241215(37)75";

        // Act
        var result = _parser.Parse(complexData);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasIzPrefix.Should().BeTrue();
        result.ParsedFieldsCount.Should().Be(5);
        
        result.SSCC!.Value.Should().Be("123456789012345678");
        result.Gtin!.Value.Should().Be("12345678901234");
        result.Lot!.Value.Should().Be("BATCH123");
        result.ExpiryDate!.Value.Should().Be(new DateOnly(2024, 12, 15));
        result.Quantity!.Value.Should().Be(75);
    }

    [Fact]
    public void Parse_WithoutIzPrefix_ShouldNotDetectPrefix()
    {
        // Arrange
        var gs1Code = "(00)123456789012345678(10)NORMAL";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasIzPrefix.Should().BeFalse();
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public void Parse_WithControlCharacters_ShouldRemoveAndParse()
    {
        // Arrange
        var dataWithControlChars = "\x01\x02(00)123456789012345678\x03\x04(10)TEST\x05\x06";

        // Act
        var result = _parser.Parse(dataWithControlChars);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.SSCC!.Value.Should().Be("123456789012345678");
        result.Lot!.Value.Should().Be("TEST");
    }

    [Fact]
    public void Parse_WithLeadingAndTrailingSpaces_ShouldTrimAndParse()
    {
        // Arrange
        var dataWithSpaces = "  \t(00)123456789012345678(10)TRIMMED  \n  ";

        // Act
        var result = _parser.Parse(dataWithSpaces);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasSSCC.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.Lot!.Value.Should().Be("TRIMMED");
    }

    [Fact]
    public void Parse_WithMaximumLengthLotNumber_ShouldParseCorrectly()
    {
        // Arrange - AI 10 supports up to 20 characters
        var longLot = new string('A', 20);
        var gs1Code = $"(10){longLot}";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.Lot!.Value.Should().Be(longLot);
        result.Lot!.Value.Length.Should().Be(20);
    }

    [Fact]
    public void Parse_WithMultipleSameAI_ShouldUseLastValue()
    {
        // Arrange - Multiple lot numbers, should use the last one
        var gs1Code = "(10)FIRST(10)SECOND(10)FINAL";

        // Act
        var result = _parser.Parse(gs1Code);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.Lot!.Value.Should().Be("FINAL");
    }

    #endregion

    #region Performance and Robustness Tests

    [Fact]
    public void Parse_WithVeryLongInput_ShouldHandleGracefully()
    {
        // Arrange - Very long but still valid input
        var longValidData = string.Concat(Enumerable.Repeat("(10)BATCH", 100));

        // Act
        var result = _parser.Parse(longValidData);

        // Assert
        result.Should().NotBeNull();
        // Should either parse successfully or fail gracefully with meaningful error
        if (!result.IsValid)
        {
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }
    }

    [Theory]
    [InlineData(1)]
    [InlineData(10)]
    [InlineData(100)]
    public void Parse_MultipleCallsWithSameData_ShouldBeConsistent(int iterations)
    {
        // Arrange
        var gs1Code = "IZ(00)123456789012345678(10)CONSISTENT(37)42";
        var expectedResults = new List<GS1ParseResult>();

        // Act
        for (int i = 0; i < iterations; i++)
        {
            expectedResults.Add(_parser.Parse(gs1Code));
        }

        // Assert
        expectedResults.Should().AllSatisfy(result =>
        {
            result.IsValid.Should().BeTrue();
            result.HasIzPrefix.Should().BeTrue();
            result.SSCC!.Value.Should().Be("123456789012345678");
            result.Lot!.Value.Should().Be("CONSISTENT");
            result.Quantity!.Value.Should().Be(42);
        });

        // All results should be equivalent
        for (int i = 1; i < expectedResults.Count; i++)
        {
            var first = expectedResults[0];
            var current = expectedResults[i];
            
            current.IsValid.Should().Be(first.IsValid);
            current.HasIzPrefix.Should().Be(first.HasIzPrefix);
            current.ParsedFieldsCount.Should().Be(first.ParsedFieldsCount);
        }
    }

    #endregion

    #region Culture-Specific Tests

    [Fact]
    public void Parse_WithPolishCulture_ShouldReturnPolishErrorMessages()
    {
        // Arrange
        var originalCulture = CultureInfo.CurrentCulture;
        var originalUICulture = CultureInfo.CurrentUICulture;
        
        try
        {
            CultureInfo.CurrentCulture = new CultureInfo("pl-PL");
            CultureInfo.CurrentUICulture = new CultureInfo("pl-PL");

            // Act
            var result = _parser.Parse("");

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
            // Should contain Polish words
            result.ErrorMessage.Should().MatchRegex(@"\b(dane|puste|błąd|nieprawidłowy)\b", 
                "Error message should contain Polish words");
        }
        finally
        {
            CultureInfo.CurrentCulture = originalCulture;
            CultureInfo.CurrentUICulture = originalUICulture;
        }
    }

    #endregion
}
