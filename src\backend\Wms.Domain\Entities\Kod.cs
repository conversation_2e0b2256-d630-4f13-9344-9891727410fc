using Wms.Domain.Common;
using Wms.Domain.Entities.Receives; // For AwizacjaDane entity

namespace Wms.Domain.Entities;

/// <summary>
/// Encja reprezentująca kod produktu w kartotece
/// Mapowana na tabelę kody
/// </summary>
public class Kod : BaseEntity
{
    public int Id { get; set; }
    public string KodValue { get; set; } = string.Empty; // kod -> KodValue (conflict z class name)
    public string? Kod2 { get; set; }
    public string KodNazwa { get; set; } = string.Empty;
    public int SystemId { get; set; } = 0;
    public string? Jm { get; set; }
    public string EanJednostki { get; set; } = string.Empty;
    public decimal WagaSztKg { get; set; } = 0.000m;
    public int ObjetoscSztCm { get; set; } = 0;
    public string? Ean { get; set; }
    public string? OpakowanieJm { get; set; }
    public int? IloscWOpakowaniu { get; set; } = 0;
    public string EanOpakowanieZbiorcze { get; set; } = string.Empty;
    public int IloscSztWZbiorczym { get; set; } = 0;
    public string? KodProducent { get; set; }
    public int IloscOpakWZbiorczym { get; set; } = 1;
    public string? KodKategoria { get; set; }
    public string? KodPlec { get; set; }
    public int Active { get; set; } = 1;
    public string Gln { get; set; } = string.Empty;
    public string Wlasciciel { get; set; } = string.Empty;
    public string BrandId { get; set; } = "1";
    public int? KodyGrupyId { get; set; }
    public string? Wysokosc { get; set; }
    public string JednostkaWagi { get; set; } = string.Empty;
    public int IloscSztPalecie { get; set; } = 0;
    public int IloscDniPrzydatnosci { get; set; } = 0;
    public int WymaganaPartia { get; set; } = 0;
    public int WymaganaDataWaznosci { get; set; } = 0;
    public int WymaganaDataprod { get; set; } = 0;
    public int? StatusJakosciDomyslny { get; set; }
    public int DlugoscSztMm { get; set; } = 0;
    public int SzerokoscSztMm { get; set; } = 0;
    public int WysokoscSztMm { get; set; } = 0;
    public int JednostkaSkladowaniaDomyslna { get; set; } = 0;
    public int KrajPochodzeniaId { get; set; } = 0;
    public int NowyKodMail { get; set; } = 0;
    public DateTime? TsCreated { get; set; }
    public int AdrTowary { get; set; } = 0;
    
    // Navigation properties
    public ICollection<AwizacjaDane> AwizacjaDane { get; set; } = new List<AwizacjaDane>();
    public ICollection<Label> Labels { get; set; } = new List<Label>();
    
    // Business logic properties
    public bool IsActive => Active == 1;
    public bool RequiresLot => WymaganaPartia == 1;
    public bool RequiresExpiryDate => WymaganaDataWaznosci == 1;
    public bool RequiresProductionDate => WymaganaDataprod == 1;
    public bool HasPackaging => IloscWOpakowaniu > 0;
    
    /// <summary>
    /// Sprawdza czy kod można zidentyfikować po EAN
    /// </summary>
    public bool MatchesEAN(string eanCode)
    {
        return !string.IsNullOrEmpty(eanCode) && (
            eanCode == Ean || 
            eanCode == EanJednostki || 
            eanCode == EanOpakowanieZbiorcze
        );
    }
    
    /// <summary>
    /// Oblicza ilość sztuk na podstawie AI 37 (count)
    /// </summary>
    public decimal CalculateQuantityFromAI37(int ai37Value)
    {
        if (!HasPackaging) return ai37Value;
        
        return ai37Value * (IloscWOpakowaniu ?? 1);
    }
    
    /// <summary>
    /// Zwraca wymagania walidacyjne dla UI
    /// </summary>
    public ValidationRequirements GetValidationRequirements()
    {
        return new ValidationRequirements
        {
            RequiresLot = this.RequiresLot,
            RequiresExpiryDate = this.RequiresExpiryDate,
            RequiresProductionDate = this.RequiresProductionDate
        };
    }
}

/// <summary>
/// Pomocnicza klasa dla wymagań walidacyjnych
/// </summary>
public class ValidationRequirements
{
    public bool RequiresLot { get; set; }
    public bool RequiresExpiryDate { get; set; }
    public bool RequiresProductionDate { get; set; }
}
