using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Wms.Infrastructure.Data;

public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<WmsDbContext>
{
    public WmsDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<WmsDbContext>();
        
        // Design-time connection string dla dev serwera
        var connectionString = "Server=25.21.88.159;Database=wmsggtest;User=test;Password=test123;Port=3306;";
        
        optionsBuilder.UseMySql(connectionString, ServerVersion.Parse("8.0.39-mysql"));
        
        return new WmsDbContext(optionsBuilder.Options);
    }
}
