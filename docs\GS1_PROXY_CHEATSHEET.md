# GS1 Proxy Cheat Sheet

Szybki zestaw komend do testowania parsowania GS1-128 przez API Proxy (127.0.0.1:8080).

## 1) <PERSON><PERSON><PERSON> środowisk<PERSON>e

```bash path=null start=null
export BASE_URL=http://127.0.0.1:8080
```

PowerShell:
```powershell path=null start=null
$env:BASE_URL = 'http://127.0.0.1:8080'
```

## 2) Health check

```bash path=null start=null
curl -s "$BASE_URL/api/v1/health"
```

## 3) Login – pobranie tokena JWT

Bash (bez jq – wytnij token ręcznie lub użyj narzędzia jq jeśli masz):
```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/auth/login-scan" \
  -H "Content-Type: application/json" \
  -d '{"cardNumber":"1234567","deviceId":"CLI"}'
# Skopiuj warto<PERSON> pola token i ustaw zmienną AUTH_TOKEN poniżej
export AUTH_TOKEN={{JWT_TOKEN}}
```

PowerShell:
```powershell path=null start=null
$resp = curl -s -Method POST "$env:BASE_URL/api/v1/auth/login-scan" -ContentType 'application/json' -Body '{"cardNumber":"1234567","deviceId":"CLI"}'
# Wyodrębnij token ręcznie z $resp.Content i ustaw:
$env:AUTH_TOKEN = "{{JWT_TOKEN}}"
```

Uwaga: nie wyświetlaj tokena w logach produkcyjnych. Traktuj go jako sekret.

## 4) Testy parsowania GS1

- SSCC + Lot + Data + Ilość (z prefiksem IZ)
```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C10012345678901234567810ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

- GTIN (AI 01) + Lot + Data + Ilość (z prefiksem IZ) – poprawny GTIN-14
```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C1010590123412345710ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

- GTIN (AI 02) + Lot + Data + Ilość – pamiętaj o prawidłowej cyfrze kontrolnej
```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C102{GTIN14}10ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

## 5) Typowe błędy
- scanType: Unknown – użyto nawiasów (np. (01)). Używaj surowego ciągu bez nawiasów.
- Nie znaleziono AI na pozycji X – brak FNC1 po polu o zmiennej długości (np. AI 10).
- GTIN = null – błędna cyfra kontrolna lub źle sformatowany łańcuch.

