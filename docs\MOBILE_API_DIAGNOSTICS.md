# 🔍 Diagnostyka API w aplikacji mobilnej WMS

## ✅ ROZWIĄZANO: Problem z logowaniem "Login failed" (2025-09-04)

### 🎯 **Status**: **PROBLEM ROZWIĄZANY**
- **Przyczyna**: Niekompatybilność modelu `UserInfo` między aplikacją mobilną a backend
- **Rozwiązanie**: Us<PERSON>ęto pole `Username` z modelu mobilnego
- **Rezultat**: Logowanie kartą `1234567` działa poprawnie przez reverse proxy

### 🔍 **Szczegóły techniczne naprawy**:
1. **Model mobilny** zawierał pole `Username`, którego **backend nie zwracał**
2. **Deserializacja JSON** się nie udawała, mimo kodu 200 z serwera
3. **`response.Content`** był `null` przez błąd deserializacji
4. **Aplikacja pokazy<PERSON>ła** "Login failed" zamiast prawidłowego logowania

---

## Problem: "Paleta nie została znaleziona w systemie" dla *********

### ✅ Status weryfikacji (2025-09-03):
- **API produkcyjne (***********)**: ✅ **DZIAŁA POPRAWNIE**
- **Paleta ***********: ✅ **ISTNIEJE** (ID: 3345469, Lokalizacja: MP-002-DSV-001-1)
- **Mock service**: ✅ **MA DANE** dla *********

## 🚨 Najczęstsze przyczyny problemu:

### 1. **Aplikacja używa trybu Mock zamiast serwera produkcyjnego**
**Symptom**: Komunikat "Paleta nie została znaleziona w systemie"  
**Przyczyna**: Domyślnie aplikacja startuje w trybie Mock (dane testowe)

### 2. **Jak sprawdzić aktualne środowisko API:**

#### Na ekranie logowania:
1. **Selektor środowiska** - sprawdź wybrane środowisko w pierwszym polu
2. **Panel diagnostyczny** - pod selektorem powinny być widoczne informacje:
   ```
   🔧 Current API: Serwer produkcyjny (***********)
   📡 URL: http://***********/api/v1/
   🔄 Service: Real API
   ```

#### Jeśli widzisz:
```
🔧 Current API: Mock (Dane testowe)
📡 URL: mock://localhost/
🔄 Service: Mock Data
```
**→ To oznacza, że aplikacja używa danych testowych!**

## 🛠️ **Rozwiązanie krok po kroku:**

### Krok 1: Zmiana środowiska API
1. **Otwórz aplikację WMS**
2. **Na ekranie logowania** znajdź selektor "Choose API Environment"
3. **Zmień z "Mock (Dane testowe)"** na **"Serwer produkcyjny (***********)"**
4. **Sprawdź panel diagnostyczny** - powinien pokazać:
   - URL: `http://***********/api/v1/`
   - Service: `Real API`

### Krok 2: Weryfikacja połączenia
1. **Zaloguj się kartą 1234567** (karta testowa która działa)
2. **Przejdź do funkcji sprawdzania palet**
3. **Wpisz kod ***********
4. **Powinno zwrócić dane palety**

### Krok 3: Diagnostyka problemów
Jeśli nadal nie działa, sprawdź:

#### Połączenie sieciowe:
```bash
# Z terminala lub cmd:
curl -H "Accept: application/json" http://***********/api/v1/health
```
**Oczekiwany wynik**: Status 200 OK

#### Autoryzacja:
- Sprawdź czy token JWT nie wygasł
- Spróbuj wylogować się i zalogować ponownie

#### Debug logi:
W Visual Studio/Android Studio sprawdź logi aplikacji:
```
[WMS] Creating API service for: http://***********/api/v1/
[WMS] HTTP Client configured with timeout: 00:01:00
[WMS] Updated API Service - Environment: ProductionServer, URL: http://***********/api/v1/, Service Type: RefitClient
```

## 📊 **Mapowanie środowisk:**

| Nazwa w aplikacji | URL | Zastosowanie |
|-------------------|-----|--------------|
| **Mock (Dane testowe)** | `mock://localhost/` | Tryb offline, dane testowe |
| **Serwer developerski** | `http://************/api/` | Serwer deweloperski |
| **Serwer produkcyjny** | `http://***********/api/v1/` | ✅ **SERWER PRODUKCYJNY** |
| Android Emulator | `http://********:8080/api/v1/` | Dla emulatora Android |
| Android Host | `http://***********:8080/api/v1/` | Dla urządzeń Android |

## ⚠️ **Typowe błędy:**

### 1. "Network timeout - check WiFi connection"
- **Przyczyna**: Brak połączenia z serwerem ***********
- **Rozwiązanie**: Sprawdź sieć WiFi, VPN lub dostępność serwera

### 2. "Backend API not available"
- **Przyczyna**: Serwer API jest niedostępny
- **Rozwiązanie**: Skontaktuj się z administratorem IT

### 3. "401 Unauthorized"
- **Przyczyna**: Nieprawidłowe dane logowania lub wygasły token
- **Rozwiązanie**: Wyloguj się i zaloguj ponownie

## 🎯 **Test weryfikacyjny:**

### Dla sprawdzenia czy aplikacja łączy się z serwerem produkcyjnym:

1. **Ustaw środowisko**: "Serwer produkcyjny (***********)"
2. **Zaloguj się kartą**: `1234567`
3. **Sprawdź paletę**: `*********`
4. **Oczekiwany wynik**: 
   ```
   Paleta: *********
   ID: 3345469
   Lokalizacja: MP-002-DSV-001-1
   Etykiety: 4 (2 aktywne, 2 nieaktywne)
   ```

### Jeśli test się nie powiedzie:
1. **Sprawdź panel diagnostyczny** na ekranie logowania
2. **Zweryfikuj połączenie sieciowe** 
3. **Skontaktuj się z działem IT** jeśli serwer jest niedostępny

---

**Podsumowanie**: Problem "Paleta nie została znaleziona w systemie" dla ********* **nie jest związany z kodem lub danymi**, ale z **konfiguracją środowiska API w aplikacji mobilnej**. Serwer produkcyjny działa poprawnie i zawiera wymagane dane.
