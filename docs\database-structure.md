# WMS Database Structure

## Overview

WMS system uses MySQL database with mixed legacy and modern structure. The database preserves existing production tables with Polish naming conventions while adding new tables for enhanced functionality.

## Database Connection

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=************;Database=wmsggtest;User=test;Password=test123;Port=3306;"
  }
}
```

## Table Structure

### Core Tables (Existing Legacy)

#### `pracownicy` (Users)
Stores employee information and authentication data.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Auto-increment |
| `imie_nazwisko` | VARCHAR(100) | Full name | Employee display name |
| `karta` | VARCHAR(50) | Card number | RFID/Barcode for authentication |
| `pozycja` | VARCHAR(100) | Job position | Role/title |
| `email` | VARCHAR(100) | Email address | Optional contact |
| `dzial` | VARCHAR(100) | Department | Organizational unit |
| `is_active` | TINYINT(1) | Active status | **NEW**: Default 1, for soft delete |

**Entity Mapping**: `User` class in `Wms.Domain.Entities`

---

#### `miejsca` (Locations)
Warehouse storage location definitions.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Auto-increment |
| `kod` | VARCHAR(50) | Location code | Format: MP-H-R-M-P |
| `hala` | INT | Hall number | Warehouse hall |
| `regal` | VARCHAR(10) | Rack identifier | Shelf/rack code |
| `miejsce` | INT | Position number | Position on rack |
| `poziom` | VARCHAR(10) | Level identifier | Height level (optional) |
| `max_pojemnosc` | DECIMAL(10,0) | Max capacity | Maximum pallets |
| `is_visible` | TINYINT(1) | Visibility flag | If location is active |
| `is_picking` | TINYINT(1) | Picking location | Used for order picking |

**Entity Mapping**: `Location` class  
**Business Rules**: 
- Location codes validated with regex patterns
- Capacity management with overflow checks
- Picking locations have special workflow rules

---

#### `palety` (Pallets)
Physical pallet definitions and main SSCC codes.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Auto-increment |
| `sscc_main` | VARCHAR(18) | Main SSCC code | 18-digit international code |
| `created_at` | DATETIME | Creation timestamp | When pallet was first registered |
| `status` | VARCHAR(50) | Current status | Active, Shipped, etc. |

**Entity Mapping**: `Pallet` class  
**Business Rules**:
- One pallet can have multiple labels/SSCCs
- Main SSCC used for primary identification
- Status tracked for lifecycle management

---

#### `etykiety` (Labels)
Individual labels/barcodes attached to pallets with detailed product information.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Composite with system_id |
| `system_id` | INT | System identifier | Part of composite key |
| `etykieta_klient` | VARCHAR(100) | Client label/DS code | Alternative identifier |
| `sscc` | VARCHAR(18) | SSCC barcode | 18-digit code |
| `paleta_id` | INT | Pallet reference | FK to palety.id |
| `miejscep` | INT | Current location | FK to miejsca.id |
| `ilosc` | DECIMAL(10,2) | Quantity | Product quantity |
| `data_waznosci` | DATE | Expiry date | Product expiration |
| `lot` | VARCHAR(50) | Batch/lot number | Production batch |
| `active` | INT | Active status | 1 = active, 0 = inactive |
| `ts` | DATETIME | Timestamp | Last modification |

**Entity Mapping**: `Label` class  
**Key Structure**: Composite primary key (`id`, `system_id`)  
**Navigation Properties**: References to `Pallet` and `Location`

---

#### `ruchy` (Movements)
Historical record of all pallet movements and operations.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Auto-increment |
| `typ` | VARCHAR(20) | Movement type | MOVE, IN, OUT, etc. |
| `doc_nr` | INT | Document number | Reference number |
| `pracownik_id` | INT | Worker ID | FK to pracownicy.id |
| `data` | DATE | Movement date | Date of operation |
| `etykieta` | INT | Label ID | FK to etykiety.id |
| `system_id` | INT | System ID | FK to etykiety.system_id |
| `stare_m` | INT | From location | Previous location ID |
| `nowe_m` | INT | To location | New location ID |
| `doc_internal` | VARCHAR(20) | Internal document | Source system (WM = WMS Mobile) |
| `stat` | INT | Status | Movement status |
| `tszm` | DATETIME | Timestamp | Exact movement time |
| `uwagi` | TEXT | Notes | Additional comments |

**Entity Mapping**: `Movement` class  
**Business Rules**:
- Complete audit trail of all operations
- Foreign key references maintain data integrity
- Timestamps provide precise tracking

---

### New Tables (Added for WMS Mobile)

#### `sessions` (JWT Sessions)
Tracks JWT authentication sessions for security and audit purposes.

| Column | Type | Description | Notes |
|--------|------|-------------|--------|
| `id` | INT | Primary key | Auto-increment |
| `user_id` | INT | User reference | FK to pracownicy.id |
| `device_id` | VARCHAR(100) | Device identifier | Mobile device ID (optional) |
| `ip_address` | VARCHAR(45) | Client IP | IPv4/IPv6 address |
| `login_time` | DATETIME | Login timestamp | Session start |
| `logout_time` | DATETIME | Logout timestamp | Session end (nullable) |
| `is_active` | TINYINT(1) | Active session | 1 = active, 0 = logged out |

**Entity Mapping**: `Session` class  
**Purpose**: JWT token validation, session management, security audit  
**Creation Script**:
```sql
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_id VARCHAR(100),
    ip_address VARCHAR(45),
    login_time DATETIME NOT NULL,
    logout_time DATETIME NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES pracownicy(id)
);
```

---

## Entity Relationships

### Primary Relationships

```mermaid
erDiagram
    pracownicy ||--o{ sessions : "user_id"
    pracownicy ||--o{ ruchy : "pracownik_id"
    miejsca ||--o{ etykiety : "miejscep"
    miejsca ||--o{ ruchy : "stare_m/nowe_m"
    palety ||--o{ etykiety : "paleta_id" 
    etykiety ||--o{ ruchy : "etykieta,system_id"
```

### Key Business Rules

1. **User Authentication**: `pracownicy.karta` → JWT session → `sessions` table
2. **Pallet Location**: `etykiety.miejscep` → `miejsca.id` (current location)
3. **Movement Tracking**: Every location change creates `ruchy` record
4. **Composite Keys**: `etykiety` uses (`id`, `system_id`) composite primary key
5. **Soft Deletes**: Users marked inactive via `is_active` flag

---

## Migration Strategy

### Existing Data Preservation
- **No schema changes** to production tables (`pracownicy`, `miejsca`, `palety`, `etykiety`, `ruchy`)
- **Additive approach** - only new tables and columns added
- **Backward compatibility** maintained with existing systems

### New Additions
1. **`sessions` table** - Complete new table for JWT management
2. **`pracownicy.is_active`** - New column for user management
3. **EF Core migrations** - Manages only new additions

### Manual Schema Script
```sql
-- Add sessions table
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_id VARCHAR(100),
    ip_address VARCHAR(45),
    login_time DATETIME NOT NULL,
    logout_time DATETIME NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES pracownicy(id)
);

-- Add is_active to users
ALTER TABLE pracownicy ADD COLUMN is_active TINYINT(1) DEFAULT 1;
```

---

## Code Validation Integration

The database structure supports comprehensive code validation:

| Code Type | Table Column | Validation Pattern | Purpose |
|-----------|--------------|-------------------|---------|
| SSCC | `etykiety.sscc` | `^\d{18}$` | Pallet identification |
| DS Codes | `etykiety.etykieta_klient` | `^DS\d{4,9}$` | Client labeling |
| Location | `miejsca.kod` | `^MP-\d{1,2}-[A-Z]{1,2}-\d{1,2}-[A-Z0-9]{1,2}$` | Location addressing |

---

## Performance Considerations

### Indexing Strategy
- **Primary Keys**: Auto-increment integers for optimal performance
- **Foreign Keys**: All foreign key columns indexed automatically
- **Lookup Columns**: `pracownicy.karta`, `etykiety.sscc`, `miejsca.kod` should have indexes
- **Composite Keys**: `etykiety(id, system_id)` indexed as composite

### Query Optimization
- **Current Location**: Direct lookup via `etykiety.miejscep`
- **Movement History**: Efficiently queried via `ruchy.etykieta` and `ruchy.system_id`
- **User Sessions**: Fast lookup via `sessions.user_id` and `sessions.is_active`

---

## Data Integrity

### Referential Integrity
- All foreign key relationships enforced at database level
- Cascade rules prevent orphaned records
- Transaction-based operations ensure consistency

### Business Rule Enforcement
- Code validation prevents invalid identifiers
- Capacity checks prevent location overflow
- Session management ensures authentication security
- Movement audit trail maintains complete history
