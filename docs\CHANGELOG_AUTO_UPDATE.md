# Auto-Update Feature - Changelog

All notable changes to the auto-update functionality will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-01-09

### Added
- **Complete auto-update system implementation**
  - `AppManifest` model for JSON manifest parsing
  - `UpdateInfo` model for update status information
  - `DownloadProgress` model for download progress tracking
  - `DownloadStatus` enum for download state management

- **UpdateService implementation**
  - `CheckForUpdatesAsync()` - Manifest fetching and version comparison
  - `DownloadApkAsync()` - APK download with progress reporting
  - `VerifyApkIntegrityAsync()` - SHA-256 integrity verification
  - `InstallApkAsync()` - APK installation with FileProvider support
  - `GetCurrentVersion()` / `GetCurrentVersionCode()` - Version info
  - `CanInstallFromUnknownSources()` - Permission checking
  - `RequestInstallPermissionAsync()` - Permission request flow

- **Android platform support**
  - FileProvider configuration for secure APK sharing
  - Required permissions in AndroidManifest.xml
  - Support for Android API 29+ (Android 10+)
  - FileProvider paths configuration

- **UI Components**
  - `AboutPage` with version information and update controls
  - `AboutViewModel` with complete update workflow logic
  - Automatic update checking on app startup (3-second delay)
  - Update dialog with optional/required update handling
  - Download progress visualization
  - User-friendly error messages in Polish

- **Security features**
  - HTTPS-only communication
  - SHA-256 file integrity verification
  - FileProvider for secure file sharing (Android N+)
  - Proper permission handling for unknown sources

- **Configuration**
  - Dependency injection integration
  - HttpClient configuration with 60-second timeout
  - Manifest URL configuration (`https://172.6.1.249/wms_android_update/app.json`)
  - Automatic startup check integration in AppShell

### Technical Details

#### Files Added
```
Models/Update/
├── AppManifest.cs           # JSON manifest model
├── UpdateInfo.cs            # Update result model  
└── DownloadProgress.cs      # Download progress tracking

Services/
├── Contracts/
│   └── IUpdateService.cs    # Service interface
└── UpdateService.cs         # Main implementation (370+ lines)

ViewModels/
└── AboutViewModel.cs        # UI logic (180+ lines)

Views/
├── AboutPage.xaml           # Update UI page
└── AboutPage.xaml.cs        # Code-behind

Platforms/Android/
├── AndroidManifest.xml      # Added permissions & FileProvider
└── Resources/xml/
    └── file_provider_paths.xml  # FileProvider configuration
```

#### Dependencies Modified
- `MauiProgram.cs` - Added UpdateService registration
- `AppShell.xaml` / `AppShell.xaml.cs` - Added AboutPage route and automatic update checking
- `OptionsPage.xaml` / `OptionsViewModel.cs` - Added "About" navigation

#### Manifest Format Specification
```json
{
  "version": "string",           // Human-readable version (e.g., "1.0.5")
  "versionCode": "number",       // Numeric version code for comparison
  "downloadUrl": "string",       // HTTPS URL to APK file
  "sha256": "string",           // SHA-256 hash of APK file
  "fileSize": "number",         // File size in bytes
  "releaseNotes": "string",     // What's new description
  "required": "boolean",        // Whether update is mandatory
  "minSdkVersion": "number",    // Minimum Android SDK version
  "releaseDate": "string"       // ISO 8601 timestamp
}
```

### Workflow Implementation

1. **Automatic Check (Startup)**
   - App starts → 3-second delay → Background update check
   - If update available → Show dialog with release notes
   - Required updates are blocking, optional allow "Later"

2. **Manual Check (About Page)**
   - User navigates to Options → About
   - "Check for Updates" button triggers manual check
   - Real-time status updates and progress indicators

3. **Download & Install Process**
   - User accepts update → Download starts with progress
   - SHA-256 verification → Install APK via Android system
   - Error handling for network issues, permission problems, hash mismatches

### Security Measures

- **Network Security**: HTTPS-only manifest and APK downloads
- **File Integrity**: SHA-256 verification before installation
- **Permissions**: Proper Android permission handling for unknown sources
- **File Sharing**: FileProvider for secure APK file access

### Testing

- **Compilation**: All platforms compile successfully (Android, iOS, Windows, macOS)
- **Functionality**: Core update checking and download logic implemented
- **Error Handling**: Network errors, permission issues, and hash mismatches handled
- **UI**: Complete user interface with progress indicators and status messages

### Performance

- **Network**: Efficient streaming download with progress reporting
- **Memory**: Uses buffered I/O (8KB chunks) for large APK files
- **Threading**: Background operations don't block UI thread
- **Startup**: Minimal impact with 3-second delayed background check

### Documentation

- `AUTO_UPDATE_GUIDE.md` - Complete technical documentation (1000+ lines)
- `AUTO_UPDATE_README.md` - Developer quick-start guide (600+ lines)
- `example-app.json` - Sample manifest file for testing

### Known Limitations

- **Platform**: Auto-update only works on Android (by design)
- **Server**: Requires HTTPS server hosting manifest and APK files
- **Permissions**: Requires user approval for installation from unknown sources
- **Size**: Large APK files may take significant time to download

### Breaking Changes
None - This is the initial implementation.

### Deprecated
None - This is the initial implementation.

### Removed
None - This is the initial implementation.

### Fixed
- Compilation issues with XAML converters resolved
- FileProvider configuration for Android N+ compatibility
- Dependency injection configuration for UpdateService

---

## Compatibility

- **.NET**: 9.0+
- **MAUI**: Latest version
- **Android**: API 29+ (Android 10+)
- **Server**: Apache 2.4+ with SSL support

## Migration Guide

Not applicable - this is the initial implementation.

## Contributors

- WMS System Development Team

## Support

For issues and questions regarding the auto-update functionality:
1. Check the documentation files
2. Review the example manifest format
3. Verify server configuration
4. Test with provided example files

---

## [1.0.1] - 2025-09-04

### Fixed
- **Critical Login Bug Resolved**
  - Fixed `UserInfo` model compatibility issue between mobile and backend
  - Removed `Username` field from mobile `UserInfo` model to match backend response
  - Login now properly deserializes successful authentication responses
  - Added detailed debugging logs for login process
  - Updated `MockWmsApiService` to use corrected model structure

### Changed
- Added `TC21Proxy` environment support in `ApiConfigurationService`
- Enhanced login debugging with response content validation
- Improved error reporting for failed authentication attempts

### Technical Details
- **Files Modified**:
  - `src/mobile/WmsApp/Models/Auth/LoginScanRequest.cs` - Removed Username field
  - `src/mobile/WmsApp/Services/MockWmsApiService.cs` - Updated mock response
  - `src/mobile/WmsApp/Services/ApiConfigurationService.cs` - Added TC21Proxy support
  - `src/mobile/WmsApp/ViewModels/LoginViewModel.cs` - Enhanced debugging

- **Root Cause**: Mobile app expected `Username` field in JWT response, but backend only returns `fullName`, `position`, `email`, `department`
- **Resolution**: Aligned mobile model with actual backend response structure
- **Testing**: App now successfully authenticates with card number `1234567` via reverse proxy

### Breaking Changes
None - This is a bug fix that improves compatibility.

### Security
- No security implications - purely a data model alignment fix
- JWT token handling remains unchanged and secure

---

**Release Date**: 2025-01-09  
**Total Files Changed**: 15+  
**Lines of Code Added**: 2000+  
**Features Implemented**: 100% of Faza 5 from TODO_FRONTEND.md
