using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class LabelRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly LabelRepository _labelRepository;

    public LabelRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _labelRepository = new LabelRepository(_context);

        SeedTestData();
    }

    [Fact]
    public async Task GetBySSCCAsync_WithValidActiveSSCC_ShouldReturnLabelWithIncludes()
    {
        // Arrange
        var sscc = "123456789012345678";

        // Act
        var result = await _labelRepository.GetBySSCCAsync(sscc);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.Sscc.Should().Be(sscc);
        result.Active.Should().Be(1);
        
        // Verify includes
        result.Pallet.Should().NotBeNull();
        result.Pallet!.Id.Should().Be(1);
        result.Location.Should().NotBeNull();
        result.Location!.Id.Should().Be(1);
    }

    [Fact]
    public async Task GetBySSCCAsync_WithInactiveSSCC_ShouldReturnNull()
    {
        // Arrange
        var inactiveSSCC = "987654321098765432"; // This SSCC exists but is inactive

        // Act
        var result = await _labelRepository.GetBySSCCAsync(inactiveSSCC);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetBySSCCAsync_WithNonExistentSSCC_ShouldReturnNull()
    {
        // Arrange
        var nonExistentSSCC = "999999999999999999";

        // Act
        var result = await _labelRepository.GetBySSCCAsync(nonExistentSSCC);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByClientCodeAsync_WithValidActiveClientCode_ShouldReturnLabelWithIncludes()
    {
        // Arrange
        var clientCode = "CLIENT-001";

        // Act
        var result = await _labelRepository.GetByClientCodeAsync(clientCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.EtykietaKlient.Should().Be(clientCode);
        result.Active.Should().Be(1);
        
        // Verify includes
        result.Pallet.Should().NotBeNull();
        result.Location.Should().NotBeNull();
    }

    [Fact]
    public async Task GetByClientCodeAsync_WithInactiveClientCode_ShouldReturnNull()
    {
        // Arrange
        var inactiveClientCode = "CLIENT-INACTIVE";

        // Act
        var result = await _labelRepository.GetByClientCodeAsync(inactiveClientCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByClientCodeAsync_WithNonExistentClientCode_ShouldReturnNull()
    {
        // Arrange
        var nonExistentClientCode = "NON-EXISTENT";

        // Act
        var result = await _labelRepository.GetByClientCodeAsync(nonExistentClientCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByPalletIdAsync_ShouldReturnAllLabelsOrderedByActiveAndTimestamp()
    {
        // Arrange
        var palletId = 1;

        // Act
        var result = await _labelRepository.GetByPalletIdAsync(palletId);

        // Assert
        var labels = result.ToList();
        labels.Should().HaveCount(3);
        
        // Should be ordered by Active (desc), then Ts (desc)
        labels[0].Should().Match<Label>(l => l.Id == 1 && l.Active == 1);
        labels[1].Should().Match<Label>(l => l.Id == 2 && l.Active == 1);
        labels[2].Should().Match<Label>(l => l.Id == 4 && l.Active == 0); // Inactive should be last
        
        // Verify includes
        labels.Should().AllSatisfy(l => l.Location.Should().NotBeNull());
    }

    [Fact]
    public async Task GetByPalletIdAsync_WithNonExistentPalletId_ShouldReturnEmptyCollection()
    {
        // Arrange
        var nonExistentPalletId = 999;

        // Act
        var result = await _labelRepository.GetByPalletIdAsync(nonExistentPalletId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetActiveByPalletIdAsync_ShouldReturnOnlyActiveLabels()
    {
        // Arrange
        var palletId = 1;

        // Act
        var result = await _labelRepository.GetActiveByPalletIdAsync(palletId);

        // Assert
        var labels = result.ToList();
        labels.Should().HaveCount(2); // Only active labels
        labels.Should().AllSatisfy(l => l.Active.Should().Be(1));
        labels.Should().Contain(l => l.Id == 1);
        labels.Should().Contain(l => l.Id == 2);
        labels.Should().NotContain(l => l.Id == 4); // Inactive label
        
        // Verify includes
        labels.Should().AllSatisfy(l => l.Location.Should().NotBeNull());
    }

    [Fact]
    public async Task GetActiveByPalletIdAsync_WithNonExistentPalletId_ShouldReturnEmptyCollection()
    {
        // Arrange
        var nonExistentPalletId = 999;

        // Act
        var result = await _labelRepository.GetActiveByPalletIdAsync(nonExistentPalletId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task UpdateLocationAsync_WithValidLabel_ShouldUpdateLocation()
    {
        // Arrange
        var labelId = 1;
        var systemId = 1;
        var newLocationId = 2;
        
        // Verify initial state
        var initialLabel = await _context.Labels
            .FirstAsync(l => l.Id == labelId && l.SystemId == systemId);
        var initialLocationId = initialLabel.Miejscep;
        initialLocationId.Should().NotBe(newLocationId);

        // Act
        await _labelRepository.UpdateLocationAsync(labelId, systemId, newLocationId);

        // Assert
        var updatedLabel = await _context.Labels
            .FirstAsync(l => l.Id == labelId && l.SystemId == systemId);
        updatedLabel.Miejscep.Should().Be(newLocationId);
    }

    [Fact]
    public async Task UpdateLocationAsync_WithNonExistentLabel_ShouldNotThrowException()
    {
        // Arrange
        var nonExistentLabelId = 999;
        var nonExistentSystemId = 999;
        var newLocationId = 2;

        // Act & Assert - Should not throw exception
        await _labelRepository.UpdateLocationAsync(nonExistentLabelId, nonExistentSystemId, newLocationId);

        // Verify no changes were made to existing labels - they should maintain their original locations
        var existingLabels = await _context.Labels.ToListAsync();
        // Since the non-existent label update should be ignored, existing labels keep their locations
        // We just verify that the method didn't throw and the database state is consistent
    }

    [Theory]
    [InlineData(1, 1, 2)]
    [InlineData(2, 1, 1)]
    [InlineData(3, 1, 3)]
    public async Task UpdateLocationAsync_WithDifferentLabels_ShouldUpdateCorrectLabel(
        int labelId, int systemId, int newLocationId)
    {
        // Arrange - Get initial state
        var initialLabel = await _context.Labels
            .FirstOrDefaultAsync(l => l.Id == labelId && l.SystemId == systemId);
        
        if (initialLabel == null)
        {
            // Skip test if label doesn't exist
            return;
        }

        var initialLocationId = initialLabel.Miejscep;

        // Act
        await _labelRepository.UpdateLocationAsync(labelId, systemId, newLocationId);

        // Assert
        var updatedLabel = await _context.Labels
            .FirstAsync(l => l.Id == labelId && l.SystemId == systemId);
        updatedLabel.Miejscep.Should().Be(newLocationId);

        // This test only verifies that the specific label was updated correctly
        // Other labels can have the same location - that's normal behavior
    }

    [Fact]
    public async Task GetBySSCCAsync_WithNullOrEmptySSCC_ShouldReturnNull()
    {
        // Act & Assert
        var resultNull = await _labelRepository.GetBySSCCAsync(null!);
        var resultEmpty = await _labelRepository.GetBySSCCAsync("");
        var resultWhitespace = await _labelRepository.GetBySSCCAsync("   ");

        resultNull.Should().BeNull();
        resultEmpty.Should().BeNull();
        resultWhitespace.Should().BeNull();
    }

    [Fact]
    public async Task GetByClientCodeAsync_WithNullOrEmptyClientCode_ShouldReturnNull()
    {
        // Act & Assert
        var resultNull = await _labelRepository.GetByClientCodeAsync(null!);
        var resultEmpty = await _labelRepository.GetByClientCodeAsync("");
        var resultWhitespace = await _labelRepository.GetByClientCodeAsync("   ");

        resultNull.Should().BeNull();
        resultEmpty.Should().BeNull();
        resultWhitespace.Should().BeNull();
    }

    [Fact]
    public async Task GetByPalletIdDirectAsync_WithValidPalletId_ShouldReturnLabelWithIncludes()
    {
        // Arrange
        var palletId = 1;

        // Act
        var result = await _labelRepository.GetByPalletIdDirectAsync(palletId);

        // Assert
        result.Should().NotBeNull();
        result!.PaletaId.Should().Be(palletId);
        result.Active.Should().Be(1); // Should return active label
        
        // Verify includes
        result.Pallet.Should().NotBeNull();
        result.Pallet!.Id.Should().Be(palletId);
        result.Location.Should().NotBeNull();
        result.Location!.Id.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetByPalletIdDirectAsync_WithNonExistentPalletId_ShouldReturnNull()
    {
        // Arrange
        var nonExistentPalletId = 999;

        // Act
        var result = await _labelRepository.GetByPalletIdDirectAsync(nonExistentPalletId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByPalletIdDirectAsync_WithInactivePalletOnly_ShouldReturnNull()
    {
        // Arrange - Create a pallet with only inactive labels
        var testPalletId = 200;
        var testPallet = new Pallet { Id = testPalletId, TsUtworzenia = DateTime.UtcNow };
        var inactiveLabel = new Label
        {
            Id = 200, SystemId = 1, PaletaId = testPalletId, Active = 0,
            Sscc = "inactive_only", Miejscep = 1, Ts = DateTime.UtcNow
        };
        
        _context.Pallets.Add(testPallet);
        _context.Labels.Add(inactiveLabel);
        await _context.SaveChangesAsync();

        // Act
        var result = await _labelRepository.GetByPalletIdDirectAsync(testPalletId);

        // Assert
        result.Should().BeNull(); // Should return null since only inactive label exists
    }

    [Fact]
    public async Task GetByPalletIdAsync_OrderingShouldBeCorrect()
    {
        // Arrange - Add labels with different timestamps and activity status
        var testPalletId = 100;
        var now = DateTime.UtcNow;
        
        var testLabels = new[]
        {
            new Label
            {
                Id = 100, SystemId = 1, PaletaId = testPalletId, Active = 0, Ts = now.AddHours(-3),
                Sscc = "old_inactive", Miejscep = 1
            },
            new Label
            {
                Id = 101, SystemId = 1, PaletaId = testPalletId, Active = 1, Ts = now.AddHours(-2),
                Sscc = "newer_active", Miejscep = 1
            },
            new Label
            {
                Id = 102, SystemId = 1, PaletaId = testPalletId, Active = 1, Ts = now.AddHours(-1),
                Sscc = "newest_active", Miejscep = 1
            }
        };

        _context.Labels.AddRange(testLabels);
        await _context.SaveChangesAsync();

        // Act
        var result = await _labelRepository.GetByPalletIdAsync(testPalletId);

        // Assert
        var labels = result.ToList();
        labels.Should().HaveCount(3);
        
        // Should be ordered by Active (desc), then Ts (desc)
        labels[0].Id.Should().Be(102); // Newest active
        labels[1].Id.Should().Be(101); // Older active
        labels[2].Id.Should().Be(100); // Inactive (regardless of timestamp)
    }

    private void SeedTestData()
    {
        // Create test pallets
        var pallets = new[]
        {
            new Pallet { Id = 1, TsUtworzenia = DateTime.UtcNow },
            new Pallet { Id = 2, TsUtworzenia = DateTime.UtcNow }
        };

        // Create test locations
        var locations = new[]
        {
            new Location
            {
                Id = 1, Hala = 1, Regal = "A", Miejsce = 1, Poziom = "1",
                Widoczne = 1, MaxPojemnosc = 10
            },
            new Location
            {
                Id = 2, Hala = 2, Regal = "B", Miejsce = 2, Poziom = "2",
                Widoczne = 1, MaxPojemnosc = 5
            },
            new Location
            {
                Id = 3, Hala = 3, Regal = "C", Miejsce = 3, Poziom = "3",
                Widoczne = 1, MaxPojemnosc = 15
            }
        };

        // Create test labels
        var labels = new[]
        {
            new Label
            {
                Id = 1, SystemId = 1, PaletaId = 1, Miejscep = 1, Active = 1,
                Sscc = "123456789012345678", EtykietaKlient = "CLIENT-001",
                Ts = DateTime.UtcNow.AddHours(-1)
            },
            new Label
            {
                Id = 2, SystemId = 1, PaletaId = 1, Miejscep = 1, Active = 1,
                Sscc = "223456789012345679", EtykietaKlient = "CLIENT-002",
                Ts = DateTime.UtcNow.AddHours(-2)
            },
            new Label
            {
                Id = 3, SystemId = 1, PaletaId = 2, Miejscep = 2, Active = 1,
                Sscc = "323456789012345680", EtykietaKlient = "CLIENT-003",
                Ts = DateTime.UtcNow.AddHours(-3)
            },
            new Label
            {
                Id = 4, SystemId = 1, PaletaId = 1, Miejscep = 1, Active = 0, // Inactive
                Sscc = "987654321098765432", EtykietaKlient = "CLIENT-INACTIVE",
                Ts = DateTime.UtcNow.AddHours(-4)
            }
        };

        _context.Pallets.AddRange(pallets);
        _context.Locations.AddRange(locations);
        _context.Labels.AddRange(labels);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
