<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Login Page -->
  <data name="Login_Title" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Login_AppName" xml:space="preserve">
    <value>WMS System</value>
  </data>
  <data name="Login_Subtitle" xml:space="preserve">
    <value>Warehouse Management</value>
  </data>
  <data name="Login_Language_Label" xml:space="preserve">
    <value>Choose language</value>
  </data>
  <data name="Login_Environment_Label" xml:space="preserve">
    <value>Choose API Environment</value>
  </data>
  <data name="Login_Environment_Picker_Title" xml:space="preserve">
    <value>Select Environment</value>
  </data>
  <data name="Login_CardNumber_Placeholder" xml:space="preserve">
    <value>Card Number</value>
  </data>
  <data name="Login_CardPrompt_Label" xml:space="preserve">
    <value>Scan or enter your card number</value>
  </data>
  <data name="Login_ScanCard_Button" xml:space="preserve">
    <value>📷 Scan Card</value>
  </data>
  <data name="Login_Login_Button" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Login_DemoMode_Title" xml:space="preserve">
    <value>🎯 Demo Mode</value>
  </data>
  <data name="Login_DemoMode_Subtitle" xml:space="preserve">
    <value>Try these card numbers:</value>
  </data>
  <data name="Login_DemoMode_Items" xml:space="preserve">
    <value>DEMO • 123456 • TEST • ADMIN</value>
  </data>
  <data name="Login_Version_Label" xml:space="preserve">
    <value>Version 1.0.0</value>
  </data>

  <!-- Common -->
  <data name="Common_Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ReceivesSelection_Title" xml:space="preserve">
    <value>Receive Selection</value>
  </data>
  <data name="ReceivesSelection_HeaderMain" xml:space="preserve">
    <value>RECEIVING GOODS</value>
  </data>
  <data name="ReceivesSelection_HeaderSubtitle" xml:space="preserve">
    <value>Choose a delivery or enter LK number</value>
  </data>
  <data name="ReceivesSelection_LK_Label" xml:space="preserve">
    <value>LK number</value>
  </data>
  <data name="ReceivesSelection_LK_Placeholder" xml:space="preserve">
    <value>Enter or scan LK (e.g., LK123)</value>
  </data>
  <data name="ReceivesSelection_LK_Confirm_Button" xml:space="preserve">
    <value>Confirm LK</value>
  </data>
  <data name="ReceivesSelection_List_Label" xml:space="preserve">
    <value>Available Deliveries</value>
  </data>
  <data name="ReceivesSelection_Refresh_Button" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="ReceivesSelection_GenerateDS_Button" xml:space="preserve">
    <value>Generate DS</value>
  </data>
  <data name="ReceivesSelection_ListReceives_Button" xml:space="preserve">
    <value>List Deliveries</value>
  </data>
  <data name="ReceivesSelection_GeneratedDS_Header" xml:space="preserve">
    <value>Generated DS codes:</value>
  </data>
  <data name="ReceivesSelection_ProceedToRegistration_Button" xml:space="preserve">
    <value>Proceed to Registration</value>
  </data>
  <data name="Receives_GenerateDS_Title" xml:space="preserve">
    <value>Generate DS Codes</value>
  </data>
  <data name="Receives_GenerateDS_TypPalety_Label" xml:space="preserve">
    <value>Pallet type:</value>
  </data>
  <data name="Receives_GenerateDS_Count_Label" xml:space="preserve">
    <value>Number of pallets (1-100):</value>
  </data>
  <data name="Receives_GenerateDS_Printer_Label" xml:space="preserve">
    <value>Printer (optional):</value>
  </data>
  <data name="Receives_GenerateDS_Print_Checkbox_Label" xml:space="preserve">
    <value>Print labels</value>
  </data>

  <data name="ReceivesRegistration_Title" xml:space="preserve">
    <value>Receive Registration</value>
  </data>
  <data name="ReceivesRegistration_HeaderTitle" xml:space="preserve">
    <value>POSITION REGISTRATION</value>
  </data>
  <data name="ReceivesRegistration_Scan_Label" xml:space="preserve">
    <value>Scan code (GS1, SSCC, DS)</value>
  </data>
  <data name="ReceivesRegistration_Scan_Placeholder" xml:space="preserve">
    <value>Scan or enter code</value>
  </data>
  <data name="ReceivesRegistration_Scan_Button" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ReceivesRegistration_ItemData_Label" xml:space="preserve">
    <value>Item data</value>
  </data>
  <data name="ReceivesRegistration_Source_Awizacja_Button" xml:space="preserve">
    <value>Advance notice</value>
  </data>
  <data name="ReceivesRegistration_Source_Kartoteka_Button" xml:space="preserve">
    <value>Catalog</value>
  </data>
  <data name="ReceivesRegistration_Towar_Label" xml:space="preserve">
    <value>Item:</value>
  </data>
  <data name="ReceivesRegistration_Towar_Placeholder" xml:space="preserve">
    <value>Item code</value>
  </data>
  <data name="ReceivesRegistration_Search_Button" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ReceivesRegistration_Search_Placeholder" xml:space="preserve">
    <value>Enter code or name to search</value>
  </data>
  <data name="ReceivesRegistration_Quantities_Opakowania_Label" xml:space="preserve">
    <value>Packages quantity:</value>
  </data>
  <data name="ReceivesRegistration_Quantities_Sztuk_Label" xml:space="preserve">
    <value>Pieces quantity:</value>
  </data>
  <data name="ReceivesRegistration_Partia_Label" xml:space="preserve">
    <value>Lot/Batch:</value>
  </data>
  <data name="Common_Optional" xml:space="preserve">
    <value>Optional</value>
  </data>
  <data name="ReceivesRegistration_DataWaznosci_Label" xml:space="preserve">
    <value>Expiry date:</value>
  </data>
  <data name="ReceivesRegistration_DataProdukcji_Label" xml:space="preserve">
    <value>Production date:</value>
  </data>
  <data name="ReceivesRegistration_Certyfikat_Label" xml:space="preserve">
    <value>Certificate:</value>
  </data>
  <data name="ReceivesRegistration_TypPaletyForAuto_Label" xml:space="preserve">
    <value>Pallet type (for new carrier):</value>
  </data>
  <data name="ReceivesRegistration_Register_Button" xml:space="preserve">
    <value>Register position</value>
  </data>
  <data name="ReceivesRegistration_Actions_CompleteNosnik_Button" xml:space="preserve">
    <value>Carrier complete</value>
  </data>
  <data name="ReceivesRegistration_Actions_Finish_Button" xml:space="preserve">
    <value>Finish</value>
  </data>
  <data name="ReceivesRegistration_AwizacjaPositions_Header" xml:space="preserve">
    <value>Advance notice positions:</value>
  </data>
  <data name="ReceivesRegistration_Confirmation_Title" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="ReceivesRegistration_Preview_Close_Button" xml:space="preserve">
    <value>Close preview</value>
  </data>
  <data name="ReceivesRegistration_KodSelection_Title" xml:space="preserve">
    <value>Select item</value>
  </data>

  <data name="Common_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Common_Generate" xml:space="preserve">
    <value>Generate</value>
  </data>
  <data name="Common_Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Common_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Common_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Common_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Options_Title" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="Options_Header" xml:space="preserve">
    <value>Select operation</value>
  </data>
  <data name="Options_MovePallet_Button" xml:space="preserve">
    <value>📦 Move pallet</value>
  </data>
  <data name="Options_SystemOptions_Header" xml:space="preserve">
    <value>System options</value>
  </data>
  <data name="Options_About_Button" xml:space="preserve">
    <value>ℹ️ About</value>
  </data>

  <data name="MovePallet_Title" xml:space="preserve">
    <value>Move pallet</value>
  </data>
  <data name="MovePallet_PalletInfo_Header" xml:space="preserve">
    <value>Pallet info:</value>
  </data>
  <data name="Common_ConfirmCode" xml:space="preserve">
    <value>Confirm code</value>
  </data>

  <data name="Main_Title" xml:space="preserve">
    <value>WMS System</value>
  </data>
  <data name="Main_Header" xml:space="preserve">
    <value>Main Menu</value>
  </data>
  <data name="Main_Tile_Acceptance" xml:space="preserve">
    <value>Receiving</value>
  </data>
  <data name="Main_Tile_Issues" xml:space="preserve">
    <value>Dispatch</value>
  </data>
  <data name="Main_Tile_Operations" xml:space="preserve">
    <value>Operations</value>
  </data>
  <data name="Main_Tile_Tasks" xml:space="preserve">
    <value>Tasks</value>
  </data>
  <data name="Main_Tile_Inventory" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="Main_Tile_Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="Main_Tile_Movements" xml:space="preserve">
    <value>Moves</value>
  </data>
  <data name="Main_Tile_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>

  <data name="Pallets_Title" xml:space="preserve">
    <value>Pallets</value>
  </data>
  <data name="Pallets_Search_Placeholder" xml:space="preserve">
    <value>Search pallets by SSCC or label...</value>
  </data>
  <data name="Pallets_Loading_Label" xml:space="preserve">
    <value>Loading pallets...</value>
  </data>
  <data name="Pallets_Empty_Title" xml:space="preserve">
    <value>No pallets found</value>
  </data>
  <data name="Pallets_Empty_Subtitle" xml:space="preserve">
    <value>Try refreshing or adjusting your search</value>
  </data>

  <data name="Locations_Title" xml:space="preserve">
    <value>Locations</value>
  </data>
  <data name="Locations_Search_Placeholder" xml:space="preserve">
    <value>Search locations...</value>
  </data>
  <data name="Locations_Loading_Label" xml:space="preserve">
    <value>Loading locations...</value>
  </data>
  <data name="Locations_Empty_Title" xml:space="preserve">
    <value>No locations found</value>
  </data>
  <data name="Locations_Empty_Subtitle" xml:space="preserve">
    <value>Try refreshing or adjusting your search</value>
  </data>

  <data name="EmulatorScanner_Title" xml:space="preserve">
    <value>🖥️ Mock Scanner (Emulator)</value>
  </data>
  <data name="EmulatorScanner_DeviceInfo" xml:space="preserve">
    <value>Device info:</value>
  </data>
  <data name="EmulatorScanner_ManualInput_Label" xml:space="preserve">
    <value>Enter a code to simulate scanning:</value>
  </data>
  <data name="EmulatorScanner_Scan_Button" xml:space="preserve">
    <value>Scan</value>
  </data>
  <data name="EmulatorScanner_Generate_Header" xml:space="preserve">
    <value>Generate sample codes:</value>
  </data>
  <data name="EmulatorScanner_Generate_GS1" xml:space="preserve">
    <value>GS1</value>
  </data>
  <data name="EmulatorScanner_Generate_SSCC" xml:space="preserve">
    <value>SSCC</value>
  </data>
  <data name="EmulatorScanner_Generate_DS" xml:space="preserve">
    <value>DS</value>
  </data>
  <data name="EmulatorScanner_Generate_LK" xml:space="preserve">
    <value>LK</value>
  </data>
  <data name="EmulatorScanner_Generate_PrinterIP" xml:space="preserve">
    <value>Printer IP</value>
  </data>
  <data name="Common_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="EmulatorScanner_History_Header" xml:space="preserve">
    <value>Scan history:</value>
  </data>
  <data name="EmulatorScanner_ClearHistory_Button" xml:space="preserve">
    <value>Clear history</value>
  </data>
  <data name="EmulatorScanner_Instructions_Header" xml:space="preserve">
    <value>Instructions:</value>
  </data>
  <data name="EmulatorScanner_Instructions_1" xml:space="preserve">
    <value>• Enter a code manually or generate a sample</value>
  </data>
  <data name="EmulatorScanner_Instructions_2" xml:space="preserve">
    <value>• Click 'Scan' to simulate scanning</value>
  </data>
  <data name="EmulatorScanner_Instructions_3" xml:space="preserve">
    <value>• Generated codes are scanned automatically</value>
  </data>
  <data name="EmulatorScanner_Instructions_4" xml:space="preserve">
    <value>• This feature is available only on emulators</value>
  </data>

  <data name="About_Title" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="About_Updates_Header" xml:space="preserve">
    <value>Updates</value>
  </data>
  <data name="About_CheckUpdates_Button" xml:space="preserve">
    <value>Check updates</value>
  </data>
  <data name="About_UpdateRequired_Label" xml:space="preserve">
    <value>⚠️ Update required</value>
  </data>
  <data name="About_DownloadInstall_Button" xml:space="preserve">
    <value>Download and Install</value>
  </data>

  <!-- Inventory Selection Page -->
  <data name="InventorySelection_Title" xml:space="preserve">
    <value>Inventory Selection</value>
  </data>
  <data name="InventorySelection_Header" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="InventorySelection_Subtitle" xml:space="preserve">
    <value>Choose inventory type</value>
  </data>
  <data name="InventorySelection_ChooseType" xml:space="preserve">
    <value>Available inventory types:</value>
  </data>

  <!-- Inventory Product Page -->
  <data name="InventoryProduct_Title" xml:space="preserve">
    <value>Product Inventory</value>
  </data>
  <data name="InventoryProduct_Header" xml:space="preserve">
    <value>Product Inventory</value>
  </data>
  <data name="InventoryProduct_Subtitle" xml:space="preserve">
    <value>Inventory of specific products</value>
  </data>
  <data name="InventoryProduct_SelectInventory" xml:space="preserve">
    <value>Select active inventory:</value>
  </data>
  <data name="InventoryProduct_NoActiveInventories" xml:space="preserve">
    <value>No active product inventories</value>
  </data>
  <data name="InventoryProduct_SessionActive" xml:space="preserve">
    <value>Active inventory session</value>
  </data>
  <data name="InventoryProduct_ScanSection" xml:space="preserve">
    <value>Scanning</value>
  </data>
  <data name="InventoryProduct_ScanPrompt" xml:space="preserve">
    <value>Scan label, pallet or product code:</value>
  </data>
  <data name="InventoryProduct_ScanPlaceholder" xml:space="preserve">
    <value>Code to scan</value>
  </data>
  <data name="InventoryProduct_EndSession" xml:space="preserve">
    <value>End session</value>
  </data>

  <!-- Common Inventory -->
  <data name="Common_Process" xml:space="preserve">
    <value>Process</value>
  </data>
  <data name="Common_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Common_Back" xml:space="preserve">
    <value>Back</value>
  </data>
</root>

