using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs;
using Wms.Application.Features.Receives.Queries;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.Receives.Handlers;

/// <summary>
/// Handler do pobierania danych etykiety z awizacji po kodzie SSCC
/// </summary>
public class GetAwizacjaLabelDataHandler : IRequestHandler<GetAwizacjaLabelDataQuery, AwizacjaLabelDataDto?>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IKodRepository _kodRepository;
    private readonly ILogger<GetAwizacjaLabelDataHandler> _logger;

    public GetAwizacjaLabelDataHandler(
        IReceiveRepository receiveRepository,
        IKodRepository kodRepository,
        ILogger<GetAwizacjaLabelDataHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _kodRepository = kodRepository;
        _logger = logger;
    }

    public async Task<AwizacjaLabelDataDto?> Handle(GetAwizacjaLabelDataQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Wyszukiwanie danych etykiety w awizacji dla SSCC: {SSCC}", request.SSCC);

        try
        {
            // Znajdź pozycję awizacji po SSCC (EtykietaKlient)
            var awizacjaPosition = await _receiveRepository.GetAwizacjaPositionBySSCCAsync(request.SSCC, cancellationToken);
            
            if (awizacjaPosition == null)
            {
                _logger.LogWarning("Nie znaleziono pozycji awizacji dla SSCC: {SSCC}", request.SSCC);
                return null;
            }

            _logger.LogInformation("Znaleziono pozycję awizacji: ID={Id}, Kod={Kod}, Lot={Lot}", 
                awizacjaPosition.Id, awizacjaPosition.Kod, awizacjaPosition.Lot);

            // Pobierz dane kodu towaru z kartoteki
            Domain.Entities.Kod? kodEntity = null;
            if (!string.IsNullOrEmpty(awizacjaPosition.Kod))
            {
                kodEntity = await _kodRepository.GetByKodValueAsync(awizacjaPosition.Kod, cancellationToken: cancellationToken);
            }

            // Przygotuj dane do zwrócenia
            var result = new AwizacjaLabelDataDto
            {
                EtykietaKlient = awizacjaPosition.EtykietaKlient,
                Kod = awizacjaPosition.Kod,
                KodId = kodEntity?.Id,
                KodNazwa = kodEntity?.KodNazwa,
                Lot = awizacjaPosition.Lot,
                DataWaznosci = awizacjaPosition.DataWaznosci?.ToString("yyyy-MM-dd"),
                Blloc = awizacjaPosition.Blloc,
                Ilosc = awizacjaPosition.Ilosc,
                IloscWOpakowaniu = kodEntity?.IloscWOpakowaniu,
                IsFromAdvice = true
            };

            // Dodaj ostrzeżenia jeśli brakuje danych
            var warnings = new List<string>();
            if (kodEntity == null && !string.IsNullOrEmpty(awizacjaPosition.Kod))
            {
                warnings.Add($"Nie znaleziono kodu {awizacjaPosition.Kod} w kartotece");
            }
            if (string.IsNullOrEmpty(awizacjaPosition.Lot))
            {
                warnings.Add("Brak numeru partii w awizacji");
            }
            if (!awizacjaPosition.DataWaznosci.HasValue)
            {
                warnings.Add("Brak daty ważności w awizacji");
            }

            if (warnings.Count > 0)
            {
                result.WarningMessage = string.Join("; ", warnings);
            }

            _logger.LogInformation("Zwrócono dane etykiety z awizacji dla SSCC: {SSCC}, Ostrzeżenia: {WarningCount}", 
                request.SSCC, warnings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania danych etykiety z awizacji dla SSCC: {SSCC}", request.SSCC);
            throw;
        }
    }
}
