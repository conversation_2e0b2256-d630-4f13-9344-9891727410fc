{"permissions": {"allow": ["Bash(set DISABLE_FLUENT_VALIDATION=1)", "<PERSON><PERSON>(set:*)", "Bash(set ASPNETCORE_ENVIRONMENT=Development)", "Bash(dotnet run:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(chcp 65001)", "Bash(DISABLE_FLUENT_VALIDATION=1 ASPNETCORE_URLS=http://127.0.0.1:8081 ASPNETCORE_ENVIRONMENT=Development dotnet run --project src/backend/Wms.Api/Wms.Api.csproj --no-launch-profile)", "<PERSON><PERSON>(curl:*)", "Bash(dotnet build:*)", "Bash(find:*)", "Read(//c/Users/<USER>/Desktop/**)", "<PERSON><PERSON>(npx playwright:*)", "Bash(npm run test:*)"], "deny": [], "ask": []}}