using Wms.Domain.Exceptions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący ilość produktu
/// Obsługuje AI 37 (count) i przeliczenia na podstawie jednostki opakowania
/// </summary>
public record Quantity : IComparable<Quantity>
{
    public decimal Value { get; private init; }
    public QuantityUnit Unit { get; private init; }
    public bool IsWholeNumber => Value == Math.Floor(Value);
    public bool IsPositive => Value > 0;
    public string FormattedValue => Unit switch
    {
        QuantityUnit.Pieces => $"(37) {Value:0}",
        QuantityUnit.Kilograms => $"{Value:0.000} kg",
        QuantityUnit.Liters => $"{Value:0.000} l",
        QuantityUnit.Meters => $"{Value:0.000} m",
        _ => Value.ToString("0.000")
    };

    private Quantity(decimal value, QuantityUnit unit = QuantityUnit.Pieces)
    {
        Value = value;
        Unit = unit;
    }

    /// <summary>
    /// Tworzy Quantity dla sztuk (domyślnie z AI 37)
    /// </summary>
    public static Quantity CreatePieces(int pieces)
    {
        if (pieces < 0)
            throw new InvalidGS1FormatException(pieces.ToString(), "Ilość nie może być ujemna");

        if (pieces > 999999) // AI 37 max 6 digits
            throw new InvalidGS1FormatException(pieces.ToString(), "Ilość przekracza maksymalną wartość dla AI 37 (999999)");

        return new Quantity(pieces, QuantityUnit.Pieces);
    }

    /// <summary>
    /// Tworzy Quantity z dowolnej wartości decimal
    /// </summary>
    public static Quantity Create(decimal value, QuantityUnit unit = QuantityUnit.Pieces)
    {
        if (value < 0)
            throw new InvalidGS1FormatException(value.ToString(), "Ilość nie może być ujemna");

        return new Quantity(value, unit);
    }

    /// <summary>
    /// Tworzy Quantity z AI 37 (count) - maksymalnie 6 cyfr
    /// </summary>
    public static Quantity CreateFromAI37(string countValue)
    {
        if (string.IsNullOrWhiteSpace(countValue))
            throw new InvalidGS1FormatException(countValue ?? "null", "Wartość AI 37 nie może być pusta");

        // Remove any non-digits
        var digitsOnly = System.Text.RegularExpressions.Regex.Replace(countValue.Trim(), @"[^\d]", "");
        
        if (string.IsNullOrEmpty(digitsOnly))
            throw new InvalidGS1FormatException(countValue, "AI 37 musi zawierać cyfry");

        if (digitsOnly.Length > 8) // Extended to handle larger numbers in practice
            throw new InvalidGS1FormatException(countValue, $"AI 37 może zawierać maksymalnie 8 cyfr, otrzymano: {digitsOnly.Length}");

        if (!int.TryParse(digitsOnly, out var count))
            throw new InvalidGS1FormatException(countValue, $"Nie można sparsować wartości AI 37: {digitsOnly}");

        return CreatePieces(count);
    }

    /// <summary>
    /// Próbuje utworzyć Quantity z AI 37
    /// </summary>
    public static bool TryCreateFromAI37(string countValue, out Quantity? quantity)
    {
        quantity = null;
        
        if (string.IsNullOrWhiteSpace(countValue))
            return false;

        try
        {
            var digitsOnly = System.Text.RegularExpressions.Regex.Replace(countValue.Trim(), @"[^\d]", "");
            
            if (string.IsNullOrEmpty(digitsOnly) || digitsOnly.Length > 8)
                return false;

            if (!int.TryParse(digitsOnly, out var count) || count < 0)
                return false;

            quantity = CreatePieces(count);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Przelicza ilość na podstawie jednostki opakowania
    /// </summary>
    public Quantity MultiplyByPackagingUnit(int packagingUnit)
    {
        if (packagingUnit <= 0)
            throw new ArgumentException("Jednostka opakowania musi być większa od zera", nameof(packagingUnit));

        var newValue = Value * packagingUnit;
        return new Quantity(newValue, Unit);
    }

    /// <summary>
    /// Dzieli ilość przez jednostkę opakowania (odwrotność MultiplyByPackagingUnit)
    /// </summary>
    public Quantity DivideByPackagingUnit(int packagingUnit)
    {
        if (packagingUnit <= 0)
            throw new ArgumentException("Jednostka opakowania musi być większa od zera", nameof(packagingUnit));

        var newValue = Value / packagingUnit;
        return new Quantity(newValue, Unit);
    }

    /// <summary>
    /// Dodaje dwie ilości (muszą być tej samej jednostki)
    /// </summary>
    public Quantity Add(Quantity other)
    {
        if (other.Unit != Unit)
            throw new InvalidOperationException($"Nie można dodać ilości o różnych jednostkach: {Unit} + {other.Unit}");

        return new Quantity(Value + other.Value, Unit);
    }

    /// <summary>
    /// Odejmuje dwie ilości (muszą być tej samej jednostki)
    /// </summary>
    public Quantity Subtract(Quantity other)
    {
        if (other.Unit != Unit)
            throw new InvalidOperationException($"Nie można odjąć ilości o różnych jednostkach: {Unit} - {other.Unit}");

        var result = Value - other.Value;
        if (result < 0)
            throw new InvalidOperationException("Wynik odejmowania nie może być ujemny");

        return new Quantity(result, Unit);
    }

    /// <summary>
    /// Zwraca wartość w formacie AI 37 (tylko dla jednostek)
    /// </summary>
    public string ToAI37Format()
    {
        if (Unit != QuantityUnit.Pieces)
            throw new InvalidOperationException("Format AI 37 dostępny tylko dla jednostek (Pieces)");

        if (!IsWholeNumber)
            throw new InvalidOperationException("AI 37 wymaga liczb całkowitych");

        return ((int)Value).ToString("D6"); // 6-digit format with leading zeros
    }

    /// <summary>
    /// Waliduje czy wartość jest prawidłowa dla AI 37
    /// </summary>
    public static bool IsValidForAI37(string? value)
    {
        return TryCreateFromAI37(value ?? "", out _);
    }

    /// <summary>
    /// Porównuje ilości
    /// </summary>
    public int CompareTo(Quantity? other)
    {
        if (other is null) return 1;
        
        // Porównaj jednostki
        var unitComparison = Unit.CompareTo(other.Unit);
        if (unitComparison != 0) return unitComparison;
        
        return Value.CompareTo(other.Value);
    }

    public static bool operator <(Quantity left, Quantity right) => left.CompareTo(right) < 0;
    public static bool operator >(Quantity left, Quantity right) => left.CompareTo(right) > 0;
    public static bool operator <=(Quantity left, Quantity right) => left.CompareTo(right) <= 0;
    public static bool operator >=(Quantity left, Quantity right) => left.CompareTo(right) >= 0;

    public static Quantity operator +(Quantity left, Quantity right) => left.Add(right);
    public static Quantity operator -(Quantity left, Quantity right) => left.Subtract(right);

    public override string ToString() => FormattedValue;
    
    public static implicit operator decimal(Quantity quantity) => quantity.Value;
    public static explicit operator Quantity(decimal value) => Create(value);
    public static explicit operator Quantity(int value) => CreatePieces(value);
}

/// <summary>
/// Jednostki ilości
/// </summary>
public enum QuantityUnit
{
    Pieces = 0,      // Sztuki (domyślnie dla AI 37)
    Kilograms = 1,   // Kilogramy
    Liters = 2,      // Litry
    Meters = 3       // Metry
}
