using FluentAssertions;
using System.Net;
using Wms.Application.DTOs.Auth;
using Wms.Application.DTOs.Pallets;
using Wms.IntegrationTests.Infrastructure;

namespace Wms.IntegrationTests.Controllers;

public class PalletsControllerIntegrationTests : IntegrationTestBase
{
    public PalletsControllerIntegrationTests(WmsWebApplicationFactory factory) : base(factory)
    {
    }

    private async Task<string> GetAuthTokenAsync()
    {
        var loginRequest = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "integration-test-device"
        };

        var loginResponse = await Client.PostAsync(
            "/api/v1.0/auth/login-scan",
            CreateJsonContent(loginRequest));

        loginResponse.EnsureSuccessStatusCode();
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(loginResponse);
        return loginData!.Token;
    }

    private async Task SetupAuthenticatedClientAsync()
    {
        var token = await GetAuthTokenAsync();
        SetAuthorizationHeader(token);
        SetDeviceIdHeader("integration-test-device");
        Client.DefaultRequestHeaders.Add("X-Forwarded-For", "192.168.1.100");
    }

    [Fact]
    public async Task GetPallet_WithValidSSCC_ReturnsPalletInfo()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345675";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var palletInfo = await DeserializeResponseAsync<PalletInfo>(response);
        palletInfo.Should().NotBeNull();
        palletInfo!.MainSSCC.Should().Be(palletCode);
        palletInfo.CurrentLocation.Should().NotBeNull();
        palletInfo.CurrentLocation!.Code.Should().Be("MP-01-A-01-01");
        palletInfo.Labels.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetPallet_WithNonExistentPallet_ReturnsNotFound()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "999999999999999999";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetPallet_WithoutAuthentication_ReturnsUnauthorized()
    {
        // Arrange
        ClearHeaders();
        var palletCode = "123456789012345675";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task MovePallet_ValidMove_ReturnsSuccess()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345675";
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Integration test move"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var moveResponse = await DeserializeResponseAsync<MovePalletResponse>(response);
        moveResponse.Should().NotBeNull();
        moveResponse!.PalletCode.Should().Be(palletCode);
        moveResponse.FromLocationCode.Should().Be("MP-01-A-01-01");
        moveResponse.ToLocationCode.Should().Be("MP-01-A-01-02");
        moveResponse.MovedBy.Should().Be("Test User");
        moveResponse.MovementId.Should().BeGreaterThan(0);
        moveResponse.MovementTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task MovePallet_ToSameLocation_ReturnsConflict()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345675";
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01", // Same location as current
            Notes = "Same location move test"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert - This might be Conflict or BadRequest depending on business rules
        response.StatusCode.Should().BeOneOf(HttpStatusCode.Conflict, HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task MovePallet_ToNonExistentLocation_ReturnsNotFound()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345675";
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-99-Z-99-99", // Non-existent location
            Notes = "Non-existent location test"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task MovePallet_WithInvalidPalletCode_ReturnsBadRequest()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "INVALID-CODE";
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Invalid pallet code test"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task MovePallet_WithoutAuthentication_ReturnsUnauthorized()
    {
        // Arrange
        ClearHeaders();
        var palletCode = "123456789012345675";
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Unauthorized test"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task PalletWorkflow_EndToEndMovementScenario()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345678";

        // Step 1: Get initial pallet info
        var initialResponse = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");
        initialResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var initialPallet = await DeserializeResponseAsync<PalletInfo>(initialResponse);
        var initialLocation = initialPallet!.CurrentLocation!.Code;

        // Step 2: Move pallet to new location
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "End-to-end test movement"
        };

        var moveResponse = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        moveResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var moveData = await DeserializeResponseAsync<MovePalletResponse>(moveResponse);
        moveData!.FromLocationCode.Should().Be(initialLocation);
        moveData.ToLocationCode.Should().Be("MP-01-A-01-02");

        // Step 3: Verify pallet is in new location
        var finalResponse = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");
        finalResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var finalPallet = await DeserializeResponseAsync<PalletInfo>(finalResponse);
        finalPallet!.CurrentLocation!.Code.Should().Be("MP-01-A-01-02");

        // Step 4: Move back to original location
        var moveBackRequest = new MovePalletRequest
        {
            ToLocationCode = initialLocation,
            Notes = "Moving back to original location"
        };

        var moveBackResponse = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveBackRequest));

        moveBackResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task MovePallet_ChecksDatabaseConsistency()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345678";
        
        // Get database context to verify data integrity
        using var dbContext = await GetDbContextAsync();

        // Get initial label location
        var initialLabel = dbContext.Labels.First(l => l.Sscc == palletCode);
        var initialLocationId = initialLabel.Miejscep;

        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Database consistency test"
        };

        // Act
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // Verify database state
        var updatedLabel = dbContext.Labels.First(l => l.Sscc == palletCode);
        var newLocation = dbContext.Locations.First(l => l.Code == "MP-01-A-01-02");
        
        updatedLabel.Miejscep.Should().Be(newLocation.Id);
        updatedLabel.Miejscep.Should().NotBe(initialLocationId);

        // Verify movement record was created
        var movements = dbContext.Movements.Where(m => m.Etykieta == updatedLabel.Id).ToList();
        movements.Should().NotBeEmpty();
        
        var latestMovement = movements.OrderByDescending(m => m.Tszm).First();
        latestMovement.NoweM.Should().Be(newLocation.Id);
        latestMovement.StareM.Should().Be(initialLocationId);
        latestMovement.Uwagi.Should().Be("Database consistency test");
    }

    [Fact]
    public async Task MovePallet_WithClientLabel_WorksCorrectly()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var clientCode = "CLIENT-001"; // Using client label instead of SSCC
        
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Client label movement test"
        };

        // Act - This should work if the system supports client labels
        var response = await Client.PostAsync(
            $"/api/v1.0/pallets/{clientCode}/move",
            CreateJsonContent(moveRequest));

        // Assert - Expect success if DS codes are supported, otherwise BadRequest
        response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("123456789012345678")] // SSCC format
    [InlineData("987654321098765432")] // Another SSCC
    public async Task GetPallet_WithVariousPalletCodes_ReturnsCorrectData(string palletCode)
    {
        // Arrange
        await SetupAuthenticatedClientAsync();

        // Act
        var response = await Client.GetAsync($"/api/v1.0/pallets/{palletCode}");

        // Assert
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var palletInfo = await DeserializeResponseAsync<PalletInfo>(response);
            palletInfo.Should().NotBeNull();
            palletInfo!.Labels.Should().ContainSingle(l => l.SSCC == palletCode);
        }
        else
        {
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }
    }

    [Fact]
    public async Task MovePallet_ConcurrentMovements_HandlesProperly()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var palletCode = "123456789012345678";
        
        var moveRequest1 = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-02",
            Notes = "Concurrent test 1"
        };
        
        var moveRequest2 = new MovePalletRequest
        {
            ToLocationCode = "MP-02-B-05-01",
            Notes = "Concurrent test 2"
        };

        // Act - Simulate concurrent requests
        var task1 = Client.PostAsync($"/api/v1.0/pallets/{palletCode}/move", CreateJsonContent(moveRequest1));
        var task2 = Client.PostAsync($"/api/v1.0/pallets/{palletCode}/move", CreateJsonContent(moveRequest2));

        var responses = await Task.WhenAll(task1, task2);

        // Assert - One should succeed, the other might fail due to concurrency
        var successCount = responses.Count(r => r.StatusCode == HttpStatusCode.OK);
        var conflictCount = responses.Count(r => r.StatusCode == HttpStatusCode.Conflict);
        
        successCount.Should().BeGreaterThan(0); // At least one should succeed
        
        // The system should handle concurrency gracefully - either through proper locking
        // or by returning appropriate conflict responses
    }
}
