# Schemat Bazy Danych - <PERSON><PERSON>ł Inwentaryzacji

## Przegląd

<PERSON>duł inwentaryzacji wykorzystuje relacyjną bazę danych MySQL z mapowaniem przez Entity Framework Core. System został zaprojektowany z zachowaniem kompatybilności z legacy schema przy jednoczesnym zastosowaniu wzorców Clean Architecture.

## Główne Tabele Modułu

### Tabela `inwentaryzacja` - Centrum Inwentaryzacji

Główna tabela przechowująca wszystkie dane inwentaryzacyjne z pełnym audytem operacji.

```sql
CREATE TABLE `inwentaryzacja` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `data` date DEFAULT NULL COMMENT 'Data przeprowadzenia inwentaryzacji',
  `opis` varchar(45) DEFAULT NULL COMMENT 'Opis sesji inwentaryzacji',
  `etykieta_id` varchar(23) DEFAULT NULL COMMENT 'ID etykiety z systemu',
  `paleta_id` int NOT NULL DEFAULT '0' COMMENT 'ID palety (relac<PERSON> z tabela palety)',
  `kod` varchar(60) DEFAULT NULL COMMENT 'Kod produktu EAN/PLU',
  `ilosc` decimal(10,3) DEFAULT NULL COMMENT 'Ilość teoretyczna z systemu',
  `jm` varchar(5) NOT NULL DEFAULT '' COMMENT 'Jednostka miary',
  `miejscep` int unsigned DEFAULT NULL COMMENT 'ID miejsca (relacja z tabela miejsca)',
  `ilosc_spisana` decimal(10,3) DEFAULT NULL COMMENT 'Ilość rzeczywista spisana przez pracownika',
  `pracownik` varchar(45) DEFAULT NULL COMMENT 'Imię i nazwisko pracownika',
  `ts` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'Timestamp operacji',
  `inwentaryzacja_id` int unsigned DEFAULT NULL COMMENT 'ID sesji inwentaryzacji - grupowanie',
  `active` int unsigned NOT NULL DEFAULT '1' COMMENT 'Flaga aktywności rekordu',
  `hala` int unsigned DEFAULT NULL COMMENT 'Numer hali magazynowej',
  `regal` char(5) DEFAULT NULL COMMENT 'Oznaczenie regału',
  `miejsce` int unsigned DEFAULT NULL COMMENT 'Numer miejsca w regale',
  `poziom` varchar(4) DEFAULT NULL COMMENT 'Poziom miejsca',
  `stat` varchar(4) DEFAULT NULL COMMENT 'Status pozycji inwentaryzacyjnej',
  `nrsap` varchar(45) DEFAULT NULL COMMENT 'Numer SAP produktu',
  `podkod` varchar(45) DEFAULT NULL COMMENT 'Dodatkowy kod produktu',
  `skan` varchar(65) DEFAULT NULL COMMENT 'Oryginalnie zeskanowany kod',
  `uwaga` varchar(45) DEFAULT NULL COMMENT 'Uwagi specjalne (brak_w_inw, byla_juz)',
  `nadwyzka` varchar(1) NOT NULL DEFAULT '1' COMMENT 'Flaga nadwyżki/niedoboru',
  `system_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID systemu/klienta',
  `proba` int unsigned NOT NULL DEFAULT '1' COMMENT 'Numer próby inwentaryzacji',
  `nr_wspolny` int unsigned DEFAULT NULL COMMENT 'Wspólny identyfikator dla grupowania',
  `magazyn` varchar(45) DEFAULT NULL COMMENT 'Kod magazynu',
  PRIMARY KEY (`id`),
  KEY `idx_etykieta_id` (`etykieta_id`) COMMENT 'Index dla wyszukiwania po etykiecie',
  KEY `idx_miejscep` (`miejscep`) COMMENT 'Index dla lokalizacji',
  KEY `idx_inwentaryzacja_id` (`inwentaryzacja_id`) COMMENT 'Index dla sesji inwentaryzacji',
  KEY `idx_nrsap` (`nrsap`) COMMENT 'Index dla kodów SAP',
  KEY `idx_system_id` (`system_id`) COMMENT 'Index dla systemu',
  KEY `idx_active` (`active`) COMMENT 'Index dla aktywnych rekordów',
  KEY `idx_session_active` (`inwentaryzacja_id`, `active`) COMMENT 'Compound index dla wydajności'
) ENGINE=InnoDB AUTO_INCREMENT=720814 DEFAULT CHARSET=utf8mb4 COMMENT='Główna tabela inwentaryzacji';
```

### Mapowanie Entity Framework Core

```csharp
public class InventoryConfiguration : IEntityTypeConfiguration<Inventory>
{
    public void Configure(EntityTypeBuilder<Inventory> builder)
    {
        builder.ToTable("inwentaryzacja");

        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).HasColumnName("id");

        // Właściwości podstawowe
        builder.Property(e => e.Data).HasColumnName("data");
        builder.Property(e => e.Opis).HasColumnName("opis").HasMaxLength(45);
        builder.Property(e => e.EtykietaId).HasColumnName("etykieta_id").HasMaxLength(23);
        builder.Property(e => e.PaletaId).HasColumnName("paleta_id");
        builder.Property(e => e.Kod).HasColumnName("kod").HasMaxLength(60);

        // Ilości z precyzją
        builder.Property(e => e.Ilosc).HasColumnName("ilosc")
            .HasColumnType("decimal(10,3)");
        builder.Property(e => e.IloscSpisana).HasColumnName("ilosc_spisana")
            .HasColumnType("decimal(10,3)");

        // Jednostka miary
        builder.Property(e => e.JednostkaMiary).HasColumnName("jm").HasMaxLength(5);

        // Lokalizacja
        builder.Property(e => e.MiejscepId).HasColumnName("miejscep");
        builder.Property(e => e.Hala).HasColumnName("hala");
        builder.Property(e => e.Regal).HasColumnName("regal").HasMaxLength(5);
        builder.Property(e => e.Miejsce).HasColumnName("miejsce");
        builder.Property(e => e.Poziom).HasColumnName("poziom").HasMaxLength(4);

        // Audyt i metadane
        builder.Property(e => e.Pracownik).HasColumnName("pracownik").HasMaxLength(45);
        builder.Property(e => e.Timestamp).HasColumnName("ts");
        builder.Property(e => e.InwentaryzacjaId).HasColumnName("inwentaryzacja_id");
        builder.Property(e => e.Active).HasColumnName("active");
        builder.Property(e => e.SystemId).HasColumnName("system_id");

        // Pola specjalne
        builder.Property(e => e.Status).HasColumnName("stat").HasMaxLength(4);
        builder.Property(e => e.NumerSap).HasColumnName("nrsap").HasMaxLength(45);
        builder.Property(e => e.Podkod).HasColumnName("podkod").HasMaxLength(45);
        builder.Property(e => e.Skan).HasColumnName("skan").HasMaxLength(65);
        builder.Property(e => e.Uwaga).HasColumnName("uwaga").HasMaxLength(45);
        builder.Property(e => e.Nadwyzka).HasColumnName("nadwyzka").HasMaxLength(1);
        builder.Property(e => e.Proba).HasColumnName("proba");
        builder.Property(e => e.NumerWspolny).HasColumnName("nr_wspolny");
        builder.Property(e => e.Magazyn).HasColumnName("magazyn").HasMaxLength(45);

        // Relacje
        builder.HasOne(e => e.Etykieta)
            .WithMany()
            .HasPrincipalKey(e => e.Id)
            .HasForeignKey(e => e.EtykietaId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.Miejsce)
            .WithMany()
            .HasForeignKey(e => e.MiejscepId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indeksy dla wydajności
        builder.HasIndex(e => e.InwentaryzacjaId).HasDatabaseName("idx_inwentaryzacja_id");
        builder.HasIndex(e => e.EtykietaId).HasDatabaseName("idx_etykieta_id");
        builder.HasIndex(e => e.Active).HasDatabaseName("idx_active");
        builder.HasIndex(e => new { e.InwentaryzacjaId, e.Active }).HasDatabaseName("idx_session_active");
    }
}
```

### Encja Domenowa

```csharp
public class Inventory : BaseEntity
{
    public int Id { get; set; }
    public DateTime? Data { get; set; }
    public string? Opis { get; set; }
    public string? EtykietaId { get; set; }
    public int PaletaId { get; set; }
    public string? Kod { get; set; }
    public decimal? Ilosc { get; set; }
    public string JednostkaMiary { get; set; } = string.Empty;
    public int? MiejscepId { get; set; }
    public decimal? IloscSpisana { get; set; }
    public string? Pracownik { get; set; }
    public DateTime Timestamp { get; set; }
    public int? InwentaryzacjaId { get; set; }
    public int Active { get; set; } = 1;
    public int? Hala { get; set; }
    public string? Regal { get; set; }
    public int? Miejsce { get; set; }
    public string? Poziom { get; set; }
    public string? Status { get; set; }
    public string? NumerSap { get; set; }
    public string? Podkod { get; set; }
    public string? Skan { get; set; }
    public string? Uwaga { get; set; }
    public string Nadwyzka { get; set; } = "1";
    public int SystemId { get; set; }
    public int Proba { get; set; } = 1;
    public int? NumerWspolny { get; set; }
    public string? Magazyn { get; set; }

    // Navigation Properties
    public virtual Etykieta? Etykieta { get; set; }
    public virtual Miejsce? Miejsce { get; set; }

    // Domain Methods
    public bool IsCompleted => IloscSpisana.HasValue;
    public bool HasLocationChanged => /* logic for location change detection */;
    public decimal? GetDifference() => IloscSpisana - Ilosc;
}
```

## Tabele Powiązane

### Tabela `etykiety` - Etykiety Produktów

```sql
CREATE TABLE `etykiety` (
  `id` int NOT NULL AUTO_INCREMENT,
  `system_id` int NOT NULL DEFAULT '0',
  `etykieta_klient` varchar(45) DEFAULT NULL COMMENT 'Kod etykiety klienta/SSCC',
  `magazyn` int unsigned NOT NULL DEFAULT '0',
  `active` int unsigned DEFAULT NULL COMMENT 'Status aktywności etykiety',
  `miejscep` int NOT NULL DEFAULT '0' COMMENT 'ID miejsca lokalizacji',
  `kod_id` int unsigned DEFAULT NULL COMMENT 'ID kodu produktu',
  `status_id` int NOT NULL DEFAULT '1',
  `stat` varchar(17) DEFAULT NULL,
  `paleta_id` int unsigned DEFAULT NULL COMMENT 'ID palety macierzystej',
  `kartony` int unsigned DEFAULT NULL COMMENT 'Liczba kartonów',
  `dataprod` date DEFAULT NULL COMMENT 'Data produkcji',
  `data_waznosci` date DEFAULT NULL COMMENT 'Data ważności',
  `ilosc` decimal(10,3) DEFAULT NULL COMMENT 'Ilość na etykiecie',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(30) DEFAULT NULL,
  `blloc` varchar(45) DEFAULT NULL COMMENT 'BL/LOC certyfikat',
  `lot` varchar(300) DEFAULT NULL COMMENT 'Numer partii/LOT',
  `sscc` varchar(35) DEFAULT NULL COMMENT 'SSCC code',
  `gtin` varchar(24) DEFAULT NULL COMMENT 'GTIN/EAN code',
  PRIMARY KEY (`id`,`system_id`),
  UNIQUE KEY `id` (`id`),
  KEY `idx_paleta` (`paleta_id`),
  KEY `idx_miejscep` (`miejscep`),
  KEY `idx_etykieta_klient` (`etykieta_klient`),
  KEY `idx_active_system` (`system_id`,`active`)
) ENGINE=InnoDB AUTO_INCREMENT=7624131 DEFAULT CHARSET=utf8mb4;
```

### Mapowanie Etykiety

```csharp
public class EtykietaConfiguration : IEntityTypeConfiguration<Etykieta>
{
    public void Configure(EntityTypeBuilder<Etykieta> builder)
    {
        builder.ToTable("etykiety");

        builder.HasKey(e => new { e.Id, e.SystemId });

        builder.Property(e => e.Id).HasColumnName("id");
        builder.Property(e => e.SystemId).HasColumnName("system_id");
        builder.Property(e => e.EtykietaKlient).HasColumnName("etykieta_klient").HasMaxLength(45);
        builder.Property(e => e.Active).HasColumnName("active");
        builder.Property(e => e.MiejscepId).HasColumnName("miejscep");
        builder.Property(e => e.Ilosc).HasColumnName("ilosc").HasColumnType("decimal(10,3)");
        builder.Property(e => e.Lot).HasColumnName("lot").HasMaxLength(300);
        builder.Property(e => e.SSCC).HasColumnName("sscc").HasMaxLength(35);
        builder.Property(e => e.GTIN).HasColumnName("gtin").HasMaxLength(24);

        // Relacje
        builder.HasOne(e => e.Miejsce)
            .WithMany()
            .HasForeignKey(e => e.MiejscepId);
    }
}
```

### Tabela `miejsca` - Lokalizacje Magazynowe

```sql
CREATE TABLE `miejsca` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hala` int NOT NULL DEFAULT '0' COMMENT 'Numer hali',
  `regal` char(10) NOT NULL DEFAULT '' COMMENT 'Identyfikator regału',
  `miejsce` int NOT NULL DEFAULT '0' COMMENT 'Numer miejsca w regale',
  `poziom` varchar(4) DEFAULT NULL COMMENT 'Poziom miejsca',
  `wysokosc` varchar(3) NOT NULL DEFAULT '' COMMENT 'Wysokość miejsca',
  `widoczne` int unsigned NOT NULL DEFAULT '1' COMMENT 'Czy miejsce jest widoczne',
  `zbiorka` int unsigned NOT NULL DEFAULT '0' COMMENT 'Flaga miejsca zbiórki',
  `max_pojemnosc` int unsigned NOT NULL DEFAULT '0' COMMENT 'Maksymalna pojemność',
  `max_udzwig_kg` int unsigned NOT NULL DEFAULT '0' COMMENT 'Maksymalny udźwig w kg',
  PRIMARY KEY (`id`),
  KEY `idx_location_full` (`hala`, `regal`, `miejsce`, `poziom`),
  KEY `idx_widoczne` (`widoczne`),
  KEY `idx_zbiorka` (`zbiorka`)
) ENGINE=InnoDB AUTO_INCREMENT=84906 DEFAULT CHARSET=utf8mb4;
```

### Tabela `kody` - Kartoteka Produktów

```sql
CREATE TABLE `kody` (
  `id` int NOT NULL AUTO_INCREMENT,
  `kod` varchar(100) NOT NULL COMMENT 'Kod produktu EAN/PLU',
  `kod_nazwa` varchar(200) DEFAULT '' COMMENT 'Nazwa produktu',
  `system_id` int NOT NULL DEFAULT '0' COMMENT 'ID systemu',
  `jm` varchar(8) DEFAULT NULL COMMENT 'Jednostka miary podstawowa',
  `ean` varchar(100) DEFAULT NULL COMMENT 'Kod EAN produktu',
  `active` int unsigned NOT NULL DEFAULT '1' COMMENT 'Status aktywności produktu',
  `waga_szt_kg` decimal(9,3) NOT NULL DEFAULT '0.000' COMMENT 'Waga jednostki w kg',
  `wymagana_partia` int unsigned NOT NULL DEFAULT '0' COMMENT 'Czy wymaga numeru partii',
  `wymagana_data_waznosci` int unsigned NOT NULL DEFAULT '0' COMMENT 'Czy wymaga daty ważności',
  PRIMARY KEY (`id`),
  UNIQUE KEY `kod_system_id` (`kod`,`system_id`),
  KEY `idx_kod` (`kod`),
  KEY `idx_active` (`active`),
  KEY `idx_ean` (`ean`)
) ENGINE=InnoDB AUTO_INCREMENT=78453 DEFAULT CHARSET=utf8mb4;
```

## Tabele Operacyjne

### Tabela `operacje` - Audyt Operacji

```sql
CREATE TABLE `operacje` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `etykieta_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID etykiety której dotyczy operacja',
  `doc_type` varchar(5) NOT NULL DEFAULT '' COMMENT 'Typ dokumentu (INW, REC, etc.)',
  `doc_nr` varchar(25) NOT NULL DEFAULT '0' COMMENT 'Numer dokumentu/sesji',
  `imie_nazwisko` varchar(25) NOT NULL DEFAULT '' COMMENT 'Pracownik wykonujący',
  `typ_operacji` varchar(60) NOT NULL DEFAULT '' COMMENT 'Typ operacji (INVENTORY_COUNT, etc.)',
  `ts_ins` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp operacji',
  `wozek` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID wózka/terminala',
  `system_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID systemu',
  `operac_id` varchar(10) NOT NULL DEFAULT '0' COMMENT 'ID operacji zewnętrznej',
  `ilosc` int unsigned NOT NULL DEFAULT '0' COMMENT 'Ilość w operacji',
  `jm` varchar(5) NOT NULL DEFAULT 'PLT' COMMENT 'Jednostka miary',
  PRIMARY KEY (`id`),
  KEY `idx_etykieta_id` (`etykieta_id`),
  KEY `idx_doc_nr` (`doc_nr`),
  KEY `idx_ts_ins` (`ts_ins`),
  KEY `idx_typ_operacji` (`typ_operacji`),
  KEY `idx_system_operac` (`system_id`, `operac_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6225666 DEFAULT CHARSET=utf8mb4;
```

### Tabela `zmianym` - Rejestr Zmian Lokalizacji

```sql
CREATE TABLE `zmianym` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `typ` varchar(5) NOT NULL DEFAULT '' COMMENT 'Typ zmiany (ZM = zmiana miejsca)',
  `doc_nr` int NOT NULL DEFAULT '0' COMMENT 'Numer dokumentu/sesji',
  `pracownik_id` int unsigned DEFAULT NULL COMMENT 'ID pracownika',
  `data` date NOT NULL COMMENT 'Data zmiany',
  `etykieta` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID etykiety',
  `system_id` int DEFAULT NULL COMMENT 'ID systemu',
  `stare_m` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID starego miejsca',
  `nowe_m` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID nowego miejsca',
  `doc_internal` varchar(2) DEFAULT NULL COMMENT 'Typ dokumentu wewnętrzny',
  `stat` int unsigned DEFAULT '0' COMMENT 'Status zmiany',
  `tszm` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `uwagi` varchar(45) NOT NULL DEFAULT '' COMMENT 'Uwagi do zmiany',
  PRIMARY KEY (`id`),
  KEY `idx_doc_nr` (`doc_nr`),
  KEY `idx_etykieta` (`etykieta`),
  KEY `idx_data` (`data`),
  KEY `idx_miejsca` (`stare_m`, `nowe_m`),
  KEY `idx_pracownik_doc` (`pracownik_id`, `doc_internal`)
) ENGINE=InnoDB AUTO_INCREMENT=5894639 DEFAULT CHARSET=utf8mb4;
```

## Relacje i Więzy Referentne

### Diagram Relacji
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   inwentaryzacja│────┤    etykiety     ├────┤     miejsca     │
│                 │    │                 │    │                 │
│ PK: id          │    │ PK: id,system_id│    │ PK: id          │
│ FK: etykieta_id │    │ FK: miejscep    │    │ hala            │
│ FK: miejscep    │    │ FK: kod_id      │    │ regal           │
│     inwent_id   │    │     paleta_id   │    │ miejsce         │
│     system_id   │    │     sscc        │    │ poziom          │
└─────────────────┘    │     lot         │    └─────────────────┘
         │              └─────────────────┘
         │                       │
         │              ┌─────────────────┐
         └──────────────┤      kody       │
                        │                 │
                        │ PK: id          │
                        │ UK: kod,system_id│
                        │     kod_nazwa   │
                        │     ean         │
                        └─────────────────┘
```

### Ograniczenia i Triggers

```sql
-- Constraint sprawdzający poprawność paleta_id
ALTER TABLE etykiety
ADD CONSTRAINT chk_paleta_id
CHECK ((paleta_id <> 0) AND (paleta_id IS NOT NULL));

-- Trigger aktualizujący timestamp przy modyfikacji
DELIMITER //
CREATE TRIGGER inwentaryzacja_update_ts
    BEFORE UPDATE ON inwentaryzacja
    FOR EACH ROW
BEGIN
    SET NEW.ts = CURRENT_TIMESTAMP;
END//
DELIMITER ;

-- Trigger logujący zmiany lokalizacji
DELIMITER //
CREATE TRIGGER etykiety_location_change
    AFTER UPDATE ON etykiety
    FOR EACH ROW
BEGIN
    IF OLD.miejscep != NEW.miejscep THEN
        INSERT INTO zmianym(typ, etykieta, stare_m, nowe_m, data, system_id)
        VALUES ('ZM', NEW.id, OLD.miejscep, NEW.miejscep, CURDATE(), NEW.system_id);
    END IF;
END//
DELIMITER ;
```

## Optymalizacje Wydajnościowe

### Indeksy Strategiczne

```sql
-- Indeks dla najczęstszego zapytania - status inwentaryzacji
CREATE INDEX idx_session_status
ON inwentaryzacja(inwentaryzacja_id, active, kod, ilosc_spisana);

-- Indeks dla wyszukiwania etykiet w sesji
CREATE INDEX idx_session_etykieta
ON inwentaryzacja(inwentaryzacja_id, etykieta_id, active);

-- Indeks dla wyszukiwania po kodzie produktu
CREATE INDEX idx_product_search
ON inwentaryzacja(kod, system_id, active);

-- Indeks pokrywający dla statystyk
CREATE INDEX idx_inventory_stats
ON inwentaryzacja(inwentaryzacja_id, active, kod, ilosc_spisana, ilosc)
INCLUDE (id, ts, pracownik);
```

### Partycjonowanie (Dla Dużych Wolumenów)

```sql
-- Partycjonowanie po roku dla archiwizacji
ALTER TABLE inwentaryzacja
PARTITION BY RANGE (YEAR(data)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### Zapytania Zoptymalizowane

```sql
-- Najczęstsze zapytanie - stan inwentaryzacji (zoptymalizowane)
SELECT
    COUNT(CASE WHEN ilosc_spisana IS NOT NULL THEN 1 END) as zliczone,
    COUNT(*) as wszystkie,
    ROUND(
        (COUNT(CASE WHEN ilosc_spisana IS NOT NULL THEN 1 END) * 100.0) / COUNT(*),
        1
    ) as procent_wykonania
FROM inwentaryzacja
WHERE inwentaryzacja_id = ?
    AND active = 1
    AND kod != '10101';

-- Wyszukiwanie różnic inwentaryzacyjnych
SELECT
    kod, kod_nazwa, ilosc as teoretyczna, ilosc_spisana as rzeczywista,
    (ilosc_spisana - ilosc) as roznica,
    CASE
        WHEN ilosc_spisana > ilosc THEN 'NADWYZKA'
        WHEN ilosc_spisana < ilosc THEN 'NIEDOBOR'
        ELSE 'ZGODNE'
    END as status_roznic
FROM inwentaryzacja i
LEFT JOIN kody k ON k.kod = i.kod AND k.system_id = i.system_id
WHERE i.inwentaryzacja_id = ?
    AND i.active = 1
    AND ABS(i.ilosc_spisana - i.ilosc) > 0.001
ORDER BY ABS(i.ilosc_spisana - i.ilosc) DESC;
```

---

*Dokument zaktualizowany: 2025-01-14 - Schemat bazy danych z mapowaniem EF Core* ✅