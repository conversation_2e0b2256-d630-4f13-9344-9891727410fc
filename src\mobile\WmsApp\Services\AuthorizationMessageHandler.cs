using System.Net.Http.Headers;

namespace WmsApp.Services;

public class AuthorizationMessageHandler : DelegatingHandler
{
    private readonly IAuthService _authService;

    public AuthorizationMessageHandler(IAuthService authService)
    {
        _authService = authService;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Nie dodawaj Authorization header dla endpointu logowania
        if (request.RequestUri?.AbsolutePath.Contains("/auth/login") == true)
        {
            System.Diagnostics.Debug.WriteLine($"[AUTH-HANDLER] Skipping auth header for login endpoint: {request.RequestUri}");
            return await base.SendAsync(request, cancellationToken);
        }

        // Pobierz aktualny token
        var authHeader = _authService.GetAuthorizationHeader();
        if (!string.IsNullOrEmpty(authHeader))
        {
            System.Diagnostics.Debug.WriteLine($"[AUTH-HANDLER] Adding auth header for: {request.RequestUri}");
            request.Headers.Authorization = AuthenticationHeaderValue.Parse(authHeader);
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"[AUTH-HANDLER] No auth token available for: {request.RequestUri}");
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
