using AutoMapper;
using MediatR;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Queries;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.Receives.Handlers;

public class GetAvailableReceivesHandler : IRequestHandler<GetAvailableReceivesQuery, ReceiveListResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IMapper _mapper;

    public GetAvailableReceivesHandler(IReceiveRepository receiveRepository, IMapper mapper)
    {
        _receiveRepository = receiveRepository;
        _mapper = mapper;
    }

    public async Task<ReceiveListResponse> Handle(GetAvailableReceivesQuery request, CancellationToken cancellationToken)
    {
        var receives = await _receiveRepository.GetAvailableReceivesAsync(
            request.MagazynId,
            request.IncludeAssigned,
            request.CurrentUserId,
            cancellationToken);

        var limitedReceives = receives.Take(request.Limit);

        var receivesDtos = _mapper.Map<List<ReceiveDto>>(limitedReceives);

        // Ustaw IsOccupiedByOther na podstawie currentUserId
        if (request.CurrentUserId.HasValue)
        {
            foreach (var dto in receivesDtos)
            {
                dto.IsOccupiedByOther = dto.IsAssigned && dto.RealizujacyPracownikId != request.CurrentUserId.Value;
            }
        }
        else
        {
            foreach (var dto in receivesDtos)
            {
                dto.IsOccupiedByOther = dto.IsAssigned;
            }
        }

        return new ReceiveListResponse
        {
            Receives = receivesDtos,
            TotalCount = receivesDtos.Count
        };
    }
}
