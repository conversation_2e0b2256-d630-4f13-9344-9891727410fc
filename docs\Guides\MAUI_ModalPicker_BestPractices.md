# Najlepsze praktyki – MAUI: modalny wybór produktu (Awizacja | Kartoteka)

Ten dokument zbiera wnioski z wdrożenia i debugowania modalnego wyboru produktu w zakładce „Rejestracja dostaw” (MAUI). Zawiera typowe symptomy, przyczyny oraz skuteczne rozwiązania i wzorce, które warto powielać w podobnych funkcjach.

---

## 1) Symptomy i ich przyczyny

1. Kliknięcia w elementy listy nic nie robią
   - Przyczyna: TapGestureRecognizer w DataTemplate odwoływał się do komend VM, ale błędny kontekst danych/binding (brak x:Name strony, niepoprawne x:Reference) lub ogólna kruchość TapGesture w DataTemplate.
   - Skutek: Komendy VM nie były wywoływane.

2. StaticResource not found (np. InverseBoolToColorConverter)
   - Przyczyna: Zasób (konwerter) użyty w XAML nie został zdefiniowany lub nie był zarejestrowany w App.xaml.
   - Skutek: Błąd XAML podczas renderowania.

3. VirtualView cannot be null here (po wyborze kodu i zamknięciu modala)
   - Przyczyna: Manipulacja właściwościami selekcji (SelectedItem/reset) albo wywołanie komendy, gdy strona/element są już usuwane z drzewa wizualnego.
   - Skutek: Nieobsłużony wyjątek UI podczas zamykania modala.

4. Ponowna inicjalizacja widoku po zamknięciu modala (reset stanu)
   - Przyczyna: Shell/IQueryAttributable – ApplyQueryAttributes może zostać wywołane ponownie (np. przy powrocie), co inicjalizowało VM ponownie.
   - Skutek: Nadpisywanie bieżącego stanu formularza.

---

## 2) Skuteczne rozwiązania i wzorce

1) Zamiast TapGestureRecognizer – użyj wzorca selekcji CollectionView
- Ustaw:
  - SelectionMode="Single"
  - SelectedItem="{Binding SelectedKodItem}" (i analogicznie SelectedAwizacjaItem)
- W VM obsłuż partial metody OnSelectedKodItemChanged/OnSelectedAwizacjaItemChanged.
- Uruchamiaj logikę selekcji asynchronicznie na głównym wątku z krótkim opóźnieniem:
  - BeginInvokeOnMainThread + krótkie Task.Delay(50–100 ms). To stabilizuje cykl życia UI (unikamy kolizji z animacją zamknięcia modala).
- Nie resetuj SelectedItem na null z VM – to często prowokuje błędy VirtualView. Jeśli musisz, rób to po stronie UI z rozwagą, lub po bezpiecznym czasie/zdarzeniu.

2) Debounce i anty-reentrancy dla wyboru
- W VM dodaj flagę _isSelectionProcessing, aby uniknąć wielokrotnego wywołania wyboru (np. przez szybkie kliknięcia/zmiany SelectedItem).
- Przed ustawieniem wyniku sprawdzaj, czy już coś nie jest przetwarzane.

3) Wzorzec przepływu wyniku – TaskCompletionSource
- Inicjuj TCS w serwisie otwierającym modal i przekaż do VM przez Init(lk, tcs).
- Po wyborze – VM ustawia wynik: _tcs.TrySetResult(selection).
- Serwis: await tcs.Task; następnie bezpiecznie zamyka modal i zwraca wynik do nadawcy (np. ReceivesRegistrationViewModel).
- Zalety: brak globalnych komunikatów, przejrzysty i synchroniczny przepływ wartości.

4) Bezpieczna nawigacja modalna
- Przed PopModalAsync sprawdzaj, czy strona nadal jest w ModalStack.
- Owiń Push/Pop w try/catch – a w finally postaraj się zamknąć modal tylko jeśli nadal jest w stosie.
- Zapobiega to wyjątkom przy szybkim zamykaniu/zdublowanych próbach.

5) Zasoby i konwertery XAML
- Jeśli używasz zasobu w XAML (np. InverseBoolToColorConverter), upewnij się, że:
  - Konwerter jest zaimplementowany w kodzie (np. w Converters/ValueConverters.cs),
  - Zarejestrowany w App.xaml w ResourceDictionary.
- Alternatywa: zamiast „Inverse...” – rozważ jeden konwerter z parametrem/negacją.

6) Ochrona przed ponowną inicjalizacją po zamknięciu modala
- W VM implementującym IQueryAttributable (ApplyQueryAttributes) dodaj straż:
  - Zapamiętaj, że dany LK już zainicjalizowano (_hasInitialized + porównanie aktualnego LK).
  - Jeśli LK się nie zmienił – nie inicjalizuj ponownie.

7) Diagnostyka i logowanie
- W krytycznych miejscach dodaj ILogger:
  - Service otwierający modal: logi Push/await/Pop i odbieranego rezultatu,
  - VM pickera: logi OnSelected..., Select..., ustawiania TCS,
  - VM nadrzędny: log po otrzymaniu selection (KodId/Kod/Nazwa), aktualizacja pól formularza.
- Dzięki temu szybko odtworzysz sekwencję przyczynowo-skutkową w logach.

8) Uwagi do compiled bindings (x:DataType) w DataTemplate
- Gdy używasz x:DataType w DataTemplate, odwołania typu Path=BindingContext/Source={x:Reference} mogą generować ostrzeżenia XamlC.
- Jeśli to przeszkadza, rozważ:
  - EventToCommandBehavior zamiast TapGesture,
  - Lub rezygnację z x:DataType w tych konkretnych DataTemplate (świadomie, z trade-off wydajnościowym).

9) Deprecation: Application.MainPage
- Zalecane: dla aplikacji jedno-okienkowych używać Windows[0].Page, dla wielo-okienkowych – dobrać właściwe Window.
- Operacje w serwisie modala można później przenieść na sugerowany mechanizm, aby wyciszyć ostrzeżenia.

---

## 3) Checklist wdrożenia podobnej funkcji

- [ ] UI: CollectionView z SelectionMode=Single, SelectedItem zbindowane do właściwości w VM
- [ ] VM: OnSelected...Changed → BeginInvokeOnMainThread (+ krótki Delay), wywołanie komendy/ustawienie TCS
- [ ] VM: flaga _isSelectionProcessing (anty-wyścig)
- [ ] Serwis: TaskCompletionSource, PushModalAsync, await result, bezpieczny PopModalAsync z kontrolą ModalStack
- [ ] Nadrzędny VM: Po otrzymaniu wyniku – ustaw pola formularza, przelicz ilości/komunikaty
- [ ] IQueryAttributable: Straż przeciwko ponownej inicjalizacji tego samego kontekstu (np. ten sam LK)
- [ ] Konwertery: Implementacja + rejestracja w App.xaml (brak „StaticResource not found”)
- [ ] Logowanie: Informacyjne logi na kluczowych etapach

---

## 4) Przykładowy przebieg (logi – oczekiwane)

- ProductPickerService: „Otwarto modal” → „Czekanie na rezultat…”
- ProductPickerViewModel: „OnSelectedKodItemChanged …” → „SelectCode …” → „Ustawianie rezultatu TCS”
- ProductPickerService: „Otrzymano rezultat: …” → „Zamknięto modal”
- ReceivesRegistrationViewModel: „Otrzymano wynik z pickera …” → „Ustawiono w formularzu …”

---

## 5) Wnioski

- Gesty (TapGestureRecognizer) w DataTemplate są kruche – preferuj wzorzec selekcji CollectionView z SelectedItem.
- Nie manipuluj SelectedItem z VM (np. reset na null) – to typowe źródło „VirtualView cannot be null here”.
- Zawsze bezpiecznie zamykaj modal (sprawdź ModalStack), a logika selekcji powinna być odporna na wyścigi (_isSelectionProcessing).
- TCS to prosty i czytelny sposób zwrotu wyniku z modala do nadawcy.
- Dodanie minimalnego opóźnienia przy selekcji i wykonywanie na głównym wątku stabilizuje UI.
- Chronić VM przed niepotrzebną reinicjalizacją po powrocie z modala (IQueryAttributable guard).

