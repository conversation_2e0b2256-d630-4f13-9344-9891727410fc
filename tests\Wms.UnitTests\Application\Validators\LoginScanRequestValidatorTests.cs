using FluentAssertions;
using Wms.Application.DTOs.Auth;
using Wms.Application.Validators;

namespace Wms.UnitTests.Application.Validators;

public class LoginScanRequestValidatorTests
{
    private readonly LoginScanRequestValidator _validator;

    public LoginScanRequestValidatorTests()
    {
        _validator = new LoginScanRequestValidator();
    }

    [Fact]
    public void Validate_WithValidRequest_ShouldReturnValid()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Validate_WithEmptyCardNumber_ShouldReturnInvalid(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage == "Card number is required");
    }

    [Theory]
    [InlineData("12345")] // 5 characters - too short
    [InlineData("A")] // 1 character - too short
    public void Validate_WithTooShortCardNumber_ShouldReturnInvalid(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage == "Card number must be between 6 and 50 characters");
    }

    [Fact]
    public void Validate_WithTooLongCardNumber_ShouldReturnInvalid()
    {
        // Arrange
        var cardNumber = new string('1', 51); // 51 characters - too long
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage == "Card number must be between 6 and 50 characters");
    }

    [Theory]
    [InlineData("123456")] // 6 characters - minimum valid
    [InlineData("1234567890123456")] // 16 characters - typical card length
    [InlineData("ABC123XYZ")] // Mixed letters and numbers
    [InlineData("12345678901234567890123456789012345678901234567890")] // 50 characters - maximum valid
    public void Validate_WithValidCardNumberLength_ShouldReturnValid(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("123-456")] // Contains hyphen
    [InlineData("123 456")] // Contains space
    [InlineData("123@456")] // Contains special character
    [InlineData("123.456")] // Contains dot
    [InlineData("123_456")] // Contains underscore
    [InlineData("123#456")] // Contains hash
    public void Validate_WithInvalidCardNumberCharacters_ShouldReturnInvalid(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage == "Card number can contain only letters and numbers");
    }

    [Theory]
    [InlineData("ABC123")] // Letters and numbers
    [InlineData("123456")] // Only numbers
    [InlineData("ABCDEF")] // Only letters
    [InlineData("aBc123")] // Mixed case
    public void Validate_WithValidCardNumberCharacters_ShouldReturnValid(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "mobile-device-001"
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("mobile-device-001")]
    [InlineData("tablet-123")]
    public void Validate_WithValidDeviceId_ShouldReturnValid(string deviceId)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = deviceId
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithTooLongDeviceId_ShouldReturnInvalid()
    {
        // Arrange
        var deviceId = new string('A', 101); // 101 characters - too long
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = deviceId
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.DeviceId) 
            && e.ErrorMessage == "Device ID cannot exceed 100 characters");
    }

    [Fact]
    public void Validate_WithMaximumLengthDeviceId_ShouldReturnValid()
    {
        // Arrange
        var deviceId = new string('A', 100); // 100 characters - maximum valid
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = deviceId
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithMultipleErrors_ShouldReturnAllErrors()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "12345", // Too short
            DeviceId = new string('A', 101) // Too long
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.DeviceId));
    }

    [Fact]
    public void Validate_WithBothCardNumberAndDeviceIdProblems_ShouldReturnAllRelevantErrors()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "12@34", // Invalid characters and too short (5 chars)
            DeviceId = new string('B', 101) // Too long
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Count.Should().BeGreaterThanOrEqualTo(2);
        
        // Should have card number errors
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage.Contains("letters and numbers"));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.CardNumber) 
            && e.ErrorMessage.Contains("6 and 50 characters"));
            
        // Should have device ID error
        result.Errors.Should().Contain(e => e.PropertyName == nameof(LoginScanRequest.DeviceId) 
            && e.ErrorMessage.Contains("100 characters"));
    }
}
