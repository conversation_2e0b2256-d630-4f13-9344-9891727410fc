using System.ComponentModel;
using System.Globalization;
using System.Resources;

namespace WmsApp.Localization;

public sealed class LocalizationResourceManager : INotifyPropertyChanged
{
    private static readonly Lazy<LocalizationResourceManager> _current = new(() => new LocalizationResourceManager());
    public static LocalizationResourceManager Current => _current.Value;

    private ResourceManager? _resourceManager;
    private CultureInfo _culture = CultureInfo.CurrentUICulture;

    public event PropertyChangedEventHandler? PropertyChanged;

    public void Init(ResourceManager resourceManager)
    {
        _resourceManager = resourceManager;
    }

    public void SetCulture(CultureInfo culture)
    {
        _culture = culture;
        // Powiadom wszystkie powiązania korzystające z indeksatora
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(null));
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs("Item[]"));
    }

    public string this[string key]
        => _resourceManager?.GetString(key, _culture) ?? key;
}

