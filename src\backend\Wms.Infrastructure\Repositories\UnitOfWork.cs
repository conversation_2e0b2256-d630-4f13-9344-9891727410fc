using Microsoft.EntityFrameworkCore.Storage;
using Wms.Application.Interfaces;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class EfTransaction : ITransaction
{
    private readonly IDbContextTransaction _transaction;

    public EfTransaction(IDbContextTransaction transaction)
    {
        _transaction = transaction;
    }

    public async Task CommitAsync()
    {
        await _transaction.CommitAsync();
    }

    public async Task RollbackAsync()
    {
        await _transaction.RollbackAsync();
    }

    public void Dispose()
    {
        _transaction?.Dispose();
    }
}

public class UnitOfWork : IUnitOfWork
{
    private readonly WmsDbContext _context;

    public UnitOfWork(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<ITransaction> BeginTransactionAsync()
    {
        var efTransaction = await _context.Database.BeginTransactionAsync();
        return new EfTransaction(efTransaction);
    }

    public async Task ExecuteInTransactionAsync(Func<Task> operation)
    {
        var strategy = _context.Database.CreateExecutionStrategy();
        await strategy.ExecuteAsync<object?, bool>(
            state: null,
            operation: async (dbContext, state, cancellationToken) =>
            {
                using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    await operation();
                    await transaction.CommitAsync(cancellationToken);
                    return true; // Success
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
            },
            verifySucceeded: null,
            cancellationToken: CancellationToken.None);
    }

    public async Task SaveChangesAsync()
    {
        await _context.SaveChangesAsync();
    }
}
