using FluentAssertions;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.ValueObjects;

public class GS1ParseResultTests
{
    [Fact]
    public void Success_WithAllFields_ShouldCreateValidResult()
    {
        // Arrange
        var sscc = "123456789012345678";
        var productCode = "1234567890123";
        var lot = "LOT123";
        var expiryDate = new DateOnly(2024, 12, 31);

        // Act
        var result = GS1ParseResult.Success(sscc, productCode, lot, expiryDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.SSCC.Should().Be(sscc);
        result.ProductCode.Should().Be(productCode);
        result.Lot.Should().Be(lot);
        result.ExpiryDate.Should().Be(expiryDate);
        result.ErrorMessage.Should().BeNull();
        result.HasProductCode.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.HasExpiryDate.Should().BeTrue();
    }

    [Fact]
    public void Success_WithSSCCOnly_ShouldCreateValidResult()
    {
        // Arrange
        var sscc = "123456789012345678";

        // Act
        var result = GS1ParseResult.Success(sscc);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.SSCC.Should().Be(sscc);
        result.ProductCode.Should().BeNull();
        result.Lot.Should().BeNull();
        result.ExpiryDate.Should().BeNull();
        result.ErrorMessage.Should().BeNull();
        result.HasProductCode.Should().BeFalse();
        result.HasLot.Should().BeFalse();
        result.HasExpiryDate.Should().BeFalse();
    }

    [Fact]
    public void Success_WithPartialFields_ShouldCreateValidResult()
    {
        // Arrange
        var sscc = "123456789012345678";
        var productCode = "1234567890123";
        var lot = "LOT123";

        // Act
        var result = GS1ParseResult.Success(sscc, productCode, lot);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.SSCC.Should().Be(sscc);
        result.ProductCode.Should().Be(productCode);
        result.Lot.Should().Be(lot);
        result.ExpiryDate.Should().BeNull();
        result.ErrorMessage.Should().BeNull();
        result.HasProductCode.Should().BeTrue();
        result.HasLot.Should().BeTrue();
        result.HasExpiryDate.Should().BeFalse();
    }

    [Theory]
    [InlineData("Nieprawidłowy format kodu kreskowego")]
    [InlineData("Brak wymaganych danych w kodzie")]
    [InlineData("Błąd parsowania daty")]
    public void Failure_ShouldCreateFailedResult(string errorMessage)
    {
        // Act
        var result = GS1ParseResult.Failure(errorMessage);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.SSCC.Should().BeNull();
        result.ProductCode.Should().BeNull();
        result.Lot.Should().BeNull();
        result.ExpiryDate.Should().BeNull();
        result.ErrorMessage.Should().Be(errorMessage);
        result.HasProductCode.Should().BeFalse();
        result.HasLot.Should().BeFalse();
        result.HasExpiryDate.Should().BeFalse();
    }

    [Fact]
    public void Failure_WithNullErrorMessage_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => GS1ParseResult.Failure(null!));
    }

    [Fact]
    public void Failure_WithEmptyErrorMessage_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => GS1ParseResult.Failure(""));
    }

    [Fact]
    public void Success_WithNullSSCC_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => GS1ParseResult.Success(null!));
    }

    [Fact]
    public void Success_WithEmptySSCC_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => GS1ParseResult.Success(""));
    }

    [Fact]
    public void HasProductCode_WithProductCode_ShouldReturnTrue()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678", "1234567890123");

        // Act & Assert
        result.HasProductCode.Should().BeTrue();
    }

    [Fact]
    public void HasProductCode_WithoutProductCode_ShouldReturnFalse()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678");

        // Act & Assert
        result.HasProductCode.Should().BeFalse();
    }

    [Fact]
    public void HasLot_WithLot_ShouldReturnTrue()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678", null, "LOT123");

        // Act & Assert
        result.HasLot.Should().BeTrue();
    }

    [Fact]
    public void HasLot_WithoutLot_ShouldReturnFalse()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678");

        // Act & Assert
        result.HasLot.Should().BeFalse();
    }

    [Fact]
    public void HasExpiryDate_WithExpiryDate_ShouldReturnTrue()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678", null, null, new DateOnly(2024, 12, 31));

        // Act & Assert
        result.HasExpiryDate.Should().BeTrue();
    }

    [Fact]
    public void HasExpiryDate_WithoutExpiryDate_ShouldReturnFalse()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678");

        // Act & Assert
        result.HasExpiryDate.Should().BeFalse();
    }

    [Fact]
    public void ToString_SuccessWithAllFields_ShouldReturnFormattedString()
    {
        // Arrange
        var result = GS1ParseResult.Success(
            "123456789012345678", 
            "1234567890123", 
            "LOT123", 
            new DateOnly(2024, 12, 31)
        );

        // Act
        var toString = result.ToString();

        // Assert
        toString.Should().Be("GS1ParseResult { IsSuccess = True, SSCC = 123456789012345678, ProductCode = 1234567890123, Lot = LOT123, ExpiryDate = 2024-12-31 }");
    }

    [Fact]
    public void ToString_SuccessWithSSCCOnly_ShouldReturnFormattedString()
    {
        // Arrange
        var result = GS1ParseResult.Success("123456789012345678");

        // Act
        var toString = result.ToString();

        // Assert
        toString.Should().Be("GS1ParseResult { IsSuccess = True, SSCC = 123456789012345678 }");
    }

    [Fact]
    public void ToString_Failure_ShouldReturnFormattedString()
    {
        // Arrange
        var result = GS1ParseResult.Failure("Błąd parsowania");

        // Act
        var toString = result.ToString();

        // Assert
        toString.Should().Be("GS1ParseResult { IsSuccess = False, ErrorMessage = Błąd parsowania }");
    }

    [Fact]
    public void Equals_SameSuccessResults_ShouldReturnTrue()
    {
        // Arrange
        var result1 = GS1ParseResult.Success("123456789012345678", "1234567890123", "LOT123", new DateOnly(2024, 12, 31));
        var result2 = GS1ParseResult.Success("123456789012345678", "1234567890123", "LOT123", new DateOnly(2024, 12, 31));

        // Act & Assert
        result1.Equals(result2).Should().BeTrue();
        (result1 == result2).Should().BeTrue();
        (result1 != result2).Should().BeFalse();
    }

    [Fact]
    public void Equals_DifferentSuccessResults_ShouldReturnFalse()
    {
        // Arrange
        var result1 = GS1ParseResult.Success("123456789012345678", "1234567890123");
        var result2 = GS1ParseResult.Success("876543210987654321", "3210987654321");

        // Act & Assert
        result1.Equals(result2).Should().BeFalse();
        (result1 == result2).Should().BeFalse();
        (result1 != result2).Should().BeTrue();
    }

    [Fact]
    public void Equals_SameFailureResults_ShouldReturnTrue()
    {
        // Arrange
        var result1 = GS1ParseResult.Failure("Błąd parsowania");
        var result2 = GS1ParseResult.Failure("Błąd parsowania");

        // Act & Assert
        result1.Equals(result2).Should().BeTrue();
        (result1 == result2).Should().BeTrue();
        (result1 != result2).Should().BeFalse();
    }

    [Fact]
    public void Equals_DifferentFailureResults_ShouldReturnFalse()
    {
        // Arrange
        var result1 = GS1ParseResult.Failure("Błąd 1");
        var result2 = GS1ParseResult.Failure("Błąd 2");

        // Act & Assert
        result1.Equals(result2).Should().BeFalse();
        (result1 == result2).Should().BeFalse();
        (result1 != result2).Should().BeTrue();
    }

    [Fact]
    public void Equals_SuccessAndFailure_ShouldReturnFalse()
    {
        // Arrange
        var success = GS1ParseResult.Success("123456789012345678");
        var failure = GS1ParseResult.Failure("Błąd parsowania");

        // Act & Assert
        success.Equals(failure).Should().BeFalse();
        (success == failure).Should().BeFalse();
        (success != failure).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_SameResults_ShouldReturnSameHashCode()
    {
        // Arrange
        var result1 = GS1ParseResult.Success("123456789012345678", "1234567890123", "LOT123");
        var result2 = GS1ParseResult.Success("123456789012345678", "1234567890123", "LOT123");

        // Act & Assert
        result1.GetHashCode().Should().Be(result2.GetHashCode());
    }
}
