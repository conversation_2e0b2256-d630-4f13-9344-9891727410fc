using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class LabelRepository : ILabelRepository
{
    private readonly WmsDbContext _context;
    private readonly ILogger<LabelRepository> _logger;

    public LabelRepository(WmsDbContext context, ILogger<LabelRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    // Utrzymanie kompatybilności z testami, które tworzyły repo bez loggera
    public LabelRepository(WmsDbContext context) : this(context, NullLogger<LabelRepository>.Instance)
    {
    }

    public async Task<Label?> GetBySSCCAsync(string sscc)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .FirstOrDefaultAsync(l => l.Sscc == sscc && l.Active == 1);
    }

    public async Task<Label?> GetBySSCCAsNoTrackingAsync(string sscc)
    {
        return await _context.Labels
            .AsNoTracking()
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .FirstOrDefaultAsync(l => l.Sscc == sscc && l.Active == 1);
    }

    public async Task<Label?> GetByClientCodeAsync(string clientCode)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .FirstOrDefaultAsync(l => l.EtykietaKlient == clientCode && l.Active == 1);
    }

    public async Task<Label?> GetByClientCodeAsNoTrackingAsync(string clientCode)
    {
        return await _context.Labels
            .AsNoTracking()
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .FirstOrDefaultAsync(l => l.EtykietaKlient == clientCode && l.Active == 1);
    }

    public async Task<Label?> GetByPalletIdDirectAsync(int palletId)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .Where(l => l.PaletaId == palletId && l.Active == 1)
            .OrderByDescending(l => l.Id) // Najnowsza etykieta
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Label>> GetByPalletIdAsync(int palletId)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Include(l => l.Location)
            .Where(l => l.PaletaId == palletId)
            .OrderByDescending(l => l.Active)
            .ThenByDescending(l => l.Ts)
            .ToListAsync();
    }

    public async Task<IEnumerable<Label>> GetActiveByPalletIdAsync(int palletId)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Include(l => l.Location)
            .Where(l => l.PaletaId == palletId && l.Active == 1)
            .ToListAsync();
    }

    public async Task<IEnumerable<Label>> GetActiveByPalletIdAsNoTrackingAsync(int palletId)
    {
        return await _context.Labels
            .AsNoTracking()
            .Include(l => l.Location)
            .Where(l => l.PaletaId == palletId && l.Active == 1)
            .ToListAsync();
    }

    public async Task<Dictionary<string, decimal>> GetReceivedQuantitiesByCodeAsync(int listControlId)
    {
        // Zapytanie zgodne z logiką SQL z bazy danych:
        // SELECT k.kod, sum(e.ilosc) as ilosc_przyj
        // FROM list_control l
        // left join listcontrol_palety lp on lp.listcontrol_id=l.id
        // left join etykiety e on e.paleta_id=lp.paleta_id
        // left join kody k on e.kod_id=k.id
        // WHERE l.id= '29415' and e.kod_id is not null and (doc_type is null or doc_type not in ('PP', 'MM', 'RP', 'ZP'))
        // group by e.kod_id

        var result = await (from lc in _context.ListControls
                           join lcp in _context.ListControlPallets on lc.Id equals lcp.ListcontrolId
                           join e in _context.Labels on lcp.PaletaId equals e.PaletaId
                           join k in _context.Kody on e.KodId equals k.Id
                           where lc.Id == listControlId
                                 && e.KodId != null
                           group e by k.KodValue into g
                           select new { Kod = g.Key, IloscPrzyjeta = g.Sum(x => x.Ilosc ?? 0) })
                          .ToListAsync();

        return result.ToDictionary(x => x.Kod, x => x.IloscPrzyjeta);
    }

    public async Task UpdateLocationAsync(int labelId, int systemId, int newLocationId)
    {
        _logger.LogInformation("Updating label {LabelId}, systemId: {SystemId}, newLocation: {NewLocationId}", labelId, systemId, newLocationId);
        
        // Szukaj wśród już śledzonych encji w kontekście
        var label = _context.ChangeTracker.Entries<Label>()
            .Where(e => e.Entity.Id == labelId && e.Entity.SystemId == systemId)
            .Select(e => e.Entity)
            .FirstOrDefault();

        // Jeśli nie znaleziono w ChangeTracker, załaduj z AsNoTracking i dołącz
        if (label == null)
        {
            label = await _context.Labels
                .AsNoTracking()
                .FirstOrDefaultAsync(l => l.Id == labelId && l.SystemId == systemId);
                
            if (label != null)
            {
                // Dołącz jako już istniejącą encję
                _context.Labels.Attach(label);
            }
        }

        if (label != null)
        {
            var oldLocation = label.Miejscep;
            label.Miejscep = newLocationId;
            
            // Wymuszenie oznaczenia jako zmodyfikowane
            var entry = _context.Entry(label);
            entry.Property(x => x.Miejscep).IsModified = true;
            
            _logger.LogInformation("Label {LabelId} marked for update from {OldLocation} to {NewLocation}, EntityState: {State}, IsModified: {IsModified}", 
                labelId, oldLocation, newLocationId, entry.State, entry.Property(x => x.Miejscep).IsModified);
                
            // SaveChangesAsync() zostanie wywołane przez transakcję
        }
        else
        {
            _logger.LogWarning("Label {LabelId} with systemId {SystemId} NOT FOUND!", labelId, systemId);
        }
    }

    public void UpdateLocationDirect(Label label, int newLocationId)
    {
        _logger.LogInformation("Updating label {LabelId}, systemId: {SystemId}, newLocation: {NewLocationId} (direct)", label.Id, label.SystemId, newLocationId);
        
        var oldLocation = label.Miejscep;
        label.Miejscep = newLocationId;
        
        // Wymuszenie oznaczenia jako zmodyfikowane
        var entry = _context.Entry(label);
        entry.Property(x => x.Miejscep).IsModified = true;
        
        _logger.LogInformation("Label {LabelId} marked for update from {OldLocation} to {NewLocation}, EntityState: {State}, IsModified: {IsModified}", 
            label.Id, oldLocation, newLocationId, entry.State, entry.Property(x => x.Miejscep).IsModified);
    }

    public async Task UpdateLocationByIdAsync(int labelId, int newLocationId)
    {
        _logger.LogDebug("Updating label {LabelId} to location {NewLocationId}", labelId, newLocationId);

        var label = await _context.Labels
            .FirstOrDefaultAsync(l => l.Id == labelId);

        if (label != null)
        {
            var oldLocation = label.Miejscep;
            label.Miejscep = newLocationId;
            // SaveChangesAsync() zostanie wywołane przez transakcję

            _logger.LogInformation("Label {LabelId} marked for update from {OldLocation} to {NewLocation}", labelId, oldLocation, newLocationId);
        }
        else
        {
            _logger.LogWarning("Label {LabelId} NOT FOUND!", labelId);
        }
    }

    public void Add(Label label)
    {
        _logger.LogDebug("Adding new label to context: SystemId={SystemId}, PaletaId={PaletaId}, KodId={KodId}",
            label.SystemId, label.PaletaId, label.KodId);

        _context.Labels.Add(label);
    }
}
