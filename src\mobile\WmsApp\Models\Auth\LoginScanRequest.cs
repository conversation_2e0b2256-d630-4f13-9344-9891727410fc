namespace WmsApp.Models.Auth;

public record LoginScanRequest
{
    public string CardNumber { get; init; } = null!;
    public string? DeviceId { get; init; }
}

public record LoginScanResponse
{
    public string Token { get; init; } = null!;
    public DateTime ExpiresAt { get; init; }
    public UserInfo User { get; init; } = null!;
}

public record UserInfo
{
    public int Id { get; init; }
    public string FullName { get; init; } = null!;
    public string Position { get; init; } = null!;
    public string Role => Position; // Alias for compatibility
    public string? Email { get; init; }
    public string? Department { get; init; }
    public bool IsActive { get; init; } = true;
    public DateTime LastLogin { get; init; } = DateTime.UtcNow;
}
