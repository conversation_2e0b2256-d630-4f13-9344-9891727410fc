namespace WmsApp.Services;

/// <summary>
/// Interfejs serwisu detekcji urządzeń i środowiska
/// </summary>
public interface IDeviceDetectionService
{
    /// <summary>
    /// Sprawdza czy aplikacja działa na emulatorze
    /// </summary>
    bool IsEmulator { get; }
    
    /// <summary>
    /// Sprawdza czy urządzenie to Zebra z DataWedge
    /// </summary>
    bool IsZebraDevice { get; }
    
    /// <summary>
    /// Sprawdza czy DataWedge jest dostępny
    /// </summary>
    bool IsDataWedgeAvailable { get; }
    
    /// <summary>
    /// Sprawdza czy skanowanie jest dostępne
    /// </summary>
    bool IsScanningAvailable { get; }
    
    /// <summary>
    /// Informacja o typie urządzenia dla użytkownika
    /// </summary>
    string DeviceTypeInfo { get; }
}
