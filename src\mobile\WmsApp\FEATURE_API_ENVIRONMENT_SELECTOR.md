# FEATURE: Wybór środowiska API na ekranie logowania

## Opis funkcjonalności
Dodano możliwość wyboru między trybem mock (dane testowe) a rzeczywistym API na serwerze developerskim bezpośrednio z ekranu logowania.

## Nowe komponenty

### 1. ApiEnvironment Enum
```csharp
public enum ApiEnvironment
{
    Mock,                // Dane testowe
    DevelopmentServer    // Serwer developerski ************
}
```

### 2. ApiConfigurationService
- **Interfejs**: `IApiConfigurationService`
- **Funkcje**:
  - Zarządzanie bieżącym środowiskiem API
  - Persystencja wyboru w `IPreferences`
  - Dynamiczne tworzenie odpowiedniego `IWmsApiService`

### 3. Rozszerzenia ApiEnvironment
- `GetDisplayName()` - przyjazne nazwy dla UI
- `GetBaseUrl()` - URL endpointu dla każdego środowiska

## Konfiguracja środowisk

| Środowisko | Opis | URL | Funkcjonalność |
|------------|------|-----|----------------|
| **Mock** | Dane testowe | `mock://localhost/` | `MockWmsApiService` - symulowane odpowiedzi |
| **DevelopmentServer** | Serwer dev | `http://************/api/` | `RefitClient` - rzeczywiste API |

## Zmiany w UI

### LoginPage.xaml
Dodano sekcję wyboru środowiska:
- **Picker** z dostępnymi środowiskami
- **ApiEnvironmentDisplayConverter** do wyświetlania nazw
- **Persystencja** wyboru między sesjami

### LoginViewModel.cs
Nowe właściwości:
- `SelectedEnvironment` - bieżące środowisko
- `AvailableEnvironments` - lista dostępnych opcji
- `ShowEnvironmentSelector` - widoczność selektora

## Przepływ działania

1. **Uruchomienie aplikacji**:
   - Ładowany jest zapisany wybór środowiska (domyślnie Mock)
   - Tworzony jest odpowiedni `IWmsApiService`

2. **Zmiana środowiska przez użytkownika**:
   - Wybór zapisywany w `IPreferences`
   - Tworzony nowy `IWmsApiService`
   - Resetowany komunikat błędu

3. **Logowanie**:
   - Używany bieżący `IWmsApiService` zgodnie z wybranym środowiskiem
   - Mock: symulowane dane, zawsze sukces dla demo kart
   - Dev Server: rzeczywiste wywołanie HTTP na ************

## Konfiguracja DI

### MauiProgram.cs
```csharp
// Zastąpiono stałą rejestrację IWmsApiService
services.AddSingleton<IApiConfigurationService, ApiConfigurationService>();

// LoginViewModel teraz używa IApiConfigurationService
services.AddTransient<LoginViewModel>();
```

## Korzyści

### Dla deweloperów:
- **Łatwe przełączanie** między trybami testowymi a rzeczywistym API
- **Persystencja wyboru** - nie trzeba wybierać za każdym razem
- **Izolacja** - testy nie wpływają na rzeczywiste dane

### Dla testerów:
- **Elastyczność testowania** - można testować offline (mock) i online (dev server)
- **Debugowanie** - łatwe porównanie zachowania mock vs API
- **Demonstracje** - zawsze działający tryb demo

## Pliki dodane/zmienione

### Nowe pliki:
- `Models/ApiEnvironment.cs` - enum i rozszerzenia
- `Services/ApiConfigurationService.cs` - serwis konfiguracji

### Zmienione pliki:
- `ViewModels/LoginViewModel.cs` - obsługa wyboru środowiska
- `Views/LoginPage.xaml` - UI selektora środowiska
- `Converters/ValueConverters.cs` - konwerter nazw środowisk
- `App.xaml` - rejestracja konwertera
- `MauiProgram.cs` - nowa konfiguracja DI

## Testowanie

### Tryb Mock (domyślny):
- Kody testowe: `DEMO`, `123456`, `TEST`, `ADMIN`, `USER`
- Zawsze zwraca sukces dla poprawnych kodów
- Nie wymaga połączenia sieciowego

### Tryb Development Server:
- Endpoint: `http://************/api/`
- Rzeczywiste wywołania HTTP
- Wymagane: serwer backend na Debian + Apache

## Wynik
- ✅ **Projekt kompiluje się bez błędów**
- ✅ **Dynamiczne przełączanie środowisk**
- ✅ **Persystencja wyboru użytkownika**
- ✅ **Przyjazny interfejs wyboru**
- ✅ **Kompatybilność wsteczna z istniejącym kodem**

---
*Data: 2025-09-03*
*Status: ✅ Ukończone*
