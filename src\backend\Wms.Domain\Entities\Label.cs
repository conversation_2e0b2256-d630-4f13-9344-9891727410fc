using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class Label : BaseEntity
{
    public int Id { get; set; }
    public int SystemId { get; set; } = 0;
    public string? EtykietaKlient { get; set; }
    public int Magazyn { get; set; } = 0;
    public int? Active { get; set; }
    public int Miejscep { get; set; } = 0; // Foreign Key do miejsca.id
    public int? KodId { get; set; }
    public int StatusIdOld { get; set; } = 1;
    public int StatusId { get; set; } = 1;
    public string? Stat { get; set; }
    public int? PaletaId { get; set; } // Foreign Key do palety.id
    public int? Kartony { get; set; }
    public DateOnly? Dataprod { get; set; }
    public DateOnly? DataWaznosci { get; set; }
    public decimal? Ilosc { get; set; }
    public DateTime Ts { get; set; } = DateTime.UtcNow;
    public string? Status { get; set; }
    public string? Blloc { get; set; }
    public int? AkcjaId { get; set; }
    public string? StatusPrism { get; set; }
    public string? Lot { get; set; }
    public string? Sscc { get; set; } // SSCC kod (18 cyfr)
    public string? Gtin { get; set; }
    public int PrzeznczenieId { get; set; } = 1;
    public int Nretykiety { get; set; } = 0;
    public int? DocinId { get; set; }
    public int? DocoutId { get; set; }
    public int? DeliveryId { get; set; }
    public int? ListcontrolId { get; set; }
    public int? StatusId2 { get; set; }
    public int EdycjaEt { get; set; } = 1;
    
    // Navigation properties
    public Pallet? Pallet { get; set; }
    public Location? Location { get; set; }
    
    // Helper properties
    public bool IsActive => Active == 1;
    public bool HasSSCC => !string.IsNullOrEmpty(Sscc);
    
    // Business logic helpers
    public string GetDisplayCode()
    {
        // Priorytet: SSCC > EtykietaKlient > Id
        return Sscc ?? EtykietaKlient ?? Id.ToString();
    }
}
