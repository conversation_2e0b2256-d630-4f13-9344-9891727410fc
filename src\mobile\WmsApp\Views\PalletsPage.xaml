<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.PalletsPage"
             x:Name="PalletsPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:models="clr-namespace:WmsApp.Models.Pallets"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:PalletsViewModel"
             Title="{loc:Translate Key=Pallets_Title}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Search Bar -->
        <Frame Grid.Row="0" 
               BackgroundColor="White" 
               Padding="15" 
               Margin="10"
               CornerRadius="10" 
               HasShadow="True">
            
            <SearchBar x:Name="PalletSearchBar"
                       Text="{Binding SearchText}"
Placeholder="{loc:Translate Key=Pallets_Search_Placeholder}"
                       SearchCommand="{Binding SearchPalletsCommand}"
                       FontSize="16" />
        </Frame>

        <!-- Main Content -->
        <RefreshView Grid.Row="1" 
                     IsRefreshing="{Binding IsRefreshing}"
                     RefreshColor="{DynamicResource Primary}"
                     Command="{Binding RefreshCommand}">

            <CollectionView ItemsSource="{Binding Pallets}"
                            BackgroundColor="{DynamicResource Gray100}">
                
                <CollectionView.ItemTemplate>
<DataTemplate x:DataType="models:PalletInfo">
                        <Grid Padding="10,5">
                            <Frame BackgroundColor="White"
                                   CornerRadius="12"
                                   HasShadow="True"
                                   Padding="15">
                                
                                <Frame.GestureRecognizers>
<TapGestureRecognizer Command="{Binding Source={x:Reference PalletsPageRoot}, Path=VM.SelectPalletCommand}"
                                                          CommandParameter="{Binding .}" />
                                </Frame.GestureRecognizers>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <!-- Main SSCC or Pallet ID -->
                                    <Label Grid.Row="0" Grid.Column="0"
                                           Text="{Binding MainSSCC, FallbackValue='Mixed Pallet'}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{DynamicResource Primary}" />

                                    <!-- Move Button -->
<Button Grid.Row="0" Grid.Column="1"
                                            Text="📦"
Command="{Binding Source={x:Reference PalletsPageRoot}, Path=VM.MovePalletCommand}"
                                            CommandParameter="{Binding .}"
                                            BackgroundColor="{DynamicResource Secondary}"
                                            TextColor="White"
                                            CornerRadius="15"
                                            WidthRequest="40"
                                            HeightRequest="40"
                                            FontSize="16"
                                            Padding="0" />

                                    <!-- Location Info -->
                                    <StackLayout Grid.Row="1" Grid.ColumnSpan="2" 
                                                 Orientation="Horizontal" 
                                                 Spacing="10"
                                                 Margin="0,5,0,0">
                                        <Label Text="📍" FontSize="14" />
                                        <Label Text="{Binding CurrentLocation.Code, FallbackValue='No Location'}" 
                                               FontSize="14" 
                                               TextColor="{DynamicResource Gray600}" />
                                        <Label Text="{Binding CurrentLocation.Hala, StringFormat='Hall {0}'}" 
                                               FontSize="12" 
                                               TextColor="{DynamicResource Gray500}"
                                               IsVisible="{Binding CurrentLocation, Converter={StaticResource IsNotNullConverter}}" />
                                    </StackLayout>

                                    <!-- Labels Info -->
                                    <StackLayout Grid.Row="2" Grid.ColumnSpan="2" 
                                                 Margin="0,8,0,0"
                                                 IsVisible="{Binding Labels.Count, Converter={StaticResource CountToBoolConverter}}">
                                        <Label Text="{Binding Labels.Count, StringFormat='Labels: {0}'}" 
                                               FontSize="12" 
                                               FontAttributes="Bold"
                                               TextColor="{DynamicResource Primary}" />
                                        
                                        <FlexLayout BindableLayout.ItemsSource="{Binding Labels}" 
                                                    Direction="Row"
                                                    Wrap="Wrap"
                                                    JustifyContent="Start"
                                                    AlignItems="Start"
                                                    Margin="0,5,0,0">
                                            <BindableLayout.ItemTemplate>
                                                <DataTemplate x:DataType="models:LabelInfo">
                                                    <Frame BackgroundColor="{DynamicResource Gray200}"
                                                           CornerRadius="8"
                                                           Padding="8,4"
                                                           Margin="0,0,5,5"
                                                           HasShadow="False">
                                                        <StackLayout Orientation="Horizontal" Spacing="3">
                                                            <Label Text="{Binding ClientLabel, FallbackValue='No Label'}"
                                                                   FontSize="10"
                                                                   FontAttributes="Bold"
                                                                   TextColor="{DynamicResource Gray700}" />
                                                            <Label Text="{Binding Quantity, StringFormat='({0})'}"
                                                                   FontSize="10"
                                                                   TextColor="{DynamicResource Gray600}"
                                                                   IsVisible="{Binding Quantity, Converter={StaticResource CountToBoolConverter}}" />
                                                        </StackLayout>
                                                    </Frame>
                                                </DataTemplate>
                                            </BindableLayout.ItemTemplate>
                                        </FlexLayout>
                                    </StackLayout>

                                    <!-- Last Movement Info -->
                                    <Grid Grid.Row="3" Grid.ColumnSpan="2" 
                                          Margin="0,8,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <StackLayout Grid.Column="0" Orientation="Horizontal" Spacing="5">
                                            <Label Text="⏱️" FontSize="12" />
                                            <Label Text="{Binding LastMovementAt, StringFormat='Last moved: {0:dd/MM HH:mm}', FallbackValue='Never moved'}" 
                                                   FontSize="11" 
                                                   TextColor="{DynamicResource Gray500}" />
                                        </StackLayout>

                                        <!-- Expiry Warning -->
                                        <Label Grid.Column="1"
                                               Text="⚠️"
                                               FontSize="16"
                                               TextColor="Orange"
                                               IsVisible="{Binding ., Converter={StaticResource HasExpiringLabelsConverter}}"
                                               ToolTipProperties.Text="Has expiring items" />
                                    </Grid>
                                </Grid>
                            </Frame>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>

                <CollectionView.EmptyView>
                    <StackLayout Padding="50" VerticalOptions="CenterAndExpand">
                        <Label Text="📦" 
                               FontSize="48" 
                               HorizontalOptions="Center" 
                               Opacity="0.5" />
<Label Text="{loc:Translate Key=Pallets_Empty_Title}" 
                               FontSize="18" 
                               HorizontalOptions="Center" 
                               TextColor="{DynamicResource Gray400}" />
<Label Text="{loc:Translate Key=Pallets_Empty_Subtitle}" 
                               FontSize="14" 
                               HorizontalOptions="Center" 
                               TextColor="{DynamicResource Gray400}" 
                               Margin="0,5,0,0" />
                    </StackLayout>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>

        <!-- Loading Overlay -->
        <Grid Grid.Row="1" 
              IsVisible="{Binding IsLoading}"
              BackgroundColor="#80000000">
            
            <StackLayout VerticalOptions="Center" HorizontalOptions="Center">
                <ActivityIndicator IsRunning="{Binding IsLoading}"
                                   Color="{DynamicResource Primary}"
                                   WidthRequest="50"
                                   HeightRequest="50" />
<Label Text="{loc:Translate Key=Pallets_Loading_Label}" 
                       TextColor="White" 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       Margin="0,10,0,0" />
            </StackLayout>
        </Grid>

        <!-- Error Message -->
        <Frame Grid.Row="1"
               IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
               BackgroundColor="Red"
               Margin="20"
               CornerRadius="8"
               VerticalOptions="End">
            <Label Text="{Binding ErrorMessage}"
                   TextColor="White"
                   FontSize="14"
                   HorizontalOptions="Center" />
        </Frame>
    </Grid>

</ContentPage>
