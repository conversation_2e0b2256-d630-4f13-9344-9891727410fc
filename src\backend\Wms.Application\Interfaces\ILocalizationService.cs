using System.Globalization;

namespace Wms.Application.Interfaces;

/// <summary>
/// Interfejs serwisu lokalizacji dla obsługi komunikatów w różnych języ<PERSON>ch
/// </summary>
public interface ILocalizationService
{
    /// <summary>
    /// Pobiera zlokalizowany komunikat
    /// </summary>
    /// <param name="key">Klucz komunikatu</param>
    /// <param name="culture">Kultura (opcjonalna, domyślnie pl-PL)</param>
    /// <returns>Zlokalizowany komunikat</returns>
    string GetString(string key, CultureInfo? culture = null);

    /// <summary>
    /// Pobiera zlokalizowany komunikat z formatowaniem parametrów
    /// </summary>
    /// <param name="key">Klucz komunikatu</param>
    /// <param name="args">Parametry do formatowania</param>
    /// <returns>Sformatowany zlokalizowany komunikat</returns>
    string GetString(string key, params object[] args);

    /// <summary>
    /// Pobiera zlokalizowany komunikat z formatowaniem parametrów dla określonej kultury
    /// </summary>
    /// <param name="key">Klucz komunikatu</param>
    /// <param name="culture">Kultura</param>
    /// <param name="args">Parametry do formatowania</param>
    /// <returns>Sformatowany zlokalizowany komunikat</returns>
    string GetString(string key, CultureInfo? culture, params object[] args);

    /// <summary>
    /// Sprawdza czy zasób istnieje
    /// </summary>
    /// <param name="key">Klucz komunikatu</param>
    /// <param name="culture">Kultura (opcjonalna)</param>
    /// <returns>True jeśli zasób istnieje</returns>
    bool HasResource(string key, CultureInfo? culture = null);

    /// <summary>
    /// Ustawia domyślną kulturę dla aplikacji
    /// </summary>
    /// <param name="culture">Kultura do ustawienia jako domyślna</param>
    void SetDefaultCulture(CultureInfo culture);
}
