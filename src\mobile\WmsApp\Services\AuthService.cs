using WmsApp.Models.Auth;

namespace WmsApp.Services;

public interface IAuthService
{
    bool IsAuthenticated { get; }
    string? CurrentToken { get; }
    UserInfo? CurrentUser { get; }
    DateTime? TokenExpiry { get; }
    
    Task SetAuthenticationAsync(LoginScanResponse loginResponse);
    Task ClearAuthenticationAsync();
    Task<bool> IsTokenValidAsync();
    string? GetAuthorizationHeader();
}

public class AuthService : IAuthService
{
    private const string TokenKey = "jwt_token";
    private const string UserKey = "current_user";
    private const string ExpiryKey = "token_expiry";
    
    private readonly IPreferences _preferences;
    
    private string? _currentToken;
    private UserInfo? _currentUser;
    private DateTime? _tokenExpiry;

    public AuthService(IPreferences preferences)
    {
        _preferences = preferences;
        LoadStoredAuth();
    }

    public bool IsAuthenticated => !string.IsNullOrEmpty(_currentToken) && IsTokenValidAsync().Result;

    public string? CurrentToken => _currentToken;

    public UserInfo? CurrentUser => _currentUser;

    public DateTime? TokenExpiry => _tokenExpiry;

    public async Task SetAuthenticationAsync(LoginScanResponse loginResponse)
    {
        _currentToken = loginResponse.Token;
        _currentUser = loginResponse.User;
        _tokenExpiry = loginResponse.ExpiresAt;

        // Zapisz w preferencjach
        _preferences.Set(TokenKey, _currentToken);
        _preferences.Set(ExpiryKey, _tokenExpiry?.ToString("O") ?? "");
        
        // Zapisz dane użytkownika jako JSON
        var userJson = System.Text.Json.JsonSerializer.Serialize(_currentUser);
        _preferences.Set(UserKey, userJson);

        System.Diagnostics.Debug.WriteLine($"[AUTH] Token saved, expires at: {_tokenExpiry}");
        
        await Task.CompletedTask;
    }

    public async Task ClearAuthenticationAsync()
    {
        _currentToken = null;
        _currentUser = null;
        _tokenExpiry = null;

        // Usuń z preferencji
        _preferences.Remove(TokenKey);
        _preferences.Remove(UserKey);
        _preferences.Remove(ExpiryKey);

        System.Diagnostics.Debug.WriteLine("[AUTH] Authentication cleared");
        
        await Task.CompletedTask;
    }

    public async Task<bool> IsTokenValidAsync()
    {
        if (string.IsNullOrEmpty(_currentToken))
            return false;

        if (_tokenExpiry == null)
            return false;

        // Sprawdź czy token nie wygasł (dodaj 5 min bufor)
        var isValid = _tokenExpiry > DateTime.UtcNow.AddMinutes(5);
        
        System.Diagnostics.Debug.WriteLine($"[AUTH] Token valid: {isValid}, expires: {_tokenExpiry}, now: {DateTime.UtcNow}");
        
        return await Task.FromResult(isValid);
    }

    public string? GetAuthorizationHeader()
    {
        if (string.IsNullOrEmpty(_currentToken))
            return null;

        return $"Bearer {_currentToken}";
    }

    private void LoadStoredAuth()
    {
        try
        {
            _currentToken = _preferences.Get(TokenKey, (string?)null);
            var expiryStr = _preferences.Get(ExpiryKey, (string?)null);
            var userJson = _preferences.Get(UserKey, (string?)null);

            if (!string.IsNullOrEmpty(expiryStr) && DateTime.TryParse(expiryStr, out var expiry))
            {
                _tokenExpiry = expiry;
            }

            if (!string.IsNullOrEmpty(userJson))
            {
                _currentUser = System.Text.Json.JsonSerializer.Deserialize<UserInfo>(userJson);
            }

            System.Diagnostics.Debug.WriteLine($"[AUTH] Loaded stored auth - Token: {(!string.IsNullOrEmpty(_currentToken) ? "present" : "none")}, User: {_currentUser?.FullName ?? "none"}, Expires: {_tokenExpiry}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[AUTH] Error loading stored auth: {ex.Message}");
            // W przypadku błędu, wyczyść uszkodzone dane
            ClearAuthenticationAsync().ConfigureAwait(false);
        }
    }
}
