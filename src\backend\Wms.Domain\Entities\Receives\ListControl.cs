using Wms.Domain.Common;
using Wms.Domain.Entities; // For User, Location entities

namespace Wms.Domain.Entities.Receives;

/// <summary>
/// Encja reprezentująca dostawę w systemie WMS
/// Mapowana na tabelę list_control
/// </summary>
public class ListControl : BaseEntity
{
    public int Id { get; set; }
    public int? ListcontrolSystemId { get; set; }
    public int Numer { get; set; } = 0;
    public DateOnly? Data { get; set; }
    public string? DokumentDostawy { get; set; }
    public string? Transport { get; set; }
    public string? Uwagi { get; set; }
    public int ListInternal { get; set; } = 1;
    public DateTime Ts { get; set; } = DateTime.UtcNow;
    public int? PracownikId { get; set; }
    public int ListcontrolTypeId { get; set; } = 0;
    public int AwizacjeId { get; set; } = 0;
    public int IloscEtykiet { get; set; } = 0;
    public int MiejsceId { get; set; } = 0;
    public int WdPrzyjecieHeadId { get; set; } = 0;
    public string? ListControlcol { get; set; }
    public string? PaletyOpisWydruk { get; set; }
    
    // NOWA KOLUMNA - dla claim/release dostaw
    public int? RealizujacyPracownikId { get; set; }
    
    // Navigation properties
    public User? RealizujacyPracownik { get; set; }
    public User? Pracownik { get; set; }
    public Location? MiejsceDostawy { get; set; }
    public System? SystemEntity { get; set; }
    public ICollection<ListControlPallet> ListControlPallets { get; set; } = new List<ListControlPallet>();
    public ICollection<Label> Labels { get; set; } = new List<Label>();
    
    // Business logic properties
    public bool IsAssigned => RealizujacyPracownikId.HasValue;
    public bool IsCompleted => false; // TODO: logika kompletności awizacji
    
    /// <summary>
    /// Formatuje identyfikator dostawy jako LK{Id}
    /// </summary>
    public string GetLKCode() => $"LK{Id}";
    
    /// <summary>
    /// Sprawdza czy dostawa może być zajęta przez pracownika
    /// </summary>
    public bool CanBeClaimed() => !IsAssigned;
    
    /// <summary>
    /// Sprawdza czy dostawa może być zwolniona przez danego pracownika
    /// </summary>
    public bool CanBeReleasedBy(int userId) => RealizujacyPracownikId == userId;
}
