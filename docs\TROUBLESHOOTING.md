# Troubleshooting Guide - WMS API

## 🔍 Najczęstsze Problemy i Rozwiązania

### 1. Błąd 404 - "Nie można znaleźć tej strony"

#### Problem
```
https://***********/api/v1/health
HTTP ERROR 404
```

#### Przyczyna
API używa wersji `v1.0`, nie `v1`.

#### ✅ Rozwiązanie
```bash
# ❌ Niepoprawne
https://***********/api/v1/health

# ✅ Poprawne  
https://***********/api/v1.0/health
```

#### Wery<PERSON><PERSON><PERSON><PERSON>
```bash
curl -k https://***********/api/v1.0/health
```

---

### 2. Błąd Certyfikatu - "net::ERR_CERT_AUTHORITY_INVALID"

#### Problem
```
Twoje połączenie nie jest prywatne
net::ERR_CERT_AUTHORITY_INVALID
```

#### <PERSON>rzy<PERSON><PERSON>ż<PERSON>wamy samopodpisanego certyfikatu SSL - to normalne zachowanie.

#### ✅ Rozwiązanie

**W przeglądarce:**
1. Kliknij "Zaawansowane" (Advanced)
2. Kliknij "Przejdź do *********** (niebezpieczne)" (Proceed to *********** unsafe)

**W curl:**
```bash
curl -k https://***********/health
```

**Dla Postman/Insomnia:**
- Wyłącz "SSL certificate verification"

---

### 3. Błąd 503 - "Service Unavailable"

#### Problem
```
503 Service Unavailable
The server is temporarily unable to service your request
```

#### Przyczyna
WMS API nie działa lub nie nasłuchuje na porcie 5000.

#### ✅ Rozwiązanie

**1. Sprawdź status usługi:**
```bash
systemctl status wms-api
```

**2. Jeśli nie działa - uruchom:**
```bash
systemctl start wms-api
```

**3. Sprawdź porty:**
```bash
ss -tlnp | grep :5000
```

**4. Test lokalny:**
```bash
curl http://127.0.0.1:5000/health
```

**5. Jeśli nadal nie działa - restart:**
```bash
systemctl restart wms-api
sleep 3
systemctl status wms-api
```

---

### 4. Błąd PowerShell - "positional parameter"

#### Problem
```powershell
publish-and-deploy.ps1: A positional parameter cannot be found that accepts argument '2>/dev/null'.
```

#### Przyczyna
Niepoprawnie escapowane znaki w skrypcie PowerShell.

#### ✅ Rozwiązanie
Problem został naprawiony w aktualnej wersji skryptu. Jeśli występuje:

```powershell
# Aktualizuj skrypt
git pull origin main

# Lub uruchom ponownie
pwsh -File ./scripts/publish-and-deploy.ps1 dev
```

---

### 5. Błąd .NET Framework

#### Problem
```
It was not possible to find any compatible framework version
The framework 'Microsoft.NETCore.App', version '9.0.0' was not found.
```

#### Przyczyna
Brak .NET 9.0 runtime na serwerze.

#### ✅ Rozwiązanie
```bash
# Zaloguj się jako root
ssh root@***********

# Zainstaluj .NET 9.0 runtime  
apt-get update
apt-get install -y aspnetcore-runtime-9.0

# Weryfikacja
dotnet --list-runtimes
```

---

### 6. Problem z Uprawnieniami

#### Problem
```bash
Permission denied
./Wms.Api: cannot execute binary file
```

#### Przyczyna
Brak uprawnień wykonania lub niepoprawny właściciel pliku.

#### ✅ Rozwiązanie
```bash
# Ustaw właściciela
chown -R wms-dev:users /var/www/wms-backend

# Ustaw uprawnienia
chmod +x /var/www/wms-backend/Wms.Api
chmod 755 /var/www/wms-backend

# Weryfikacja
ls -la /var/www/wms-backend/Wms.Api
```

---

### 7. Aplikacja się Nie Uruchamia

#### Problem
```bash
systemctl status wms-api
● wms-api.service - WMS API Service
   Active: failed (Result: exit-code)
```

#### Diagnostyka
```bash
# Sprawdź szczegółowe logi
journalctl -u wms-api -n 50

# Test ręcznego uruchomienia  
cd /var/www/wms-backend
./Wms.Api --urls http://127.0.0.1:5000
```

#### ✅ Możliwe Rozwiązania

**A. Brak pliku wykonywalnego:**
```bash
ls -la /var/www/wms-backend/Wms.Api
# Jeśli nie ma - ponowne wdrożenie
```

**B. Zła konfiguracja:**
```bash
# Sprawdź plik usługi
cat /etc/systemd/system/wms-api.service
systemctl daemon-reload
```

**C. Konflikt portów:**
```bash
# Sprawdź czy port 5000 jest zajęty
ss -tlnp | grep :5000
```

---

### 8. Apache Błąd Konfiguracji

#### Problem
```bash
apache2ctl configtest
Syntax error on line X of /etc/apache2/sites-enabled/...
```

#### ✅ Rozwiązanie
```bash
# Test konfiguracji
apache2ctl configtest

# Jeśli błąd - sprawdź składnię
nano /etc/apache2/sites-available/000-default.conf
nano /etc/apache2/sites-available/default-ssl.conf

# Po poprawkach
systemctl reload apache2
```

---

### 9. Połączenie SSH Timeout

#### Problem
```bash
ssh: connect to host *********** port 22: Connection timed out
```

#### ✅ Rozwiązanie
```bash
# Sprawdź łączność
ping ***********

# Spróbuj innego portu SSH (jeśli zmieniony)
ssh -p 2222 root@***********

# Sprawdź firewall (jeśli masz dostęp lokalny)
ufw status
```

---

### 10. Problem z białymi literami w aplikacji mobilnej

#### Problem
W formularzu logowania aplikacji MAUI Android wpisywane litery są białe i na białym tle są niewidoczne. Dotyczy to:
- Pola "Card Number" (Entry)
- Selektora "API Environment" (Picker)  
- Labelów opisu

#### Przyczyna
Globalne style w `Styles.xaml` korzystają z `AppThemeBinding` które w kontekście białego Frame'a powodowały nieprawidłowy kontrast kolorów.

#### ✅ Rozwiązanie

**1. Bezpośrednie ustawienie kolorów w LoginPage.xaml:**
```xml
<!-- Entry -->
<Entry TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
       PlaceholderColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}" />

<!-- Picker -->
<Picker TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}"
        TitleColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}" />

<!-- Labels -->
<Label TextColor="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
```

**2. Dedykowane style w Styles.xaml:**
```xml
<!-- Dodano style LoginFormEntry, LoginFormPicker, LoginFormLabel -->
<Style TargetType="Entry" x:Key="LoginFormEntry">
    <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
    <Setter Property="PlaceholderColor" Value="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}" />
</Style>
```

**3. Weryfikacja:**
- Gray900 (#212121) na białym tle - wysoki kontrast WCAG AAA
- Obsługa zarówno light jak i dark theme
- Wszystkie elementy formularza są teraz czytelne

#### Pliki zmienione
- `src/mobile/WmsApp/Views/LoginPage.xaml` - bezpośrednie kolory
- `src/mobile/WmsApp/Resources/Styles/Styles.xaml` - dedykowane style
- `docs/TODO.md` - dokumentacja naprawy
- `docs/TODO_FRONTEND.md` - aktualizacja postępu

---

### 11. Problem z kodem DS - "Nierozpoznany format kodu"

#### Problem
Kod palety typu DS (np. `*********`) jest odrzucany jako "Nierozpoznany format kodu" w aplikacji mobilnej, mimo że powinien być prawidłowy.

#### Przyczyna
Niezgodność między regexem w aplikacji mobilnej a backendem:
- **Backend**: `^DS\\d{4,9}$` (DS + 4-9 cyfr) - poprawny
- **Frontend** (błędnie): `^DS\\d{8}$` (DS + dokładnie 8 cyfr) - za restrykcyjny

Kod `*********` ma 7 cyfr po "DS", więc nie przechodzą walidacji frontend.

#### ✅ Rozwiązanie

**1. Poprawiono regex w aplikacji mobilnej:**
```csharp
// Przed
private readonly Regex _dsPattern = new(@"^DS\\\\d{8}$", RegexOptions.Compiled);

// Po naprawie
private readonly Regex _dsPattern = new(@"^DS\\\\d{4,9}$", RegexOptions.Compiled);
```

**2. Dodano przykładowe palety do mock danych:**
- `DS12345678` (8 cyfr)
- `*********` (7 cyfr) - nowo dodany

**3. Zaktualizowano dokumentację testową.**

#### Weryfikacja
Teraz powinny działać kody DS z 4-9 cyframi:
- ✅ `DS1234` (4 cyfry - minimum)
- ✅ `*********` (7 cyfr)
- ✅ `DS12345678` (8 cyfr)
- ✅ `DS123456789` (9 cyfr - maksimum)

#### Pliki zmienione
- `src/mobile/WmsApp/Services/CodeValidationService.cs` - poprawiony regex
- `src/mobile/WmsApp/Services/MockWmsApiService.cs` - dodana paleta *********
- `src/mobile/WmsApp/Test_Regex.md` - aktualizacja dokumentacji
- `docs/TROUBLESHOOTING.md` - ta sekcja

---

### 13. Backend - DS paleta nie znaleziona mimo istnienia w bazie

#### Problem
Paleta ********* istnieje w bazie danych (`SELECT * FROM etykiety WHERE paleta_id=3345469`), ale API zwraca 404 "Pallet Not Found".

#### Przyczyna
Backend szukał kodów DS w kolumnie `etykieta_klient`, ale w rzeczywistości kody DS są przechowywane w sposób:
- ************* -> numeryczna część **3345469** to `paleta_id`
- Backend musiał wyciągać liczbę z kodu i szukać w `etykiety.paleta_id`

#### ✅ Rozwiązanie

**1. Dodano nową metodę w `ILabelRepository`:**
```csharp
Task<Label?> GetByPalletIdDirectAsync(int palletId);
```

**2. Zmodyfikowano logikę `FindLabelByCodeAsync`:**
```csharp
if (_codeValidationService.ValidateDS(code))
{
    // Wyciągnij numeryczną część z kodu DS (np. ********* -> 3345469)
    var numericPart = code.Substring(2);
    if (int.TryParse(numericPart, out int palletId))
    {
        // Najpierw szukaj po paleta_id
        var label = await _labelRepository.GetByPalletIdDirectAsync(palletId);
        if (label != null) return label;
    }
    
    // Jeśli nie znaleziono po paleta_id, szukaj po etykieta_klient
    return await _labelRepository.GetByClientCodeAsync(code);
}
```

**3. Wdrożono na serwer przez `publish-and-deploy.ps1`**

#### Weryfikacja
```bash
# Test API - teraz działa!
curl -k -X GET https://***********/api/v1.0/pallets/********* \
  -H "Authorization: Bearer $TOKEN"

# Odpowiedź:
{
  "id": 3345469,
  "mainSSCC": null,
  "currentLocation": {
    "code": "MP-002-DSV-001-1",
    "hala": 2, "regal": "DSV", "miejsce": 1, "poziom": "1"
  },
  "labels": [...]
}
```

#### Pliki zmienione
- `src/backend/Wms.Application/Interfaces/ILabelRepository.cs` - nowa metoda
- `src/backend/Wms.Infrastructure/Repositories/LabelRepository.cs` - implementacja
- `src/backend/Wms.Application/Services/PalletService.cs` - nowa logika DS search

---

### 12. Błędy build Android - FileNotFoundException classes.jar

#### Problem
```
XACDJ7028: System.IO.FileNotFoundException: Could not find file 'obj\\Debug\\net9.0-android\\lp\\97\\jl\\classes.jar'.
```

#### Przyczyna
Problem z cache Android build w MAUI - uszkodzone pliki pomśrowe lub niekompatybilne pliki .jar.

#### ✅ Rozwiązanie

**1. Pełne oczyszczenie projektu:**
```bash
cd src/mobile/WmsApp
dotnet clean
rm -rf bin obj  # lub w Windows: rmdir /s bin obj
```

**2. Usuń problematyczne pliki JAR:**
```bash
find . -name "*.jar" -type f -delete
# lub w Windows: forfiles /s /m *.jar /c "cmd /c del @path"
```

**3. Przywracanie i rebuild:**
```bash
dotnet restore
dotnet build -f net9.0-android
```

#### Alternatywne rozwiązania
Jeśli problem się powtarza:

**1. Restart Visual Studio / VS Code**

**2. Wyłącz i włącz Android Emulator**

**3. Aktualizacja Android SDK:**
- Otwórz Visual Studio Installer
- Zainstaluj najnowsze Android SDK

**4. Sprawdzenie zmiennych środowiskowych:**
```bash
echo $ANDROID_HOME
echo $JAVA_HOME
```

#### Profilaktyka
- Regularnie uruchamiaj `dotnet clean` po większych zmianach
- Unikaj przerywania procesów build w trakcie
- Trzymaj Android SDK aktualne

---

### 13. Wysoka Zużycie Pamięci

#### Problem
```bash
free -h
              total        used        free
Mem:           4.0G        3.8G        200M
```

#### Diagnostyka
```bash
# Top procesów pamięciowych
ps aux --sort=-%mem | head -10

# Pamięć WMS API
systemctl show wms-api --property=MemoryCurrent
```

#### ✅ Rozwiązanie
```bash
# Restart WMS API (uwolni pamięć)
systemctl restart wms-api

# Sprawdź limit pamięci w usłudze
grep MemoryLimit /etc/systemd/system/wms-api.service

# Zmień limit jeśli potrzeba (np. z 512M na 1G)
nano /etc/systemd/system/wms-api.service
systemctl daemon-reload
systemctl restart wms-api
```

---

## 🛠️ Narzędzia Diagnostyczne

### Kompleksowe Sprawdzenie Systemu
```bash
#!/bin/bash
echo "=== WMS SYSTEM DIAGNOSTICS ==="
echo "Date: $(date)"
echo ""

echo "=== SYSTEM INFO ==="
uname -a
cat /etc/os-release | grep PRETTY_NAME

echo -e "\n=== RESOURCES ==="
free -h
df -h /var/www
uptime

echo -e "\n=== SERVICES STATUS ==="
systemctl status apache2 --no-pager -l
echo ""
systemctl status wms-api --no-pager -l

echo -e "\n=== NETWORK ==="
ss -tlnp | grep -E ':(80|443|5000)'

echo -e "\n=== WMS API TEST ==="
curl -k -m 5 https://127.0.0.1/health 2>/dev/null || echo "FAILED"

echo -e "\n=== RECENT LOGS ==="
journalctl -u wms-api -n 5 --no-pager
```

### Monitoring w Czasie Rzeczywistym
```bash
# Terminal 1 - Logi WMS
journalctl -u wms-api -f

# Terminal 2 - Logi Apache
tail -f /var/log/apache2/error.log

# Terminal 3 - Zasoby systemowe  
watch -n 2 'free -h && echo "" && ss -tlnp | grep -E ":(80|443|5000)"'
```

### Test Automatyczny
```bash
# Test connectivity (powtarzaj co 30s)
watch -n 30 'curl -k -s -o /dev/null -w "HTTP: %{http_code}, Time: %{time_total}s\n" https://***********/health'
```

---

## 📋 Checklist Rozwiązywania Problemów

### Kiedy API nie odpowiada:
- [ ] `systemctl status wms-api` - sprawdź status usługi
- [ ] `journalctl -u wms-api -n 20` - sprawdź logi  
- [ ] `ss -tlnp | grep :5000` - sprawdź port
- [ ] `curl http://127.0.0.1:5000/health` - test lokalny
- [ ] `systemctl restart wms-api` - restart usługi
- [ ] `systemctl status apache2` - sprawdź Apache
- [ ] `curl -k https://***********/health` - test zewnętrzny

### Kiedy wdrażanie się nie udaje:
- [ ] Sprawdź połączenie SSH
- [ ] Sprawdź uprawnienia katalogów
- [ ] Sprawdź czy .NET 9.0 jest zainstalowany
- [ ] Sprawdź czy poprzednia usługa została zatrzymana
- [ ] Sprawdź czy skrypt ma właściwą składnię

### Przed zgłoszeniem problemu:
- [ ] Zapisz dokładny komunikat błędu
- [ ] Uruchom diagnostykę systemową (skrypt powyżej)
- [ ] Sprawdź logi z ostatnich 30 minut
- [ ] Spróbuj podstawowych rozwiązań (restart)

---

## 📞 Eskalacja Problemów

### Poziom 1 - Podstawowe problemy
- Restart usług
- Sprawdzenie logów  
- Test podstawowych endpointów
- Weryfikacja uprawnień

### Poziom 2 - Problemy konfiguracji
- Analiza konfiguracji Apache/SystemD
- Sprawdzenie certyfikatów SSL
- Diagnostyka sieci
- Przywracanie z backupu

### Poziom 3 - Problemy systemowe
- Analiza zasobów systemowych
- Aktualizacja .NET runtime
- Rekonfiguracja serwera
- Kontakt z administratorem systemu

---

**Ostatnia aktualizacja:** 2025-09-03  
**Wersja:** 1.0  
**Status:** Aktualne dla serwera ***********
