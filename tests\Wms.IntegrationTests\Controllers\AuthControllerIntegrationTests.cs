using FluentAssertions;
using System.Net;
using System.Net.Http.Headers;
using Wms.Application.DTOs.Auth;
using Wms.IntegrationTests.Infrastructure;

namespace Wms.IntegrationTests.Controllers;

public class AuthControllerIntegrationTests : IntegrationTestBase
{
    public AuthControllerIntegrationTests(WmsWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task LoginScan_WithValidCard_ReturnsToken()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "integration-test-device"
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var loginResponse = await DeserializeResponseAsync<LoginScanResponse>(response);
        loginResponse.Should().NotBeNull();
        loginResponse!.Token.Should().NotBeNullOrEmpty();
        loginResponse.User.Should().NotBeNull();
        loginResponse.User.FullName.Should().Be("Test User");
        loginResponse.ExpiresAt.Should().BeAfter(DateTime.UtcNow);
    }

    [Fact]
    public async Task LoginScan_WithInvalidCard_ReturnsUnauthorized()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "9999999999",
            DeviceId = "integration-test-device"
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task LoginScan_WithEmptyCardNumber_ReturnsBadRequest()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "",
            DeviceId = "integration-test-device"
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Logout_WithValidToken_ReturnsOk()
    {
        // Arrange - First login to get a token
        var loginRequest = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "integration-test-device"
        };

        var loginResponse = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(loginRequest));

        loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(loginResponse);
        var token = loginData!.Token;

        // Set authorization header
        SetAuthorizationHeader(token);
        SetDeviceIdHeader("integration-test-device");

        // Act
        var logoutResponse = await Client.PostAsync("/api/v1.0/auth/logout", null);

        // Assert
        logoutResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var logoutData = await DeserializeResponseAsync<object>(logoutResponse);
        logoutData.Should().NotBeNull();
    }

    [Fact]
    public async Task Logout_WithoutToken_ReturnsUnauthorized()
    {
        // Arrange
        ClearHeaders();

        // Act
        var response = await Client.PostAsync("/api/v1.0/auth/logout", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task LoginScan_FlowWithDifferentDevices_WorksCorrectly()
    {
        // Arrange
        var device1 = "device-001";
        var device2 = "device-002";

        var request1 = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = device1
        };

        var request2 = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = device2
        };

        // Act - Login from device 1
        var response1 = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request1));

        // Act - Login from device 2
        var response2 = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request2));

        // Assert
        response1.StatusCode.Should().Be(HttpStatusCode.OK);
        response2.StatusCode.Should().Be(HttpStatusCode.OK);

        var loginData1 = await DeserializeResponseAsync<LoginScanResponse>(response1);
        var loginData2 = await DeserializeResponseAsync<LoginScanResponse>(response2);

        loginData1!.Token.Should().NotBeNullOrEmpty();
        loginData2!.Token.Should().NotBeNullOrEmpty();
        
        // Tokens should be different (if system generates unique tokens per session)
        // This depends on implementation - comment out if tokens are same per user
        // loginData1.Token.Should().NotBe(loginData2.Token);
    }

    [Fact]
    public async Task AuthWorkflow_LoginAndLogoutCycle_WorksEndToEnd()
    {
        // Arrange
        var deviceId = "end-to-end-device";
        var cardNumber = "1234567890";

        // Step 1: Login
        var loginRequest = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = deviceId
        };

        var loginResponse = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(loginRequest));

        loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(loginResponse);
        loginData!.Token.Should().NotBeNullOrEmpty();

        // Step 2: Use token for authenticated request (testing authorization)
        SetAuthorizationHeader(loginData.Token);
        SetDeviceIdHeader(deviceId);

        // Step 3: Logout
        var logoutResponse = await Client.PostAsync("/api/v1.0/auth/logout", null);
        logoutResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 4: Try to logout again (should still work or return appropriate status)
        var secondLogoutResponse = await Client.PostAsync("/api/v1.0/auth/logout", null);
        // This might be OK (idempotent) or Unauthorized - depends on implementation
        secondLogoutResponse.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.Unauthorized);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task LoginScan_WithInvalidCardNumbers_ReturnsBadRequest(string cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber!,
            DeviceId = "test-device"
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task LoginScan_WithMissingDeviceId_StillWorks()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = null // Missing device ID
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(response);
        loginData!.Token.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task LoginScan_ChecksCorrectHeaders()
    {
        // Arrange
        Client.DefaultRequestHeaders.Add("X-Forwarded-For", "*************");

        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "header-test-device"
        };

        // Act
        var response = await Client.PostAsync(
            "/api/v1.0/auth/login-scan", 
            CreateJsonContent(request));

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // The authentication service should receive the IP address from X-Forwarded-For header
        // This is more of an integration test to ensure the header extraction works correctly
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(response);
        loginData!.Token.Should().NotBeNullOrEmpty();
    }
}
