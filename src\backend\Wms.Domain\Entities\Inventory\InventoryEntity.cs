using Wms.Domain.Common;

namespace Wms.Domain.Entities.Inventory;

/// <summary>
/// Encja reprezentująca inwentaryzację
/// Mapowana na tabelę inwentaryzacja
/// </summary>
public class InventoryEntity : BaseEntity
{
    public int Id { get; set; }
    public DateTime? Data { get; set; }
    public string? Opis { get; set; }
    public string? EtykietaId { get; set; }
    public int PaletaId { get; set; } = 0;
    public string? Kod { get; set; }
    public decimal? Ilosc { get; set; }
    public string JednostkaMiary { get; set; } = string.Empty;
    public int? MiejscepId { get; set; }
    public decimal? IloscSpisana { get; set; }
    public string? Pracownik { get; set; }
    public DateTime Timestamp { get; set; }
    public int? InwentaryzacjaId { get; set; }
    public int Active { get; set; } = 1;
    public int? Hala { get; set; }
    public string? Regal { get; set; }
    public int? Miejsce { get; set; }
    public string? Poziom { get; set; }
    public string? Status { get; set; }
    public string? NumerSap { get; set; }
    public string? Podkod { get; set; }
    public string? Skan { get; set; }
    public string? Uwaga { get; set; }
    public string Nadwyzka { get; set; } = "1";
    public int SystemId { get; set; } = 0;
    public int Proba { get; set; } = 1;
    public int? NumerWspolny { get; set; }
    public string? Magazyn { get; set; }

    // Navigation properties
    // EtykietaId to SSCC kod (string), nie foreign key - brak navigation property dla Etykieta
    public virtual Location? MiejsceNavigation { get; set; }
    public virtual User? PracownikNavigation { get; set; }

    // Business logic properties
    public bool IsActive => Active == 1;
    public bool IsCompleted => IloscSpisana.HasValue;
    public decimal? GetDifference() => IloscSpisana - Ilosc;
    public bool HasDifference() => Math.Abs(GetDifference() ?? 0) > 0.001m;
    
    /// <summary>
    /// Sprawdza czy nastąpiła zmiana lokalizacji
    /// </summary>
    public bool HasLocationChanged(int newHala, string newRegal, int newMiejsce, string newPoziom)
    {
        return Hala != newHala || Regal != newRegal || Miejsce != newMiejsce || Poziom != newPoziom;
    }
    
    /// <summary>
    /// Formatuje lokalizację jako string
    /// </summary>
    public string FormatLocation()
    {
        return $"MP-{Hala}-{Regal}-{Miejsce}-{Poziom}";
    }
    
    /// <summary>
    /// Sprawdza czy inwentaryzacja obsługuje palety
    /// </summary>
    public bool SupportsPallets()
    {
        return Status == "19"; // Inwentaryzacja Ogólna
    }
}

/// <summary>
/// Encja reprezentująca sesję inwentaryzacji
/// </summary>
public class InventorySessionEntity : BaseEntity
{
    public int Id { get; set; }
    public int InventoryId { get; set; }
    public int PracownikId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? EndedAt { get; set; }
    public bool IsActive { get; set; } = true;
    public string? CurrentOperacja { get; set; }
    public int SystemId { get; set; } = 1;

    // Navigation properties
    public User? Pracownik { get; set; }
    
    // Business logic properties
    public TimeSpan? Duration => EndedAt?.Subtract(StartedAt);
    public bool IsExpired => StartedAt.AddHours(8) < DateTime.UtcNow; // 8h timeout
}

/// <summary>
/// Encja reprezentująca pozycję inwentaryzacji
/// Alias dla InventoryEntity dla lepszej czytelności
/// </summary>
public class InventoryItemEntity : InventoryEntity
{
    // Dziedziczy wszystkie właściwości z InventoryEntity
    // Ta klasa służy jako alias dla lepszej czytelności kodu
    // gdy pracujemy z pojedynczymi pozycjami inwentaryzacji
}

// LocationChangeEntity removed - conflicted with existing Movement entity on zmianym table

/// <summary>
/// Encja reprezentująca operację inwentaryzacji
/// Mapowana na tabelę operacje
/// </summary>
public class InventoryOperationEntity : BaseEntity
{
    public int Id { get; set; }
    public string? EtykietaId { get; set; }
    public string DocType { get; set; } = "INW";
    public string? DocNr { get; set; }
    public string? ImieNazwisko { get; set; }
    public string TypOperacji { get; set; } = "INW";
    public int SystemId { get; set; }
    public string? Wozek { get; set; }
    public int OperacId { get; set; }
    public string Ilosc { get; set; } = "1";
    public DateTime Ts { get; set; } = DateTime.UtcNow;
}
