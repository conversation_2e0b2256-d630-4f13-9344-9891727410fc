using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WmsApp.Models.Update;
using WmsApp.Services.Contracts;

namespace WmsApp.ViewModels;

public partial class AboutViewModel : ObservableObject
{
    private readonly IUpdateService _updateService;
    private readonly ILogger<AboutViewModel> _logger;

    [ObservableProperty]
    private string appName = "WMS App";

    [ObservableProperty]
    private string currentVersion = string.Empty;

    [ObservableProperty]
    private string buildNumber = string.Empty;

    [ObservableProperty]
    private bool isCheckingUpdate;

    [ObservableProperty]
    private bool isDownloading;

    [ObservableProperty]
    private string updateStatus = string.Empty;

    [ObservableProperty]
    private string downloadProgress = string.Empty;

    [ObservableProperty]
    private bool updateAvailable;

    [ObservableProperty]
    private string newVersion = string.Empty;

    [ObservableProperty]
    private string releaseNotes = string.Empty;

    [ObservableProperty]
    private bool updateRequired;

    private UpdateInfo? _currentUpdateInfo;

    public AboutViewModel(IUpdateService updateService, ILogger<AboutViewModel> logger)
    {
        _updateService = updateService;
        _logger = logger;
        
        LoadAppInfo();
    }

    private void LoadAppInfo()
    {
        CurrentVersion = _updateService.GetCurrentVersion();
        BuildNumber = _updateService.GetCurrentVersionCode().ToString();
        _logger.LogInformation("Załadowano informacje o aplikacji: v{Version} (build {Build})", CurrentVersion, BuildNumber);
    }

    [RelayCommand]
    private async Task CheckForUpdatesAsync()
    {
        if (IsCheckingUpdate) return;

        try
        {
            IsCheckingUpdate = true;
            UpdateStatus = "Sprawdzanie aktualizacji...";
            UpdateAvailable = false;

            _logger.LogInformation("Rozpoczynam sprawdzanie aktualizacji");

            var updateInfo = await _updateService.CheckForUpdatesAsync();
            _currentUpdateInfo = updateInfo;

            if (!string.IsNullOrEmpty(updateInfo.ErrorMessage))
            {
                UpdateStatus = $"Błąd: {updateInfo.ErrorMessage}";
                _logger.LogWarning("Błąd podczas sprawdzania aktualizacji: {Error}", updateInfo.ErrorMessage);
                return;
            }

            if (updateInfo.IsAvailable)
            {
                UpdateAvailable = true;
                NewVersion = updateInfo.NewVersion;
                UpdateRequired = updateInfo.IsRequired;
                ReleaseNotes = updateInfo.Manifest?.ReleaseNotes ?? "Brak informacji o zmianach";
                
                UpdateStatus = UpdateRequired 
                    ? $"Wymagana aktualizacja do wersji {NewVersion}" 
                    : $"Dostępna aktualizacja do wersji {NewVersion}";

                _logger.LogInformation("Dostępna aktualizacja: v{NewVersion} (wymagana: {Required})", 
                    NewVersion, UpdateRequired);
            }
            else
            {
                UpdateStatus = "Aplikacja jest aktualna";
                _logger.LogInformation("Aplikacja jest aktualna");
            }
        }
        catch (Exception ex)
        {
            UpdateStatus = $"Błąd podczas sprawdzania aktualizacji: {ex.Message}";
            _logger.LogError(ex, "Nieoczekiwany błąd podczas sprawdzania aktualizacji");
        }
        finally
        {
            IsCheckingUpdate = false;
        }
    }

    [RelayCommand]
    private async Task DownloadAndInstallAsync()
    {
        if (_currentUpdateInfo?.Manifest == null || IsDownloading) return;

        try
        {
            IsDownloading = true;
            DownloadProgress = "Przygotowanie do pobierania...";

            _logger.LogInformation("Rozpoczynam pobieranie aktualizacji {Version}", _currentUpdateInfo.NewVersion);

            // Progress reporter
            var progress = new Progress<DownloadProgress>(p =>
            {
                DownloadProgress = p.Status switch
                {
                    DownloadStatus.InProgress => $"Pobieranie... {p.PercentageComplete}% ({FormatBytes(p.BytesDownloaded)} / {FormatBytes(p.TotalBytes)})",
                    DownloadStatus.Completed => "Pobieranie ukończone, weryfikacja pliku...",
                    DownloadStatus.Failed => $"Błąd pobierania: {p.ErrorMessage}",
                    _ => "Pobieranie..."
                };
            });

            // Pobierz APK
            var apkPath = await _updateService.DownloadApkAsync(_currentUpdateInfo.Manifest, progress);
            
            DownloadProgress = "Weryfikacja integralności pliku...";
            
            // Weryfikuj integralność
            var isValid = await _updateService.VerifyApkIntegrityAsync(apkPath, _currentUpdateInfo.Manifest.Sha256);
            if (!isValid)
            {
                DownloadProgress = "Błąd: Plik APK jest uszkodzony";
                _logger.LogError("Weryfikacja SHA-256 pliku APK nie powiodła się");
                return;
            }

            DownloadProgress = "Uruchamianie instalacji...";
            
            // Zainstaluj APK
            var installStarted = await _updateService.InstallApkAsync(apkPath);
            if (installStarted)
            {
                DownloadProgress = "Instalacja uruchomiona - postępuj zgodnie z instrukcjami na ekranie";
                _logger.LogInformation("Instalacja APK została uruchomiona");
            }
            else
            {
                DownloadProgress = "Błąd podczas uruchamiania instalacji";
                _logger.LogError("Nie udało się uruchomić instalacji APK");
            }
        }
        catch (Exception ex)
        {
            DownloadProgress = $"Błąd: {ex.Message}";
            _logger.LogError(ex, "Błąd podczas pobierania i instalacji aktualizacji");
        }
        finally
        {
            IsDownloading = false;
        }
    }

    private static string FormatBytes(long bytes)
    {
        return bytes switch
        {
            < 1024 => $"{bytes} B",
            < 1024 * 1024 => $"{bytes / 1024:F1} KB",
            < 1024 * 1024 * 1024 => $"{bytes / (1024 * 1024):F1} MB",
            _ => $"{bytes / (1024 * 1024 * 1024):F2} GB"
        };
    }
}
