# Auto-aktualizacja APK - Dokumentacja

**Wersja**: 1.0  
**Data**: 2025-01-09  
**Autor**: WMS System  

## 📖 Przegląd

System auto-aktualizacji umożliwia automatyczne sprawdzanie, pobieranie i instalowanie nowych wersji aplikacji WMS bez konieczności ręcznej dystrybucji. Funkcjonalność działa w oparciu o manifest JSON hostowany na serwerze i bezpieczne pobieranie plików APK.

## 🏗️ Architektura

### Komponenty systemu

```
┌─────────────────┐    HTTPS    ┌──────────────────┐
│   Aplikacja     │◄────────────┤     Serwer       │
│   MAUI          │             │   (Apache)       │
└─────────────────┘             └──────────────────┘
         │                              │
         ▼                              ▼
┌─────────────────┐             ┌──────────────────┐
│  UpdateService  │             │   app.json       │
│                 │             │   app.apk        │
│ • CheckUpdates  │             │                  │
│ • DownloadAPK   │             │                  │
│ • VerifySHA256  │             │                  │
│ • InstallAPK    │             │                  │
└─────────────────┘             └──────────────────┘
```

### Przepływ aktualizacji

```mermaid
sequenceDiagram
    participant App as Aplikacja
    participant US as UpdateService
    participant Server as Serwer
    participant System as System Android

    App->>US: Sprawdź aktualizacje
    US->>Server: GET /wms_android_update/app.json
    Server-->>US: Manifest JSON
    US->>US: Porównaj wersje
    
    alt Aktualizacja dostępna
        US-->>App: UpdateInfo (available=true)
        App->>App: Pokaż dialog/UI
        
        alt Użytkownik akceptuje
            App->>US: Pobierz APK
            US->>Server: GET /wms_android_update/app.apk
            Server-->>US: Plik APK (stream)
            US->>US: Zapisz + weryfikuj SHA-256
            US->>System: Uruchom instalator
            System->>System: Instaluj APK
        end
    else Brak aktualizacji
        US-->>App: UpdateInfo (available=false)
    end
```

## 🔧 Implementacja

### 1. Modele danych

#### AppManifest
```csharp
public record AppManifest
{
    [JsonPropertyName("version")]
    public string Version { get; init; } = string.Empty; // "1.0.5"
    
    [JsonPropertyName("versionCode")]
    public int VersionCode { get; init; }                // 105
    
    [JsonPropertyName("downloadUrl")]
    public string DownloadUrl { get; init; } = string.Empty; // URL do APK
    
    [JsonPropertyName("sha256")]
    public string Sha256 { get; init; } = string.Empty;  // Hash weryfikacyjny
    
    [JsonPropertyName("fileSize")]
    public long FileSize { get; init; }                  // Rozmiar w bajtach
    
    [JsonPropertyName("releaseNotes")]
    public string ReleaseNotes { get; init; } = string.Empty; // Zmiany
    
    [JsonPropertyName("required")]
    public bool Required { get; init; }                  // Wymagana aktualizacja
    
    [JsonPropertyName("minSdkVersion")]
    public int MinSdkVersion { get; init; } = 29;       // Min Android API
    
    [JsonPropertyName("releaseDate")]
    public string ReleaseDate { get; init; } = string.Empty; // ISO 8601
}
```

#### UpdateInfo
```csharp
public record UpdateInfo
{
    public bool IsAvailable { get; init; }              // Czy dostępna
    public string CurrentVersion { get; init; }         // Obecna wersja
    public string NewVersion { get; init; }             // Nowa wersja
    public bool IsRequired { get; init; }               // Wymagana?
    public AppManifest? Manifest { get; init; }         // Szczegóły
    public string? ErrorMessage { get; init; }          // Błędy
}
```

#### DownloadProgress
```csharp
public record DownloadProgress
{
    public long BytesDownloaded { get; init; }          // Pobrane bajty
    public long TotalBytes { get; init; }               // Całkowity rozmiar
    public int PercentageComplete { get; }              // Procent (0-100)
    public DownloadStatus Status { get; init; }         // Status pobierania
    public string? ErrorMessage { get; init; }          // Błąd
}
```

### 2. UpdateService

#### Główne metody

```csharp
public interface IUpdateService
{
    // Sprawdza dostępność aktualizacji
    Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default);
    
    // Pobiera plik APK z serwerem
    Task<string> DownloadApkAsync(AppManifest manifest, 
        IProgress<DownloadProgress>? progress = null, 
        CancellationToken cancellationToken = default);
    
    // Weryfikuje integralność pliku
    Task<bool> VerifyApkIntegrityAsync(string filePath, string expectedSha256);
    
    // Uruchamia instalację APK
    Task<bool> InstallApkAsync(string apkFilePath);
    
    // Pobiera informacje o obecnej wersji
    string GetCurrentVersion();
    int GetCurrentVersionCode();
    
    // Sprawdza uprawnienia do instalacji
    bool CanInstallFromUnknownSources();
    Task<bool> RequestInstallPermissionAsync();
}
```

#### Przykład użycia

```csharp
// Sprawdzenie aktualizacji
var updateInfo = await _updateService.CheckForUpdatesAsync();

if (updateInfo.IsAvailable)
{
    // Progress reporting
    var progress = new Progress<DownloadProgress>(p => 
    {
        Console.WriteLine($"Pobieranie: {p.PercentageComplete}%");
    });
    
    // Pobieranie APK
    var apkPath = await _updateService.DownloadApkAsync(
        updateInfo.Manifest, progress);
    
    // Weryfikacja
    var isValid = await _updateService.VerifyApkIntegrityAsync(
        apkPath, updateInfo.Manifest.Sha256);
    
    if (isValid)
    {
        // Instalacja
        await _updateService.InstallApkAsync(apkPath);
    }
}
```

### 3. UI Components

#### AboutPage
Strona zawierająca:
- Informacje o obecnej wersji
- Przycisk sprawdzania aktualizacji
- Progress bar pobierania
- Informacje o nowej wersji

#### Automatyczne sprawdzanie
```csharp
// W AppShell.xaml.cs - sprawdzanie przy starcie
_ = Task.Run(async () =>
{
    await Task.Delay(3000); // Opóźnienie 3 sekundy
    await CheckForUpdatesOnStartupAsync();
});
```

## 🔐 Bezpieczeństwo

### 1. HTTPS
- Wszystkie połączenia używają HTTPS
- Weryfikacja certyfikatów SSL

### 2. Integralność plików
```csharp
// Weryfikacja SHA-256
using var sha256 = SHA256.Create();
using var fileStream = File.OpenRead(filePath);
var hashBytes = await sha256.ComputeHashAsync(fileStream);
var actualSha256 = Convert.ToHexString(hashBytes).ToLowerInvariant();

return string.Equals(actualSha256, expectedSha256, StringComparison.OrdinalIgnoreCase);
```

### 3. FileProvider (Android N+)
```xml
<!-- AndroidManifest.xml -->
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_provider_paths" />
</provider>
```

### 4. Uprawnienia Android
```xml
<!-- Uprawnienia wymagane -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
                 android:maxSdkVersion="28" />
```

## 📋 Konfiguracja serwera

### 1. Struktura katalogów
```
/var/www/wms_android_update/
├── app.json          # Manifest aplikacji
├── app.apk           # Najnowsza wersja APK
└── versions/         # Archiwum wersji (opcjonalne)
    ├── v1.0.4/
    └── v1.0.5/
```

### 2. Przykład app.json
```json
{
  "version": "1.0.5",
  "versionCode": 105,
  "downloadUrl": "https://***********/wms_android_update/app.apk",
  "sha256": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890",
  "fileSize": 15728640,
  "releaseNotes": "• Dodano funkcję auto-aktualizacji\n• Poprawiono wydajność skanowania\n• Naprawiono błąd w logowaniu\n• Dodano stronę 'O aplikacji'",
  "required": false,
  "minSdkVersion": 29,
  "releaseDate": "2025-01-09T10:00:00Z"
}
```

### 3. Konfiguracja Apache
```apache
<VirtualHost *:443>
    ServerName ***********
    DocumentRoot /var/www
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/cert.pem
    SSLCertificateKeyFile /path/to/key.pem
    
    # WMS Android Update endpoint
    Alias /wms_android_update /var/www/wms_android_update
    <Directory "/var/www/wms_android_update">
        Options Indexes FollowSymLinks
        AllowOverride None
        Require all granted
        
        # MIME types
        AddType application/json .json
        AddType application/vnd.android.package-archive .apk
        
        # Headers for APK downloads
        <Files "*.apk">
            Header set Content-Disposition "attachment"
            Header set Cache-Control "no-cache"
        </Files>
    </Directory>
</VirtualHost>
```

## 🧪 Testowanie

### 1. Testowanie lokalne
```csharp
// Mock UpdateService dla testów
public class MockUpdateService : IUpdateService
{
    public async Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(1000); // Symulacja opóźnienia sieci
        
        return new UpdateInfo
        {
            IsAvailable = true,
            CurrentVersion = "1.0.4",
            NewVersion = "1.0.5",
            IsRequired = false,
            Manifest = new AppManifest
            {
                Version = "1.0.5",
                VersionCode = 105,
                ReleaseNotes = "Test update"
                // ... inne właściwości
            }
        };
    }
}
```

### 2. Przypadki testowe

#### Test 1: Sprawdzanie aktualizacji
```csharp
[Test]
public async Task CheckForUpdates_WhenNewerVersionAvailable_ReturnsUpdateInfo()
{
    // Arrange
    var updateService = new UpdateService(httpClient, logger);
    
    // Act
    var result = await updateService.CheckForUpdatesAsync();
    
    // Assert
    Assert.IsTrue(result.IsAvailable);
    Assert.AreEqual("1.0.5", result.NewVersion);
}
```

#### Test 2: Weryfikacja SHA-256
```csharp
[Test]
public async Task VerifyApkIntegrity_WithCorrectHash_ReturnsTrue()
{
    // Arrange
    var testFile = CreateTestApkFile();
    var expectedHash = CalculateSHA256(testFile);
    
    // Act
    var result = await updateService.VerifyApkIntegrityAsync(testFile, expectedHash);
    
    // Assert
    Assert.IsTrue(result);
}
```

### 3. Testowanie na urządzeniu

#### Przygotowanie
1. **Wygeneruj test APK** z wyższym versionCode
2. **Oblicz SHA-256** hash pliku APK
3. **Zaktualizuj app.json** na serwerze
4. **Uruchom aplikację** na urządzeniu testowym

#### Scenariusze testowe
- ✅ Aktualizacja dostępna (opcjonalna)
- ✅ Aktualizacja wymagana (blokująca)
- ✅ Brak aktualizacji
- ✅ Błąd sieci
- ✅ Niepoprawny hash SHA-256
- ✅ Brak uprawnień do instalacji

## 🐛 Rozwiązywanie problemów

### 1. Błędy sieci
```csharp
// Diagnostyka połączenia
try 
{
    var response = await httpClient.GetAsync(ManifestUrl);
    response.EnsureSuccessStatusCode();
}
catch (HttpRequestException ex)
{
    logger.LogError(ex, "Błąd połączenia: {Message}", ex.Message);
    // Sprawdź:
    // - Czy serwer jest dostępny
    // - Czy certyfikat SSL jest ważny
    // - Czy firewall nie blokuje połączenia
}
```

### 2. Błędy instalacji APK
```csharp
// Sprawdzenie uprawnień
if (!updateService.CanInstallFromUnknownSources())
{
    var granted = await updateService.RequestInstallPermissionAsync();
    if (!granted)
    {
        // Użytkownik musi ręcznie nadać uprawnienia w Ustawieniach
        // Settings > Apps > Special app access > Install unknown apps
    }
}
```

### 3. Błędy weryfikacji
- **Niepoprawny hash**: Plik został uszkodzony podczas pobierania
- **Brak pliku**: Sprawdź ścieżkę zapisu i uprawnienia
- **Hash nie pasuje**: Sprawdź czy SHA-256 w manifeście jest poprawny

## 📊 Logowanie i monitoring

### 1. Poziomy logów
```csharp
// Information - normalne operacje
logger.LogInformation("Sprawdzanie aktualizacji z {ManifestUrl}", ManifestUrl);

// Warning - błędy nie krytyczne
logger.LogWarning("Błąd podczas sprawdzania aktualizacji: {Error}", error);

// Error - błędy krytyczne
logger.LogError(ex, "Nieoczekiwany błąd podczas pobierania APK");
```

### 2. Metryki do monitorowania
- Częstotliwość sprawdzania aktualizacji
- Czas pobierania APK
- Wskaźnik powodzenia instalacji
- Najpopularniejsze wersje aplikacji

## 🔄 Proces wdrażania nowej wersji

### 1. Przygotowanie releasu
```bash
# 1. Build APK
dotnet build -c Release -f net9.0-android

# 2. Podpisanie APK (jeśli wymagane)
jarsigner -keystore mykey.keystore app-release.apk myalias

# 3. Obliczenie hash
sha256sum app.apk
```

### 2. Aktualizacja serwera
```bash
# 1. Backup poprzedniej wersji
cp /var/www/wms/app.apk /var/www/wms/versions/v1.0.4/

# 2. Upload nowego APK
scp app.apk server:/var/www/wms/app.apk

# 3. Aktualizacja manifestu
cat > /var/www/wms/app.json << EOF
{
  "version": "1.0.5",
  "versionCode": 105,
  // ... reszta konfiguracji
}
EOF
```

### 3. Weryfikacja
```bash
# Sprawdź dostępność
curl -I https://***********/wms/app.json
curl -I https://***********/wms/app.apk

# Sprawdź hash
sha256sum /var/www/wms/app.apk
```

## 📖 Bibliografia

- [Android Package Installation](https://developer.android.com/guide/topics/manifest/uses-permission-element#install-packages)
- [FileProvider Documentation](https://developer.android.com/reference/androidx/core/content/FileProvider)
- [MAUI App Lifecycle](https://docs.microsoft.com/en-us/dotnet/maui/fundamentals/app-lifecycle)
- [HttpClient Best Practices](https://docs.microsoft.com/en-us/dotnet/fundamentals/networking/http/httpclient-guidelines)

---

**© 2025 WMS System - Auto-Update Documentation v1.0**
