using FluentValidation;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;

namespace Wms.Application.Validators;

public class MovePalletRequestValidator : AbstractValidator<MovePalletRequest>
{
    public MovePalletRequestValidator(ICodeValidationService codeValidationService)
    {
        RuleFor(x => x.PalletCode)
            .NotEmpty()
            .WithMessage("Pallet code is required")
            .Must(code => codeValidationService.ValidateSSCC(code) || codeValidationService.ValidateDS(code))
            .WithMessage("Pallet code must be a valid SSCC (18 digits) or DS code (DS followed by 4-9 digits)");

        RuleFor(x => x.ToLocationCode)
            .NotEmpty()
            .WithMessage("Target location code is required")
            .Must(codeValidationService.ValidateLocationCode)
            .WithMessage("Location code must be in format MP-H-R-M-P (e.g., MP-1-A-1-1)");

        RuleFor(x => x.Notes)
            .MaximumLength(500)
            .WithMessage("Notes cannot exceed 500 characters");
    }
}
