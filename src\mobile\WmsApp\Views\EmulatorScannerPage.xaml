<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.EmulatorScannerPage"
             x:Name="EmulatorScannerPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:EmulatorScannerViewModel"
             Title="{loc:Translate Key=EmulatorScanner_Title}">

    <ScrollView Padding="16">
        <VerticalStackLayout Spacing="20">
            
            <!-- Device Info -->
            <Frame BackgroundColor="LightBlue" Padding="12">
                <StackLayout>
<Label Text="{loc:Translate Key=EmulatorScanner_DeviceInfo}" 
                           FontAttributes="Bold" />
                    <Label Text="{Binding DeviceInfo}" 
                           FontSize="12" />
                </StackLayout>
            </Frame>

            <!-- Manual Scan Input -->
            <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=EmulatorScanner_ManualInput_Label}" 
                       FontAttributes="Bold" />
                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                    <Entry x:Name="ScanInputEntry"
                           Text="{Binding ScanInput}"
                           Placeholder="Wprowadź kod lub wygeneruj przykładowy"
                           Grid.Column="0" />
<Button Text="{loc:Translate Key=EmulatorScanner_Scan_Button}"
                            Command="{Binding SimulateScanCommand}"
                            Grid.Column="1" />
                </Grid>
            </StackLayout>

            <!-- Test Code Generators -->
            <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=EmulatorScanner_Generate_Header}" 
                       FontAttributes="Bold" />
                
                <Grid RowDefinitions="Auto,Auto,Auto" 
                      ColumnDefinitions="*,*" 
                      RowSpacing="10" 
                      ColumnSpacing="10">
                    
<Button Text="{loc:Translate Key=EmulatorScanner_Generate_GS1}"
                            Command="{Binding GenerateTestCodeCommand}"
                            CommandParameter="GS1"
                            BackgroundColor="Green"
                            TextColor="White"
                            Grid.Row="0" Grid.Column="0" />
                    
<Button Text="{loc:Translate Key=EmulatorScanner_Generate_SSCC}"
                            Command="{Binding GenerateTestCodeCommand}"
                            CommandParameter="SSCC"
                            BackgroundColor="Blue"
                            TextColor="White"
                            Grid.Row="0" Grid.Column="1" />
                    
<Button Text="{loc:Translate Key=EmulatorScanner_Generate_DS}"
                            Command="{Binding GenerateTestCodeCommand}"
                            CommandParameter="DS"
                            BackgroundColor="Orange"
                            TextColor="White"
                            Grid.Row="1" Grid.Column="0" />
                    
<Button Text="{loc:Translate Key=EmulatorScanner_Generate_LK}"
                            Command="{Binding GenerateTestCodeCommand}"
                            CommandParameter="LK"
                            BackgroundColor="Purple"
                            TextColor="White"
                            Grid.Row="1" Grid.Column="1" />
                    
<Button Text="{loc:Translate Key=EmulatorScanner_Generate_PrinterIP}"
                            Command="{Binding GenerateTestCodeCommand}"
                            CommandParameter="PrinterIP"
                            BackgroundColor="Brown"
                            TextColor="White"
                            Grid.Row="2" Grid.Column="0" />
                    
<Button Text="{loc:Translate Key=Common_Clear}"
                            Command="{Binding ClearCommand}"
                            BackgroundColor="Gray"
                            TextColor="White"
                            Grid.Row="2" Grid.Column="1" />
                </Grid>
            </StackLayout>

            <!-- Recent Scans History -->
            <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=EmulatorScanner_History_Header}" 
                       FontAttributes="Bold" />
                
                <CollectionView ItemsSource="{Binding RecentScans}"
                                MaximumHeightRequest="200">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="x:String">
                            <Grid Padding="8" 
                                  ColumnDefinitions="*,Auto">
                                <Label Text="{Binding}"
                                       FontFamily="Courier"
                                       FontSize="12"
                                       Grid.Column="0" />
<Label Text="{Binding Source={x:Reference EmulatorScannerPageRoot}, Path=VM.LastScanTime, StringFormat='{0:HH:mm:ss}'}"
                                       FontSize="10"
                                       TextColor="Gray"
                                       Grid.Column="1" />
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
<Button Text="{loc:Translate Key=EmulatorScanner_ClearHistory_Button}"
                        Command="{Binding ClearHistoryCommand}"
                        BackgroundColor="Red"
                        TextColor="White" />
            </StackLayout>

            <!-- Instructions -->
            <Frame BackgroundColor="LightYellow" Padding="12">
                <StackLayout Spacing="5">
<Label Text="{loc:Translate Key=EmulatorScanner_Instructions_Header}" 
                           FontAttributes="Bold" />
<Label Text="{loc:Translate Key=EmulatorScanner_Instructions_1}" 
                           FontSize="12" />
<Label Text="{loc:Translate Key=EmulatorScanner_Instructions_2}" 
                           FontSize="12" />
<Label Text="{loc:Translate Key=EmulatorScanner_Instructions_3}" 
                           FontSize="12" />
<Label Text="{loc:Translate Key=EmulatorScanner_Instructions_4}" 
                           FontSize="12" />
                </StackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
