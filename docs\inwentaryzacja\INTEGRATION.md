# Integracja z DataWedge i Skanerami - Moduł Inwentaryzacji

## Przegląd

Dokument opisuje szczegółową integrację modułu inwentaryzacji z systemem DataWedge na urządzeniach Zebra MC3300, konfigurację skanerów oraz implementację w aplikacji MAUI Android.

## DataWedge - Konfiguracja

### Przegląd DataWedge

DataWedge to preinstalowana aplikacja na urządzeniach Zebra, która zarządza skanowaniem kodów kreskowych i przekazuje dane do aplikacji użytkownika poprzez Intent Android.

### Tworzenie Profilu DataWedge

#### 1. Uruchomienie i Konfiguracja Podstawowa

```xml
<!-- Profil: WmsInventory -->
<profile name="WmsInventory" version="1.0">
    <associated_apps>
        <app package="com.wms.app" activity="*"/>
    </associated_apps>
    <barcode_input enabled="true">
        <scanner_selection>auto</scanner_selection>
        <scanner_input_enabled>true</scanner_input_enabled>
        <trigger_mode>hard_trigger</trigger_mode>
    </barcode_input>
</profile>
```

#### 2. Ustawienia Dekoderów

```xml
<!-- Dekodery dla inwentaryzacji -->
<decoders>
    <!-- Kody produktów EAN -->
    <decoder name="ean13" enabled="true"/>
    <decoder name="ean8" enabled="true"/>
    <decoder name="upca" enabled="true"/>
    <decoder name="upce" enabled="true"/>

    <!-- Kody systemowe -->
    <decoder name="code128" enabled="true"/>
    <decoder name="code39" enabled="true"/>
    <decoder name="code93" enabled="true"/>

    <!-- GS1 DataBar -->
    <decoder name="gs1_databar" enabled="true"/>
    <decoder name="gs1_databar_expanded" enabled="true"/>

    <!-- 2D codes -->
    <decoder name="datamatrix" enabled="true"/>
    <decoder name="qr_code" enabled="true"/>
    <decoder name="pdf417" enabled="true"/>

    <!-- Specific settings -->
    <decoder name="code128">
        <length1>4</length1>
        <length2>55</length2>
        <check_digit>no</check_digit>
        <security_level>1</security_level>
    </decoder>
</decoders>
```

#### 3. Konfiguracja Intent Output

```xml
<intent_output enabled="true">
    <intent_action>com.wms.INVENTORY_SCAN</intent_action>
    <intent_category>android.intent.category.DEFAULT</intent_category>
    <intent_delivery>broadcast</intent_delivery>
    <intent_extras>
        <extra name="com.symbol.datawedge.data_string" value="data"/>
        <extra name="com.symbol.datawedge.label_type" value="label_type"/>
        <extra name="com.symbol.datawedge.decode_data" value="decode_data"/>
        <extra name="com.symbol.datawedge.scanner_identifier" value="scanner_identifier"/>
        <extra name="com.symbol.datawedge.source" value="source"/>
    </intent_extras>
</intent_output>
```

#### 4. Ustawienia Formatowania Danych

```xml
<data_processing>
    <data_prefix></data_prefix>
    <data_suffix></data_suffix>
    <send_data>true</send_data>
    <send_hex_data>false</send_hex_data>
    <send_tab>false</send_tab>
    <send_enter>false</send_enter>
</data_processing>
```

### Konfiguracja Programowa DataWedge

#### Wysyłanie Komend do DataWedge

```csharp
public class DataWedgeService : IDataWedgeService
{
    private const string DATAWEDGE_ACTION = "com.symbol.datawedge.api.ACTION";
    private const string EXTRA_CREATE_PROFILE = "com.symbol.datawedge.api.CREATE_PROFILE";
    private const string EXTRA_SET_CONFIG = "com.symbol.datawedge.api.SET_CONFIG";

    public void CreateInventoryProfile()
    {
        // Tworzenie profilu
        var createProfileIntent = new Intent();
        createProfileIntent.SetAction(DATAWEDGE_ACTION);
        createProfileIntent.PutExtra(EXTRA_CREATE_PROFILE, "WmsInventory");
        Android.App.Application.Context.SendBroadcast(createProfileIntent);

        // Konfiguracja profilu
        ConfigureProfile();
    }

    private void ConfigureProfile()
    {
        var profileConfig = new Bundle();
        profileConfig.PutString("PROFILE_NAME", "WmsInventory");
        profileConfig.PutString("PROFILE_ENABLED", "true");
        profileConfig.PutString("CONFIG_MODE", "UPDATE");

        // Konfiguracja aplikacji
        var appConfig = new Bundle();
        appConfig.PutString("PACKAGE_NAME", "com.wms.app");
        appConfig.PutStringArray("ACTIVITY_LIST", new[] { "*" });
        profileConfig.PutParcelable("APP_LIST", appConfig);

        // Konfiguracja skanera
        var barcodeConfig = new Bundle();
        barcodeConfig.PutString("PLUGIN_NAME", "BARCODE");
        barcodeConfig.PutString("RESET_CONFIG", "true");

        var barcodeProps = new Bundle();
        barcodeProps.PutString("scanner_selection", "auto");
        barcodeProps.PutString("scanner_input_enabled", "true");
        barcodeProps.PutString("trigger_mode", "1"); // Hard trigger

        barcodeConfig.PutBundle("PARAM_LIST", barcodeProps);
        profileConfig.PutParcelable("PLUGIN_CONFIG", barcodeConfig);

        // Konfiguracja Intent output
        var intentConfig = new Bundle();
        intentConfig.PutString("PLUGIN_NAME", "INTENT");
        intentConfig.PutString("RESET_CONFIG", "true");

        var intentProps = new Bundle();
        intentProps.PutString("intent_output_enabled", "true");
        intentProps.PutString("intent_action", "com.wms.INVENTORY_SCAN");
        intentProps.PutString("intent_delivery", "2"); // Broadcast

        intentConfig.PutBundle("PARAM_LIST", intentProps);
        profileConfig.PutParcelable("PLUGIN_CONFIG", intentConfig);

        // Wysłanie konfiguracji
        var configIntent = new Intent();
        configIntent.SetAction(DATAWEDGE_ACTION);
        configIntent.PutExtra(EXTRA_SET_CONFIG, profileConfig);
        Android.App.Application.Context.SendBroadcast(configIntent);
    }
}
```

## Implementacja BroadcastReceiver w MAUI

### Receiver dla Skanów Inwentaryzacji

```csharp
[BroadcastReceiver(Enabled = true, Exported = true)]
[IntentFilter(new[] { "com.wms.INVENTORY_SCAN" })]
public class InventoryDataWedgeReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        try
        {
            if (intent.Action == "com.wms.INVENTORY_SCAN")
            {
                var scanData = ExtractScanData(intent);
                ProcessInventoryScan(scanData);
            }
        }
        catch (Exception ex)
        {
            Log.Error("InventoryDataWedge", $"Error processing scan: {ex.Message}");
            NotifyError(ex.Message);
        }
    }

    private ScanData ExtractScanData(Intent intent)
    {
        var data = intent.GetStringExtra("com.symbol.datawedge.data_string");
        var labelType = intent.GetStringExtra("com.symbol.datawedge.label_type");
        var source = intent.GetStringExtra("com.symbol.datawedge.source");
        var decodedData = intent.GetByteArrayExtra("com.symbol.datawedge.decode_data");

        return new ScanData
        {
            Data = data ?? string.Empty,
            LabelType = labelType ?? "UNKNOWN",
            Source = source ?? "SCANNER",
            DecodedData = decodedData,
            Timestamp = DateTime.Now,
            IsValid = !string.IsNullOrEmpty(data)
        };
    }

    private void ProcessInventoryScan(ScanData scanData)
    {
        // Walidacja podstawowa
        if (!scanData.IsValid)
        {
            NotifyError("Pusty kod skanowania");
            return;
        }

        // Identyfikacja typu kodu
        var codeType = IdentifyCodeType(scanData.Data);

        var inventoryScan = new InventoryScanResult
        {
            OriginalData = scanData.Data,
            CodeType = codeType,
            LabelType = scanData.LabelType,
            Source = scanData.Source,
            Timestamp = scanData.Timestamp,
            ProcessingStatus = ProcessingStatus.Pending
        };

        // Wysłanie do ViewModelu
        WeakReferenceMessenger.Default.Send(new InventoryScanMessage(inventoryScan));

        // Sygnał dźwiękowy sukcesu
        PlaySuccessSound();
    }

    private CodeType IdentifyCodeType(string data)
    {
        if (string.IsNullOrEmpty(data))
            return CodeType.Unknown;

        // SSCC (18 cyfr)
        if (Regex.IsMatch(data, @"^\d{18}$"))
            return CodeType.SSCC;

        // DS Code (DS + 4-9 cyfr)
        if (Regex.IsMatch(data, @"^DS\d{4,9}$"))
            return CodeType.DSCode;

        // Lokalizacja (MP-H-R-M-P)
        if (Regex.IsMatch(data, @"^MP-\d+-\d+-\d+-\d+$"))
            return CodeType.Location;

        // EAN-13
        if (Regex.IsMatch(data, @"^\d{13}$"))
            return CodeType.EAN13;

        // EAN-8
        if (Regex.IsMatch(data, @"^\d{8}$"))
            return CodeType.EAN8;

        // GS1-128 (z prefiksem IZ dla dostaw)
        if (data.StartsWith("IZ") && data.Contains("("))
            return CodeType.GS1_128_IZ;

        // GS1-128 standardowy
        if (data.Contains("(") && Regex.IsMatch(data, @"\(\d+\)"))
            return CodeType.GS1_128;

        return CodeType.ProductCode;
    }

    private void PlaySuccessSound()
    {
        try
        {
            // Dźwięk systemowy sukcesu
            var audioManager = Android.App.Application.Context
                .GetSystemService(Context.AudioService) as AudioManager;

            if (audioManager != null)
            {
                audioManager.PlaySoundEffect(SoundEffect.KeyClick);
            }

            // Wibracja
            var vibrator = Android.App.Application.Context
                .GetSystemService(Context.VibratorService) as Vibrator;

            if (vibrator != null && vibrator.HasVibrator)
            {
                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    vibrator.Vibrate(VibrationEffect.CreateOneShot(100, VibrationEffect.DefaultAmplitude));
                }
                else
                {
                    vibrator.Vibrate(100);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Warning("InventoryDataWedge", $"Could not play success sound: {ex.Message}");
        }
    }

    private void NotifyError(string message)
    {
        WeakReferenceMessenger.Default.Send(new InventoryErrorMessage(message));
        PlayErrorSound();
    }

    private void PlayErrorSound()
    {
        try
        {
            // Dwukrotny beep dla błędu
            var audioManager = Android.App.Application.Context
                .GetSystemService(Context.AudioService) as AudioManager;

            if (audioManager != null)
            {
                audioManager.PlaySoundEffect(SoundEffect.KeyClick);
                Task.Delay(200).ContinueWith(t =>
                    audioManager.PlaySoundEffect(SoundEffect.KeyClick));
            }

            // Dłuższa wibracja dla błędu
            var vibrator = Android.App.Application.Context
                .GetSystemService(Context.VibratorService) as Vibrator;

            if (vibrator != null && vibrator.HasVibrator)
            {
                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    vibrator.Vibrate(VibrationEffect.CreateOneShot(300, VibrationEffect.DefaultAmplitude));
                }
                else
                {
                    vibrator.Vibrate(300);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Warning("InventoryDataWedge", $"Could not play error sound: {ex.Message}");
        }
    }
}
```

### Modele Danych dla Skanowania

```csharp
public record ScanData
{
    public string Data { get; set; } = string.Empty;
    public string LabelType { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public byte[]? DecodedData { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsValid { get; set; }
}

public record InventoryScanResult
{
    public string OriginalData { get; set; } = string.Empty;
    public CodeType CodeType { get; set; }
    public string LabelType { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public ProcessingStatus ProcessingStatus { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public enum CodeType
{
    Unknown,
    SSCC,
    DSCode,
    Location,
    EAN13,
    EAN8,
    EAN128,
    GS1_128,
    GS1_128_IZ,
    ProductCode,
    QRCode,
    DataMatrix
}

public enum ProcessingStatus
{
    Pending,
    Processing,
    Completed,
    Error
}
```

### Messages dla MVVM

```csharp
public record InventoryScanMessage(InventoryScanResult ScanResult);
public record InventoryErrorMessage(string ErrorMessage);

public class InventoryProductViewModel : ObservableObject
{
    public InventoryProductViewModel()
    {
        WeakReferenceMessenger.Default.Register<InventoryScanMessage>(this, OnScanReceived);
        WeakReferenceMessenger.Default.Register<InventoryErrorMessage>(this, OnErrorReceived);
    }

    private async void OnScanReceived(object recipient, InventoryScanMessage message)
    {
        await MainThread.InvokeOnMainThreadAsync(async () =>
        {
            await ProcessScanAsync(message.ScanResult);
        });
    }

    private async void OnErrorReceived(object recipient, InventoryErrorMessage message)
    {
        await MainThread.InvokeOnMainThreadAsync(() =>
        {
            ShowError(message.ErrorMessage);
        });
    }

    private async Task ProcessScanAsync(InventoryScanResult scanResult)
    {
        try
        {
            IsProcessingScan = true;

            switch (scanResult.CodeType)
            {
                case CodeType.SSCC:
                    await ProcessSSCCScanAsync(scanResult.OriginalData);
                    break;

                case CodeType.DSCode:
                    await ProcessDSCodeScanAsync(scanResult.OriginalData);
                    break;

                case CodeType.Location:
                    await ProcessLocationScanAsync(scanResult.OriginalData);
                    break;

                case CodeType.EAN13:
                case CodeType.EAN8:
                case CodeType.ProductCode:
                    await ProcessProductCodeScanAsync(scanResult.OriginalData);
                    break;

                case CodeType.GS1_128:
                case CodeType.GS1_128_IZ:
                    await ProcessGS1ScanAsync(scanResult.OriginalData);
                    break;

                default:
                    ShowError($"Nierozpoznany typ kodu: {scanResult.OriginalData}");
                    break;
            }
        }
        catch (Exception ex)
        {
            ShowError($"Błąd przetwarzania skanu: {ex.Message}");
        }
        finally
        {
            IsProcessingScan = false;
        }
    }
}
```

## Konfiguracja Zaawansowana

### Profile DataWedge dla Różnych Typów Inwentaryzacji

#### Profil dla Inwentaryzacji Produktowej

```xml
<profile name="WmsInventoryProduct" version="1.0">
    <!-- Priorytet dla kodów produktów -->
    <decoder_settings>
        <decoder name="ean13" priority="1"/>
        <decoder name="ean8" priority="2"/>
        <decoder name="code128" priority="3"/>
        <decoder name="upca" priority="4"/>
    </decoder_settings>

    <!-- Szybkie skanowanie -->
    <performance_settings>
        <illumination_mode>on</illumination_mode>
        <pick_list_mode>hardware_reticle</pick_list_mode>
        <scene_detection>enabled</scene_detection>
    </performance_settings>
</profile>
```

#### Profil dla Inwentaryzacji Miejsc

```xml
<profile name="WmsInventoryLocation" version="1.0">
    <!-- Priorytet dla lokalizacji i etykiet -->
    <decoder_settings>
        <decoder name="code39" priority="1"/>
        <decoder name="code128" priority="2"/>
        <decoder name="datamatrix" priority="3"/>
        <decoder name="qr_code" priority="4"/>
    </decoder_settings>

    <!-- Skanowanie z większej odległości -->
    <performance_settings>
        <illumination_mode>on</illumination_mode>
        <aim_mode>on</aim_mode>
        <beam_width>wide</beam_width>
    </performance_settings>
</profile>
```

### Zarządzanie Profilami Programowo

```csharp
public class DataWedgeProfileManager
{
    private const string PROFILE_PRODUCT = "WmsInventoryProduct";
    private const string PROFILE_LOCATION = "WmsInventoryLocation";
    private const string PROFILE_GENERAL = "WmsInventoryGeneral";

    public async Task SwitchToProductProfileAsync()
    {
        await SwitchProfileAsync(PROFILE_PRODUCT);
    }

    public async Task SwitchToLocationProfileAsync()
    {
        await SwitchProfileAsync(PROFILE_LOCATION);
    }

    private async Task SwitchProfileAsync(string profileName)
    {
        var switchIntent = new Intent();
        switchIntent.SetAction("com.symbol.datawedge.api.ACTION");
        switchIntent.PutExtra("com.symbol.datawedge.api.SWITCH_TO_PROFILE", profileName);

        Android.App.Application.Context.SendBroadcast(switchIntent);

        // Oczekiwanie na potwierdzenie przełączenia
        await Task.Delay(500);
    }

    public async Task<bool> IsProfileActiveAsync(string profileName)
    {
        var resultReceiver = new DataWedgeResultReceiver();

        var queryIntent = new Intent();
        queryIntent.SetAction("com.symbol.datawedge.api.ACTION");
        queryIntent.PutExtra("com.symbol.datawedge.api.GET_ACTIVE_PROFILE", "");
        queryIntent.PutExtra("SEND_RESULT", resultReceiver);

        Android.App.Application.Context.SendBroadcast(queryIntent);

        var result = await resultReceiver.GetResultAsync();
        return result?.GetString("ACTIVE_PROFILE") == profileName;
    }
}
```

## Integracja z Serwisami Backend

### Service Layer dla Przetwarzania Skanów

```csharp
public interface IInventoryScanProcessingService
{
    Task<ScanProcessingResult> ProcessScanAsync(string scannedData, int sessionId);
    Task<ProductLookupResult> LookupProductAsync(string productCode);
    Task<LocationValidationResult> ValidateLocationAsync(string locationCode);
    Task<bool> IsItemAlreadyCountedAsync(string itemIdentifier, int sessionId);
}

public class InventoryScanProcessingService : IInventoryScanProcessingService
{
    private readonly IInventoryApiClient _apiClient;
    private readonly ICodeValidationService _codeValidation;
    private readonly ILogger<InventoryScanProcessingService> _logger;

    public async Task<ScanProcessingResult> ProcessScanAsync(string scannedData, int sessionId)
    {
        try
        {
            // Walidacja kodu
            var validationResult = _codeValidation.ValidateCode(scannedData);
            if (!validationResult.IsValid)
            {
                return ScanProcessingResult.Error(validationResult.ErrorMessage);
            }

            // Wywołanie API do przetworzenia skanu
            var apiRequest = new ProcessInventoryScanRequest
            {
                ScannedCode = scannedData,
                SessionId = sessionId,
                DeviceId = DeviceInfo.Name,
                Timestamp = DateTime.Now
            };

            var apiResponse = await _apiClient.ProcessInventoryScanAsync(apiRequest);

            if (apiResponse.IsSuccessStatusCode)
            {
                var result = apiResponse.Content;
                return ScanProcessingResult.Success(result);
            }
            else
            {
                var errorMessage = await apiResponse.GetErrorMessageAsync();
                return ScanProcessingResult.Error(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing inventory scan: {ScannedData}", scannedData);
            return ScanProcessingResult.Error("Błąd przetwarzania skanu. Spróbuj ponownie.");
        }
    }
}
```

## Diagnostyka i Debugowanie

### Logi DataWedge

```csharp
public class DataWedgeDiagnostics
{
    public async Task<DataWedgeStatus> GetStatusAsync()
    {
        var resultReceiver = new DataWedgeResultReceiver();

        var statusIntent = new Intent();
        statusIntent.SetAction("com.symbol.datawedge.api.ACTION");
        statusIntent.PutExtra("com.symbol.datawedge.api.GET_VERSION_INFO", "");
        statusIntent.PutExtra("SEND_RESULT", resultReceiver);

        Android.App.Application.Context.SendBroadcast(statusIntent);

        var result = await resultReceiver.GetResultAsync();

        return new DataWedgeStatus
        {
            Version = result?.GetString("DATAWEDGE") ?? "Unknown",
            IsRunning = result != null,
            ActiveProfile = result?.GetString("ACTIVE_PROFILE") ?? "None"
        };
    }

    public void LogScanEvent(InventoryScanResult scanResult)
    {
        var logData = new
        {
            Timestamp = scanResult.Timestamp,
            Data = scanResult.OriginalData,
            CodeType = scanResult.CodeType.ToString(),
            LabelType = scanResult.LabelType,
            Source = scanResult.Source,
            ProcessingStatus = scanResult.ProcessingStatus.ToString()
        };

        Log.Info("InventoryDiagnostics", $"Scan Event: {JsonSerializer.Serialize(logData)}");
    }
}

public record DataWedgeStatus(string Version, bool IsRunning, string ActiveProfile);
```

### Testowanie Integracji

```csharp
[Test]
public async Task DataWedgeIntegration_ScanSSCC_ShouldProcessCorrectly()
{
    // Arrange
    var testSSCC = "123456789012345678";
    var mockScanResult = new InventoryScanResult
    {
        OriginalData = testSSCC,
        CodeType = CodeType.SSCC,
        LabelType = "EAN128",
        Timestamp = DateTime.Now
    };

    // Act
    await _viewModel.ProcessScanAsync(mockScanResult);

    // Assert
    Assert.That(_viewModel.CurrentProduct?.SSCC, Is.EqualTo(testSSCC));
    Assert.That(_viewModel.ScanStatus, Is.EqualTo(ScanStatus.Success));
}

[Test]
public async Task DataWedgeIntegration_InvalidCode_ShouldShowError()
{
    // Arrange
    var invalidCode = "INVALID_CODE_123";
    var mockScanResult = new InventoryScanResult
    {
        OriginalData = invalidCode,
        CodeType = CodeType.Unknown,
        ProcessingStatus = ProcessingStatus.Error,
        ErrorMessage = "Nierozpoznany format kodu"
    };

    // Act
    await _viewModel.ProcessScanAsync(mockScanResult);

    // Assert
    Assert.That(_viewModel.ErrorMessage, Contains.Substring("Nierozpoznany"));
    Assert.That(_viewModel.ScanStatus, Is.EqualTo(ScanStatus.Error));
}
```

## Troubleshooting i FAQ

### Najczęstsze Problemy

#### 1. DataWedge nie wysyła Intent
**Przyczyny**:
- Profil nie jest aktywny
- Błędna konfiguracja Intent action
- Aplikacja nie jest zarejestrowana w profilu

**Rozwiązanie**:
```csharp
// Sprawdzenie aktywnego profilu
var diagnostics = new DataWedgeDiagnostics();
var status = await diagnostics.GetStatusAsync();
if (status.ActiveProfile != "WmsInventory")
{
    await _profileManager.SwitchToProductProfileAsync();
}
```

#### 2. Skanowanie działa, ale dane są niepoprawne
**Przyczyny**:
- Błędne dekodery
- Interference między profilami
- Problemy z formatowaniem danych

**Rozwiązanie**:
```csharp
// Restart profilu DataWedge
private async Task RestartDataWedgeProfileAsync()
{
    var intent = new Intent("com.symbol.datawedge.api.ACTION");
    intent.PutExtra("com.symbol.datawedge.api.SWITCH_TO_PROFILE", "");
    Android.App.Application.Context.SendBroadcast(intent);

    await Task.Delay(1000);

    intent.PutExtra("com.symbol.datawedge.api.SWITCH_TO_PROFILE", "WmsInventory");
    Android.App.Application.Context.SendBroadcast(intent);
}
```

#### 3. Brak sygnałów dźwiękowych
```csharp
// Konfiguracja audio feedback
private void ConfigureAudioFeedback()
{
    var audioIntent = new Intent("com.symbol.datawedge.api.ACTION");
    var audioConfig = new Bundle();
    audioConfig.PutString("PROFILE_NAME", "WmsInventory");

    var keystrokeConfig = new Bundle();
    keystrokeConfig.PutString("PLUGIN_NAME", "KEYSTROKE");
    keystrokeConfig.PutString("keystroke_output_enabled", "true");
    keystrokeConfig.PutString("keystroke_action_char", "9"); // Tab
    keystrokeConfig.PutString("keystroke_delay_extended_ascii", "0");

    audioConfig.PutParcelable("PLUGIN_CONFIG", keystrokeConfig);
    audioIntent.PutExtra("com.symbol.datawedge.api.SET_CONFIG", audioConfig);
    Android.App.Application.Context.SendBroadcast(audioIntent);
}
```

---

*Dokument zaktualizowany: 2025-01-14 - Kompletna integracja DataWedge z modułem inwentaryzacji* 📱🔧✅