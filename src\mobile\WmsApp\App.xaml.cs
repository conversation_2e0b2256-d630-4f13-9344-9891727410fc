namespace WmsApp;

public partial class App : Application
{
    public App()
    {
        InitializeComponent();
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        var appShell = IPlatformApplication.Current?.Services?.GetService<AppShell>() 
                       ?? new AppShell(null!, null!);
        var window = new Window(appShell);

#if WINDOWS
        // Ustaw rozmiar okna na Windows przy starcie (800x480)
        window.Created += (s, e) =>
        {
            try
            {
                const int targetWidth = 480;
                const int targetHeight = 800;

                var nativeWindow = (Microsoft.Maui.MauiWinUIWindow)window.Handler.PlatformView;
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(nativeWindow);
                var windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(hwnd);
                var appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
                if (appWindow is not null)
                {
                    appWindow.Resize(new Windows.Graphics.SizeInt32(targetWidth, targetHeight));
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WINDOWS] Failed to set initial window size: {ex}");
            }
        };
#endif

        return window;
    }
}
