using FluentAssertions;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Services;
using Xunit;

namespace Wms.UnitTests.Domain.Services;

public class ReceiveDomainServiceTests
{
    private readonly ReceiveDomainService _service;

    public ReceiveDomainServiceTests()
    {
        _service = new ReceiveDomainService();
    }

    [Fact]
    public void CanClaimReceive_ValidReceive_ShouldReturnSuccess()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = null };
        var userId = 1;

        // Act
        var result = _service.CanClaimReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CanClaimReceive_NullReceive_ShouldReturnFailure()
    {
        // Arrange
        ListControl receive = null!;
        var userId = 1;

        // Act
        var result = _service.CanClaimReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa nie została znaleziona");
    }

    [Fact]
    public void CanClaimReceive_AlreadyClaimed_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 2 };
        var userId = 1;

        // Act
        var result = _service.CanClaimReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa jest zajęta przez pracownika ID: 2");
    }

    [Fact]
    public void CanClaimReceive_SameUser_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 1 };
        var userId = 1;

        // Act
        var result = _service.CanClaimReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa jest już zajęta przez tego pracownika");
    }

    [Fact]
    public void ClaimReceive_ValidClaim_ShouldSucceed()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = null };
        var userId = 1;
        var claimedAt = DateTime.UtcNow;

        // Act
        var result = _service.ClaimReceive(receive, userId, claimedAt);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ClaimedAt.Should().Be(claimedAt);
        receive.RealizujacyPracownikId.Should().Be(userId);
        receive.IsAssigned.Should().BeTrue();
    }

    [Fact]
    public void ClaimReceive_InvalidClaim_ShouldFail()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 2 };
        var userId = 1;
        var claimedAt = DateTime.UtcNow;

        // Act
        var result = _service.ClaimReceive(receive, userId, claimedAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ClaimedAt.Should().BeNull();
        receive.RealizujacyPracownikId.Should().Be(2); // Unchanged
    }

    [Fact]
    public void CanReleaseReceive_ValidRelease_ShouldReturnSuccess()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 1 };
        var userId = 1;

        // Act
        var result = _service.CanReleaseReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CanReleaseReceive_NotAssigned_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = null };
        var userId = 1;

        // Act
        var result = _service.CanReleaseReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa nie jest przypisana do żadnego pracownika");
    }

    [Fact]
    public void CanReleaseReceive_WrongUser_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 2 };
        var userId = 1;

        // Act
        var result = _service.CanReleaseReceive(receive, userId);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa jest przypisana do innego pracownika (ID: 2)");
    }

    [Fact]
    public void ReleaseReceive_ValidRelease_ShouldSucceed()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 1 };
        var userId = 1;
        var releasedAt = DateTime.UtcNow;

        // Act
        var result = _service.ReleaseReceive(receive, userId, releasedAt);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ReleasedAt.Should().Be(releasedAt);
        receive.RealizujacyPracownikId.Should().BeNull();
        receive.IsAssigned.Should().BeFalse();
    }

    [Fact]
    public void ReleaseReceive_InvalidRelease_ShouldFail()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 2 };
        var userId = 1;
        var releasedAt = DateTime.UtcNow;

        // Act
        var result = _service.ReleaseReceive(receive, userId, releasedAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ReleasedAt.Should().BeNull();
        receive.RealizujacyPracownikId.Should().Be(2); // Unchanged
    }

    [Fact]
    public void IsReceiveComplete_NoExpectedItems_ShouldReturnTrue()
    {
        // Arrange
        var receive = new ListControl { Id = 123 };
        var expectedItems = new List<AwizacjaDane>();
        var receivedLabels = new List<Label>();

        // Act
        var result = _service.IsReceiveComplete(receive, expectedItems, receivedLabels);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsReceiveComplete_AllItemsReceived_ShouldReturnTrue()
    {
        // Arrange
        var receive = new ListControl { Id = 123 };
        var expectedItems = new List<AwizacjaDane>
        {
            new() { EtykietaKlient = "123456789012345678" },
            new() { EtykietaKlient = "987654321098765432" }
        };
        var receivedLabels = new List<Label>
        {
            new() { Sscc = "123456789012345678", ListcontrolId = 123 },
            new() { Sscc = "987654321098765432", ListcontrolId = 123 }
        };

        // Act
        var result = _service.IsReceiveComplete(receive, expectedItems, receivedLabels);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsReceiveComplete_MissingItems_ShouldReturnFalse()
    {
        // Arrange
        var receive = new ListControl { Id = 123 };
        var expectedItems = new List<AwizacjaDane>
        {
            new() { EtykietaKlient = "123456789012345678" },
            new() { EtykietaKlient = "987654321098765432" }
        };
        var receivedLabels = new List<Label>
        {
            new() { Sscc = "123456789012345678", ListcontrolId = 123 }
            // Missing second item
        };

        // Act
        var result = _service.IsReceiveComplete(receive, expectedItems, receivedLabels);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CanCreatePallet_ValidParameters_ShouldReturnSuccess()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 1 };
        var userId = 1;
        var palletType = new TypyPalet { Id = 1, Opis = "Standard", UdzialSkladowania = 1.0m };

        // Act
        var result = _service.CanCreatePallet(receive, userId, palletType);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CanCreatePallet_NotAssignedReceive_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = null };
        var userId = 1;
        var palletType = new TypyPalet { Id = 1, Opis = "Standard", UdzialSkladowania = 1.0m };

        // Act
        var result = _service.CanCreatePallet(receive, userId, palletType);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Dostawa musi być przypisana do pracownika");
    }

    [Fact]
    public void CanCreatePallet_InvalidPalletType_ShouldReturnFailure()
    {
        // Arrange
        var receive = new ListControl { Id = 123, RealizujacyPracownikId = 1 };
        var userId = 1;
        var palletType = new TypyPalet { Id = 1, Opis = "", UdzialSkladowania = 0.0m }; // Invalid

        // Act
        var result = _service.CanCreatePallet(receive, userId, palletType);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Typ palety '' nie może być używany do generowania DS");
    }

    [Theory]
    [InlineData("LK123", true)]
    [InlineData("LK1", true)]
    [InlineData("**********", true)]
    [InlineData("", false)]
    [InlineData("123", false)]
    [InlineData("LKabc", false)]
    [InlineData(null, false)]
    public void IsValidLKCode_ShouldReturnExpectedResult(string code, bool expected)
    {
        // Act
        var result = ReceiveDomainService.IsValidLKCode(code);

        // Assert
        result.Should().Be(expected);
    }
}
