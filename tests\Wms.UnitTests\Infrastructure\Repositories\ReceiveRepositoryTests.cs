using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class ReceiveRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly ReceiveRepository _receiveRepository;

    public ReceiveRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _receiveRepository = new ReceiveRepository(_context);

        SeedTestData();
    }

    [Fact]
    public async Task GetAvailableReceivesAsync_WithoutFilters_ShouldReturnOnlyUnassignedReceives()
    {
        // Act
        var result = await _receiveRepository.GetAvailableReceivesAsync();

        // Assert
        result.Should().NotBeNull();
        var receivesList = result.ToList();
        receivesList.Should().HaveCount(2); // Tylko nie przypisane dostawy
        receivesList.Should().AllSatisfy(r => r.RealizujacyPracownikId.Should().BeNull());
        receivesList.Should().Contain(r => r.Id == 1);
        receivesList.Should().Contain(r => r.Id == 2);

        // Verify entities are not tracked (AsNoTrackingWithIdentityResolution)
        foreach (var receive in receivesList)
        {
            var entry = _context.Entry(receive);
            entry.State.Should().Be(Microsoft.EntityFrameworkCore.EntityState.Detached);
        }
    }

    [Fact]
    public async Task GetAvailableReceivesAsync_WithIncludeAssigned_ShouldReturnAllReceives()
    {
        // Act
        var result = await _receiveRepository.GetAvailableReceivesAsync(includeAssigned: true);

        // Assert
        result.Should().NotBeNull();
        var receivesList = result.ToList();
        receivesList.Should().HaveCount(3); // Wszystkie dostawy
        receivesList.Should().Contain(r => r.Id == 1);
        receivesList.Should().Contain(r => r.Id == 2);
        receivesList.Should().Contain(r => r.Id == 3);
    }

    [Fact]
    public async Task GetAvailableReceivesAsync_WithWarehouseFilter_ShouldReturnFilteredReceives()
    {
        // Arrange
        int warehouseId = 1;

        // Act
        var result = await _receiveRepository.GetAvailableReceivesAsync(warehouseId: warehouseId);

        // Assert
        result.Should().NotBeNull();
        var receivesList = result.ToList();
        receivesList.Should().HaveCount(1);
        receivesList.Should().AllSatisfy(r => r.MiejsceId.Should().Be(warehouseId));
        receivesList.First().Id.Should().Be(1);
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnReceive()
    {
        // Arrange
        var receiveId = 1;

        // Act
        var result = await _receiveRepository.GetByIdAsync(receiveId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(receiveId);
        result.Numer.Should().Be(1001);
        result.Transport.Should().Be("Test Transport 1");

        // Verify entity is not tracked (AsNoTracking)
        var entry = _context.Entry(result);
        entry.State.Should().Be(Microsoft.EntityFrameworkCore.EntityState.Detached);
    }

    [Fact]
    public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var invalidId = 999;

        // Act
        var result = await _receiveRepository.GetByIdAsync(invalidId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByIdForUpdateAsync_WithValidId_ShouldReturnTrackedReceive()
    {
        // Arrange
        var receiveId = 1;

        // Act
        var result = await _receiveRepository.GetByIdForUpdateAsync(receiveId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(receiveId);
        result.Numer.Should().Be(1001);

        // Verify entity is tracked (for updates)
        var entry = _context.Entry(result);
        entry.State.Should().Be(Microsoft.EntityFrameworkCore.EntityState.Unchanged);
    }

    [Fact]
    public async Task GetWithDetailsAsync_WithValidId_ShouldReturnReceiveWithDetails()
    {
        // Arrange
        var receiveId = 1;

        // Act
        var result = await _receiveRepository.GetWithDetailsAsync(receiveId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(receiveId);
        result.MiejsceDostawy.Should().NotBeNull();
        result.ListControlPallets.Should().NotBeNull();
    }

    [Fact]
    public async Task ClaimReceiveAsync_WithUnassignedReceive_ShouldSucceed()
    {
        // Arrange
        var receiveId = 1;
        var userId = 1;

        // Act
        var result = await _receiveRepository.ClaimReceiveAsync(receiveId, userId);

        // Assert
        result.Should().BeTrue();

        // Verify the assignment is tracked but not yet saved
        var receive = await _context.ListControls.FindAsync(receiveId);
        receive.Should().NotBeNull();
        receive!.RealizujacyPracownikId.Should().Be(userId);

        // Verify entity is tracked
        var entry = _context.Entry(receive);
        entry.State.Should().Be(Microsoft.EntityFrameworkCore.EntityState.Modified);
    }

    [Fact]
    public async Task ClaimReceiveAsync_WithAlreadyAssignedReceive_ShouldFail()
    {
        // Arrange
        var receiveId = 3; // Already assigned to user 1
        var userId = 2;

        // Act
        var result = await _receiveRepository.ClaimReceiveAsync(receiveId, userId);

        // Assert
        result.Should().BeFalse();

        // Verify the assignment didn't change
        var receive = await _context.ListControls.FindAsync(receiveId);
        receive.Should().NotBeNull();
        receive!.RealizujacyPracownikId.Should().Be(1); // Still assigned to user 1
    }

    [Fact]
    public async Task ClaimReceiveAsync_WithNonExistentReceive_ShouldFail()
    {
        // Arrange
        var nonExistentReceiveId = 999;
        var userId = 1;

        // Act
        var result = await _receiveRepository.ClaimReceiveAsync(nonExistentReceiveId, userId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ReleaseReceiveAsync_WithProperlyAssignedReceive_ShouldSucceed()
    {
        // Arrange
        var receiveId = 3; // Assigned to user 1
        var userId = 1;

        // Act
        var result = await _receiveRepository.ReleaseReceiveAsync(receiveId, userId);

        // Assert
        result.Should().BeTrue();

        // Verify the release
        var receive = await _context.ListControls.FindAsync(receiveId);
        receive.Should().NotBeNull();
        receive!.RealizujacyPracownikId.Should().BeNull();
    }

    [Fact]
    public async Task ReleaseReceiveAsync_WithWrongUser_ShouldFail()
    {
        // Arrange
        var receiveId = 3; // Assigned to user 1
        var wrongUserId = 2;

        // Act
        var result = await _receiveRepository.ReleaseReceiveAsync(receiveId, wrongUserId);

        // Assert
        result.Should().BeFalse();

        // Verify the assignment didn't change
        var receive = await _context.ListControls.FindAsync(receiveId);
        receive.Should().NotBeNull();
        receive!.RealizujacyPracownikId.Should().Be(1); // Still assigned to user 1
    }

    [Fact]
    public async Task ReleaseReceiveAsync_WithNonExistentReceive_ShouldFail()
    {
        // Arrange
        var nonExistentReceiveId = 999;
        var userId = 1;

        // Act
        var result = await _receiveRepository.ReleaseReceiveAsync(nonExistentReceiveId, userId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsReceiveClaimedByUserAsync_WithCorrectUser_ShouldReturnTrue()
    {
        // Arrange
        var receiveId = 3; // Assigned to user 1
        var userId = 1;

        // Act
        var result = await _receiveRepository.IsReceiveClaimedByUserAsync(receiveId, userId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsReceiveClaimedByUserAsync_WithWrongUser_ShouldReturnFalse()
    {
        // Arrange
        var receiveId = 3; // Assigned to user 1
        var wrongUserId = 2;

        // Act
        var result = await _receiveRepository.IsReceiveClaimedByUserAsync(receiveId, wrongUserId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsReceiveClaimedByUserAsync_WithUnassignedReceive_ShouldReturnFalse()
    {
        // Arrange
        var receiveId = 1; // Not assigned
        var userId = 1;

        // Act
        var result = await _receiveRepository.IsReceiveClaimedByUserAsync(receiveId, userId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetExpectedItemsAsync_WithValidReceiveWithAwizacja_ShouldReturnItems()
    {
        // Arrange
        var receiveId = 1; // Has awizacja with ID 1

        // Act
        var result = await _receiveRepository.GetExpectedItemsAsync(receiveId);

        // Assert
        result.Should().NotBeNull();
        var itemsList = result.ToList();
        itemsList.Should().HaveCount(2);
        itemsList.Should().Contain(i => i.Kod == "TEST001");
        itemsList.Should().Contain(i => i.Kod == "TEST002");
    }

    [Fact]
    public async Task GetExpectedItemsAsync_WithReceiveWithoutAwizacja_ShouldReturnEmptyCollection()
    {
        // Arrange
        var receiveId = 2; // No awizacja

        // Act
        var result = await _receiveRepository.GetExpectedItemsAsync(receiveId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task UpdateAsync_WithValidReceive_ShouldUpdateSuccessfully()
    {
        // Arrange
        var receiveId = 1;
        var receive = await _context.ListControls.FindAsync(receiveId);
        receive.Should().NotBeNull();
        
        var newTransport = "Updated Transport";
        receive!.Transport = newTransport;

        // Act
        await _receiveRepository.UpdateAsync(receive);

        // Assert
        var updatedReceive = await _context.ListControls.FindAsync(receiveId);
        updatedReceive.Should().NotBeNull();
        updatedReceive!.Transport.Should().Be(newTransport);
    }

    private void SeedTestData()
    {
        // Dodaj lokalizacje
        var locations = new[]
        {
            new Location
            {
                Id = 1,
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "01",
                Wysokosc = "1",
                Widoczne = 1,
                MaxPojemnosc = 100
            },
            new Location
            {
                Id = 2,
                Hala = 2,
                Regal = "B",
                Miejsce = 1,
                Poziom = "01",
                Wysokosc = "1",
                Widoczne = 1,
                MaxPojemnosc = 200
            }
        };
        _context.Locations.AddRange(locations);

        // Dodaj użytkowników
        var users = new[]
        {
            new User
            {
                Id = 1,
                Login = "user1",
                ImieNazwisko = "Test User 1",
                Haslo = "password",
                JednostkaId = 1
            },
            new User
            {
                Id = 2,
                Login = "user2",
                ImieNazwisko = "Test User 2",
                Haslo = "password",
                JednostkaId = 1
            }
        };
        _context.Users.AddRange(users);

        // Dodaj awizacje
        var awizacjaHead = new AwizacjaHead
        {
            Id = 1,
            SystemId = 1,
            Magazyn = 1,
            Data = "2023-01-01",
            Godzina = "10:00",
            Ts = DateTime.Now,
            Transport = "Test Transport Head"
        };
        _context.AwizacjaHeads.Add(awizacjaHead);

        var awizacjaDanes = new[]
        {
            new AwizacjaDane
            {
                Id = 1,
                AwizacjeDostawId = 1,
                EtykietaKlient = "CLIENT001",
                Kod = "TEST001",
                Ilosc = 100,
                PozycjaZamowienia = 1
            },
            new AwizacjaDane
            {
                Id = 2,
                AwizacjeDostawId = 1,
                EtykietaKlient = "CLIENT002",
                Kod = "TEST002",
                Ilosc = 200,
                PozycjaZamowienia = 2
            }
        };
        _context.AwizacjaDanes.AddRange(awizacjaDanes);

        // Dodaj dostawy
        var listControls = new[]
        {
            new ListControl
            {
                Id = 1,
                Numer = 1001,
                Data = DateOnly.FromDateTime(DateTime.Now),
                Transport = "Test Transport 1",
                MiejsceId = 1,
                AwizacjeId = 1, // Ma awizację
                RealizujacyPracownikId = null // Nie przypisana
            },
            new ListControl
            {
                Id = 2,
                Numer = 1002,
                Data = DateOnly.FromDateTime(DateTime.Now),
                Transport = "Test Transport 2",
                MiejsceId = 2,
                AwizacjeId = 0, // Bez awizacji
                RealizujacyPracownikId = null // Nie przypisana
            },
            new ListControl
            {
                Id = 3,
                Numer = 1003,
                Data = DateOnly.FromDateTime(DateTime.Now),
                Transport = "Test Transport 3",
                MiejsceId = 1,
                AwizacjeId = 0,
                RealizujacyPracownikId = 1 // Przypisana do użytkownika 1
            }
        };
        _context.ListControls.AddRange(listControls);

        // Dodaj palety dla dostaw
        var listControlPallets = new[]
        {
            new ListControlPallet
            {
                Id = 1,
                ListcontrolId = 1,
                PaletaId = 100,
                Wydruk = 1
            },
            new ListControlPallet
            {
                Id = 2,
                ListcontrolId = 1,
                PaletaId = 101,
                Wydruk = 1
            }
        };
        _context.ListControlPallets.AddRange(listControlPallets);

        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
