using System.Text.RegularExpressions;
using Wms.Domain.Exceptions;
using Wms.Domain.ValueObjects;

namespace Wms.Domain.Services.GS1;

/// <summary>
/// Parser kodów GS1-128 z obsługą prefiksu IZ
/// Adaptacja z starej klasy Etykieta do Clean Architecture
/// </summary>
public class GS1Parser
{
    private readonly Dictionary<string, ApplicationIdentifier> _aiDictionary;
    private readonly string[] _sortedAiCodes;
    private readonly int _minAiLength;
    private readonly int _maxAiLength;
    
    // GS1 constants
    private const char GroupSeparator = (char)29; // FNC1
    private const string Ean128StartCode = "]C1";
    private const string IzPrefix = "IZ";

    public GS1Parser()
    {
        _aiDictionary = InitializeApplicationIdentifiers();
        _sortedAiCodes = _aiDictionary.Keys.ToArray();
        _minAiLength = _aiDictionary.Values.Min(ai => ai.LengthOfAI);
        _maxAiLength = _aiDictionary.Values.Max(ai => ai.LengthOfAI);
    }

    /// <summary>
    /// Parsuje kod GS1-128 z opcjonalnym prefiksem IZ
    /// </summary>
    public GS1ParseResult Parse(string rawData)
    {
        if (string.IsNullOrWhiteSpace(rawData))
            return GS1ParseResult.Failure(rawData ?? "", "Dane wejściowe nie mogą być puste");

        try
        {
            var cleanData = PreprocessData(rawData, out var hasIzPrefix);
            var parsedData = ParseGS1Data(cleanData);
            
            return GS1ParseResult.Success(
                rawData: rawData,
                sscc: parsedData.SSCC,
                gtin: parsedData.Gtin,
                lot: parsedData.Lot,
                expiryDate: parsedData.ExpiryDate,
                quantity: parsedData.Quantity,
                hasIzPrefix: hasIzPrefix
            );
        }
        catch (InvalidGS1FormatException ex)
        {
            return GS1ParseResult.Failure(rawData, ex.Message);
        }
        catch (Exception ex)
        {
            return GS1ParseResult.Failure(rawData, $"Błąd parsowania GS1: {ex.Message}");
        }
    }

    /// <summary>
    /// Wstępne przetwarzanie danych - usuwanie prefiksów, kodów startowych
    /// </summary>
    private string PreprocessData(string rawData, out bool hasIzPrefix)
    {
        var data = rawData.Trim();
        hasIzPrefix = false;

        // Sprawdź prefiks IZ
        if (data.StartsWith(IzPrefix, StringComparison.OrdinalIgnoreCase))
        {
            hasIzPrefix = true;
            data = data[IzPrefix.Length..];
        }

        // Usuń kod startowy EAN-128
        if (data.StartsWith(Ean128StartCode))
        {
            data = data[Ean128StartCode.Length..];
        }

        // Usuń znaki kontrolne ale zachowaj separator grupowy (FNC1)
        data = Regex.Replace(data, @"[\x00-\x08\x0A-\x1C\x1E-\x1F\x7F]", "");

        return data;
    }

    /// <summary>
    /// Główna logika parsowania GS1
    /// </summary>
    private ParsedGS1Data ParseGS1Data(string data)
    {
        var result = new ParsedGS1Data();
        var index = 0;

        while (index < data.Length)
        {
            var ai = FindApplicationIdentifier(data, ref index);
            if (ai == null)
            {
                throw new InvalidGS1FormatException(data, $"Nie znaleziono AI na pozycji {index}: '{data[index..]}'");
            }

            var aiCode = data.Substring(index - ai.LengthOfAI, ai.LengthOfAI);
            var aiData = ExtractAiData(data, ai, aiCode, ref index);
            
            MapAiDataToResult(ai, aiCode, aiData, result);
        }

        return result;
    }

    /// <summary>
    /// Znajduje AI na bieżącej pozycji
    /// </summary>
    private ApplicationIdentifier? FindApplicationIdentifier(string data, ref int index)
    {
        // Próbuj różne długości AI (od najkrótszego do najdłuższego)
        for (int length = _minAiLength; length <= _maxAiLength; length++)
        {
            if (index + length > data.Length) continue;

            var candidate = data.Substring(index, length);
            
            // Sprawdź dokładne dopasowanie
            if (_aiDictionary.TryGetValue(candidate, out var exactMatch))
            {
                index += length;
                return exactMatch;
            }

            // Sprawdź wzorce z 'd' (np. "310d")
            var patternMatch = _aiDictionary.Values
                .FirstOrDefault(ai => ai.Matches(candidate));
                
            if (patternMatch != null)
            {
                index += length;
                return patternMatch;
            }
        }

        return null;
    }

    /// <summary>
    /// Wyciąga dane dla konkretnego AI
    /// </summary>
    private string ExtractAiData(string data, ApplicationIdentifier ai, string aiCode, ref int index)
    {
        var maxLength = Math.Min(ai.MaxDataLength, data.Length - index);
        var extractedData = data.Substring(index, maxLength);

        if (ai.SupportsFNC1)
        {
            // Szukaj separatora grupowego FNC1
            var separatorIndex = extractedData.IndexOf(GroupSeparator);
            if (separatorIndex >= 0)
            {
                maxLength = separatorIndex + 1; // Uwzględnij separator
                extractedData = data.Substring(index, maxLength);
            }
        }

        // Usuń separator grupowy z danych
        extractedData = extractedData.Replace(GroupSeparator.ToString(), "");
        
        index += maxLength;
        return extractedData;
    }

    /// <summary>
    /// Mapuje dane AI do odpowiednich Value Objects
    /// </summary>
    private void MapAiDataToResult(ApplicationIdentifier ai, string aiCode, string aiData, ParsedGS1Data result)
    {
        switch (ai.Code.TrimEnd('d')) // Usuń 'd' z wzorców
        {
            case "00": // SSCC
                if (SSCCCode.TryCreate(aiData, out var sscc))
                    result.SSCC = sscc;
                break;

            case "01": // GTIN (jednostkowy)
                if (GtinCode.TryCreate(aiData, out var gtin01))
                    result.Gtin = gtin01;
                break;

            case "02": // GTIN (zawarte jednostki)
                if (GtinCode.TryCreate(aiData, out var gtin))
                    result.Gtin = gtin;
                break;

            case "10": // Lot Number
                if (LotNumber.TryCreate(aiData, out var lot))
                    result.Lot = lot;
                break;

            case "17": // Expiry Date
                if (ExpiryDate.TryCreateFromGS1(aiData, out var expiryDate))
                    result.ExpiryDate = expiryDate;
                break;

            case "37": // Count/Quantity
                if (Quantity.TryCreateFromAI37(aiData, out var quantity))
                    result.Quantity = quantity;
                break;

            case "15": // Best Before Date (podobnie jak expiry date)
                if (result.ExpiryDate == null && ExpiryDate.TryCreateFromGS1(aiData, out var bestBefore))
                    result.ExpiryDate = bestBefore;
                break;

            // Inne AI można dodać w przyszłości
            default:
                // Ignoruj nieobsługiwane AI (nie jest błędem)
                break;
        }
    }

    /// <summary>
    /// Inicjalizuje słownik Application Identifiers
    /// Adaptacja z oryginalnej klasy Etykieta
    /// </summary>
    private Dictionary<string, ApplicationIdentifier> InitializeApplicationIdentifiers()
    {
        var dictionary = new Dictionary<string, ApplicationIdentifier>();

        // Kluczowe AI dla WMS
        Add(dictionary, "00", "SSCC (Serial Shipping Container Code)", 2, DataType.Numeric, 18, false);
        Add(dictionary, "01", "GTIN", 2, DataType.Numeric, 14, false);
        Add(dictionary, "02", "GTIN of trade items contained", 2, DataType.Numeric, 14, false);
        Add(dictionary, "10", "Batch/Lot Number", 2, DataType.Alphanumeric, 20, true);
        Add(dictionary, "11", "Production Date (YYMMDD)", 2, DataType.Numeric, 6, false);
        Add(dictionary, "12", "Due Date (YYMMDD)", 2, DataType.Numeric, 6, false);
        Add(dictionary, "13", "Packing Date (YYMMDD)", 2, DataType.Numeric, 6, false);
        Add(dictionary, "15", "Best Before Date (YYMMDD)", 2, DataType.Numeric, 6, false);
        Add(dictionary, "17", "Expiry Date (YYMMDD)", 2, DataType.Numeric, 6, false);
        Add(dictionary, "20", "Product Variant", 2, DataType.Numeric, 2, false);
        Add(dictionary, "21", "Serial Number", 2, DataType.Alphanumeric, 20, true);
        Add(dictionary, "37", "Count", 2, DataType.Numeric, 8, true);

        // Wymiary i wagi z wzorcami 'd'
        Add(dictionary, "310d", "Net Weight (kg)", 4, DataType.Numeric, 6, false);
        Add(dictionary, "311d", "Length (m)", 4, DataType.Numeric, 6, false);
        Add(dictionary, "312d", "Width (m)", 4, DataType.Numeric, 6, false);
        Add(dictionary, "313d", "Height (m)", 4, DataType.Numeric, 6, false);
        Add(dictionary, "315d", "Net Volume (l)", 4, DataType.Numeric, 6, false);
        Add(dictionary, "330d", "Gross Weight (kg)", 4, DataType.Numeric, 6, false);

        // Logistyka
        Add(dictionary, "400", "Customer PO Number", 3, DataType.Alphanumeric, 30, true);
        Add(dictionary, "401", "Consignment Number", 3, DataType.Alphanumeric, 30, true);
        Add(dictionary, "402", "Bill of Lading Number", 3, DataType.Numeric, 17, false);
        Add(dictionary, "410", "Ship To Location", 3, DataType.Numeric, 13, false);
        Add(dictionary, "420", "Ship To Postal Code", 3, DataType.Alphanumeric, 20, true);

        return dictionary;
    }

    private void Add(Dictionary<string, ApplicationIdentifier> dictionary, 
        string code, string description, int lengthOfAI, DataType dataType, int maxDataLength, bool supportsFNC1)
    {
        var ai = ApplicationIdentifier.Create(code, description, lengthOfAI, dataType, maxDataLength, supportsFNC1);
        dictionary[code] = ai;
    }

    /// <summary>
    /// Pomocnicza struktura dla przechowywania sparsowanych danych
    /// </summary>
    private class ParsedGS1Data
    {
        public SSCCCode? SSCC { get; set; }
        public GtinCode? Gtin { get; set; }
        public LotNumber? Lot { get; set; }
        public ExpiryDate? ExpiryDate { get; set; }
        public Quantity? Quantity { get; set; }
    }
}
