using Wms.Application.DTOs.Inventory;
using Wms.Domain.Entities.Inventory;

namespace Wms.Application.Interfaces;

/// <summary>
/// Interfejs repozytorium dla operacji inwentaryzacji
/// </summary>
public interface IInventoryRepository
{
    // Read-only queries (AsNoTracking)
    Task<IEnumerable<InventoryDto>> GetActiveInventoriesAsync(
        InventoryType? type = null, 
        int limit = 40, 
        CancellationToken cancellationToken = default);
    
    Task<InventoryEntity?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    
    Task<InventoryItemEntity?> SearchLabelAsync(
        int inventoryId, 
        string? etykietaId, 
        string? nrSap, 
        string? paletaId, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<InventoryEntity>> GetInventoryItemsAsync(
        int inventoryId,
        int? pracownikId = null,
        bool onlyCompleted = false,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<InventoryEntity>> GetInventoryItemsAsync(
        int inventoryId,
        int systemId,
        bool onlyCompleted = false);

    Task<InventoryEntity?> FindByEtykietaIdAsync(
        int inventoryId,
        string etykietaId,
        int systemId);
    
    Task<InventoryProgressDto> GetInventoryProgressAsync(
        int inventoryId, 
        CancellationToken cancellationToken = default);

    // Sesje inwentaryzacji
    Task<InventorySessionEntity?> GetSessionAsync(
        int sessionId,
        CancellationToken cancellationToken = default);

    // Write operations
    Task<InventorySessionEntity> CreateSessionAsync(
        int inventoryId, 
        int pracownikId, 
        string deviceId, 
        CancellationToken cancellationToken = default);
    
    Task<InventoryItemEntity> CreateInventoryItemAsync(
        int inventoryId,
        int pracownikId,
        string? etykietaId,
        string? paletaId,
        string? kod,
        decimal iloscSpisana,
        int hala,
        int regal,
        int miejsce,
        int poziom,
        string? podkod,
        string? skan,
        string? nrSap,
        CancellationToken cancellationToken = default);
    
    Task<InventoryItemEntity> UpdateInventoryItemAsync(
        int inventoryItemId,
        decimal iloscSpisana,
        int pracownikId,
        int hala,
        int regal,
        int miejsce,
        int poziom,
        string? podkod,
        string? skan,
        CancellationToken cancellationToken = default);
    
    Task<bool> EndSessionAsync(
        int sessionId, 
        CancellationToken cancellationToken = default);

    // Location change tracking
    Task CreateLocationChangeAsync(
        string etykietaId,
        int pracownikId,
        string stareMiejsce,
        string noweMiejsce,
        int systemId,
        CancellationToken cancellationToken = default);
    
    // Operations logging
    Task CreateOperationLogAsync(
        string etykietaId,
        int pracownikId,
        string docNr,
        int systemId,
        string wozek,
        int operacId,
        CancellationToken cancellationToken = default);
}
