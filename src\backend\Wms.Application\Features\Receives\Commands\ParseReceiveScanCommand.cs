using MediatR;
using Wms.Application.DTOs;

namespace Wms.Application.Features.Receives.Commands;

/// <summary>
/// Komenda do parsowania skanu w kontek<PERSON>cie rejestracji dostaw
/// </summary>
public record ParseReceiveScanCommand : IRequest<ParseReceiveScanResponse>
{
    public string ScanData { get; init; } = string.Empty;
    public int? ListControlId { get; init; }
    public int UserId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public string? Context { get; init; } // Opcjonalny kontekst (np. "delivery")
}

/// <summary>
/// Odpowiedź na parsowanie skanu
/// </summary>
public record ParseReceiveScanResponse
{
    public bool IsSuccess { get; init; }
    public string RawScanData { get; init; } = string.Empty;
    public string ScanType { get; init; } = string.Empty;
    public string? ErrorMessage { get; init; }
    
    // GS1 parsed data
    public bool HasIzPrefix { get; init; }
    public GS1ScanData? ParsedData { get; init; }
    
    // Form suggestion data
    public ReceiveFormSuggestion? FormSuggestion { get; init; }
    
    /// <summary>
    /// Tworzy odpowiedź sukcesu
    /// </summary>
    public static ParseReceiveScanResponse Success(
        string rawData, 
        string scanType,
        bool hasIzPrefix = false,
        GS1ScanData? parsedData = null,
        ReceiveFormSuggestion? formSuggestion = null)
    {
        return new ParseReceiveScanResponse
        {
            IsSuccess = true,
            RawScanData = rawData,
            ScanType = scanType,
            HasIzPrefix = hasIzPrefix,
            ParsedData = parsedData,
            FormSuggestion = formSuggestion
        };
    }
    
    /// <summary>
    /// Tworzy odpowiedź błędu
    /// </summary>
    public static ParseReceiveScanResponse Failure(string rawData, string scanType, string errorMessage)
    {
        return new ParseReceiveScanResponse
        {
            IsSuccess = false,
            RawScanData = rawData,
            ScanType = scanType,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Sparsowane dane GS1 w formacie DTO
/// </summary>
public record GS1ScanData
{
    public string? SSCC { get; init; }
    public string? GTIN { get; init; }
    public string? Lot { get; init; }
    public string? ExpiryDate { get; init; } // Format: yyyy-MM-dd
    public decimal? Quantity { get; init; }
    public int ParsedFieldsCount { get; init; }
    
    /// <summary>
    /// Tworzy z Domain Value Objects
    /// </summary>
    public static GS1ScanData FromDomain(Domain.ValueObjects.GS1ParseResult gs1Result)
    {
        return new GS1ScanData
        {
            SSCC = gs1Result.SSCC?.Value,
            GTIN = gs1Result.Gtin?.Value,
            Lot = gs1Result.Lot?.Value,
            ExpiryDate = gs1Result.ExpiryDate?.DisplayFormat,
            Quantity = gs1Result.Quantity?.Value,
            ParsedFieldsCount = gs1Result.ParsedFieldsCount
        };
    }
}

/// <summary>
/// Sugestie wypełnienia formularza na podstawie skanu
/// </summary>
public record ReceiveFormSuggestion
{
    public int? KodId { get; init; }           // ID kodu z bazy danych
    public string? KodValue { get; init; }     // Wartość kodu (EAN)
    public string? KodNazwa { get; init; }     // Nazwa produktu
    public string? Lot { get; init; }          // Numer partii
    public string? ExpiryDate { get; init; }   // Data ważności (yyyy-MM-dd)
    public decimal? SuggestedQuantity { get; init; }  // Ilość po przeliczeniu przez packaging unit
    public int? PackagingUnit { get; init; }  // Jednostka opakowania z bazy
    public bool IsFromAdvice { get; init; }   // Czy dane pochodzą z awizacji
    public string? WarningMessage { get; init; } // Ostrzeżenia (np. produkt wygasły)
    
    public bool HasSuggestions => KodId.HasValue || !string.IsNullOrEmpty(Lot) || !string.IsNullOrEmpty(ExpiryDate);
}
