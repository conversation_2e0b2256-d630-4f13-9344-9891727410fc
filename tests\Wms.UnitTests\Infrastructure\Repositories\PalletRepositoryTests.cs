using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class PalletRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly PalletRepository _palletRepository;

    public PalletRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _palletRepository = new PalletRepository(_context);

        SeedTestData();
    }

    [Fact]
    public async Task CreatePalletAsync_WithValidPalletType_ShouldCreatePallet()
    {
        // Arrange
        var palletTypeId = 1;

        // Act
        var result = await _palletRepository.CreatePalletAsync(palletTypeId);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BeGreaterThan(0);
        result.TypypaletId.Should().Be(palletTypeId);
        result.Ilosc.Should().Be(0);
        result.TsUtworzenia.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(5));

        // Verify it's saved in database
        var palletInDb = await _context.Pallets.FindAsync(result.Id);
        palletInDb.Should().NotBeNull();
        palletInDb!.TypypaletId.Should().Be(palletTypeId);
    }

    [Fact]
    public async Task CreateListControlPalletAsync_WithNewCombination_ShouldCreateCarrier()
    {
        // Arrange
        var listControlId = 1;
        var paletaId = 999; // New pallet ID
        var shouldPrint = true;

        // Act
        var result = await _palletRepository.CreateListControlPalletAsync(listControlId, paletaId, shouldPrint);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BeGreaterThan(0);
        result.ListcontrolId.Should().Be(listControlId);
        result.PaletaId.Should().Be(paletaId);
        result.Wydruk.Should().Be(1);

        // Verify it's saved in database
        var carrierInDb = await _context.ListControlPallets.FindAsync(result.Id);
        carrierInDb.Should().NotBeNull();
        carrierInDb!.ListcontrolId.Should().Be(listControlId);
        carrierInDb.PaletaId.Should().Be(paletaId);
    }

    [Fact]
    public async Task CreateListControlPalletAsync_WithExistingCombination_ShouldReturnExisting()
    {
        // Arrange
        var listControlId = 1;
        var paletaId = 100; // Existing combination in test data
        var shouldPrint = false;

        // Act
        var result = await _palletRepository.CreateListControlPalletAsync(listControlId, paletaId, shouldPrint);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(1); // Should return existing record
        result.ListcontrolId.Should().Be(listControlId);
        result.PaletaId.Should().Be(paletaId);
        result.Wydruk.Should().Be(1); // Should keep original value

        // Verify no duplicate was created
        var carriersCount = await _context.ListControlPallets
            .CountAsync(lcp => lcp.ListcontrolId == listControlId && lcp.PaletaId == paletaId);
        carriersCount.Should().Be(1);
    }

    [Fact]
    public async Task CreateListControlPalletAsync_WithShouldPrintFalse_ShouldSetWydrukToZero()
    {
        // Arrange
        var listControlId = 2;
        var paletaId = 998;
        var shouldPrint = false;

        // Act
        var result = await _palletRepository.CreateListControlPalletAsync(listControlId, paletaId, shouldPrint);

        // Assert
        result.Should().NotBeNull();
        result.Wydruk.Should().Be(0);
    }

    [Fact]
    public async Task GetCarriersForReceiveAsync_WithValidReceiveId_ShouldReturnCarriers()
    {
        // Arrange
        var listControlId = 1;

        // Act
        var result = await _palletRepository.GetCarriersForReceiveAsync(listControlId);

        // Assert
        result.Should().NotBeNull();
        var carriersList = result.ToList();
        carriersList.Should().HaveCount(2);
        carriersList.Should().Contain(c => c.PaletaId == 100);
        carriersList.Should().Contain(c => c.PaletaId == 101);
        carriersList.Should().AllSatisfy(c => c.ListcontrolId.Should().Be(listControlId));

        // Should be ordered by PaletaId
        carriersList[0].PaletaId.Should().BeLessThanOrEqualTo(carriersList[1].PaletaId);
    }

    [Fact]
    public async Task GetCarriersForReceiveAsync_WithInvalidReceiveId_ShouldReturnEmptyCollection()
    {
        // Arrange
        var invalidListControlId = 999;

        // Act
        var result = await _palletRepository.GetCarriersForReceiveAsync(invalidListControlId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetCarrierAsync_WithValidCombination_ShouldReturnCarrier()
    {
        // Arrange
        var listControlId = 1;
        var paletaId = 100;

        // Act
        var result = await _palletRepository.GetCarrierAsync(listControlId, paletaId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.ListcontrolId.Should().Be(listControlId);
        result.PaletaId.Should().Be(paletaId);
        result.ListControl.Should().NotBeNull();
    }

    [Fact]
    public async Task GetCarrierAsync_WithInvalidCombination_ShouldReturnNull()
    {
        // Arrange
        var invalidListControlId = 999;
        var invalidPaletaId = 999;

        // Act
        var result = await _palletRepository.GetCarrierAsync(invalidListControlId, invalidPaletaId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetCarrierItemsAsync_WithValidPaletaId_ShouldReturnLabels()
    {
        // Arrange
        var paletaId = 1;

        // Act
        var result = await _palletRepository.GetCarrierItemsAsync(paletaId);

        // Assert
        result.Should().NotBeNull();
        var labelsList = result.ToList();
        labelsList.Should().HaveCount(2);
        labelsList.Should().AllSatisfy(l => l.PaletaId.Should().Be(paletaId));

        // Should be ordered by Id, then SystemId
        labelsList[0].Id.Should().BeLessThanOrEqualTo(labelsList[1].Id);
    }

    [Fact]
    public async Task GetCarrierItemsAsync_WithInvalidPaletaId_ShouldReturnEmptyCollection()
    {
        // Arrange
        var invalidPaletaId = 999;

        // Act
        var result = await _palletRepository.GetCarrierItemsAsync(invalidPaletaId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task UpdateAsync_WithValidPallet_ShouldUpdatePallet()
    {
        // Arrange
        var pallet = await _context.Pallets.FirstAsync();
        var originalQuantity = pallet.Ilosc;
        var newQuantity = originalQuantity + 10;
        pallet.Ilosc = newQuantity;

        // Act
        await _palletRepository.UpdateAsync(pallet);

        // Assert
        var updatedPallet = await _context.Pallets.FindAsync(pallet.Id);
        updatedPallet.Should().NotBeNull();
        updatedPallet!.Ilosc.Should().Be(newQuantity);
    }

    [Fact]
    public async Task UpdateListControlPalletAsync_WithValidCarrier_ShouldUpdateCarrier()
    {
        // Arrange
        var carrier = await _context.ListControlPallets.FirstAsync();
        var originalWydruk = carrier.Wydruk;
        var newWydruk = originalWydruk == 1 ? 0 : 1;
        carrier.Wydruk = newWydruk;

        // Act
        await _palletRepository.UpdateListControlPalletAsync(carrier);

        // Assert
        var updatedCarrier = await _context.ListControlPallets.FindAsync(carrier.Id);
        updatedCarrier.Should().NotBeNull();
        updatedCarrier!.Wydruk.Should().Be(newWydruk);
    }

    [Theory]
    [InlineData(1, true, 1)]
    [InlineData(2, false, 0)]
    [InlineData(3, true, 1)]
    public async Task CreateListControlPalletAsync_WithDifferentPrintSettings_ShouldSetCorrectWydukValue(
        int palletTypeId, bool shouldPrint, int expectedWydruk)
    {
        // Arrange
        var listControlId = 2;
        var paletaId = 900 + palletTypeId; // Unique pallet ID

        // Act
        var result = await _palletRepository.CreateListControlPalletAsync(listControlId, paletaId, shouldPrint);

        // Assert
        result.Should().NotBeNull();
        result.Wydruk.Should().Be(expectedWydruk);
    }

    [Fact]
    public async Task GetCarriersForReceiveAsync_WithMultipleCarriers_ShouldReturnInCorrectOrder()
    {
        // Arrange - Add more carriers with different PaletaIds
        var additionalCarriers = new[]
        {
            new ListControlPallet
            {
                Id = 10,
                ListcontrolId = 2,
                PaletaId = 50, // Lower number
                Wydruk = 1
            },
            new ListControlPallet
            {
                Id = 11,
                ListcontrolId = 2,
                PaletaId = 200, // Higher number
                Wydruk = 1
            },
            new ListControlPallet
            {
                Id = 12,
                ListcontrolId = 2,
                PaletaId = 150, // Middle number
                Wydruk = 0
            }
        };
        _context.ListControlPallets.AddRange(additionalCarriers);
        await _context.SaveChangesAsync();

        // Act
        var result = await _palletRepository.GetCarriersForReceiveAsync(2);

        // Assert
        result.Should().NotBeNull();
        var carriersList = result.ToList();
        carriersList.Should().HaveCount(3);
        
        // Verify ordering by PaletaId
        carriersList[0].PaletaId.Should().Be(50);
        carriersList[1].PaletaId.Should().Be(150);
        carriersList[2].PaletaId.Should().Be(200);
    }

    private void SeedTestData()
    {
        // Dodaj typy palet
        var palletTypes = new[]
        {
            new TypyPalet
            {
                Id = 1,
                Kod = "EUR",
                Opis = "Paleta EUR"
            },
            new TypyPalet
            {
                Id = 2,
                Kod = "US",
                Opis = "Paleta US"
            }
        };
        _context.TypyPalets.AddRange(palletTypes);

        // Dodaj palety
        var pallets = new[]
        {
            new Pallet
            {
                Id = 1,
                TypypaletId = 1,
                Ilosc = 10,
                TsUtworzenia = DateTime.Now.AddDays(-1)
            },
            new Pallet
            {
                Id = 2,
                TypypaletId = 2,
                Ilosc = 5,
                TsUtworzenia = DateTime.Now.AddDays(-2)
            }
        };
        _context.Pallets.AddRange(pallets);

        // Dodaj lokalizacje
        var locations = new[]
        {
            new Location
            {
                Id = 1,
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "01",
                Wysokosc = "1",
                Widoczne = 1,
                MaxPojemnosc = 100
            },
            new Location
            {
                Id = 2,
                Hala = 1,
                Regal = "A",
                Miejsce = 2,
                Poziom = "01",
                Wysokosc = "1",
                Widoczne = 1,
                MaxPojemnosc = 100
            }
        };
        _context.Locations.AddRange(locations);

        // Dodaj dostawy
        var listControls = new[]
        {
            new ListControl
            {
                Id = 1,
                Numer = 1001,
                Data = DateOnly.FromDateTime(DateTime.Now),
                Transport = "Test Transport 1",
                MiejsceId = 1
            },
            new ListControl
            {
                Id = 2,
                Numer = 1002,
                Data = DateOnly.FromDateTime(DateTime.Now),
                Transport = "Test Transport 2",
                MiejsceId = 2
            }
        };
        _context.ListControls.AddRange(listControls);

        // Dodaj nośniki (ListControlPallets)
        var listControlPallets = new[]
        {
            new ListControlPallet
            {
                Id = 1,
                ListcontrolId = 1,
                PaletaId = 100,
                Wydruk = 1
            },
            new ListControlPallet
            {
                Id = 2,
                ListcontrolId = 1,
                PaletaId = 101,
                Wydruk = 1
            }
        };
        _context.ListControlPallets.AddRange(listControlPallets);

        // Dodaj etykiety
        var labels = new[]
        {
            new Label
            {
                Id = 1,
                SystemId = 1,
                EtykietaKlient = "LABEL001",
                Magazyn = 1,
                PaletaId = 1,
                Miejscep = 1,
                Ilosc = 10,
                Ts = DateTime.Now
            },
            new Label
            {
                Id = 2,
                SystemId = 1,
                EtykietaKlient = "LABEL002",
                Magazyn = 1,
                PaletaId = 1,
                Miejscep = 2,
                Ilosc = 15,
                Ts = DateTime.Now
            }
        };
        _context.Labels.AddRange(labels);

        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
