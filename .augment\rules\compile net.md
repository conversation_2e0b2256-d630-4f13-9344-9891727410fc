---
type: "always_apply"
---

Do kompilowania używaj konfiguracji Debug.

Je<PERSON><PERSON> uruchamiasz aplikację apliakcję backend za polocą bash:
wykorzystaj do tego poniższe dane:





DISABLE_FLUENT_VALIDATION=1 ASPNETCORE_URLS=http://127.0.0.1:8081 ASPNETCORE_ENVIRONMENT=Development dotnet run --project src/backend/Wms.Api/Wms.Api.csproj --no-launch-profile

<PERSON><PERSON><PERSON> testu<PERSON> endpointy to posłuż się nr karty: 1234567
i używaj serwera proxy na porcie 8080
http://127.0.0.1:8080/api/v1/auth/login-scan

JSON: {
  "cardNumber": "1234567",
  "deviceId": "Emulator"
}

NIe używaj do testowania mock ale backend przez proxy.
Proxy moż<PERSON>z uruchomić przez: python api-proxy-local.py