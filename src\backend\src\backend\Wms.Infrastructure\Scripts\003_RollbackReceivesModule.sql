-- =================================================================
-- 003_RollbackReceivesModule.sql  
-- Rollback migracji modułu dostaw
-- Data: 2025-09-09
-- Autor: WMS System Development Team
-- OSTRZEŻENIE: Ten skrypt usuwa dane! Użyj ostrożnie!
-- =================================================================

-- ==========================================
-- INFORMACJA O ROLLBACK
-- ==========================================

SELECT 
    'UWAGA: ROZPOCZYNANIE ROLLBACK MODUŁU DOSTAW' as warning,
    'Ten proces usunie dane z claim dostaw!' as risk,
    NOW() as started_at;

-- ==========================================
-- 1. SPRAWDZENIE CZY JEST COKOLWIEK DO ROLLBACK
-- ==========================================

SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND COLUMN_NAME = 'realizujacy_pracownik_id'
);

-- ==========================================
-- 2. BACKUP DANYCH PRZED ROLLBACK (opcjonalnie)
-- ==========================================

-- Jeśli chcesz zachować dane o claim, skopiuj je przed rollback:
-- CREATE TABLE list_control_realizujacy_backup AS 
-- SELECT id, realizujacy_pracownik_id FROM list_control 
-- WHERE realizujacy_pracownik_id IS NOT NULL;

-- ==========================================  
-- 3. USUNIĘCIE FOREIGN KEY CONSTRAINT
-- ==========================================

SET @fk_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND CONSTRAINT_NAME = 'fk_list_control_realizujacy_pracownik'
);

SET @sql = CASE 
    WHEN @fk_exists > 0 THEN 
        'ALTER TABLE list_control DROP FOREIGN KEY fk_list_control_realizujacy_pracownik'
    ELSE 'SELECT "Foreign key już nie istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 4. USUNIĘCIE INDEKSU
-- ==========================================

SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND INDEX_NAME = 'idx_realizujacy_pracownik'
);

SET @sql = CASE 
    WHEN @index_exists > 0 THEN 
        'DROP INDEX idx_realizujacy_pracownik ON list_control'
    ELSE 'SELECT "Indeks już nie istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;  
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 5. USUNIĘCIE KOLUMNY realizujacy_pracownik_id
-- ==========================================

SET @sql = CASE 
    WHEN @column_exists > 0 THEN 
        'ALTER TABLE list_control DROP COLUMN realizujacy_pracownik_id'
    ELSE 'SELECT "Kolumna już nie istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 6. WERYFIKACJA ROLLBACK
-- ==========================================

SELECT 
    'ROLLBACK VERIFICATION' as step,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'list_control' 
            AND COLUMN_NAME = 'realizujacy_pracownik_id'
        ) THEN 'OK - Kolumna realizujacy_pracownik_id została usunięta'
        ELSE 'ERROR - Kolumna realizujacy_pracownik_id nadal istnieje'
    END as column_status,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'list_control' 
            AND INDEX_NAME = 'idx_realizujacy_pracownik'
        ) THEN 'OK - Indeks został usunięty'
        ELSE 'ERROR - Indeks nadal istnieje'
    END as index_status,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'list_control' 
            AND CONSTRAINT_NAME = 'fk_list_control_realizujacy_pracownik'
        ) THEN 'OK - Foreign key został usunięty'
        ELSE 'ERROR - Foreign key nadal istnieje'
    END as fk_status;

-- ==========================================
-- 7. INFORMACJA O ZAKOŃCZENIU ROLLBACK
-- ==========================================

SELECT 
    'ROLLBACK 003 ZAKOŃCZONY' as status,
    NOW() as completed_at,
    'Moduł dostaw został usunięty z bazy danych' as message,
    'docnumber dla nrpalety pozostawiony bez zmian' as note;
