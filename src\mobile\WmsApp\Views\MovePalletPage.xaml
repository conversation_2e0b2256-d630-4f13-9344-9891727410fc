<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.MovePalletPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:MovePalletViewModel"
             Title="{loc:Translate Key=MovePallet_Title}"
             BackgroundColor="{DynamicResource PageBackgroundColor}">


    <ScrollView>
        <VerticalStackLayout Spacing="25" Padding="20">
            
            <!-- Header z aktualnym krokiem -->
            <Frame BackgroundColor="{DynamicResource PrimaryColor}" 
                   CornerRadius="12" 
                   Padding="15"
                   HasShadow="True">
                <Label Text="{Binding CurrentStepTitle}"
                       FontSize="20"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center" />
            </Frame>

            <!-- Instrukcja -->
            <Label Text="{Binding CurrentInstruction}"
                   FontSize="16"
                   HorizontalOptions="Center"
                   HorizontalTextAlignment="Center"
                   TextColor="{DynamicResource PrimaryTextColor}" />

            <!-- Informacje o palecie (widoczne po zeskanowaniu palety) -->
            <Frame IsVisible="{Binding IsPalletInfoVisible}"
                   BackgroundColor="{DynamicResource SecondaryColor}"
                   CornerRadius="8"
                   Padding="15"
                   HasShadow="True">
                <VerticalStackLayout Spacing="10">
<Label Text="{loc:Translate Key=MovePallet_PalletInfo_Header}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="White" />
                    <Label Text="{Binding ScannedPalletCode, StringFormat='Kod palety: {0}'}"
                           FontSize="14"
                           TextColor="White" />
                    <Label Text="{Binding CurrentPalletLocation, StringFormat='Obecna lokalizacja: {0}'}"
                           FontSize="14"
                           TextColor="White" />
                </VerticalStackLayout>
            </Frame>

            <!-- Input dla kodów (opcjonalny, jeśli skanowanie nie działa) -->
            <Frame BackgroundColor="White"
                   CornerRadius="8"
                   Padding="0"
                   HasShadow="True">
                <Entry Placeholder="Lub wpisz kod ręcznie..."
                       Text="{Binding ManualCodeInput}"
                       FontSize="16"
                       Margin="15,10" />
            </Frame>

            <!-- Przyciski akcji -->
            <VerticalStackLayout Spacing="15">
                
                <!-- Przycisk przetwórz ręczny kod -->
<Button Text="{loc:Translate Key=Common_ConfirmCode}"
                        FontSize="16"
                        HeightRequest="50"
                        BackgroundColor="{DynamicResource PrimaryColor}"
                        TextColor="White"
                        CornerRadius="8"
                        Command="{Binding ProcessManualCodeCommand}"
                        IsVisible="{Binding IsManualInputVisible}" />

                <!-- Anuluj -->
                <Button Text="Anuluj"
                        FontSize="16"
                        HeightRequest="50"
                        BackgroundColor="{DynamicResource DangerColor}"
                        TextColor="White"
                        CornerRadius="8"
                        Command="{Binding CancelCommand}" />

            </VerticalStackLayout>

            <!-- Status / komunikaty -->
            <Frame IsVisible="{Binding IsMessageVisible}"
                   BackgroundColor="{Binding MessageBackgroundColor}"
                   CornerRadius="8"
                   Padding="15"
                   HasShadow="True">
                <Label Text="{Binding StatusMessage}"
                       FontSize="14"
                       TextColor="White"
                       HorizontalOptions="Center"
                       HorizontalTextAlignment="Center" />
            </Frame>

            <!-- Loading indicator -->
            <ActivityIndicator IsVisible="{Binding IsLoading}"
                               IsRunning="{Binding IsLoading}"
                               Color="{DynamicResource PrimaryColor}"
                               HeightRequest="40" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
