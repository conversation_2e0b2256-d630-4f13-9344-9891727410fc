using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;

namespace Wms.Application.Services;

public interface IPalletService
{
    Task<MovePalletResponse> MovePalletAsync(MovePalletRequest request, int userId, string? deviceId = null, string? ipAddress = null);
    Task<PalletInfo?> GetPalletInfoAsync(string palletCode);
    Task<LocationInfo?> GetLocationInfoAsync(string locationCode);
    Task<Label?> FindLabelByCodeAsync(string code);
}

public class PalletService : IPalletService
{
    private readonly ICodeValidationService _codeValidationService;
    private readonly ILocationRepository _locationRepository;
    private readonly ILabelRepository _labelRepository;
    private readonly IMovementRepository _movementRepository;
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PalletService> _logger;

    public PalletService(
        ICodeValidationService codeValidationService,
        ILocationRepository locationRepository,
        ILabelRepository labelRepository,
        IMovementRepository movementRepository,
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<PalletService> logger)
    {
        _codeValidationService = codeValidationService;
        _locationRepository = locationRepository;
        _labelRepository = labelRepository;
        _movementRepository = movementRepository;
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<MovePalletResponse> MovePalletAsync(MovePalletRequest request, int userId, string? deviceId = null, string? ipAddress = null)
    {
        // Walidacja kodu palety
        if (!_codeValidationService.ValidateSSCC(request.PalletCode) && 
            !_codeValidationService.ValidateDS(request.PalletCode))
        {
            throw new ArgumentException($"Nierozpoznany format kodu palety: {request.PalletCode}");
        }

        // Walidacja kodu lokalizacji
        if (!_codeValidationService.ValidateLocationCode(request.ToLocationCode))
        {
            throw new ArgumentException($"Nieprawidłowy format kodu lokalizacji: {request.ToLocationCode}");
        }

        Label? label = null;
        Location? currentLocation = null;
        Location? targetLocation = null;
        Movement? movement = null;
        User? user = null;
        
        var allLabels = new List<Label>();
        var movements = new List<Movement>();
        
        await _unitOfWork.ExecuteInTransactionAsync(async () =>
        {
            // 1. Znalezienie etykiety po kodzie (SSCC lub DS)
            label = await FindLabelByCodeAsync(request.PalletCode);
            if (label == null)
            {
                throw new KeyNotFoundException($"Paleta {request.PalletCode} nie została znaleziona w systemie");
            }

            // 2. Sprawdzenie czy paleta istnieje (zgodnie z PRD v0.2 - brak auto-tworzenia)
            if (label.Pallet == null)
            {
                throw new InvalidOperationException($"Paleta {request.PalletCode} nie ma przypisanej palety fizycznej");
            }

            // 3. Pobierz wszystkie aktywne etykiety na palecie (active=1)
            allLabels = (await _labelRepository.GetActiveByPalletIdAsNoTrackingAsync(label.Pallet.Id)).ToList();
            if (allLabels.Count == 0)
            {
                throw new InvalidOperationException($"Paleta {request.PalletCode} nie ma żadnych aktywnych etykiet");
            }

            // 4. Odczyt bieżącej lokalizacji (z pierwszej etykiety)
            currentLocation = allLabels.First().Location;
            if (currentLocation == null)
            {
                throw new InvalidOperationException($"Paleta {request.PalletCode} nie ma przypisanej lokalizacji");
            }

            // 5. Walidacja docelowej lokalizacji
            targetLocation = await _locationRepository.GetByCodeAsync(request.ToLocationCode);
            if (targetLocation == null)
            {
                throw new KeyNotFoundException($"Lokalizacja docelowa {request.ToLocationCode} nie została znaleziona");
            }

            if (!targetLocation.IsVisible)
            {
                throw new InvalidOperationException($"Lokalizacja docelowa {request.ToLocationCode} jest niedostępna");
            }

            // 6. Sprawdzenie pojemności lokalizacji (opcjonalnie)
            if (targetLocation.MaxPojemnosc > 0)
            {
                var currentCount = await _locationRepository.GetCurrentPalletCountAsync(targetLocation.Id);
                if (currentCount >= targetLocation.MaxPojemnosc)
                {
                    throw new InvalidOperationException($"Lokalizacja docelowa {request.ToLocationCode} osiągnęła maksymalną pojemność");
                }
            }

            // 7. Utworzenie rekordu ruchu dla każdej aktywnej etykiety na palecie
            var currentTime = DateTime.UtcNow;
            var currentDate = DateOnly.FromDateTime(currentTime);
            
            foreach (var labelToMove in allLabels)
            {
                var movementForLabel = new Movement
                {
                    Typ = "ZM", // Typ ruchu - Zmiana Miejsca
                    DocNr = 0, // Może być generowany później
                    PracownikId = userId,
                    Data = currentDate,
                    Etykieta = labelToMove.Id,
                    SystemId = labelToMove.SystemId,
                    StareM = currentLocation.Id,
                    NoweM = targetLocation.Id,
                    DocInternal = "Z", // Z - Zmiana
                    Stat = 1, // Status aktywny
                    Tszm = currentTime,
                    Uwagi = request.Notes ?? string.Empty
                };
                
                movements.Add(movementForLabel);
                await _movementRepository.CreateAsync(movementForLabel);
            }
            
            // Zapisz pierwszy ruch jako główny (dla zwracania w response)
            movement = movements.FirstOrDefault();

            // 8. Aktualizacja lokalizacji dla wszystkich aktywnych etykiet na palecie
            _logger.LogDebug("About to update {ActiveLabelCount} active labels to location {TargetLocationId}", allLabels.Count, targetLocation.Id);
            foreach (var labelToMove in allLabels)
            {
                _logger.LogDebug("Processing label {LabelId}, systemId: {SystemId}, currentLocation: {CurrentLocation}", 
                    labelToMove.Id, labelToMove.SystemId, labelToMove.Miejscep);
                // Aktualizacja etykiety.miejscep po id/systemId z obsługą istniejącego śledzenia w kontekście
                await _labelRepository.UpdateLocationAsync(labelToMove.Id, labelToMove.SystemId, targetLocation.Id);
            }
            
            // 9. Zapisanie wszystkich zmian w ramach transakcji
            await _unitOfWork.SaveChangesAsync();
        });
        
        _logger.LogInformation("Pallet {PalletCode} with {ActiveLabelCount} active labels moved from {FromLocation} to {ToLocation} by user {UserId}. Created {MovementCount} movement records.", 
            request.PalletCode, allLabels.Count, currentLocation!.Code, targetLocation!.Code, userId, movements.Count);
        
        // 10. Weryfikacja po-commitowa (miejscep vs nowe_m)
        var verifyLabels = (await _labelRepository.GetActiveByPalletIdAsNoTrackingAsync(label!.Pallet!.Id)).ToList();
        foreach (var vLabel in verifyLabels)
        {
            var isOk = vLabel.Miejscep == targetLocation!.Id;
            if (!isOk)
            {
                _logger.LogError("Post-commit mismatch for label {LabelId}/{SystemId}: miejscep={Miejscep} != targetLocationId={TargetLocationId}",
                    vLabel.Id, vLabel.SystemId, vLabel.Miejscep, targetLocation!.Id);
            }
            else
            {
                _logger.LogDebug("Post-commit verified for label {LabelId}/{SystemId}: miejscep={Miejscep}", vLabel.Id, vLabel.SystemId, vLabel.Miejscep);
            }
        }

        user = await _userRepository.GetByIdAsync(userId);

        return new MovePalletResponse
        {
            PalletCode = request.PalletCode,
            FromLocationCode = currentLocation.Code,
            ToLocationCode = targetLocation.Code,
            MovementTime = movement!.Tszm,
            MovedBy = user?.ImieNazwisko ?? "Unknown",
            MovementId = movement.Id
        };
    }

    public async Task<PalletInfo?> GetPalletInfoAsync(string palletCode)
    {
        var label = await FindLabelByCodeAsync(palletCode);
        if (label?.Pallet == null)
            return null;

        var allLabels = await _labelRepository.GetActiveByPalletIdAsNoTrackingAsync(label.Pallet.Id);
        var lastMovement = await _movementRepository.GetLastMovementForLabelAsync(label.Id);

        return new PalletInfo
        {
            Id = label.Pallet.Id,
            MainSSCC = label.Pallet.GetMainSSCC(),
            CurrentLocation = label.Location != null ? new LocationInfo
            {
                Id = label.Location.Id,
                Code = label.Location.Code,
                Hala = label.Location.Hala,
                Regal = label.Location.Regal,
                Miejsce = label.Location.Miejsce,
                Poziom = label.Location.Poziom,
                IsVisible = label.Location.IsVisible,
                IsPickingLocation = label.Location.IsPickingLocation,
                MaxCapacity = (int)label.Location.MaxPojemnosc,
                CurrentPalletCount = await _locationRepository.GetCurrentPalletCountAsync(label.Location.Id)
            } : null,
            LastMovementAt = lastMovement?.Tszm,
            Labels = allLabels.Select(l => new LabelInfo
            {
                Id = l.Id,
                SSCC = l.Sscc,
                ClientLabel = l.EtykietaKlient,
                Quantity = l.Ilosc,
                ExpiryDate = l.DataWaznosci,
                Batch = l.Lot,
                IsActive = l.IsActive
            }).ToList()
        };
    }

    public async Task<LocationInfo?> GetLocationInfoAsync(string locationCode)
    {
        var location = await _locationRepository.GetByCodeAsync(locationCode);
        if (location == null)
            return null;

        var currentPalletCount = await _locationRepository.GetCurrentPalletCountAsync(location.Id);

        return new LocationInfo
        {
            Id = location.Id,
            Code = location.Code,
            Hala = location.Hala,
            Regal = location.Regal,
            Miejsce = location.Miejsce,
            Poziom = location.Poziom,
            IsVisible = location.IsVisible,
            IsPickingLocation = location.IsPickingLocation,
            MaxCapacity = (int)location.MaxPojemnosc,
            CurrentPalletCount = currentPalletCount
        };
    }

    public async Task<Label?> FindLabelByCodeAsync(string code)
    {
        // Próba znalezienia po SSCC
        if (_codeValidationService.ValidateSSCC(code))
        {
            return await _labelRepository.GetBySSCCAsNoTrackingAsync(code);
        }

        // Próba znalezienia po kodzie DS
        if (_codeValidationService.ValidateDS(code))
        {
            // Wyciągnij numeryczną część z kodu DS (np. ********* -> 3345469)
            var numericPart = code.Substring(2); // Usuń prefiks "DS"
            if (int.TryParse(numericPart, out int palletId))
            {
                // Najpierw szukaj po paleta_id
                var label = await _labelRepository.GetByPalletIdDirectAsync(palletId);
                if (label != null)
                {
                    return label;
                }
            }
            
            // Jeśli nie znaleziono po paleta_id, szukaj po etykieta_klient
            return await _labelRepository.GetByClientCodeAsNoTrackingAsync(code);
        }

        return null;
    }
}
