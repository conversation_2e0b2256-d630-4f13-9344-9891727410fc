using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class Session : BaseEntity
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string DeviceId { get; set; } = null!;
    public string? IpAddress { get; set; }
    public DateTime LoginTime { get; set; } = DateTime.UtcNow;
    public DateTime? LogoutTime { get; set; }
    public bool IsActive { get; set; } = true;
    public string? JwtTokenId { get; set; } // JTI claim z JWT
    
    // Navigation properties
    public User User { get; set; } = null!;
}
