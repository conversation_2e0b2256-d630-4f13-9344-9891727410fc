using MediatR;
using Wms.Application.DTOs.Receives;

namespace Wms.Application.Features.Receives.Commands;

public record ParseGS1ScanCommand : IRequest<GS1ParseResponse>
{
    public int ListControlId { get; init; }
    public int PracownikId { get; init; }
    public string Scan { get; init; } = string.Empty;
}

public record CreateReceiveItemCommand : IRequest<ReceiveItemDto>
{
    public int ListControlId { get; init; }
    public int PracownikId { get; init; }
    public int PaletaId { get; init; }
    public int KodId { get; init; }
    public string? Lot { get; init; }
    public DateOnly? DataProd { get; init; }
    public DateOnly? DataWaznosci { get; init; }
    public decimal Ilosc { get; init; }
    public string? Sscc { get; init; }
    public string? Certyfikat { get; init; }
}

public record CreateCarrierCommand : IRequest<CarrierDto>
{
    public int ListControlId { get; init; }
    public int PracownikId { get; init; }
    public int TypPaletyId { get; init; }
    public bool Drukowac { get; init; }
    public string? DrukarkaIp { get; init; }
}

public record CompleteCarrierCommand : IRequest<bool>
{
    public int ListControlId { get; init; }
    public int PaletaId { get; init; }
    public int PracownikId { get; init; }
}

public record EndReceiveSessionCommand : IRequest<ClaimReceiveResponse>
{
    public int ListControlId { get; init; }
    public int PracownikId { get; init; }
}
