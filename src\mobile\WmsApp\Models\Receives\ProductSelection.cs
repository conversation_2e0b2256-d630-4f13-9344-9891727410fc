namespace WmsApp.Models.Receives;

/// <summary>
/// Uniwersalny wynik wyboru towaru z pickera (awizacja lub kartoteka)
/// </summary>
public record ProductSelection
{
    public int KodId { get; init; }
    public string KodValue { get; init; } = string.Empty;
    public string KodNazwa { get; init; } = string.Empty;

    public SelectionSource Source { get; init; } = SelectionSource.Kartoteka;

    // Dodatkowe informacje przydatne do formularza
    public int? PackagingUnit { get; init; }
    public bool CzyWymaganaPartia { get; init; }
    public bool CzyWymaganaDataWaznosci { get; init; }
}

public enum SelectionSource
{
    Awizacja,
    Kartoteka
}

