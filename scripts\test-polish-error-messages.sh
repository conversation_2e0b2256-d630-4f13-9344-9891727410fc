#!/bin/bash

echo "🧪 Test polskich komunikatów błędów API"
echo "======================================"
echo ""

# Najpierw sprawdzamy czy można uruchomić lokalne API dla testów
echo "ℹ️  Ten test wymaga uruchomienia lokalnej wersji API z nowymi komunikatami"
echo "   Uruchom: dotnet run --project src/backend/Wms.Api/ --urls http://localhost:5000"
echo "   lub użyj wersji produkcyjnej po wdrożeniu zmian"
echo ""

API_BASE="http://localhost:5000/api/v1"
CARD_NUMBER="1234567"

echo "Testowanie z lokalnym API: $API_BASE"
echo "====================================="

# Test 1: Autoryzacja
echo "1️⃣ Autoryzacja..."
AUTH_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -X POST "$API_BASE/auth/login-scan" \
    -d "{\"cardNumber\": \"$CARD_NUMBER\", \"deviceId\": \"test-device\", \"ipAddress\": \"127.0.0.1\"}" 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "❌ Nie można się połączyć z lokalnym API"
    echo "   Uruchom najpierw: dotnet run --project src/backend/Wms.Api/ --urls http://localhost:5000"
    exit 1
fi

TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ Autoryzacja nieudana"
    echo "Response: $AUTH_RESPONSE"
    exit 1
fi

echo "✅ Autoryzacja udana"

# Test 2: Nieistniejąca paleta
echo ""
echo "2️⃣ Test nieistniejącej palety *********..."
PALLET_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -X GET "$API_BASE/pallets/*********" 2>/dev/null)

ERROR_DETAIL=$(echo "$PALLET_RESPONSE" | grep -o '"detail":"[^"]*"' | cut -d'"' -f4)

if echo "$ERROR_DETAIL" | grep -q "********* nie została znaleziona w systemie"; then
    echo "✅ Komunikat błędu zawiera kod palety w języku polskim:"
    echo "   '$ERROR_DETAIL'"
else
    echo "❌ Komunikat błędu nie zawiera oczekiwanego tekstu:"
    echo "   Otrzymano: '$ERROR_DETAIL'"
    echo "   Oczekiwano: 'Paleta ********* nie została znaleziona w systemie'"
fi

# Test 3: Nieprawidłowy format palety
echo ""
echo "3️⃣ Test nieprawidłowego formatu palety INVALID123..."
INVALID_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -X GET "$API_BASE/pallets/INVALID123" 2>/dev/null)

INVALID_DETAIL=$(echo "$INVALID_RESPONSE" | grep -o '"detail":"[^"]*"' | cut -d'"' -f4)

if echo "$INVALID_DETAIL" | grep -q "format kodu"; then
    echo "✅ Komunikat błędu dla nieprawidłowego formatu:"
    echo "   '$INVALID_DETAIL'"
else
    echo "ℹ️  Komunikat błędu dla nieprawidłowego formatu:"
    echo "   '$INVALID_DETAIL'"
fi

echo ""
echo "🎯 Podsumowanie testów:"
echo "======================"
echo "✅ Test polskich komunikatów błędów ukończony"
echo "📝 Komunikaty zawierają konkretne kody palet (np. *********)"
echo "🌐 Po wdrożeniu na serwerze produkcyjnym, komunikaty będą po polsku"
