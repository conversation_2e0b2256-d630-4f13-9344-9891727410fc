using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WmsApp.Models.Pallets;
using WmsApp.Services;

namespace WmsApp.ViewModels;

public partial class PalletsViewModel : ObservableObject
{
    private readonly IWmsApiService _apiService;

    [ObservableProperty]
    private ObservableCollection<PalletInfo> pallets = new();

    [ObservableProperty]
    private PalletInfo? selectedPallet;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private bool isRefreshing;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private string searchText = string.Empty;

    public PalletsViewModel(IWmsApiService apiService)
    {
        _apiService = apiService;
    }

[RelayCommand]
    private async Task LoadPalletsAsync()
    {
        // Upewnij się, że metoda jest asynchroniczna (CS1998)
        await Task.Yield();
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Dla celów demo, stwórzmy kilka przykładowych palet
            var mockPallets = GenerateMockPallets();
            
            Pallets.Clear();
            foreach (var pallet in mockPallets)
            {
                Pallets.Add(pallet);
            }

            // W przyszłości tutaj będzie wywołanie API:
            // var response = await _apiService.GetPalletsAsync();
            // if (response.IsSuccessStatusCode && response.Content != null)
            // {
            //     Pallets.Clear();
            //     foreach (var pallet in response.Content)
            //     {
            //         Pallets.Add(pallet);
            //     }
            // }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Failed to load pallets: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
            IsRefreshing = false;
        }
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        IsRefreshing = true;
        await LoadPalletsAsync();
    }

    [RelayCommand]
    private async Task SelectPalletAsync(PalletInfo? pallet)
    {
        if (pallet == null) return;

        SelectedPallet = pallet;
        
        var parameters = new Dictionary<string, object>
        {
            ["PalletId"] = pallet.Id
        };

        await Shell.Current.GoToAsync("palletdetails", parameters);
    }

    [RelayCommand]
    private async Task SearchPalletsAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadPalletsAsync();
            return;
        }

        try
        {
            IsLoading = true;
            
            var allPallets = GenerateMockPallets();
            var filteredPallets = allPallets
                .Where(p => (p.MainSSCC?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true) ||
                           p.Labels.Any(l => l.SSCC?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true ||
                                           l.ClientLabel?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true))
                .ToList();

            Pallets.Clear();
            foreach (var pallet in filteredPallets)
            {
                Pallets.Add(pallet);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Search failed: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task MovePalletAsync(PalletInfo? pallet)
    {
        if (pallet == null) return;

        // Placeholder for move functionality
        await Shell.Current.DisplayAlert("Move Pallet", $"Move pallet {pallet.MainSSCC} to new location", "OK");
    }

    partial void OnSearchTextChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            _ = LoadPalletsAsync();
        }
    }

    private List<PalletInfo> GenerateMockPallets()
    {
        return new List<PalletInfo>
        {
            new PalletInfo
            {
                Id = 1,
                MainSSCC = "123456789012345678",
                CurrentLocation = new LocationInfo
                {
                    Id = 1,
                    Code = "MP-A01-001-001-001",
                    Hala = 1,
                    Regal = "A01",
                    Miejsce = 1,
                    Poziom = "001",
                    IsVisible = true
                },
                LastMovementAt = DateTime.Now.AddHours(-2),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo
                    {
                        Id = 1,
                        SSCC = "123456789012345678",
                        ClientLabel = "PROD-001-ABC",
                        Quantity = 100,
                        ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(30)),
                        Batch = "B001",
                        IsActive = true
                    }
                }
            },
            new PalletInfo
            {
                Id = 2,
                MainSSCC = "987654321098765432",
                CurrentLocation = new LocationInfo
                {
                    Id = 2,
                    Code = "MP-A01-001-002-001",
                    Hala = 1,
                    Regal = "A01",
                    Miejsce = 2,
                    Poziom = "001",
                    IsVisible = true
                },
                LastMovementAt = DateTime.Now.AddMinutes(-30),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo
                    {
                        Id = 2,
                        SSCC = "987654321098765432",
                        ClientLabel = "PROD-002-XYZ",
                        Quantity = 50,
                        ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(15)),
                        Batch = "B002",
                        IsActive = true
                    },
                    new LabelInfo
                    {
                        Id = 3,
                        SSCC = "111222333444555666",
                        ClientLabel = "PROD-003-DEF",
                        Quantity = 25,
                        ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(45)),
                        Batch = "B003",
                        IsActive = true
                    }
                }
            },
            new PalletInfo
            {
                Id = 3,
                MainSSCC = "555666777888999000",
                CurrentLocation = new LocationInfo
                {
                    Id = 3,
                    Code = "MP-B02-003-001-002",
                    Hala = 2,
                    Regal = "B02",
                    Miejsce = 3,
                    Poziom = "002",
                    IsVisible = true
                },
                LastMovementAt = DateTime.Now.AddDays(-1),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo
                    {
                        Id = 4,
                        SSCC = "555666777888999000",
                        ClientLabel = "PROD-004-GHI",
                        Quantity = 200,
                        ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(60)),
                        Batch = "B004",
                        IsActive = true
                    }
                }
            },
            new PalletInfo
            {
                Id = 4,
                MainSSCC = null, // Paleta bez głównego SSCC
                CurrentLocation = new LocationInfo
                {
                    Id = 4,
                    Code = "MP-C03-005-003-001",
                    Hala = 3,
                    Regal = "C03",
                    Miejsce = 5,
                    Poziom = "001",
                    IsVisible = true
                },
                LastMovementAt = DateTime.Now.AddHours(-6),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo
                    {
                        Id = 5,
                        ClientLabel = "MIXED-PALLET-001",
                        Quantity = 75,
                        ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(20)),
                        Batch = "MX001",
                        IsActive = true
                    }
                }
            }
        };
    }

    public async Task InitializeAsync()
    {
        await LoadPalletsAsync();
    }
}
