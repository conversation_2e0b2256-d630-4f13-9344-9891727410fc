# PRD - <PERSON><PERSON><PERSON> WMS (Product Requirements Document)

## Przegląd Projektu

### Cel Projektu
Implementacja kompletnego modułu dostaw w aplikacji mobilnej WMS (Warehouse Management System) na platformie .NET MAUI, umożliwiającego efektywne zarządzanie procesem przyjęcia towaru do magazynu.

### Zakres Funkcjonalny
- Przeglądanie i zarządzanie dostawami (awizacjami)
- Zajmowanie dostaw (claim/unclaim) przez operatorów
- Generowanie kodów DS (Dokument Spedycyjny) z integracją drukarek
- Rejestracja pozycji towarowych za pomocą skanerów kodów kreskowych
- Obsługa różnych typów kodów: GS1, SSCC, DS
- Zarządzanie nośnikami (palety, kontenery) i ich pozycjami

## Wymagania Funkcjonalne

### RF-001: Przeglądanie Dostaw
**Priorytet:** Wysoki
**Status:** ✅ Zaimplementowane

**Opis:** Operator może przeglądać listę dostępnych dostaw dla swojego magazynu

**Kryteria Akceptacji:**
- ✅ Lista dostaw filtrowana według LK (Listu Księgowego) operatora
- ✅ Wyświetlanie podstawowych informacji: numer awizacji, dostawca, data, status
- ✅ Możliwość odświeżania listy (pull-to-refresh)
- ✅ Obsługa stanów: loading, error, empty state
- ✅ Wyszukiwanie dostaw po numerze awizacji lub nazwie dostawcy

### RF-002: Zarządzanie Claimi Dostaw  
**Priorytet:** Wysoki
**Status:** ✅ Zaimplementowane

**Opis:** Operator może zajmować i zwalniać dostawy aby uniknąć konfliktów

**Kryteria Akceptacji:**
- ✅ Claim dostawy przed rozpoczęciem pracy
- ✅ Automatyczne unclaim przy opuszczeniu modułu
- ✅ Wyświetlanie informacji o operatorze zajmującym dostawę
- ✅ Obsługa błędów konfliktu (409 Conflict) gdy dostawa już zajęta
- ✅ Timeout dla claim (automatyczne zwolnienie po bezczynności)

### RF-003: Generowanie Kodów DS
**Priorytet:** Średni  
**Status:** ✅ Zaimplementowane

**Opis:** Operator może generować kody DS dla nowych palet i wysyłać je do drukarki

**Kryteria Akceptacji:**
- ✅ Modal z konfiguracją generowania kodów DS
- ✅ Wybór typu palety z listy dostępnych typów
- ✅ Wprowadzenie ilości palet do wygenerowania (1-100)
- ✅ Opcjonalny wybór drukarki z listy lub skanowanie IP
- ✅ Opcjonalne drukowanie etykiet od razu po wygenerowaniu
- ✅ Wyświetlanie wygenerowanych kodów DS z opcją kopiowania

### RF-004: Rejestracja Pozycji Towaru
**Priorytet:** Krytyczny
**Status:** ✅ Zaimplementowane

**Opis:** Operator rejestruje pozycje towaru w dostawie używając skanerów

**Kryteria Akceptacji:**
- ✅ Skanowanie kodów GS1 towarów (automatyczne wypełnianie danych)
- ✅ Ręczne wprowadzanie danych towaru z wyszukiwaniem w kartotece
- ✅ Walidacja wymaganych pól: kod towaru, partia, data produkcji
- ✅ Opcjonalne pola: data ważności, certyfikat, stan jakości
- ✅ Wybór typu palety i obliczanie ilości w opakowaniu
- ✅ Podgląd i edycja zarejestrowanych pozycji

### RF-005: Obsługa Skanowania Kodów
**Priorytet:** Krytyczny  
**Status:** ✅ Zaimplementowane

**Opis:** System rozpoznaje i przetwarza różne typy kodów kreskowych

**Kryteria Akceptacji:**
- ✅ Rozpoznawanie kodów GS1 (GTIN + dodatkowe dane)
- ✅ Parsowanie dat ważności z kodów GS1 (format YYMMDD)
- ✅ Obsługa kodów SSCC (20-cyfrowy kod kontenerów)
- ✅ Rozpoznawanie kodów DS (dokumentów spedycyjnych)
- ✅ Walidacja poprawności zeskanowanych kodów
- ✅ Komunikaty błędów dla nieprawidłowych kodów

### RF-006: Zarządzanie Nośnikami
**Priorytet:** Średni
**Status:** ✅ Zaimplementowane

**Opis:** Operator może zarządzać nośnikami (paletami) i ich pozycjami

**Kryteria Akceptacji:**
- ✅ Automatyczne tworzenie nośników przy rejestracji pozycji
- ✅ Skanowanie kodów SSCC istniejących nośników
- ✅ Wyświetlanie pozycji przypisanych do nośnika
- ✅ Możliwość zmiany nośnika dla pozycji
- ✅ Preview wszystkich nośników w sesji rejestracji

## Wymagania Niefunkcjonalne

### RNF-001: Wydajność
**Priorytet:** Wysoki
**Status:** 🟡 Częściowo zaimplementowane

- ✅ Czas ładowania listy dostaw: < 3s
- ✅ Responsywność UI podczas długotrwałych operacji
- 🟡 Cachowanie danych słownikowych (typy palet, stany jakości)
- ❌ Optymalizacja bindingów XAML (compiled bindings)

### RNF-002: Użyteczność (UX)
**Priorytet:** Wysoki  
**Status:** ✅ Zaimplementowane

- ✅ Intuicyjny interface dostosowany do urządzeń mobilnych
- ✅ Automatyczne fokusowanie pola skanera po akcjach
- ✅ Oznaczenia kolorami statusów i stanów
- ✅ Progress indicators dla operacji asynchronicznych
- ✅ Komunikaty błędów w języku polskim, zrozumiałe dla operatorów

### RNF-003: Niezawodność
**Priorytet:** Krytyczny
**Status:** ✅ Zaimplementowane  

- ✅ Obsługa błędów sieciowych z retry logic
- ✅ Walidacja danych na poziomie aplikacji
- ✅ Graceful degradation przy braku połączenia
- ✅ Automatyczne unclaim dostaw przy błędach aplikacji
- ✅ Logowanie błędów dla troubleshootingu

### RNF-004: Bezpieczeństwo
**Priorytet:** Wysoki
**Status:** 🟡 W toku

- ✅ Walidacja wszystkich danych wejściowych
- ✅ Komunikacja z API przez HTTPS
- 🟡 Sesje użytkowników z timeout
- ❌ Szyfrowanie lokalnych danych wrażliwych

## Wymagania Techniczne

### RT-001: Platforma i Technologie
- ✅ .NET MAUI (Multi-platform App UI)
- ✅ Architektura MVVM z CommunityToolkit.Mvvm
- ✅ Dependency Injection pattern
- ✅ HttpClient dla komunikacji z API REST
- ✅ Zebra DataWedge SDK dla skanerów (Android)

### RT-002: Kompatybilność
- ✅ Android 8.0+ (API level 26+)
- 🟡 iOS 12.0+ (do testów)
- 🟡 Windows 11 (do rozwoju i testów)
- ✅ Urządzenia Zebra z DataWedge 8.0+

### RT-003: Integracje
- ✅ API WMS Backend (REST)
- ✅ Zebra DataWedge (BroadcastReceiver)
- 🟡 Drukarki etykiet Zebra (ZPL via IP)
- ❌ System zarządzania logami (opcjonalnie)

## Kryteria Jakości

### Testy
- ❌ Testy jednostkowe (coverage > 80%)
- ❌ Testy integracyjne z mock API
- ❌ Testy UI automatyczne (podstawowe scenariusze)
- ❌ Testy manualne na urządzeniach docelowych

### Dokumentacja
- ✅ Dokumentacja architektury
- ✅ Przewodnik stylu kodowania
- ✅ Lista zadań i postępu
- ❌ Instrukcja instalacji i konfiguracji
- ❌ Podręcznik użytkownika

### Code Quality
- ✅ Consistent coding standards
- 🟡 Code review process
- ❌ Static analysis tools (SonarQube, itp.)
- 🟡 Performance profiling

## Ograniczenia i Assumpcje

### Ograniczenia
- **Sieć:** Aplikacja wymaga stałego połączenia z internetem
- **Sprzęt:** Funkcje skanowania wymagają urządzeń Zebra lub kompatybilnych
- **API:** Zależność od dostępności backend API WMS
- **Platformy:** Pełna funkcjonalność tylko na Android (DataWedge)

### Assumpcje  
- Operatorzy mają podstawową wiedzę o obsłudze urządzeń mobilnych
- Backend API zapewnia stabilną i udokumentowaną komunikację
- Urządzenia Zebra są skonfigurowane z DataWedge
- Drukarki etykiet obsługują ZPL i komunikację IP

## Roadmap i Priorytety

### Faza 1: MVP (Ukończona ✅)
- ✅ Podstawowe funkcje przeglądania dostaw
- ✅ Claim/unclaim dostaw
- ✅ Rejestracja pozycji z podstawową walidacją
- ✅ Integracja z DataWedge

### Faza 2: Rozszerzone Funkcje (W toku 🟡)  
- ✅ Generowanie kodów DS
- ✅ Zaawansowane skanowanie (GS1, SSCC)
- ✅ Zarządzanie nośnikami
- 🟡 Optymalizacje wydajności

### Faza 3: Finalizacja (Zaplanowana ❌)
- ❌ Kompletne testy (unit, integration, UI)
- ❌ Dokumentacja użytkownika
- ❌ Performance tuning
- ❌ Security hardening

### Faza 4: Maintenance (Przyszłość)
- ❌ Monitoring i analytics
- ❌ Feature flags system  
- ❌ Offline functionality
- ❌ Advanced reporting

## Metryki Sukcesu

### KPI Biznesowe
- **Czas rejestracji dostawy:** Redukcja o 40% vs papierowy proces
- **Błędy rejestracji:** < 2% błędów w wprowadzanych danych
- **Adopcja użytkowników:** > 90% operatorów korzysta z aplikacji  
- **Satysfakcja użytkowników:** > 4.0/5.0 w ocenach

### KPI Techniczne
- **Uptime aplikacji:** > 99.5%
- **Crash rate:** < 0.1% sesji
- **Average response time:** < 2s dla głównych operacji
- **Data accuracy:** > 99.8% poprawności synchronizacji z backendem

---

**Właściciel Produktu:** Product Manager WMS  
**Tech Lead:** Development Team Lead  
**Stakeholders:** Warehouse Operations, IT Infrastructure  

**Data utworzenia:** ${new Date().toLocaleDateString('pl-PL')}  
**Ostatnia aktualizacja:** ${new Date().toLocaleDateString('pl-PL')}  
**Wersja:** 1.2
