using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Features.Receives.Handlers;
using Wms.Application.Interfaces;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Exceptions;
using Wms.Domain.Services;
using Xunit;

namespace Wms.UnitTests.Application.Handlers;

public class ClaimReceiveHandlerTests
{
    private readonly Mock<IReceiveRepository> _mockReceiveRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<ReceiveDomainService> _mockReceiveDomainService;
    private readonly Mock<ILogger<ClaimReceiveHandler>> _mockLogger;
    private readonly ClaimReceiveHandler _handler;

    public ClaimReceiveHandlerTests()
    {
        _mockReceiveRepository = new Mock<IReceiveRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockReceiveDomainService = new Mock<ReceiveDomainService>();
        _mockLogger = new Mock<ILogger<ClaimReceiveHandler>>();
        
        _handler = new ClaimReceiveHandler(
            _mockReceiveRepository.Object,
            _mockUnitOfWork.Object,
            _mockReceiveDomainService.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Handle_ReceiveExists_AndCanBeClaimed_ShouldReturnSuccess()
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = 1, PracownikId = 100 };
        var receive = new ListControl { Id = 1, RealizujacyPracownikId = null };
        var canClaimResult = CanClaimResult.Success();

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveDomainService
            .Setup(x => x.CanClaimReceive(receive, command.PracownikId))
            .Returns(canClaimResult);

        _mockReceiveRepository
            .Setup(x => x.ClaimReceiveAsync(command.ReceiveId, command.PracownikId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeTrue();
        result.Message.Should().Contain("została pomyślnie zajęta");
        result.ClaimedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));

        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReceiveDoesNotExist_ShouldThrowReceiveNotFoundException()
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = 999, PracownikId = 100 };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ListControl?)null);

        // Act & Assert
        await Assert.ThrowsAsync<ReceiveNotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ReceiveCannotBeClaimed_ShouldReturnFailure()
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = 1, PracownikId = 100 };
        var receive = new ListControl { Id = 1, RealizujacyPracownikId = 200 }; // Already claimed by another user
        var canClaimResult = CanClaimResult.Failed("Dostawa jest już zajęta przez innego pracownika");

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveDomainService
            .Setup(x => x.CanClaimReceive(receive, command.PracownikId))
            .Returns(canClaimResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeFalse();
        result.Message.Should().Contain("jest już zajęta");

        _mockReceiveRepository.Verify(x => x.ClaimReceiveAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Fact]
    public async Task Handle_ClaimFailsAtRepositoryLevel_ShouldReturnFailure()
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = 1, PracownikId = 100 };
        var receive = new ListControl { Id = 1, RealizujacyPracownikId = null };
        var canClaimResult = CanClaimResult.Success();

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveDomainService
            .Setup(x => x.CanClaimReceive(receive, command.PracownikId))
            .Returns(canClaimResult);

        _mockReceiveRepository
            .Setup(x => x.ClaimReceiveAsync(command.ReceiveId, command.PracownikId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false); // Simulation of race condition - another user claimed it first

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeFalse();
        result.Message.Should().Contain("została już zajęta przez innego pracownika");

        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Theory]
    [InlineData(1, 100)]
    [InlineData(5, 250)]
    [InlineData(10, 999)]
    public async Task Handle_WithDifferentIds_ShouldProcessCorrectly(int receiveId, int userId)
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = receiveId, PracownikId = userId };
        var receive = new ListControl { Id = receiveId, Numer = 1000 + receiveId, RealizujacyPracownikId = null };
        var canClaimResult = CanClaimResult.Success();

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(receiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveDomainService
            .Setup(x => x.CanClaimReceive(receive, userId))
            .Returns(canClaimResult);

        _mockReceiveRepository
            .Setup(x => x.ClaimReceiveAsync(receiveId, userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeTrue();
        result.Message.Should().Contain($"LK{receiveId}");

        _mockReceiveRepository.Verify(x => x.GetByIdAsync(receiveId, It.IsAny<CancellationToken>()), Times.Once);
        _mockReceiveRepository.Verify(x => x.ClaimReceiveAsync(receiveId, userId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenExceptionOccurs_ShouldLogErrorAndPropagateException()
    {
        // Arrange
        var command = new ClaimReceiveCommand { ReceiveId = 1, PracownikId = 100 };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ReceiveId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(command, CancellationToken.None));

        // Verify that error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Błąd podczas zajmowania dostawy")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
