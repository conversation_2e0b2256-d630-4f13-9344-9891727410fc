using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WmsApp.Models.Inventory;
using WmsApp.Services.Inventory;

namespace WmsApp.ViewModels.Inventory;

/// <summary>
/// ViewModel dla strony wyboru typu inwentaryzacji
/// </summary>
public partial class InventorySelectionViewModel : ObservableObject
{
    private readonly IInventoryService _inventoryService;
    private readonly ILogger<InventorySelectionViewModel> _logger;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private ObservableCollection<InventoryOptionDto> inventoryOptions = new();

    [ObservableProperty]
    private InventoryOptionDto? selectedOption;

    partial void OnSelectedOptionChanged(InventoryOptionDto? value)
    {
        if (value != null)
        {
            // Fire and forget – nawigacja wg wyboru
            SelectOptionCommand.Execute(value);
        }
    }

    public InventorySelectionViewModel(
        IInventoryService inventoryService,
        ILogger<InventorySelectionViewModel> logger)
    {
        _inventoryService = inventoryService;
        _logger = logger;
    }

    /// <summary>
    /// Inicjalizacja ViewModelu - ładowanie opcji inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task InitializeAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            _logger.LogInformation("Inicjalizacja wyboru inwentaryzacji");
            System.Diagnostics.Debug.WriteLine("[INVENTORY] InitializeAsync started, IsLoading = true");

            var options = await _inventoryService.GetInventoryOptionsAsync();

            System.Diagnostics.Debug.WriteLine($"[INVENTORY] Received {options.Length} options from service");

            InventoryOptions.Clear();
            foreach (var option in options)
            {
                InventoryOptions.Add(option);
                System.Diagnostics.Debug.WriteLine($"[INVENTORY] Added option: {option.Name}, IsEnabled: {option.IsEnabled}");
            }

            System.Diagnostics.Debug.WriteLine($"[INVENTORY] ObservableCollection now has {InventoryOptions.Count} items");
            _logger.LogInformation("Załadowano {Count} opcji inwentaryzacji", options.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas inicjalizacji wyboru inwentaryzacji");
            ErrorMessage = "Błąd podczas ładowania opcji inwentaryzacji";
            System.Diagnostics.Debug.WriteLine($"[INVENTORY] ERROR in InitializeAsync: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            System.Diagnostics.Debug.WriteLine("[INVENTORY] InitializeAsync completed, IsLoading = false");
        }
    }

    /// <summary>
    /// Wybór opcji inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task SelectOptionAsync(InventoryOptionDto option)
    {
        try
        {
            if (!option.IsEnabled)
            {
                await Shell.Current.DisplayAlert("Informacja", 
                    $"Opcja '{option.Name}' nie jest jeszcze dostępna", "OK");
                return;
            }

            _logger.LogInformation("Wybrano opcję inwentaryzacji: {Type} - {Name}", option.Type, option.Name);
            
            SelectedOption = option;

            // Nawigacja do odpowiedniej strony w zależności od typu
            switch (option.Type)
            {
                case InventoryType.Product:
                    await NavigateToProductInventoryAsync();
                    break;
                case InventoryType.General:
                    await NavigateToGeneralInventoryAsync();
                    break;
                case InventoryType.Location:
                    await NavigateToLocationInventoryAsync();
                    break;
                case InventoryType.GG:
                    await NavigateToGGInventoryAsync();
                    break;
                default:
                    await Shell.Current.DisplayAlert("Błąd", "Nieznany typ inwentaryzacji", "OK");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru opcji inwentaryzacji");
            ErrorMessage = "Błąd podczas wyboru opcji inwentaryzacji";
        }
    }

    /// <summary>
    /// Nawigacja do inwentaryzacji produktowej
    /// </summary>
    private async Task NavigateToProductInventoryAsync()
    {
        try
        {
            _logger.LogInformation("Nawigacja do inwentaryzacji produktowej");
            await Shell.Current.GoToAsync("//inventoryproduct");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas nawigacji do inwentaryzacji produktowej");
            await Shell.Current.DisplayAlert("Błąd", "Nie można przejść do inwentaryzacji produktowej", "OK");
        }
    }

    /// <summary>
    /// Nawigacja do inwentaryzacji ogólnej
    /// </summary>
    private async Task NavigateToGeneralInventoryAsync()
    {
        try
        {
            _logger.LogInformation("Nawigacja do inwentaryzacji ogólnej");
            await Shell.Current.DisplayAlert("Informacja", "Inwentaryzacja ogólna będzie dostępna wkrótce", "OK");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas nawigacji do inwentaryzacji ogólnej");
        }
    }

    /// <summary>
    /// Nawigacja do inwentaryzacji miejsc
    /// </summary>
    private async Task NavigateToLocationInventoryAsync()
    {
        try
        {
            _logger.LogInformation("Nawigacja do inwentaryzacji miejsc");
            await Shell.Current.DisplayAlert("Informacja", "Inwentaryzacja miejsc będzie dostępna wkrótce", "OK");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas nawigacji do inwentaryzacji miejsc");
        }
    }

    /// <summary>
    /// Nawigacja do inwentaryzacji GG
    /// </summary>
    private async Task NavigateToGGInventoryAsync()
    {
        try
        {
            _logger.LogInformation("Nawigacja do inwentaryzacji GG");
            await Shell.Current.DisplayAlert("Informacja", "Inwentaryzacja GG będzie dostępna wkrótce", "OK");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas nawigacji do inwentaryzacji GG");
        }
    }

    /// <summary>
    /// Odświeżenie listy opcji
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await InitializeAsync();
    }

    /// <summary>
    /// Powrót do menu głównego
    /// </summary>
    [RelayCommand]
    private async Task GoBackAsync()
    {
        try
        {
            await Shell.Current.GoToAsync("//main");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas powrotu do menu głównego");
        }
    }
}
