using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WmsApp.Models.Pallets;
using WmsApp.Services;

namespace WmsApp.ViewModels;

public partial class LocationsViewModel : ObservableObject
{
    private readonly IWmsApiService _apiService;

    [ObservableProperty]
    private ObservableCollection<LocationInfo> locations = new();

    [ObservableProperty]
    private LocationInfo? selectedLocation;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private bool isRefreshing;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private string searchText = string.Empty;

    public LocationsViewModel(IWmsApiService apiService)
    {
        _apiService = apiService;
    }

[RelayCommand]
    private async Task LoadLocationsAsync()
    {
        // Upewnij się, że metoda jest asynchroniczna (CS1998)
        await Task.Yield();
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Dla celów demo, stwórzmy kilka przykładowych lokalizacji
            var mockLocations = GenerateMockLocations();
            
            Locations.Clear();
            foreach (var location in mockLocations)
            {
                Locations.Add(location);
            }

            // W przyszłości tutaj będzie wywołanie API:
            // var response = await _apiService.GetLocationsAsync();
            // if (response.IsSuccessStatusCode && response.Content != null)
            // {
            //     Locations.Clear();
            //     foreach (var location in response.Content)
            //     {
            //         Locations.Add(location);
            //     }
            // }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Failed to load locations: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
            IsRefreshing = false;
        }
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        IsRefreshing = true;
        await LoadLocationsAsync();
    }

    [RelayCommand]
    private async Task SelectLocationAsync(LocationInfo? location)
    {
        if (location == null) return;

        SelectedLocation = location;
        
        var parameters = new Dictionary<string, object>
        {
            ["LocationId"] = location.Id
        };

        await Shell.Current.GoToAsync("locationdetails", parameters);
    }

    [RelayCommand]
    private async Task SearchLocationsAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            await LoadLocationsAsync();
            return;
        }

        try
        {
            IsLoading = true;
            
            var allLocations = GenerateMockLocations();
            var filteredLocations = allLocations
                .Where(l => l.Code.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                           $"Hala {l.Hala}".Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                .ToList();

            Locations.Clear();
            foreach (var location in filteredLocations)
            {
                Locations.Add(location);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Search failed: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    partial void OnSearchTextChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            _ = LoadLocationsAsync();
        }
    }

    private List<LocationInfo> GenerateMockLocations()
    {
        return new List<LocationInfo>
        {
            new LocationInfo 
            { 
                Id = 1, 
                Code = "MP-A01-001-001-001", 
                Hala = 1, 
                Regal = "A01", 
                Miejsce = 1, 
                Poziom = "001",
                IsVisible = true,
                IsPickingLocation = true,
                MaxCapacity = 4,
                CurrentPalletCount = 2
            },
            new LocationInfo 
            { 
                Id = 2, 
                Code = "MP-A01-001-002-001", 
                Hala = 1, 
                Regal = "A01", 
                Miejsce = 2, 
                Poziom = "001",
                IsVisible = true,
                IsPickingLocation = false,
                MaxCapacity = 4,
                CurrentPalletCount = 4
            },
            new LocationInfo 
            { 
                Id = 3, 
                Code = "MP-B02-003-001-002", 
                Hala = 2, 
                Regal = "B02", 
                Miejsce = 3, 
                Poziom = "002",
                IsVisible = true,
                IsPickingLocation = true,
                MaxCapacity = 6,
                CurrentPalletCount = 1
            },
            new LocationInfo 
            { 
                Id = 4, 
                Code = "MP-C03-005-003-001", 
                Hala = 3, 
                Regal = "C03", 
                Miejsce = 5, 
                Poziom = "001",
                IsVisible = true,
                IsPickingLocation = false,
                MaxCapacity = 8,
                CurrentPalletCount = 0
            },
            new LocationInfo 
            { 
                Id = 5, 
                Code = "MP-A01-002-001-003", 
                Hala = 1, 
                Regal = "A01", 
                Miejsce = 2, 
                Poziom = "003",
                IsVisible = false,
                IsPickingLocation = false,
                MaxCapacity = 2,
                CurrentPalletCount = 0
            }
        };
    }

    public async Task InitializeAsync()
    {
        await LoadLocationsAsync();
    }
}
