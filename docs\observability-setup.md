# WMS API - Observability Setup

## Overview

The WMS API includes comprehensive observability features implemented using industry-standard tools and practices:

- **Health Checks**: Application and dependency health monitoring
- **Metrics**: Business and system metrics with Prometheus export
- **Tracing**: Distributed tracing via OpenTelemetry
- **Logging**: Structured logging with Serilog

## Health Checks

### Available Endpoints

| Endpoint | Purpose | Description |
|----------|---------|-------------|
| `/health` | Overall health | Checks all registered health checks |
| `/health/ready` | Readiness probe | Checks if the application is ready to serve requests |
| `/health/live` | Liveness probe | Basic liveness check (always returns healthy) |

### Implemented Health Checks

1. **DatabaseHealthCheck** - Verifies database connectivity
2. **AuthenticationServiceHealthCheck** - Validates JWT authentication service
3. **PalletServiceHealthCheck** - Checks pallet management service availability

### Health Check Configuration

Health checks are configured in `Program.cs`:

```csharp
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database")
    .AddCheck<AuthenticationServiceHealthCheck>("authentication")
    .AddCheck<PalletServiceHealthCheck>("pallet_service");
```

## Metrics

### Business Metrics

The WMS API exposes custom business metrics via the `WmsMetrics` class:

#### Counters
- `wms_login_attempts_total` - Total number of login attempts
- `wms_login_successes_total` - Successful logins
- `wms_login_failures_total` - Failed logins
- `wms_pallet_move_attempts_total` - Pallet movement attempts
- `wms_pallet_move_successes_total` - Successful pallet movements
- `wms_pallet_move_failures_total` - Failed pallet movements
- `wms_api_requests_total` - Total API requests
- `wms_validation_errors_total` - Validation errors

#### Histograms (Duration Tracking)
- `wms_pallet_move_duration_seconds` - Pallet movement operation duration
- `wms_authentication_duration_seconds` - Authentication operation duration
- `wms_database_query_duration_seconds` - Database query duration
- `wms_api_request_duration_seconds` - API request duration

#### Gauges (Current State)
- `wms_active_sessions` - Number of active user sessions
- `wms_active_users` - Number of currently active users

### Prometheus Integration

Metrics are exported in Prometheus format at `/metrics` endpoint.

#### Sample Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'wms-api'
    static_configs:
      - targets: ['localhost:5001']
    metrics_path: '/metrics'
    scrape_interval: 10s
```

## OpenTelemetry Tracing

### Instrumentation

The API includes automatic instrumentation for:
- ASP.NET Core requests
- Entity Framework Core database operations
- Custom business operations

### Configuration

```csharp
builder.Services.AddOpenTelemetry()
    .ConfigureResource(resource =>
        resource.AddService(
            serviceName: "WMS.Api",
            serviceVersion: "1.0.0",
            serviceInstanceId: Environment.MachineName))
    .WithTracing(tracing =>
        tracing
            .AddAspNetCoreInstrumentation()
            .AddEntityFrameworkCoreInstrumentation())
    .WithMetrics(metrics =>
        metrics
            .AddAspNetCoreInstrumentation()
            .AddMeter(WmsMetrics.MeterName)
            .AddPrometheusExporter());
```

## Logging

### Serilog Configuration

Structured logging is implemented using Serilog with:
- Console output for development
- File rolling logs (`logs/wms-api-{date}.log`)
- Log level filtering for Microsoft and System logs

```csharp
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("System", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/wms-api-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();
```

## Middleware

### MetricsMiddleware

Automatically captures metrics for all HTTP requests:
- Request method, endpoint, status code
- Response time measurement
- Endpoint sanitization for better grouping

### Global Exception Middleware

Handles unhandled exceptions and logs them appropriately while maintaining metrics accuracy.

## Using Metrics in Controllers

Example of recording business metrics in controllers:

```csharp
[ApiController]
public class AuthController : ControllerBase
{
    private readonly WmsMetrics _metrics;

    public AuthController(WmsMetrics metrics)
    {
        _metrics = metrics;
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await _authService.LoginAsync(request.CardNumber);
            
            _metrics.RecordLoginAttempt(request.CardNumber, true);
            _metrics.AuthenticationDuration.Record(stopwatch.Elapsed.TotalSeconds);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _metrics.RecordLoginAttempt(request.CardNumber, false, ex.Message);
            throw;
        }
    }
}
```

## Testing Observability

Use the provided PowerShell script to test all observability endpoints:

```powershell
# Test all endpoints
.\scripts\test-observability.ps1

# Test with custom URL
.\scripts\test-observability.ps1 -BaseUrl "https://your-api-url"
```

## Monitoring Integration

### Grafana Dashboard

Create dashboards using the exposed metrics:

1. **API Performance Dashboard**
   - Request rate and response times
   - Error rates by endpoint
   - Status code distribution

2. **Business Metrics Dashboard**
   - Login success/failure rates
   - Pallet movement statistics
   - Active user sessions

3. **System Health Dashboard**
   - Health check status
   - Database performance
   - Application uptime

### Alerting Rules

Example Prometheus alerting rules:

```yaml
groups:
  - name: wms-api-alerts
    rules:
      - alert: WMSAPIHighErrorRate
        expr: rate(wms_api_requests_total{status_class="5xx"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in WMS API"
          
      - alert: WMSAPIHealthCheckFailed
        expr: up{job="wms-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "WMS API health check failed"
```

## Performance Considerations

1. **Metrics Collection**: Metrics collection has minimal performance impact
2. **Health Checks**: Configure appropriate timeouts for external dependencies
3. **Log Volume**: Adjust log levels based on environment (more verbose in dev, less in production)
4. **Trace Sampling**: Consider implementing trace sampling for high-volume production environments

## Security Considerations

1. **Metrics Endpoint**: Consider securing `/metrics` endpoint in production
2. **Log Content**: Avoid logging sensitive information (card numbers, passwords)
3. **Health Check Information**: Limit exposure of internal system details in health check responses
