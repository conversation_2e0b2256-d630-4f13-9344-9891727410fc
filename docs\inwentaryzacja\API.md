# API Endpoints - <PERSON><PERSON><PERSON>

## <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> inwentaryzacji udostępnia RESTful API zgodnie z wzorcem Clean Architecture i CQRS. Wszystkie endpointy wymagają autoryzacji JWT Bearer token.

## Uwierzytelnianie

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## Endpointy Inwentaryzacji

### 1. Sesje Inwentaryzacji

#### GET /api/v1/inventory/sessions
Pobiera listę dostępnych sesji inwentaryzacji.

**Parametry zapytania:**
- `type` (opcjonalny): Typ inwentaryzacji (`Product`, `General`, `Location`, `GG`)
- `active` (opcjonalny): Filtr aktywnych sesji (default: `true`)
- `page` (opcjonalny): Numer strony (default: `1`)
- `pageSize` (opcjonalny): <PERSON><PERSON><PERSON><PERSON> strony (default: `20`)

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "sessions": [
      {
        "id": 12345,
        "description": "Inwentaryzacja Q4 2024",
        "date": "2024-12-15",
        "type": "Product",
        "status": "Active",
        "totalItems": 1500,
        "countedItems": 750,
        "progressPercentage": 50.0,
        "createdBy": "Jan Kowalski",
        "createdAt": "2024-12-15T08:00:00Z",
        "nrWspolny": 2024001,
        "proba": 1
      }
    ],
    "totalCount": 15,
    "currentPage": 1,
    "totalPages": 1
  },
  "success": true,
  "message": "Sesje inwentaryzacji pobrane pomyślnie"
}
```

#### POST /api/v1/inventory/sessions
Tworzy nową sesję inwentaryzacji.

**Request Body:**
```json
{
  "description": "Inwentaryzacja roczna 2025",
  "type": "General",
  "nrWspolny": 2025001,
  "proba": 1,
  "magazyn": "MAG001"
}
```

**Odpowiedź 201 Created:**
```json
{
  "data": {
    "sessionId": 12346,
    "description": "Inwentaryzacja roczna 2025",
    "type": "General",
    "createdAt": "2025-01-14T10:30:00Z",
    "status": "Active"
  },
  "success": true,
  "message": "Sesja inwentaryzacji została utworzona"
}
```

#### PUT /api/v1/inventory/sessions/{id}/status
Zmienia status sesji inwentaryzacji.

**Path Parameters:**
- `id`: ID sesji inwentaryzacji

**Request Body:**
```json
{
  "status": "Completed",
  "completedBy": "Jan Kowalski",
  "notes": "Inwentaryzacja zakończona bez uwag"
}
```

### 2. Pozycje Inwentaryzacyjne

#### GET /api/v1/inventory/sessions/{sessionId}/items
Pobiera pozycje w sesji inwentaryzacji.

**Path Parameters:**
- `sessionId`: ID sesji inwentaryzacji

**Parametry zapytania:**
- `status` (opcjonalny): Status pozycji (`NotCounted`, `Counted`, `HasDifferences`)
- `location` (opcjonalny): Filtr po lokalizacji (np. `MP-1-145`)
- `productCode` (opcjonalny): Filtr po kodzie produktu

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "items": [
      {
        "id": 789123,
        "sessionId": 12345,
        "etykietaId": "ET001234567890",
        "kod": "5901234123457",
        "kodNazwa": "Mleko UHT 1L",
        "teoretical": 100.0,
        "counted": 98.0,
        "difference": -2.0,
        "jednostkaMiary": "SZT",
        "location": {
          "hala": 1,
          "regal": "A01",
          "miejsce": 15,
          "poziom": "1",
          "fullCode": "MP-1-A01-15-1"
        },
        "countedBy": "Anna Nowak",
        "countedAt": "2024-12-15T14:30:00Z",
        "status": "HasDifferences",
        "uwagi": null
      }
    ],
    "summary": {
      "totalItems": 1500,
      "countedItems": 750,
      "itemsWithDifferences": 45,
      "progressPercentage": 50.0
    }
  },
  "success": true
}
```

#### POST /api/v1/inventory/sessions/{sessionId}/items
Dodaje nową pozycję do inwentaryzacji.

**Path Parameters:**
- `sessionId`: ID sesji inwentaryzacji

**Request Body:**
```json
{
  "kod": "5901234123457",
  "etykietaId": "ET001234567890",
  "countedQuantity": 98.0,
  "jednostkaMiary": "SZT",
  "location": {
    "hala": 1,
    "regal": "A01",
    "miejsce": 15,
    "poziom": "1"
  },
  "skan": "5901234123457",
  "uwagi": "Uszkodzone opakowanie - 2 szt"
}
```

**Odpowiedź 201 Created:**
```json
{
  "data": {
    "id": 789124,
    "sessionId": 12345,
    "kod": "5901234123457",
    "countedQuantity": 98.0,
    "status": "Counted",
    "createdAt": "2025-01-14T10:35:00Z"
  },
  "success": true,
  "message": "Pozycja została dodana do inwentaryzacji"
}
```

#### PUT /api/v1/inventory/items/{id}
Aktualizuje pozycję inwentaryzacyjną.

**Path Parameters:**
- `id`: ID pozycji inwentaryzacyjnej

**Request Body:**
```json
{
  "countedQuantity": 100.0,
  "location": {
    "hala": 1,
    "regal": "A01",
    "miejsce": 16,
    "poziom": "1"
  },
  "uwagi": "Skorygowano po ponownym przeliczeniu"
}
```

### 3. Przetwarzanie Skanów

#### POST /api/v1/inventory/scan
Przetwarza zeskanowany kod i zwraca informacje do wypełnienia formularza.

**Request Body:**
```json
{
  "scannedCode": "5901234123457",
  "sessionId": 12345,
  "scanType": "Product",
  "deviceId": "MC3300_001"
}
```

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "scanResult": {
      "codeType": "EAN",
      "isValid": true,
      "produktInfo": {
        "kod": "5901234123457",
        "nazwa": "Mleko UHT 1L",
        "jednostkaMiary": "SZT",
        "wymaganaPartia": true,
        "wymaganaDataWaznosci": true
      },
      "existingItem": {
        "id": 789123,
        "teoretyczna": 100.0,
        "currentLocation": "MP-1-A01-15-1",
        "status": "NotCounted"
      },
      "suggestedValues": {
        "countedQuantity": null,
        "location": "MP-1-A01-15-1"
      }
    }
  },
  "success": true,
  "message": "Kod został pomyślnie zidentyfikowany"
}
```

**Odpowiedź 404 Not Found (kod nieznany):**
```json
{
  "success": false,
  "error": {
    "code": "PRODUCT_NOT_FOUND",
    "message": "Kod produktu 5901234999999 nie został znaleziony w kartotece",
    "details": {
      "scannedCode": "5901234999999",
      "suggestions": [
        "Sprawdź poprawność kodu",
        "Dodaj kod do kartoteki produktów",
        "Użyj opcji 'Nowy produkt'"
      ]
    }
  }
}
```

#### POST /api/v1/inventory/scan/location
Przetwarza skanowanie lokalizacji.

**Request Body:**
```json
{
  "locationCode": "MP-1-A01-15-1",
  "sessionId": 12345
}
```

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "location": {
      "id": 1234,
      "fullCode": "MP-1-A01-15-1",
      "hala": 1,
      "regal": "A01",
      "miejsce": 15,
      "poziom": "1",
      "isActive": true,
      "maxPojemnosc": 20,
      "currentOccupancy": 15
    },
    "expectedItems": [
      {
        "etykietaId": "ET001234567890",
        "kod": "5901234123457",
        "nazwa": "Mleko UHT 1L",
        "teoretyczna": 100.0,
        "status": "NotCounted"
      }
    ]
  },
  "success": true
}
```

### 4. Raporty i Statystyki

#### GET /api/v1/inventory/sessions/{sessionId}/status
Pobiera szczegółowy status sesji inwentaryzacji.

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "sessionInfo": {
      "id": 12345,
      "description": "Inwentaryzacja Q4 2024",
      "type": "Product",
      "startDate": "2024-12-15T08:00:00Z",
      "status": "Active"
    },
    "progress": {
      "totalItems": 1500,
      "countedItems": 750,
      "remainingItems": 750,
      "progressPercentage": 50.0,
      "estimatedCompletionTime": "2024-12-15T18:00:00Z"
    },
    "differences": {
      "itemsWithDifferences": 45,
      "totalNadwyzka": 15,
      "totalNiedobor": 30,
      "valueImpact": {
        "nadwyzkaValue": 1250.50,
        "niedoborValue": -2100.25,
        "netImpact": -849.75,
        "currency": "PLN"
      }
    },
    "workers": [
      {
        "name": "Jan Kowalski",
        "countedItems": 250,
        "lastActivity": "2024-12-15T14:45:00Z"
      }
    ]
  },
  "success": true
}
```

#### GET /api/v1/inventory/sessions/{sessionId}/differences
Pobiera listę różnic inwentaryzacyjnych.

**Parametry zapytania:**
- `type` (opcjonalny): Typ różnicy (`Nadwyzka`, `Niedobor`, `All`)
- `minValue` (opcjonalny): Minimalna wartość różnicy
- `sortBy` (opcjonalny): Sortowanie (`Value`, `Percentage`, `Quantity`)

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "differences": [
      {
        "itemId": 789123,
        "kod": "5901234123457",
        "nazwa": "Mleko UHT 1L",
        "teoretyczna": 100.0,
        "spisana": 98.0,
        "roznica": -2.0,
        "procentRoznicy": -2.0,
        "typ": "Niedobor",
        "location": "MP-1-A01-15-1",
        "wartoscRoznicy": -12.50,
        "countedBy": "Anna Nowak",
        "countedAt": "2024-12-15T14:30:00Z"
      }
    ],
    "summary": {
      "totalDifferences": 45,
      "nadwyzki": 15,
      "niedobory": 30,
      "totalValueImpact": -849.75
    }
  },
  "success": true
}
```

### 5. Operacje Specjalne

#### POST /api/v1/inventory/items/{id}/recalculate
Ponownie oblicza różnice dla pozycji (gdy zmieniły się dane teoretyczne).

**Path Parameters:**
- `id`: ID pozycji inwentaryzacyjnej

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "itemId": 789123,
    "oldTeoretyczna": 100.0,
    "newTeoretyczna": 102.0,
    "spisana": 98.0,
    "oldDifference": -2.0,
    "newDifference": -4.0,
    "recalculatedAt": "2025-01-14T10:45:00Z"
  },
  "success": true,
  "message": "Pozycja została ponownie obliczona"
}
```

#### POST /api/v1/inventory/sessions/{sessionId}/close
Zamyka sesję inwentaryzacji.

**Request Body:**
```json
{
  "forceClose": false,
  "finalNotes": "Inwentaryzacja zakończona zgodnie z planem",
  "approvedBy": "Kierownik Magazynu"
}
```

**Odpowiedź 200 OK:**
```json
{
  "data": {
    "sessionId": 12345,
    "closedAt": "2025-01-14T16:00:00Z",
    "finalStatus": "Completed",
    "summary": {
      "totalItems": 1500,
      "countedItems": 1500,
      "itemsWithDifferences": 45,
      "completionRate": 100.0
    }
  },
  "success": true,
  "message": "Sesja inwentaryzacji została zamknięta"
}
```

## Modele DTO

### InventorySessionDto
```csharp
public record InventorySessionDto(
    int Id,
    string Description,
    DateTime Date,
    InventoryType Type,
    string Status,
    int TotalItems,
    int CountedItems,
    decimal ProgressPercentage,
    string CreatedBy,
    DateTime CreatedAt,
    int? NrWspolny,
    int? Proba
);
```

### InventoryItemDto
```csharp
public record InventoryItemDto(
    int Id,
    int SessionId,
    string? EtykietaId,
    string Kod,
    string KodNazwa,
    decimal? Teoretyczna,
    decimal? Counted,
    decimal? Difference,
    string JednostkaMiary,
    LocationDto Location,
    string? CountedBy,
    DateTime? CountedAt,
    string Status,
    string? Uwagi
);
```

### LocationDto
```csharp
public record LocationDto(
    int Id,
    int Hala,
    string Regal,
    int Miejsce,
    string Poziom,
    string FullCode
);
```

### ScanResultDto
```csharp
public record ScanResultDto(
    string CodeType,
    bool IsValid,
    ProductInfoDto? ProductInfo,
    ExistingItemDto? ExistingItem,
    SuggestedValuesDto SuggestedValues
);
```

## Kody Błędów

### Standardowe Kody HTTP
- `200` - OK
- `201` - Created
- `400` - Bad Request (błędne dane wejściowe)
- `401` - Unauthorized (brak/nieprawidłowy token)
- `403` - Forbidden (brak uprawnień)
- `404` - Not Found (zasób nie istnieje)
- `409` - Conflict (konflikt biznesowy)
- `500` - Internal Server Error

### Kody Błędów Biznesowych
- `INVENTORY_SESSION_NOT_FOUND` - Sesja inwentaryzacji nie istnieje
- `INVENTORY_ITEM_ALREADY_COUNTED` - Pozycja już została spisana
- `PRODUCT_NOT_FOUND` - Kod produktu nie istnieje w kartotece
- `LOCATION_NOT_ACTIVE` - Lokalizacja nie jest aktywna
- `INVALID_SCAN_FORMAT` - Niepoprawny format zeskanowanego kodu
- `SESSION_ALREADY_CLOSED` - Sesja jest już zamknięta
- `INSUFFICIENT_PERMISSIONS` - Niewystarczające uprawnienia
- `CONCURRENT_MODIFICATION` - Równoczesna modyfikacja rekordu

### Przykład Odpowiedzi z Błędem
```json
{
  "success": false,
  "error": {
    "code": "INVENTORY_ITEM_ALREADY_COUNTED",
    "message": "Pozycja została już spisana w tej sesji inwentaryzacji",
    "details": {
      "itemId": 789123,
      "sessionId": 12345,
      "countedAt": "2024-12-15T14:30:00Z",
      "countedBy": "Anna Nowak"
    }
  },
  "traceId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
}
```

## Nagłówki Odpowiedzi

### Standardowe Nagłówki
```http
X-API-Version: v1
X-Request-Id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
X-Response-Time: 120ms
Cache-Control: no-cache
Content-Type: application/json; charset=utf-8
```

### Nagłówki Paginacji
```http
X-Pagination-Total-Count: 1500
X-Pagination-Page: 1
X-Pagination-Page-Size: 20
X-Pagination-Total-Pages: 75
```

## Rate Limiting

### Limity dla API
- **GET endpoints**: 100 żądań/minutę
- **POST/PUT endpoints**: 50 żądań/minutę
- **Scan endpoints**: 200 żądań/minutę (wyższy limit dla skanerów)

### Nagłówki Rate Limiting
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642162800
```

---

*Dokument zaktualizowany: 2025-01-14 - Specyfikacja API dla modułu inwentaryzacji* ✅