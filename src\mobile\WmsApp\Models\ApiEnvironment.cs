namespace WmsApp.Models;

public enum ApiEnvironment
{
    <PERSON><PERSON>,
    DevelopmentServer,
    ProductionServer,
    AndroidEmulator,
    AndroidHost,
    TC21Proxy,
    Localhost
}

public static class ApiEnvironmentExtensions
{
    public static string GetDisplayName(this ApiEnvironment environment)
    {
        return environment switch
        {
            ApiEnvironment.Mock => "<PERSON><PERSON> (<PERSON> testowe)",
            ApiEnvironment.DevelopmentServer => "Serwer developerski (************)",
            ApiEnvironment.ProductionServer => "Serwer produkcyjny (***********)",
            ApiEnvironment.AndroidEmulator => "Android Emulator (********:8080)",
            ApiEnvironment.AndroidHost => "Android Host (***********:8080)",
            ApiEnvironment.TC21Proxy => "TC21 via Proxy (***********:8080)",
            ApiEnvironment.Localhost => "Localhost (127.0.0.1:8080)",
            _ => environment.ToString()
        };
    }

    public static string GetBaseUrl(this ApiEnvironment environment)
    {
        return environment switch
        {
            ApiEnvironment.Mock => "mock://localhost",
            ApiEnvironment.DevelopmentServer => "http://************/api/v1",
            ApiEnvironment.ProductionServer => "http://***********/api/v1",
            ApiEnvironment.AndroidEmulator => "http://********:8080/api/v1",
            ApiEnvironment.AndroidHost => "http://***********:8080/api/v1",
            ApiEnvironment.TC21Proxy => "http://***********:8080/api/v1",
            ApiEnvironment.Localhost => "http://127.0.0.1:8080/api/v1",
            _ => throw new ArgumentException($"Unknown environment: {environment}")
        };
    }
}
