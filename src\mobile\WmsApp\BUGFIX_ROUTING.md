# BUGFIX: Naprawa błędu routingu do PalletsPage

## Problem
Aplikacja zgłaszała błąd routingu podczas próby nawigacji do strony Operacje:
```
System.ArgumentException: 'unable to figure out route for: ///pallets (Parameter 'uri')'
```

## Przyczyna
Strona `PalletsPage` istniała w projekcie (XAML i code-behind), ale:
1. **<PERSON>e była zarejestrowana w `AppShell.xaml`** - brakował wpis route `pallets`
2. **Nie była zarejestrowana w DI container** - brakował wpis w `MauiProgram.cs`
3. **`NavigateToPalletsCommand` miał pustą implementację** zamiast rzeczywistej nawigacji

## Rozwiązanie

### 1. Dodano trasę w AppShell.xaml
```xml
<!-- Pallets Page -->
<ShellContent
    Title="Pallets"
    ContentTemplate="{DataTemplate views:PalletsPage}"
    Route="pallets" />
```

### 2. Zarejestrowano w DI container (MauiProgram.cs)
```csharp
// ViewModels
services.AddTransient<PalletsViewModel>();

// Views  
services.AddTransient<PalletsPage>();
```

### 3. Poprawiono nawigację w MainViewModel
```csharp
[RelayCommand]
private async Task NavigateToPalletsAsync()
{
    await Shell.Current.GoToAsync("///pallets");
}
```

## Wynik
- ✅ Aplikacja kompiluje się bez błędów
- ✅ Przycisk "Operacje" poprawnie przekierowuje do `PalletsPage`
- ✅ `PalletsViewModel` jest poprawnie wstrzykiwany przez DI
- ✅ Wszystkie konwertery XAML są dostępne

## Pliki zmienione
- `AppShell.xaml` - dodano routing
- `MauiProgram.cs` - dodano rejestrację w DI  
- `ViewModels/MainViewModel.cs` - poprawiono nawigację

---
*Data: 2025-09-03*
*Status: ✅ Rozwiązany*
