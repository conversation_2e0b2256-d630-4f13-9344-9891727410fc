using System.Text.RegularExpressions;
using Wms.Domain.Exceptions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący kod DS (nośnik/paleta)
/// Format: DS + 4-9 cyfr (np. DS1234, DS3535620, DS12345678)
/// </summary>
public record DSCode : IComparable<DSCode>
{
    private static readonly Regex DSPattern = new(@"^DS\d{4,9}$", RegexOptions.Compiled);
    
    public string Value { get; private init; }
    public int ParsedValue { get; private init; }

    private DSCode(string value)
    {
        Value = value;
        ParsedValue = int.Parse(value[2..]);
    }

    /// <summary>
    /// Tworzy DSCode z stringa (np. "DS12345678")
    /// </summary>
    public static DSCode Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value) || !IsValid(value))
            throw new InvalidDSCodeException($"Nieprawidłowy kod DS: '{value}'. Wymagany format: DS + 4-9 cyfr");

        return new DSCode(value);
    }

    /// <summary>
    /// Próbuje utworzyć DSCode z stringa
    /// </summary>
    public static bool TryCreate(string value, out DSCode? dsCode)
    {
        dsCode = null;
        
        if (!IsValid(value))
            return false;

        dsCode = new DSCode(value);
        return true;
    }

    /// <summary>
    /// Waliduje czy string może być DS kodem
    /// </summary>
    public static bool IsValid(string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        return DSPattern.IsMatch(value);
    }

    /// <summary>
    /// Porównuje dwa kody DS na podstawie ich wartości numerycznych
    /// </summary>
    public int CompareTo(DSCode? other)
    {
        if (other is null) return 1;
        return ParsedValue.CompareTo(other.ParsedValue);
    }

    public static bool operator <(DSCode left, DSCode right) => left.CompareTo(right) < 0;
    public static bool operator >(DSCode left, DSCode right) => left.CompareTo(right) > 0;
    public static bool operator <=(DSCode left, DSCode right) => left.CompareTo(right) <= 0;
    public static bool operator >=(DSCode left, DSCode right) => left.CompareTo(right) >= 0;

    public override string ToString() => Value;
    
    public static implicit operator string(DSCode dsCode) => dsCode.Value;
    public static explicit operator DSCode(string value) => Create(value);
}
