<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.LocationsPage"
             x:Name="LocationsPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:models="clr-namespace:WmsApp.Models.Pallets"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:LocationsViewModel"
             Title="{loc:Translate Key=Locations_Title}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Search Bar -->
        <Frame Grid.Row="0" 
               BackgroundColor="White" 
               Padding="15" 
               Margin="10"
               CornerRadius="10" 
               HasShadow="True">
            
            <SearchBar x:Name="LocationSearchBar"
                       Text="{Binding SearchText}"
Placeholder="{loc:Translate Key=Locations_Search_Placeholder}"
                       SearchCommand="{Binding SearchLocationsCommand}"
                       FontSize="16" />
        </Frame>

        <!-- Main Content -->
        <RefreshView Grid.Row="1" 
                     IsRefreshing="{Binding IsRefreshing}"
                     RefreshColor="{DynamicResource Primary}"
                     Command="{Binding RefreshCommand}">

            <CollectionView ItemsSource="{Binding Locations}"
                            BackgroundColor="{DynamicResource Gray100}">
                
                <CollectionView.ItemTemplate>
<DataTemplate x:DataType="models:LocationInfo">
                        <Grid Padding="10,5">
                            <Frame BackgroundColor="White"
                                   CornerRadius="12"
                                   HasShadow="True"
                                   Padding="15">
                                
                                <Frame.GestureRecognizers>
<TapGestureRecognizer Command="{Binding Source={x:Reference LocationsPageRoot}, Path=VM.SelectLocationCommand}"
                                                          CommandParameter="{Binding .}" />
                                </Frame.GestureRecognizers>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <!-- Location Code -->
                                    <Label Grid.Row="0" Grid.Column="0"
                                           Text="{Binding Code}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{DynamicResource Primary}" />

                                    <!-- Status Indicator -->
                                    <Frame Grid.Row="0" Grid.Column="1"
                                           BackgroundColor="{Binding IsVisible, Converter={StaticResource BoolToColorConverter}}"
                                           CornerRadius="8"
                                           Padding="8,4"
                                           HasShadow="False">
                                        <Label Text="{Binding IsVisible, Converter={StaticResource BoolToStatusConverter}}"
                                               FontSize="10"
                                               TextColor="White"
                                               FontAttributes="Bold" />
                                    </Frame>

                                    <!-- Hall and Shelf Info -->
                                    <StackLayout Grid.Row="1" Grid.ColumnSpan="2" 
                                                 Orientation="Horizontal" 
                                                 Spacing="15">
                                        <Label Text="{Binding Hala, StringFormat='Hall: {0}'}" 
                                               FontSize="14" 
                                               TextColor="{DynamicResource Gray600}" />
                                        <Label Text="{Binding Regal, StringFormat='Shelf: {0}'}" 
                                               FontSize="14" 
                                               TextColor="{DynamicResource Gray600}" />
                                        <Label Text="{Binding Poziom, StringFormat='Level: {0}'}" 
                                               FontSize="14" 
                                               TextColor="{DynamicResource Gray600}" />
                                    </StackLayout>

                                    <!-- Capacity Info -->
                                    <Grid Grid.Row="2" Grid.ColumnSpan="2" Margin="0,8,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <!-- Capacity Bar -->
                                        <ProgressBar Grid.Column="0"
                                                     Progress="{Binding ., Converter={StaticResource CapacityToProgressConverter}}"
                                                     ProgressColor="{Binding ., Converter={StaticResource CapacityToColorConverter}}"
                                                     BackgroundColor="{DynamicResource Gray200}"
                                                     HeightRequest="8"
                                                     VerticalOptions="Center" />

                                        <!-- Capacity Text -->
                                        <Label Grid.Column="1"
                                               Text="{Binding ., StringFormat='{0}'}"
                                               FontSize="12"
                                               TextColor="{DynamicResource Gray600}"
                                               Margin="10,0,0,0"
                                               VerticalOptions="Center" />

                                        <!-- Picking Location Icon -->
                                        <Label Grid.Column="2"
                                               Text="📋"
                                               FontSize="16"
                                               IsVisible="{Binding IsPickingLocation}"
                                               VerticalOptions="Center"
                                               Margin="5,0,0,0"
                                               ToolTipProperties.Text="Picking Location" />
                                    </Grid>
                                </Grid>
                            </Frame>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>

                <CollectionView.EmptyView>
                    <StackLayout Padding="50" VerticalOptions="CenterAndExpand">
                        <Label Text="📍" 
                               FontSize="48" 
                               HorizontalOptions="Center" 
                               Opacity="0.5" />
<Label Text="{loc:Translate Key=Locations_Empty_Title}" 
                               FontSize="18" 
                               HorizontalOptions="Center" 
                               TextColor="{DynamicResource Gray400}" />
<Label Text="{loc:Translate Key=Locations_Empty_Subtitle}" 
                               FontSize="14" 
                               HorizontalOptions="Center" 
                               TextColor="{DynamicResource Gray400}" 
                               Margin="0,5,0,0" />
                    </StackLayout>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>

        <!-- Loading Overlay -->
        <Grid Grid.Row="1" 
              IsVisible="{Binding IsLoading}"
              BackgroundColor="#80000000">
            
            <StackLayout VerticalOptions="Center" HorizontalOptions="Center">
                <ActivityIndicator IsRunning="{Binding IsLoading}"
                                   Color="{DynamicResource Primary}"
                                   WidthRequest="50"
                                   HeightRequest="50" />
<Label Text="{loc:Translate Key=Locations_Loading_Label}" 
                       TextColor="White" 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       Margin="0,10,0,0" />
            </StackLayout>
        </Grid>

        <!-- Error Message -->
        <Frame Grid.Row="1"
               IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
               BackgroundColor="Red"
               Margin="20"
               CornerRadius="8"
               VerticalOptions="End">
            <Label Text="{Binding ErrorMessage}"
                   TextColor="White"
                   FontSize="14"
                   HorizontalOptions="Center" />
        </Frame>
    </Grid>

</ContentPage>
