<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.Inventory.InventorySelectionPage"
             x:Name="InventorySelectionPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Inventory"
             xmlns:models="clr-namespace:WmsApp.Models.Inventory"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:InventorySelectionViewModel"
             Title="{loc:Translate Key=InventorySelection_Title}">



    <Grid Padding="20" RowSpacing="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Frame Grid.Row="0" BackgroundColor="{StaticResource Primary}" 
               CornerRadius="10" 
               Padding="15">
            <StackLayout>
                <Label Text="{loc:Translate Key=InventorySelection_Header}" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       TextColor="White" 
                       HorizontalOptions="Center" />
                <Label Text="{loc:Translate Key=InventorySelection_Subtitle}" 
                       FontSize="14" 
                       TextColor="White" 
                       HorizontalOptions="Center" 
                       Margin="0,5,0,0" />
            </StackLayout>
        </Frame>

        <!-- Loading Indicator -->
        <ActivityIndicator Grid.Row="1" IsVisible="{Binding IsLoading}" 
                           IsRunning="{Binding IsLoading}" 
                           Color="{StaticResource Primary}" 
                           HeightRequest="50" />

        <!-- Error Message -->
        <Frame Grid.Row="2" IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" 
               BackgroundColor="LightCoral" 
               CornerRadius="8" 
               Padding="15">
            <Label Text="{Binding ErrorMessage}" 
                   TextColor="DarkRed" 
                   FontSize="14" 
                   HorizontalOptions="Center" />
        </Frame>

        <!-- Section title -->
        <Label Grid.Row="3" Text="{loc:Translate Key=InventorySelection_ChooseType}" 
               FontSize="16" 
               FontAttributes="Bold" 
               Margin="0,10,0,5"
               IsVisible="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}" />

        <!-- Empty list message -->
        <Frame Grid.Row="4" IsVisible="{Binding InventoryOptions.Count, Converter={StaticResource IntToBoolConverter}, ConverterParameter=0}"
               BackgroundColor="LightYellow" CornerRadius="8" Padding="10">
            <Label Text="Brak dostępnych typów inwentaryzacji" TextColor="DarkOrange" />
        </Frame>

        <!-- Inventory Options List -->
        <CollectionView Grid.Row="5" ItemsSource="{Binding InventoryOptions}" 
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedOption}"
                        ItemsLayout="VerticalList"
                        Margin="0">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame x:DataType="models:InventoryOptionDto" BackgroundColor="White" 
                           CornerRadius="12" 
                           HasShadow="True" 
                           Padding="0" 
                           Margin="0,5">
                                
                                <Grid Padding="15" ColumnSpacing="15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- Icon -->
                                    <Label Grid.Column="0" 
                                           Text="{Binding Icon}" 
                                           FontSize="32" 
                                           VerticalOptions="Center" />
                                    
                                    <!-- Content -->
                                    <StackLayout Grid.Column="1" 
                                                 VerticalOptions="Center" 
                                                 Spacing="3">
                                        <Label Text="{Binding Name}" 
                                               FontSize="16" 
                                               FontAttributes="Bold" 
                                               TextColor="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}, ConverterParameter='Black|Gray'}" />
                                        <Label Text="{Binding Description}" 
                                               FontSize="12" 
                                               TextColor="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}, ConverterParameter='Gray|LightGray'}" />
                                    </StackLayout>
                                    
                                    <!-- Status Indicator -->
                                    <Label Grid.Column="2" 
                                           Text="{Binding IsEnabled, Converter={StaticResource BoolToStringConverter}, ConverterParameter='▶|🚫'}" 
                                           FontSize="20" 
                                           VerticalOptions="Center" 
                                           TextColor="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}, ConverterParameter='Green|Red'}" />
                                    
                                    <!-- Disabled Overlay on top of content -->
                                    <BoxView Grid.ColumnSpan="3"
                                             Color="#80FFFFFF"
                                             IsVisible="{Binding IsEnabled, Converter={StaticResource InvertedBoolConverter}}"
                                             InputTransparent="True" />
                                </Grid>
                            </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <!-- Action Buttons -->
        <StackLayout Grid.Row="6" Orientation="Horizontal" 
                     HorizontalOptions="FillAndExpand" 
                     Spacing="10" 
                     Margin="0,20,0,0">
            
            <Button Text="{loc:Translate Key=Common_Refresh}" 
                    Command="{Binding RefreshCommand}" 
                    BackgroundColor="{StaticResource Secondary}" 
                    TextColor="White" 
                    FontSize="14" 
                    HorizontalOptions="FillAndExpand" />
            
            <Button Text="{loc:Translate Key=Common_Back}" 
                    Command="{Binding GoBackCommand}" 
                    BackgroundColor="{StaticResource Tertiary}" 
                    TextColor="White" 
                    FontSize="14" 
                    HorizontalOptions="FillAndExpand" />
        </StackLayout>

    </Grid>
</ContentPage>
