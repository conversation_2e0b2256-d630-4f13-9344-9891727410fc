namespace Wms.Application.DTOs.Pallets;

public record MovePalletRequest
{
    public string PalletCode { get; set; } = null!; // SSCC lub DS kod
    public string ToLocationCode { get; init; } = null!; // MP-XXX-XXX-XXX-XXX
    public string? Notes { get; init; }
}

public record MovePalletResponse
{
    public string PalletCode { get; init; } = null!;
    public string FromLocationCode { get; init; } = null!;
    public string ToLocationCode { get; init; } = null!;
    public DateTime MovementTime { get; init; }
    public string MovedBy { get; init; } = null!;
    public int MovementId { get; init; }
}

public record LocationInfo
{
    public int Id { get; init; }
    public string Code { get; init; } = null!;
    public int Hala { get; init; }
    public string Regal { get; init; } = null!;
    public int Miejsce { get; init; }
    public string? Poziom { get; init; }
    public bool IsVisible { get; init; }
    public bool IsPickingLocation { get; init; }
    public int MaxCapacity { get; init; }
    public int CurrentPalletCount { get; init; }
}

public record PalletInfo
{
    public int Id { get; init; }
    public string? MainSSCC { get; init; }
    public LocationInfo? CurrentLocation { get; init; }
    public DateTime? LastMovementAt { get; init; }
    public List<LabelInfo> Labels { get; init; } = new();
}

public record LabelInfo
{
    public int Id { get; init; }
    public string? SSCC { get; init; }
    public string? ClientLabel { get; init; }
    public decimal? Quantity { get; init; }
    public DateOnly? ExpiryDate { get; init; }
    public string? Batch { get; init; }
    public bool IsActive { get; init; }
}
