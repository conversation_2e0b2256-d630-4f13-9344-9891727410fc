using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using Wms.Api.Controllers;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;

namespace Wms.Api.Tests.Controllers;

public class PalletsControllerTests
{
    private readonly Mock<IPalletService> _mockPalletService;
    private readonly Mock<ILogger<PalletsController>> _mockLogger;
    private readonly PalletsController _controller;

    public PalletsControllerTests()
    {
        _mockPalletService = new Mock<IPalletService>();
        _mockLogger = new Mock<ILogger<PalletsController>>();
        _controller = new PalletsController(_mockPalletService.Object, _mockLogger.Object);
        
        // Konfiguracja HttpContext dla testów
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-Forwarded-For"] = "*************";
        httpContext.Request.Headers["X-Device-Id"] = "scanner-001";
        httpContext.Connection.RemoteIpAddress = System.Net.IPAddress.Parse("127.0.0.1");
        
        var claims = new List<Claim>
        {
            new("userId", "1")
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var claimsPrincipal = new ClaimsPrincipal(identity);
        httpContext.User = claimsPrincipal;
        
        _controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };
    }

    #region MovePallet Tests

    [Fact]
    public async Task MovePallet_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01",
            Notes = "Test move"
        };

        var expectedResponse = new MovePalletResponse
        {
            PalletCode = palletCode,
            FromLocationCode = "MP-01-A-01-02",
            ToLocationCode = "MP-01-A-01-01",
            MovementTime = DateTime.UtcNow,
            MovedBy = "Jan Kowalski",
            MovementId = 123
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(
                It.Is<MovePalletRequest>(r => r.PalletCode == palletCode),
                1,
                "scanner-001",
                "*************"))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.MovePallet(palletCode, request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
        
        // Verify that PalletCode was set from route
        request.PalletCode.Should().Be(palletCode);
        
        _mockPalletService.Verify(s => s.MovePalletAsync(
            It.Is<MovePalletRequest>(r => r.PalletCode == palletCode), 
            1, 
            "scanner-001", 
            "*************"), Times.Once);
    }

    [Fact]
    public async Task MovePallet_WithInvalidPalletCode_ReturnsBadRequest()
    {
        // Arrange
        var palletCode = "INVALID-CODE";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new ArgumentException($"Invalid pallet code format: {palletCode}"));

        // Act
        var result = await _controller.MovePallet(palletCode, request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = badRequestResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(400);
        problemDetails.Title.Should().Be("Nieprawidłowe dane");
        problemDetails.Detail.Should().Be($"Invalid pallet code format: {palletCode}");
    }

    [Fact]
    public async Task MovePallet_WithInvalidLocationCode_ReturnsBadRequest()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "INVALID-LOCATION"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new ArgumentException("Invalid location code format: INVALID-LOCATION"));

        // Act
        var result = await _controller.MovePallet(palletCode, request);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var problemDetails = ((BadRequestObjectResult)result.Result!).Value as ProblemDetails;
        problemDetails!.Detail.Should().Be("Invalid location code format: INVALID-LOCATION");
    }

    [Fact]
    public async Task MovePallet_WithNonExistentPallet_ReturnsNotFound()
    {
        // Arrange
        var palletCode = "999999999999999999";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new KeyNotFoundException($"Pallet with code {palletCode} not found"));

        // Act
        var result = await _controller.MovePallet(palletCode, request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<NotFoundObjectResult>();
        
        var notFoundResult = result.Result as NotFoundObjectResult;
        notFoundResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = notFoundResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(404);
        problemDetails.Title.Should().Be("Zasób nie znaleziony");
        problemDetails.Detail.Should().Be($"Pallet with code {palletCode} not found");
    }

    [Fact]
    public async Task MovePallet_WithLocationAtCapacity_ReturnsConflict()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("Target location MP-01-A-01-01 is at maximum capacity"));

        // Act
        var result = await _controller.MovePallet(palletCode, request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<ConflictObjectResult>();
        
        var conflictResult = result.Result as ConflictObjectResult;
        conflictResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = conflictResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(409);
        problemDetails.Title.Should().Be("Operacja niedozwolona");
        problemDetails.Detail.Should().Be("Target location MP-01-A-01-01 is at maximum capacity");
    }

    [Fact]
    public async Task MovePallet_LogsSuccessfulMove()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        var response = new MovePalletResponse
        {
            PalletCode = palletCode,
            FromLocationCode = "MP-01-A-01-02",
            ToLocationCode = "MP-01-A-01-01",
            MovementTime = DateTime.UtcNow,
            MovedBy = "Jan Kowalski",
            MovementId = 123
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(response);

        // Act
        await _controller.MovePallet(palletCode, request);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Pallet {palletCode} successfully moved to {request.ToLocationCode} by user 1")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task MovePallet_ExtractsCorrectUserContext()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        var response = new MovePalletResponse
        {
            PalletCode = palletCode,
            FromLocationCode = "MP-01-A-01-02",
            ToLocationCode = "MP-01-A-01-01",
            MovementTime = DateTime.UtcNow,
            MovedBy = "Jan Kowalski",
            MovementId = 123
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), 1, "scanner-001", "*************"))
            .ReturnsAsync(response);

        // Act
        await _controller.MovePallet(palletCode, request);

        // Assert
        _mockPalletService.Verify(s => s.MovePalletAsync(
            It.IsAny<MovePalletRequest>(), 
            1, 
            "scanner-001", 
            "*************"), Times.Once);
    }

    #endregion

    #region GetPallet Tests

    [Fact]
    public async Task GetPallet_WithValidPalletCode_ReturnsOkResult()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var expectedPalletInfo = new PalletInfo
        {
            Id = 1,
            MainSSCC = palletCode,
            CurrentLocation = new LocationInfo
            {
                Id = 10,
                Code = "MP-01-A-01-01",
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "01",
                IsVisible = true,
                IsPickingLocation = false,
                MaxCapacity = 10,
                CurrentPalletCount = 5
            },
            LastMovementAt = DateTime.UtcNow.AddMinutes(-30),
            Labels = new List<LabelInfo>
            {
                new()
                {
                    Id = 1,
                    SSCC = palletCode,
                    ClientLabel = "CLIENT-123",
                    Quantity = 100,
                    ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddDays(30)),
                    Batch = "BATCH-001",
                    IsActive = true
                }
            }
        };

        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ReturnsAsync(expectedPalletInfo);

        // Act
        var result = await _controller.GetPallet(palletCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedPalletInfo);
        
        _mockPalletService.Verify(s => s.GetPalletInfoAsync(palletCode), Times.Once);
    }

    [Fact]
    public async Task GetPallet_WithNonExistentPallet_ReturnsNotFound()
    {
        // Arrange
        var palletCode = "999999999999999999";
        
        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ReturnsAsync((PalletInfo?)null);

        // Act
        var result = await _controller.GetPallet(palletCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<NotFoundObjectResult>();
        
        var notFoundResult = result.Result as NotFoundObjectResult;
        notFoundResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = notFoundResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(404);
        problemDetails.Title.Should().Be("Paleta nie znaleziona");
        problemDetails.Detail.Should().Be($"Paleta {palletCode} nie została znaleziona w systemie");
    }

    [Theory]
    [InlineData("INVALID-CODE")]
    [InlineData("")]
    [InlineData("123")]
    [InlineData("TOO-LONG-CODE-THAT-EXCEEDS-LIMITS")]
    public async Task GetPallet_WithInvalidPalletCode_ReturnsBadRequest(string palletCode)
    {
        // Arrange
        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ThrowsAsync(new ArgumentException($"Invalid pallet code format: {palletCode}"));

        // Act
        var result = await _controller.GetPallet(palletCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = badRequestResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(400);
        problemDetails.Title.Should().Be("Nieprawidłowe dane");
        problemDetails.Detail.Should().Be($"Invalid pallet code format: {palletCode}");
    }

    [Fact]
    public async Task GetPallet_LogsWarningOnArgumentException()
    {
        // Arrange
        var palletCode = "INVALID-CODE";
        var exceptionMessage = "Invalid pallet code format";
        var exception = new ArgumentException(exceptionMessage);
        
        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ThrowsAsync(exception);

        // Act
        await _controller.GetPallet(palletCode);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid pallet code format")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetPallet_WithComplexPalletInfo_ReturnsFullData()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var expectedPalletInfo = new PalletInfo
        {
            Id = 42,
            MainSSCC = palletCode,
            CurrentLocation = new LocationInfo
            {
                Id = 100,
                Code = "MP-05-Z-10-05",
                Hala = 5,
                Regal = "Z",
                Miejsce = 10,
                Poziom = "05",
                IsVisible = true,
                IsPickingLocation = true,
                MaxCapacity = 20,
                CurrentPalletCount = 15
            },
            LastMovementAt = DateTime.UtcNow.AddHours(-2),
            Labels = new List<LabelInfo>
            {
                new()
                {
                    Id = 100,
                    SSCC = palletCode,
                    ClientLabel = "CLIENT-ABC-123",
                    Quantity = 250.5m,
                    ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddMonths(6)),
                    Batch = "BATCH-2024-001",
                    IsActive = true
                },
                new()
                {
                    Id = 101,
                    SSCC = "987654321098765432",
                    ClientLabel = "CLIENT-DEF-456",
                    Quantity = 100.0m,
                    ExpiryDate = DateOnly.FromDateTime(DateTime.Now.AddMonths(3)),
                    Batch = "BATCH-2024-002",
                    IsActive = true
                }
            }
        };

        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ReturnsAsync(expectedPalletInfo);

        // Act
        var result = await _controller.GetPallet(palletCode);

        // Assert
        var okResult = result.Result as OkObjectResult;
        var palletInfo = okResult!.Value as PalletInfo;

        palletInfo.Should().NotBeNull();
        palletInfo!.Id.Should().Be(42);
        palletInfo.MainSSCC.Should().Be(palletCode);
        palletInfo.CurrentLocation.Should().NotBeNull();
        palletInfo.CurrentLocation!.Code.Should().Be("MP-05-Z-10-05");
        palletInfo.Labels.Should().HaveCount(2);
        palletInfo.Labels[0].ClientLabel.Should().Be("CLIENT-ABC-123");
        palletInfo.Labels[1].Quantity.Should().Be(100.0m);
    }

    [Theory]
    [InlineData("123456789012345678")] // SSCC
    [InlineData("DS-12345678")] // DS code
    public async Task GetPallet_WithVariousValidCodes_CallsServiceCorrectly(string palletCode)
    {
        // Arrange
        var palletInfo = new PalletInfo
        {
            Id = 1,
            MainSSCC = palletCode,
            Labels = new List<LabelInfo>()
        };

        _mockPalletService
            .Setup(s => s.GetPalletInfoAsync(palletCode))
            .ReturnsAsync(palletInfo);

        // Act
        await _controller.GetPallet(palletCode);

        // Assert
        _mockPalletService.Verify(s => s.GetPalletInfoAsync(palletCode), Times.Once);
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task MovePallet_LogsWarningOnArgumentException()
    {
        // Arrange
        var palletCode = "INVALID-CODE";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new ArgumentException("Invalid input"));

        // Act
        await _controller.MovePallet(palletCode, request);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid input for pallet move")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task MovePallet_LogsWarningOnKeyNotFoundException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new KeyNotFoundException("Resource not found"));

        // Act
        await _controller.MovePallet(palletCode, request);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Resource not found for pallet move")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task MovePallet_LogsWarningOnInvalidOperationException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-01-A-01-01"
        };

        _mockPalletService
            .Setup(s => s.MovePalletAsync(It.IsAny<MovePalletRequest>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("Operation not allowed"));

        // Act
        await _controller.MovePallet(palletCode, request);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid operation for pallet move")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion
}
