using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using Wms.Api.Controllers;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;

namespace Wms.Api.Tests.Controllers;

public class LocationsControllerTests
{
    private readonly Mock<IPalletService> _mockPalletService;
    private readonly Mock<ILogger<LocationsController>> _mockLogger;
    private readonly LocationsController _controller;

    public LocationsControllerTests()
    {
        _mockPalletService = new Mock<IPalletService>();
        _mockLogger = new Mock<ILogger<LocationsController>>();
        _controller = new LocationsController(_mockPalletService.Object, _mockLogger.Object);
        
        // Konfiguracja HttpContext dla testów autoryzacji
        var httpContext = new DefaultHttpContext();
        var claims = new List<Claim>
        {
            new("userId", "1")
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var claimsPrincipal = new ClaimsPrincipal(identity);
        httpContext.User = claimsPrincipal;
        
        _controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };
    }

    [Fact]
    public async Task GetLocation_WithValidLocationCode_ReturnsOkResult()
    {
        // Arrange
        var locationCode = "MP-01-A-01-01";
        var expectedLocationInfo = new LocationInfo
        {
            Id = 1,
            Code = locationCode,
            Hala = 1,
            Regal = "A",
            Miejsce = 1,
            Poziom = "01",
            IsVisible = true,
            IsPickingLocation = false,
            MaxCapacity = 10,
            CurrentPalletCount = 3
        };

        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ReturnsAsync(expectedLocationInfo);

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedLocationInfo);
        
        _mockPalletService.Verify(s => s.GetLocationInfoAsync(locationCode), Times.Once);
    }

    [Fact]
    public async Task GetLocation_WithNonExistentLocation_ReturnsNotFound()
    {
        // Arrange
        var locationCode = "MP-99-Z-99-99";
        
        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ReturnsAsync((LocationInfo?)null);

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<NotFoundObjectResult>();
        
        var notFoundResult = result.Result as NotFoundObjectResult;
        notFoundResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = notFoundResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(404);
        problemDetails.Title.Should().Be("Location Not Found");
        problemDetails.Detail.Should().Be($"Location with code {locationCode} was not found");
    }

    [Theory]
    [InlineData("INVALID-CODE")]
    [InlineData("")]
    [InlineData("MP-AA-BB-CC")]
    [InlineData("123-456-789")]
    public async Task GetLocation_WithInvalidLocationCode_ReturnsBadRequest(string locationCode)
    {
        // Arrange
        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ThrowsAsync(new ArgumentException($"Invalid location code format: {locationCode}"));

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = result.Result as BadRequestObjectResult;
        badRequestResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = badRequestResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(400);
        problemDetails.Title.Should().Be("Invalid Input");
        problemDetails.Detail.Should().Be($"Invalid location code format: {locationCode}");
    }

    [Fact]
    public async Task GetLocation_WithValidLocationCodeButServiceThrows_ReturnsBadRequest()
    {
        // Arrange
        var locationCode = "MP-01-A-01-01";
        var exceptionMessage = "Location code validation failed";
        
        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ThrowsAsync(new ArgumentException(exceptionMessage));

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        
        var badRequestResult = result.Result as BadRequestObjectResult;
        var problemDetails = badRequestResult!.Value as ProblemDetails;
        problemDetails!.Detail.Should().Be(exceptionMessage);
        
        // Verify that logger was called with warning
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid location code format")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetLocation_LogsWarningOnArgumentException()
    {
        // Arrange
        var locationCode = "INVALID-CODE";
        var exceptionMessage = "Invalid location code format";
        var exception = new ArgumentException(exceptionMessage);
        
        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ThrowsAsync(exception);

        // Act
        await _controller.GetLocation(locationCode);

        // Assert
        _mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid location code format")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetLocation_WithComplexLocationInfo_ReturnsFullData()
    {
        // Arrange
        var locationCode = "MP-02-B-05-03";
        var expectedLocationInfo = new LocationInfo
        {
            Id = 125,
            Code = locationCode,
            Hala = 2,
            Regal = "B",
            Miejsce = 5,
            Poziom = "03",
            IsVisible = true,
            IsPickingLocation = true,
            MaxCapacity = 25,
            CurrentPalletCount = 15
        };

        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ReturnsAsync(expectedLocationInfo);

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        var okResult = result.Result as OkObjectResult;
        var locationInfo = okResult!.Value as LocationInfo;

        locationInfo.Should().NotBeNull();
        locationInfo!.Id.Should().Be(125);
        locationInfo.Code.Should().Be(locationCode);
        locationInfo.Hala.Should().Be(2);
        locationInfo.Regal.Should().Be("B");
        locationInfo.Miejsce.Should().Be(5);
        locationInfo.Poziom.Should().Be("03");
        locationInfo.IsVisible.Should().BeTrue();
        locationInfo.IsPickingLocation.Should().BeTrue();
        locationInfo.MaxCapacity.Should().Be(25);
        locationInfo.CurrentPalletCount.Should().Be(15);
    }

    [Fact]
    public async Task GetLocation_WithNullLocationCode_HandlesGracefully()
    {
        // Arrange
        string locationCode = null!;
        
        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ThrowsAsync(new ArgumentException("Location code cannot be null"));

        // Act
        var result = await _controller.GetLocation(locationCode);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }

    [Theory]
    [InlineData("MP-01-A-01-01")] // Standard format
    [InlineData("MP-10-Z-99-10")] // Edge values
    [InlineData("MP-01-AA-01-AB")] // Extended regal codes
    public async Task GetLocation_WithVariousValidCodes_CallsServiceCorrectly(string locationCode)
    {
        // Arrange
        var locationInfo = new LocationInfo
        {
            Id = 1,
            Code = locationCode,
            Hala = 1,
            Regal = "A",
            Miejsce = 1,
            IsVisible = true,
            MaxCapacity = 10,
            CurrentPalletCount = 0
        };

        _mockPalletService
            .Setup(s => s.GetLocationInfoAsync(locationCode))
            .ReturnsAsync(locationInfo);

        // Act
        await _controller.GetLocation(locationCode);

        // Assert
        _mockPalletService.Verify(s => s.GetLocationInfoAsync(locationCode), Times.Once);
    }
}
