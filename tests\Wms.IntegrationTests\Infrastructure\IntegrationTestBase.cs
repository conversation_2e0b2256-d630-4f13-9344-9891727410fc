using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Wms.Infrastructure.Data;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Wms.IntegrationTests.Infrastructure;

public abstract class IntegrationTestBase : IClassFixture<WmsWebApplicationFactory>, IAsyncLifetime
{
    protected readonly WmsWebApplicationFactory Factory;
    protected readonly HttpClient Client;
    protected readonly JsonSerializerOptions JsonOptions;

    protected IntegrationTestBase(WmsWebApplicationFactory factory)
    {
        Factory = factory;
        Client = factory.CreateClient();
        
        JsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    protected async Task<WmsDbContext> GetDbContextAsync()
    {
        var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<WmsDbContext>();
        return context;
    }

    protected async Task SeedTestDataAsync()
    {
        using var context = await GetDbContextAsync();
        
        // Clear existing data
        await context.Database.ExecuteSqlRawAsync("SET FOREIGN_KEY_CHECKS = 0");
        
        var tableNames = context.Model.GetEntityTypes()
            .Select(t => t.GetTableName())
            .Where(name => !string.IsNullOrEmpty(name))
            .ToList();
        
        foreach (var tableName in tableNames)
        {
            await context.Database.ExecuteSqlRawAsync($"DELETE FROM `{tableName}`");
        }
        
        await context.Database.ExecuteSqlRawAsync("SET FOREIGN_KEY_CHECKS = 1");

        // Seed test data - podstawowe dane wymagane do testów
        await SeedUsersAsync(context);
        await SeedLocationsAsync(context);
        await SeedPalletsAndLabelsAsync(context);
        
        await context.SaveChangesAsync();
    }

    private Task SeedUsersAsync(WmsDbContext context)
    {
        // Dodaj użytkownika testowego
        var user = new Wms.Domain.Entities.User
        {
            Login = "testuser",
            ImieNazwisko = "Test User",
            Haslo = BCrypt.Net.BCrypt.HashPassword("password123"), // W prawdziwej aplikacji już zahashowane
            Stanowisko = "Tester",
            Email = "<EMAIL>",
            IsActive = true,
            NumerKarty = "1234567890"
        };
        
        context.Users.Add(user);
        return Task.CompletedTask;
    }

    private Task SeedLocationsAsync(WmsDbContext context)
    {
        var locations = new[]
        {
            new Wms.Domain.Entities.Location
            {
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "1",
                Widoczne = 1, // IsVisible = true
                MaxPojemnosc = 10,
                Zbiorka = 0 // IsPickingLocation = false
            },
            new Wms.Domain.Entities.Location
            {
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "2",
                Widoczne = 1, // IsVisible = true
                MaxPojemnosc = 10,
                Zbiorka = 0 // IsPickingLocation = false
            },
            new Wms.Domain.Entities.Location
            {
                Hala = 2,
                Regal = "B",
                Miejsce = 5,
                Poziom = "1",
                Widoczne = 1, // IsVisible = true
                MaxPojemnosc = 5,
                Zbiorka = 1 // IsPickingLocation = true
            }
        };
        
        context.Locations.AddRange(locations);
        return Task.CompletedTask;
    }

    private Task SeedPalletsAndLabelsAsync(WmsDbContext context)
    {
        // Zapisz zmiany najpierw, żeby uzyskać ID lokalizacji
        context.SaveChanges();
        
        // Znajdź lokalizacje po utworzeniu
        var location1 = context.Locations.First(l => l.Hala == 1 && l.Regal == "A" && l.Miejsce == 1 && l.Poziom == "1");
        var location2 = context.Locations.First(l => l.Hala == 1 && l.Regal == "A" && l.Miejsce == 1 && l.Poziom == "2");
        
        // Dodaj paletę
        var pallet = new Wms.Domain.Entities.Pallet();
        context.Pallets.Add(pallet);
        context.SaveChanges(); // Zapisz, żeby uzyskać ID palety
        
        // Dodaj etykiety
        var labels = new[]
        {
            new Wms.Domain.Entities.Label
            {
                Id = 1,
                Sscc = "000000123456789012",
                EtykietaKlient = "CLIENT-001",
                SystemId = 1,
                PaletaId = pallet.Id,
                Miejscep = location1.Id,
                Ilosc = 100,
                DataWaznosci = DateOnly.FromDateTime(DateTime.Now.AddMonths(6)),
                Lot = "BATCH-001",
                Active = 1, // IsActive = true
                Ts = DateTime.UtcNow
            },
            new Wms.Domain.Entities.Label
            {
                Id = 2,
                Sscc = "000000987654321096",
                EtykietaKlient = "CLIENT-002", 
                SystemId = 1,
                PaletaId = pallet.Id,
                Miejscep = location2.Id,
                Ilosc = 50,
                DataWaznosci = DateOnly.FromDateTime(DateTime.Now.AddMonths(3)),
                Lot = "BATCH-002",
                Active = 1, // IsActive = true
                Ts = DateTime.UtcNow
            }
        };
        
        context.Labels.AddRange(labels);
        context.SaveChanges(); // Zapisz etykiety
        
        // Debug - sprawdź co zostało zapisane
        var palletCount = context.Pallets.Count();
        var labelCount = context.Labels.Count();
        var locationCount = context.Locations.Count();
        
        Console.WriteLine($"DEBUG: Seeded data - Pallets: {palletCount}, Labels: {labelCount}, Locations: {locationCount}");
        Console.WriteLine($"DEBUG: First label SSCC: {context.Labels.FirstOrDefault()?.Sscc}, PaletaId: {context.Labels.FirstOrDefault()?.PaletaId}, Active: {context.Labels.FirstOrDefault()?.Active}");
        
        return Task.CompletedTask;
    }

    protected void SetAuthorizationHeader(string token)
    {
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    protected void SetDeviceIdHeader(string deviceId)
    {
        Client.DefaultRequestHeaders.Add("X-Device-Id", deviceId);
    }

    protected void ClearHeaders()
    {
        Client.DefaultRequestHeaders.Clear();
    }

    protected StringContent CreateJsonContent(object data)
    {
        var json = JsonSerializer.Serialize(data, JsonOptions);
        return new StringContent(json, Encoding.UTF8, "application/json");
    }

    protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
    {
        var json = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(json, JsonOptions);
    }

    public virtual async Task InitializeAsync()
    {
        await SeedTestDataAsync();
    }

    public virtual async Task DisposeAsync()
    {
        Client.Dispose();
    }
}
