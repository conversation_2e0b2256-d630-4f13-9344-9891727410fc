# ADR-002: Database Mapping Strategy

## Status
Accepted

## Date
2025-09-01

## Context
The WMS system must integrate with an existing MySQL database that contains production data and established table structures with Polish column names and legacy conventions. We need to decide how to handle the mismatch between modern C# naming conventions and existing database schema.

## Decision
We will map existing database tables to C# entities while maintaining compatibility with the legacy schema:

### Mapping Strategy:
1. **Entity Names** - Use English, PascalCase entity names (e.g., `User`, `Location`, `Pallet`)
2. **Table Names** - Map to existing Polish table names using Fluent API (e.g., `pracownicy`, `miejsca`, `palety`)
3. **Column Names** - Map C# properties to existing Polish column names
4. **No Schema Changes** - Avoid modifying existing production tables
5. **Additive Approach** - Only add new tables/columns when absolutely necessary

### Key Mappings:

| Entity | Table | Purpose |
|--------|-------|---------|
| `User` | `pracownicy` | Employee data |
| `Location` | `miejsca` | Storage locations |
| `Pallet` | `palety` | Pallet information |
| `Label` | `etykiety` | Labels/barcodes |
| `Movement` | `ruchy` | Movement history |
| `Session` | `sessions` | JWT sessions (NEW) |

## Rationale

### Benefits:
1. **Zero Disruption** - No changes to existing production data
2. **Clean Code** - Modern C# naming conventions in application code
3. **Legacy Compatibility** - Maintains compatibility with existing systems
4. **Gradual Migration** - Allows for future database modernization

### Implementation Details:
- Use EF Core Fluent API for all mappings
- Composite keys mapped correctly (e.g., `Label` entity)
- Navigation properties maintain relationships
- Helper properties for business logic (e.g., `IsActive`)

## Example Mapping

```csharp
// Entity with clean C# names
public class User : BaseEntity
{
    public int Id { get; set; }
    public string? ImieNazwisko { get; set; } // Keep original for compatibility
    public string? CardNumber { get; set; }
    public bool IsActive { get; set; }
}

// Fluent API mapping to legacy table
modelBuilder.Entity<User>(entity =>
{
    entity.ToTable("pracownicy");
    entity.HasKey(e => e.Id);
    entity.Property(e => e.Id).HasColumnName("id");
    entity.Property(e => e.CardNumber).HasColumnName("karta");
});
```

## Database Schema Decisions

### New Tables Added:
- `sessions` - For JWT token management and session tracking

### New Columns Added:
- `pracownicy.is_active` - For soft delete of users

### Unchanged Legacy Structure:
- All existing tables maintain original structure
- Original relationships preserved
- No data migration required

## Consequences

### Positive:
- Zero risk to existing production data
- Clean, maintainable C# code
- Backward compatibility with legacy systems
- Future-proof for database modernization

### Negative:
- Mixed naming conventions (Polish DB vs English code)
- Complex Fluent API configuration
- Some unintuitive property names in entities

## Implementation Status
- ✅ All core entities mapped to existing tables
- ✅ Composite keys handled correctly
- ✅ Navigation properties configured
- ✅ New tables created with minimal impact
- ✅ EF Core migrations configured for design-time
