#!/usr/bin/env python3
"""
Prosty HTTP proxy do przekierowania zapytań z emulatora Android do API WMS
Uruchom: python api-proxy.py
Emulator będzie mógł połączyć się przez http://********:8080
"""

import http.server
import socketserver
import urllib.request
import urllib.error
import json

TARGET_HOST = "***********"
TARGET_PORT = 80
PROXY_PORT = 8080

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request('GET')
    
    def do_POST(self):
        self.proxy_request('POST')
    
    def do_PUT(self):
        self.proxy_request('PUT')
    
    def do_DELETE(self):
        self.proxy_request('DELETE')

    def proxy_request(self, method):
        try:
            # Zbuduj URL docelowy
            target_url = f"http://{TARGET_HOST}:{TARGET_PORT}{self.path}"
            
            print(f"[{method}] Proxying: {self.path} -> {target_url}")
            
            # Przygotuj dane dla POST/PUT
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = None
            if content_length > 0:
                post_data = self.rfile.read(content_length)
            
            # Stwórz zapytanie
            headers = {}
            for header_name, header_value in self.headers.items():
                if header_name.lower() not in ['host', 'connection', 'transfer-encoding']:
                    headers[header_name] = header_value
            
            # Jeśli mamy dane POST/PUT, dodaj Content-Length
            if post_data:
                headers['Content-Length'] = str(len(post_data))
            
            # Wykonaj zapytanie
            req = urllib.request.Request(
                target_url, 
                data=post_data, 
                headers=headers, 
                method=method
            )
            
            with urllib.request.urlopen(req) as response:
                # Wyślij status i nagłówki
                self.send_response(response.status)
                for header_name, header_value in response.headers.items():
                    if header_name.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header_name, header_value)
                self.end_headers()
                
                # Wyślij zawartość
                self.wfile.write(response.read())
                
        except urllib.error.HTTPError as e:
            # Przekaż błędy HTTP
            self.send_response(e.status)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(e.read())
            
        except Exception as e:
            # Błąd proxy
            print(f"Proxy error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = json.dumps({
                "error": "Proxy error",
                "message": str(e)
            }).encode()
            self.wfile.write(error_response)

    def log_message(self, format, *args):
        # Logowanie zapytań
        print(f"[PROXY] {format % args}")

if __name__ == "__main__":
    print(f"🚀 Starting API Proxy Server...")
    print(f"   Target API: http://{TARGET_HOST}:{TARGET_PORT}")
    print(f"   Proxy Port: {PROXY_PORT}")
    print(f"   Android Emulator URL: http://********:{PROXY_PORT}")
    print(f"   Local test URL: http://localhost:{PROXY_PORT}/api/v1/health")
    print("")
    
    # Nasłuchuj na wszystkich interfejsach sieciowych
    with socketserver.TCPServer(("0.0.0.0", PROXY_PORT), ProxyHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️  Proxy server stopped")
