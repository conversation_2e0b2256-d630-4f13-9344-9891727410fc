using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.Interfaces;
using Wms.Application.Services;

namespace Wms.UnitTests.Application.Services;

public class NumberGenerationServiceTests
{
    private readonly Mock<IDocNumberRepository> _mockDocNumberRepository;
    private readonly Mock<ILogger<NumberGenerationService>> _mockLogger;
    private readonly NumberGenerationService _service;

    public NumberGenerationServiceTests()
    {
        _mockDocNumberRepository = new Mock<IDocNumberRepository>();
        _mockLogger = new Mock<ILogger<NumberGenerationService>>();
        _service = new NumberGenerationService(_mockDocNumberRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetNextPalletNumberAsync_ShouldReturnNextNumber()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.GetNextNumberAsync("nrpalety", It.IsAny<CancellationToken>()))
            .ReturnsAsync(650001);

        // Act
        var result = await _service.GetNextPalletNumberAsync();

        // Assert
        result.Should().Be(650001);
        _mockDocNumberRepository.Verify(x => x.GetNextNumberAsync("nrpalety", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetNextLabelNumberAsync_ShouldReturnNextNumber()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.GetNextNumberAsync("nretykiety", It.IsAny<CancellationToken>()))
            .ReturnsAsync(42);

        // Act
        var result = await _service.GetNextLabelNumberAsync();

        // Assert
        result.Should().Be(42);
        _mockDocNumberRepository.Verify(x => x.GetNextNumberAsync("nretykiety", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetNextPalletNumberAsync_WhenRepositoryThrows_ShouldRethrowException()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.GetNextNumberAsync("nrpalety", It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test error"));

        // Act & Assert
        var act = async () => await _service.GetNextPalletNumberAsync();
        await act.Should().ThrowAsync<InvalidOperationException>().WithMessage("Test error");
    }

    [Fact]
    public async Task GetNextLabelNumberAsync_WhenRepositoryThrows_ShouldRethrowException()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.GetNextNumberAsync("nretykiety", It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test error"));

        // Act & Assert
        var act = async () => await _service.GetNextLabelNumberAsync();
        await act.Should().ThrowAsync<InvalidOperationException>().WithMessage("Test error");
    }

    [Fact]
    public async Task EnsureNumberingEntriesExistAsync_ShouldCreateBothEntries()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.CreateIfNotExistsAsync("nrpalety", 650000, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Domain.Entities.DocNumber { Name = "nrpalety", Last = 650000 });

        _mockDocNumberRepository
            .Setup(x => x.CreateIfNotExistsAsync("nretykiety", 1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Domain.Entities.DocNumber { Name = "nretykiety", Last = 1 });

        // Act
        await _service.EnsureNumberingEntriesExistAsync();

        // Assert
        _mockDocNumberRepository.Verify(x => x.CreateIfNotExistsAsync("nrpalety", 650000, It.IsAny<CancellationToken>()), Times.Once);
        _mockDocNumberRepository.Verify(x => x.CreateIfNotExistsAsync("nretykiety", 1, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EnsureNumberingEntriesExistAsync_WhenRepositoryThrows_ShouldRethrowException()
    {
        // Arrange
        _mockDocNumberRepository
            .Setup(x => x.CreateIfNotExistsAsync("nrpalety", 650000, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        var act = async () => await _service.EnsureNumberingEntriesExistAsync();
        await act.Should().ThrowAsync<InvalidOperationException>().WithMessage("Database error");
    }
}
