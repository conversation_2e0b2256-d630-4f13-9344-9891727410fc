using WmsApp.Models.Update;

namespace WmsApp.Services.Contracts;

/// <summary>
/// Serwis do obsługi auto-aktualizacji aplikacji
/// </summary>
public interface IUpdateService
{
    /// <summary>
    /// Sprawdza czy dostępna jest nowa wersja aplikacji
    /// </summary>
    /// <param name="cancellationToken">Token do anulowania operacji</param>
    /// <returns>Informacje o dostępnej aktualizacji</returns>
    Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Pobiera plik APK z serwera
    /// </summary>
    /// <param name="manifest">Manifest z informacjami o aktualizacji</param>
    /// <param name="progress">Obiekt do raportowania postępu</param>
    /// <param name="cancellationToken">Token do anulowania operacji</param>
    /// <returns>Ścieżka do pobranego pliku APK</returns>
    Task<string> DownloadApkAsync(AppManifest manifest, IProgress<DownloadProgress>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Weryfikuje integralność pobranego pliku APK
    /// </summary>
    /// <param name="filePath">Ścieżka do pliku APK</param>
    /// <param name="expectedSha256">Oczekiwany hash SHA-256</param>
    /// <returns>True jeśli plik jest poprawny</returns>
    Task<bool> VerifyApkIntegrityAsync(string filePath, string expectedSha256);

    /// <summary>
    /// Uruchamia instalację APK
    /// </summary>
    /// <param name="apkFilePath">Ścieżka do pliku APK</param>
    /// <returns>True jeśli instalacja została uruchomiona</returns>
    Task<bool> InstallApkAsync(string apkFilePath);

    /// <summary>
    /// Pobiera informacje o aktualnej wersji aplikacji
    /// </summary>
    /// <returns>Informacje o wersji</returns>
    string GetCurrentVersion();

    /// <summary>
    /// Pobiera kod wersji aplikacji
    /// </summary>
    /// <returns>Kod wersji</returns>
    int GetCurrentVersionCode();

    /// <summary>
    /// Sprawdza czy urządzenie ma uprawnienia do instalowania APK z nieznanych źródeł
    /// </summary>
    /// <returns>True jeśli ma uprawnienia</returns>
    bool CanInstallFromUnknownSources();

    /// <summary>
    /// Prosi użytkownika o nadanie uprawnień do instalowania z nieznanych źródeł
    /// </summary>
    /// <returns>True jeśli użytkownik nadał uprawnienia</returns>
    Task<bool> RequestInstallPermissionAsync();
}
