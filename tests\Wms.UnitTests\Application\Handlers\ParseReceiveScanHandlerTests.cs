using AutoFixture;
using AutoFixture.AutoMoq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.DTOs;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Features.Receives.Handlers;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;
using Wms.Domain.ValueObjects;

namespace Wms.UnitTests.Application.Handlers;

/// <summary>
/// Testy jednostkowe dla ParseReceiveScanHandler
/// Pokrycie: obsługa różnych typów skanów, mocki repozytoriów, walidacja
/// </summary>
public class ParseReceiveScanHandlerTests : IDisposable
{
    private readonly IFixture _fixture;
    private readonly Mock<ICodeValidationService> _mockCodeValidationService;
    private readonly Mock<IKodRepository> _mockKodRepository;
    private readonly Mock<ILogger<ParseReceiveScanHandler>> _mockLogger;
    private readonly ParseReceiveScanHandler _handler;

    public ParseReceiveScanHandlerTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        
        _mockCodeValidationService = new Mock<ICodeValidationService>();
        _mockKodRepository = new Mock<IKodRepository>();
        _mockLogger = new Mock<ILogger<ParseReceiveScanHandler>>();

        _handler = new ParseReceiveScanHandler(
            _mockCodeValidationService.Object,
            _mockKodRepository.Object,
            _mockLogger.Object);
    }

    #region Happy Path Tests

    [Fact]
    public async Task Handle_WithValidGS1Scan_ShouldReturnSuccessWithParsedData()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "IZ(00)123456789012345678(02)12345678901234(10)LOT123(17)241231(37)100",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice",
            Context = "delivery"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            sscc: SSCCCode.Create("123456789012345678"),
            gtin: GtinCode.Create("12345678901234"),
            lot: LotNumber.Create("LOT123"),
            expiryDate: ExpiryDate.Create(new DateOnly(2024, 12, 31)),
            quantity: Quantity.Create(100),
            hasIzPrefix: true
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = true,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        var mockKod = _fixture.Build<Kod>()
            .With(k => k.Id, 42)
            .With(k => k.KodValue, "12345678901234")
            .With(k => k.KodNazwa, "Test Product")
            .With(k => k.IloscWOpakowaniu, 10)
            .Create();

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync("12345678901234", It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockKod);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.ScanType.Should().Be("GS1");
        result.HasIzPrefix.Should().BeTrue();
        result.ParsedData.Should().NotBeNull();
        result.ParsedData!.SSCC.Should().Be("123456789012345678");
        result.ParsedData!.GTIN.Should().Be("12345678901234");
        result.ParsedData!.Lot.Should().Be("LOT123");
        result.ParsedData!.ExpiryDate.Should().Be("2024-12-31");
        result.ParsedData!.Quantity.Should().Be(100);

        result.FormSuggestion.Should().NotBeNull();
        result.FormSuggestion!.KodId.Should().Be(42);
        result.FormSuggestion!.KodNazwa.Should().Be("Test Product");
        result.FormSuggestion!.SuggestedQuantity.Should().Be(1000); // 100 * 10 packaging units
    }

    [Fact]
    public async Task Handle_WithValidGS1ScanButNoProductInDatabase_ShouldReturnPartialSuggestion()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "(02)99999999999999(10)UNKNOWN_PRODUCT",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            gtin: GtinCode.Create("99999999999999"),
            lot: LotNumber.Create("UNKNOWN_PRODUCT"),
            hasIzPrefix: false
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = false,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync("99999999999999", It.IsAny<CancellationToken>()))
            .ReturnsAsync((Kod?)null);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.ScanType.Should().Be("GS1");
        result.HasIzPrefix.Should().BeFalse();

        result.FormSuggestion.Should().NotBeNull();
        result.FormSuggestion!.KodId.Should().BeNull();
        result.FormSuggestion!.Lot.Should().Be("UNKNOWN_PRODUCT");
        result.FormSuggestion!.HasSuggestions.Should().BeTrue(); // Has lot suggestion
    }

    [Theory]
    [InlineData("123456789012345678", "SSCC")]
    [InlineData("**********", "DS")]
    [InlineData("LK123", "LK")]
    [InlineData("MP-01-02-03-04", "Location")]
    public async Task Handle_WithNonGS1Scans_ShouldReturnBasicSuccess(string scanData, string expectedType)
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = scanData,
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = Enum.Parse<ScanType>(expectedType),
            HasIzPrefix = false,
            RawScanData = scanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(scanData))
            .Returns(validationResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.ScanType.Should().Be(expectedType);
        result.HasIzPrefix.Should().BeFalse();
        result.ParsedData.Should().BeNull();
        result.FormSuggestion.Should().BeNull();
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task Handle_WithInvalidScan_ShouldReturnFailure()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "INVALID_SCAN_DATA",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var validationResult = new ScanValidationResult
        {
            IsValid = false,
            ScanType = ScanType.Unknown,
            ErrorMessage = "Nieprawidłowy format skanu",
            RawScanData = command.ScanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ScanType.Should().Be("Unknown");
        result.ErrorMessage.Should().Be("Nieprawidłowy format skanu");
        result.ParsedData.Should().BeNull();
        result.FormSuggestion.Should().BeNull();
    }

    [Fact]
    public async Task Handle_WhenCodeValidationServiceThrows_ShouldReturnGenericFailure()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "TEST_SCAN",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(It.IsAny<string>()))
            .Throws(new InvalidOperationException("Service error"));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ScanType.Should().Be("Error");
        result.ErrorMessage.Should().Be("Wystąpił błąd podczas przetwarzania skanu");
    }

    [Fact]
    public async Task Handle_WhenKodRepositoryThrows_ShouldStillReturnSuccess()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "IZ(02)12345678901234",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            gtin: GtinCode.Create("12345678901234"),
            hasIzPrefix: true
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = true,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.ScanType.Should().Be("GS1");
        result.HasIzPrefix.Should().BeTrue();
        result.ParsedData.Should().NotBeNull();
        result.FormSuggestion.Should().BeNull(); // No suggestion due to database error
    }

    #endregion

    #region Business Logic Tests

    [Fact]
    public async Task Handle_WithExpiredProduct_ShouldIncludeWarningMessage()
    {
        // Arrange
        var expiredDate = new DateOnly(2020, 1, 1); // Definitely expired
        var command = new ParseReceiveScanCommand
        {
            ScanData = $"(02)12345678901234(17){expiredDate:yyMMdd}",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            gtin: GtinCode.Create("12345678901234"),
            expiryDate: ExpiryDate.Create(expiredDate),
            hasIzPrefix: false
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = false,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        var mockKod = _fixture.Build<Kod>()
            .With(k => k.KodValue, "12345678901234")
            .Create();

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync("12345678901234", It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockKod);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.FormSuggestion.Should().NotBeNull();
        result.FormSuggestion!.WarningMessage.Should().Contain("przeterminowany");
    }

    [Fact]
    public async Task Handle_WithProductExpiringSoon_ShouldIncludeWarningMessage()
    {
        // Arrange
        var soonToExpireDate = DateOnly.FromDateTime(DateTime.Today.AddDays(5)); // Expires in 5 days
        var command = new ParseReceiveScanCommand
        {
            ScanData = $"(02)12345678901234(17){soonToExpireDate:yyMMdd}",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            gtin: GtinCode.Create("12345678901234"),
            expiryDate: ExpiryDate.Create(soonToExpireDate),
            hasIzPrefix: false
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = false,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        var mockKod = _fixture.Build<Kod>()
            .With(k => k.KodValue, "12345678901234")
            .Create();

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync("12345678901234", It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockKod);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.FormSuggestion.Should().NotBeNull();
        result.FormSuggestion!.WarningMessage.Should().Contain("wygasa za");
    }

    [Theory]
    [InlineData(50, 10, 500)] // 50 packages * 10 units each = 500
    [InlineData(1, 100, 100)] // 1 package * 100 units = 100
    [InlineData(25, 1, 25)] // 25 packages * 1 unit each = 25
    public async Task Handle_ShouldCalculateQuantityCorrectly(int scannedQuantity, int packagingUnit, int expectedTotal)
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = $"(02)12345678901234(37){scannedQuantity}",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            gtin: GtinCode.Create("12345678901234"),
            quantity: Quantity.Create(scannedQuantity),
            hasIzPrefix: false
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = false,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        var mockKod = _fixture.Build<Kod>()
            .With(k => k.KodValue, "12345678901234")
            .With(k => k.IloscWOpakowaniu, packagingUnit)
            .Create();

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        _mockKodRepository
            .Setup(x => x.GetByKodValueAsync("12345678901234", It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockKod);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.FormSuggestion.Should().NotBeNull();
        result.FormSuggestion!.SuggestedQuantity.Should().Be(expectedTotal);
        result.FormSuggestion!.PackagingUnit.Should().Be(packagingUnit);
    }

    #endregion

    #region Logging Tests

    [Fact]
    public async Task Handle_ShouldLogInformationForSuccessfulParsing()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "IZ(00)123456789012345678",
            ListControlId = 42,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var gs1ParseResult = GS1ParseResult.Success(
            rawData: command.ScanData,
            sscc: SSCCCode.Create("123456789012345678"),
            hasIzPrefix: true
        );

        var validationResult = new ScanValidationResult
        {
            IsValid = true,
            ScanType = ScanType.GS1,
            HasIzPrefix = true,
            GS1Data = gs1ParseResult,
            RawScanData = command.ScanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Parsowanie skanu dla ListControl: 42")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Sparsowano GS1: HasIzPrefix=True")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogWarningForInvalidScan()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "INVALID",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var validationResult = new ScanValidationResult
        {
            IsValid = false,
            ScanType = ScanType.Unknown,
            ErrorMessage = "Test error message",
            RawScanData = command.ScanData
        };

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(command.ScanData))
            .Returns(validationResult);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Niepoprawny format skanu")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogErrorForExceptions()
    {
        // Arrange
        var command = new ParseReceiveScanCommand
        {
            ScanData = "TEST",
            ListControlId = 1,
            UserId = 100,
            DeviceId = "TestDevice"
        };

        var expectedException = new InvalidOperationException("Test exception");

        _mockCodeValidationService
            .Setup(x => x.ValidateAndParseScan(It.IsAny<string>()))
            .Throws(expectedException);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Błąd podczas parsowania skanu")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    public void Dispose()
    {
        // Clean up any resources if needed
    }
}
