# Przewodnik Stylu Kodowania - WMS MAUI

## Ogólne Zasady

### Język i Komunikacja
- **Wszystkie komentarze i nazwy po polsku** - zgodnie z domeną biznesową
- **Komunikaty dla użytkownika w języku polskim**
- **Nazwy metod i właściwości publicznych po angielsku** (konwencja .NET)
- **Nazwy prywatne mogą zawierać polskie terminy biznesowe**

### Formatowanie i Styl

#### Nazewnictwo
```csharp
// ✅ Poprawnie - klasy PascalCase
public class ReceivesSelectionViewModel

// ✅ Poprawnie - właściwości PascalCase
public string CurrentLK { get; set; }

// ✅ Poprawnie - metody PascalCase  
public async Task ProcessScanAsync()

// ✅ Poprawnie - pola prywatne camelCase z prefiksem _
private readonly IReceiveService _receiveService;

// ✅ Poprawnie - parametry i zmienne lokalne camelCase
public void UpdateAwizacja(string awizacjaId, bool isActive)

// ❌ Niepoprawnie - polskie znaki w nazwach publicznych
public string CurrentŁK { get; set; }
```

#### Organizacja Using Statements
```csharp
// System namespaces first
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

// Third-party namespaces
using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.Logging;

// Project namespaces last
using WmsApp.Models.Receives;
using WmsApp.Services.Interfaces;
```

## MVVM Patterns

### ViewModels
```csharp
// ✅ Używaj CommunityToolkit.Mvvm attributes
public partial class ReceivesSelectionViewModel : ObservableObject
{
    // ✅ ObservableProperty dla bindingów
    [ObservableProperty]
    private string currentLK = string.Empty;
    
    // ✅ RelayCommand dla komend
    [RelayCommand]
    private async Task LoadAwizacjeAsync()
    {
        // Implementation
    }
    
    // ✅ Proper dependency injection
    public ReceivesSelectionViewModel(
        IReceiveService receiveService,
        ILogger<ReceivesSelectionViewModel> logger)
    {
        _receiveService = receiveService;
        _logger = logger;
    }
}
```

### Properties i Fields
```csharp
// ✅ Nullable reference types
[ObservableProperty]
private AwizacjaDto? selectedAwizacja;

// ✅ Proper initialization
[ObservableProperty]
private string errorMessage = string.Empty;

// ✅ Meaningful defaults
[ObservableProperty]
private bool isLoading = false;

// ✅ Collections initialization
public ObservableCollection<AwizacjaDto> Awizacje { get; } = new();
```

## Error Handling

### Exception Handling Pattern
```csharp
[RelayCommand]
private async Task ProcessDataAsync()
{
    try
    {
        IsLoading = true;
        ErrorMessage = string.Empty; // Clear previous errors
        
        var result = await _service.ProcessAsync();
        
        // Handle success
        SuccessMessage = "Operacja zakończona pomyślnie";
    }
    catch (HttpRequestException ex)
    {
        _logger.LogError(ex, "Błąd komunikacji z serwerem");
        ErrorMessage = "Brak połączenia z serwerem. Spróbuj ponownie.";
    }
    catch (ValidationException ex)
    {
        _logger.LogWarning(ex, "Błąd walidacji danych");
        ErrorMessage = ex.Message; // User-friendly message
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Nieoczekiwany błąd podczas przetwarzania danych");
        ErrorMessage = "Wystąpił nieoczekiwany błąd. Skontaktuj się z administratorem.";
    }
    finally
    {
        IsLoading = false;
    }
}
```

### Logging Standards
```csharp
// ✅ Structured logging z parametrami
_logger.LogInformation("Rozpoczęto przetwarzanie dostawy {AwizacjaId}", awizacjaId);

// ✅ Error logging z wyjątkami
_logger.LogError(ex, "Błąd podczas rejestracji pozycji {Position}", position.Id);

// ✅ Warning dla business logic issues
_logger.LogWarning("Nieprawidłowy kod SSCC: {SsccCode}", ssccCode);

// ❌ Unikaj string interpolation w logach
_logger.LogInformation($"Processing {awizacjaId}"); // Bad
```

## Services Layer

### Interface Design
```csharp
public interface IReceiveService
{
    // ✅ Async methods z CancellationToken
    Task<GetAwizacjaResponse> GetAwizacjeAsync(
        GetAwizacjaRequest request, 
        CancellationToken cancellationToken = default);
    
    // ✅ Proper return types
    Task<Result<ClaimAwizacjaResponse>> ClaimAwizacjaAsync(
        ClaimAwizacjaRequest request);
        
    // ✅ Event-based notifications gdzie potrzebne
    event EventHandler<AwizacjaClaimedEventArgs> AwizacjaClaimed;
}
```

### WeakReferenceMessenger Pattern
```csharp
// ✅ Definicja wiadomości
public class LkScannedMessage : ValueChangedMessage<string>
{
    public LkScannedMessage(string value) : base(value) { }
}

// ✅ Rejestracja w ViewModelu
public partial class ReceivesSelectionViewModel : ObservableObject
{
    public ReceivesSelectionViewModel()
    {
        WeakReferenceMessenger.Default.Register<LkScannedMessage>(this, OnLkScanned);
    }
    
    private void OnLkScanned(object recipient, LkScannedMessage message)
    {
        // ✅ ZAWSZE przekieruj na główny wątek dla UI updates
        MainThread.BeginInvokeOnMainThread(() => {
            ProcessScannedLk(message.Value);
        });
    }
    
    // ✅ Wyrejestrowanie w Dispose
    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            WeakReferenceMessenger.Default.UnregisterAll(this);
        }
    }
}

// ✅ Wysyłanie wiadomości
WeakReferenceMessenger.Default.Send(new LkScannedMessage(scannedValue));
```

### Service Implementation
```csharp
public class ReceiveService : IReceiveService
{
    private readonly IReceiveApiClient _apiClient;
    private readonly ILogger<ReceiveService> _logger;
    private readonly IMemoryCache _cache;
    
    public ReceiveService(
        IReceiveApiClient apiClient,
        ILogger<ReceiveService> logger,
        IMemoryCache cache)
    {
        _apiClient = apiClient;
        _logger = logger;
        _cache = cache;
    }
    
    public async Task<GetAwizacjaResponse> GetAwizacjeAsync(
        GetAwizacjaRequest request,
        CancellationToken cancellationToken = default)
    {
        // ✅ Validate input
        ArgumentNullException.ThrowIfNull(request);
        
        // ✅ Check cache first
        var cacheKey = $"awizacje_{request.Lk}";
        if (_cache.TryGetValue(cacheKey, out GetAwizacjaResponse? cached))
        {
            return cached;
        }
        
        // ✅ Call API with proper error handling
        var response = await _apiClient.GetAwizacjeAsync(request, cancellationToken);
        
        // ✅ Cache successful responses
        _cache.Set(cacheKey, response, TimeSpan.FromMinutes(5));
        
        return response;
    }
}
```

## XAML Guidelines

### XAML Binding Optimization ⚡

**KRYTYCZNE:** Zawsze używaj skompilowanych bindingów dla maksymalnej wydajności!

#### Konfiguracja projektu
```xml
<!-- W pliku .csproj dodaj to ustawienie -->
<PropertyGroup>
    <MauiEnableXamlCBindingWithSourceCompilation>true</MauiEnableXamlCBindingWithSourceCompilation>
</PropertyGroup>
```

#### Zawsze używaj x:DataType
```xml
<!-- ✅ ZAWSZE dodawaj x:DataType na poziomie strony -->
<ContentPage x:Class="WmsApp.Views.ReceivesSelectionPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Receives"
             xmlns:models="clr-namespace:WmsApp.Models.Receives"
             x:DataType="viewmodels:ReceivesSelectionViewModel"
             Title="Wybór Dostawy">

<!-- ✅ x:DataType w DataTemplate -->
<CollectionView ItemsSource="{Binding AwizacjaPositions}">
    <CollectionView.ItemTemplate>
        <DataTemplate x:DataType="models:AwizacjaPositionDto">
            <Grid Padding="8">
                <Label Text="{Binding TowarKod}" FontAttributes="Bold" />
                <Label Text="{Binding TowarNazwa}" TextColor="Gray" />
            </Grid>
        </DataTemplate>
    </CollectionView.ItemTemplate>
</CollectionView>

<!-- ✅ Skomplikowane bindingi z x:DataType -->
<Picker ItemsSource="{Binding TypyPalet}">
    <Picker.ItemDisplayBinding>
        <Binding Path="Nazwa" x:DataType="models:TypPaletyDto" />
    </Picker.ItemDisplayBinding>
    <Picker.SelectedItem>
        <Binding Path="SelectedTypPaletyId" Converter="{StaticResource IdToTypPaletyConverter}" />
    </Picker.SelectedItem>
</Picker>

<!-- ❌ NIGDY nie używaj bindingów bez x:DataType -->
<Label Text="{Binding SomeProperty}" /> <!-- BAD - brak x:DataType -->
```

#### Bindingi z RelativeSource
```xml
<!-- ✅ Poprawne użycie RelativeSource z MauiEnableXamlCBindingWithSourceCompilation -->
<DataTemplate x:DataType="models:AwizacjaPositionDto">
    <Grid>
        <Grid.GestureRecognizers>
            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:ReceivesRegistrationViewModel}}, Path=SelectAwizacjaPositionCommand}"
                                  CommandParameter="{Binding .}" />
        </Grid.GestureRecognizers>
        <!-- Content -->
    </Grid>
</DataTemplate>
```

#### Konwertery i zaawansowane bindingi
```xml
<!-- ✅ Enum bindingi z konwerterem -->
<Picker ItemsSource="{Binding AvailableEnvironments}">
    <Picker.ItemDisplayBinding>
        <Binding Path="." Converter="{StaticResource ApiEnvironmentDisplayConverter}" x:DataType="{x:Type models:ApiEnvironment}" />
    </Picker.ItemDisplayBinding>
</Picker>

<!-- ✅ String bindingi w kolekcjach -->
<CollectionView ItemsSource="{Binding GeneratedDsCodes}">
    <CollectionView.ItemTemplate>
        <DataTemplate x:DataType="x:String">
            <Label Text="{Binding}" FontFamily="Courier" />
        </DataTemplate>
    </CollectionView.ItemTemplate>
</CollectionView>
```

#### Debugowanie bindingów
```xml
<!-- ✅ W debugowaniu - sprawdź czy nie ma ostrzeżeń XC0022 i XC0025 -->
<!-- Build Output powinien pokazać: -->
<!-- ✅ 0 XC0022 warnings (Binding could be compiled) -->
<!-- ✅ 0 XC0025 warnings (Source property compilation not enabled) -->
```

#### 🔍 Szybka weryfikacja bindingów
```bash
# Sprawdź bindingi XAML po zmianach
dotnet build --verbosity normal 2>&1 | grep -E "XC0022|XC0025"

# Oczekiwany rezultat: brak output'u (0 ostrzeżeń)
# Jeśli są ostrzeżenia - sprawdź XAML_BINDING_CHEATSHEET.md
```

### Naming Conventions
```xml
<!-- ✅ Używaj x:Name dla kontrolek referencjonowanych w code-behind -->
<Entry x:Name="ScanInputEntry" 
       Text="{Binding ScanInput}" 
       Placeholder="Zeskanuj kod" />

<!-- ✅ Binduj właściwości bezpośrednio -->
<Label Text="{Binding CurrentLK}" 
       FontAttributes="Bold" />

<!-- ✅ Używaj konwerterów dla złożonych transformacji -->
<Label TextColor="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}" />
```

### Resource Organization
```xml
<!-- ✅ Globalne resources w App.xaml -->
<Application.Resources>
    <ResourceDictionary>
        <!-- Converters -->
        <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
        
        <!-- Colors -->
        <Color x:Key="Primary">#512BD4</Color>
        <Color x:Key="Success">#28A745</Color>
        <Color x:Key="Error">#DC3545</Color>
        
        <!-- Styles -->
        <Style x:Key="HeaderLabelStyle" TargetType="Label">
            <Setter Property="FontSize" Value="18" />
            <Setter Property="FontAttributes" Value="Bold" />
        </Style>
    </ResourceDictionary>
</Application.Resources>
```

### Layout Best Practices
```xml
<!-- ✅ Używaj Grid dla precyzyjnego layoutu -->
<Grid RowDefinitions="Auto,*,Auto" ColumnDefinitions="*,Auto">
    <Label Grid.Row="0" Grid.Column="0" 
           Text="Header" 
           Style="{StaticResource HeaderLabelStyle}" />
</Grid>

<!-- ✅ CollectionView dla list -->
<CollectionView ItemsSource="{Binding Awizacje}">
    <CollectionView.ItemTemplate>
        <DataTemplate x:DataType="models:AwizacjaDto">
            <Grid Padding="10">
                <Label Text="{Binding NumerAwizacji}" />
            </Grid>
        </DataTemplate>
    </CollectionView.ItemTemplate>
</CollectionView>

<!-- ✅ Proper data templates z x:DataType -->
<DataTemplate x:DataType="models:AwizacjaDto">
    <!-- Content -->
</DataTemplate>
```

## Testing Standards

### Unit Tests Naming
```csharp
// ✅ Pattern: MethodName_StateUnderTest_ExpectedBehavior
[Test]
public async Task ProcessScanAsync_ValidGS1Code_ShouldParseCorrectly()
{
    // Arrange
    var service = new ReceiveCodeValidationService();
    var gs1Code = "0112345678901231ABC123";
    
    // Act
    var result = service.ProcessScan(gs1Code);
    
    // Assert
    Assert.That(result.Type, Is.EqualTo(ReceiveScanCodeType.GS1));
    Assert.That(result.Code, Is.EqualTo(gs1Code));
}
```

### Mock Setup
```csharp
[Test]
public async Task LoadAwizacjeAsync_ServiceReturnsData_ShouldPopulateCollection()
{
    // Arrange
    var mockService = new Mock<IReceiveService>();
    var expectedAwizacje = new List<AwizacjaDto> { /* test data */ };
    
    mockService.Setup(s => s.GetAwizacjeAsync(It.IsAny<GetAwizacjaRequest>(), default))
           .ReturnsAsync(new GetAwizacjaResponse { Awizacje = expectedAwizacje });
    
    var viewModel = new ReceivesSelectionViewModel(mockService.Object, Mock.Of<ILogger<ReceivesSelectionViewModel>>());
    
    // Act
    await viewModel.LoadAwizacjeCommand.ExecuteAsync(null);
    
    // Assert
    Assert.That(viewModel.Awizacje, Has.Count.EqualTo(expectedAwizacje.Count));
}
```

## Performance Best Practices

### Async/Await
```csharp
// ✅ Proper async method signature
public async Task<Result> ProcessDataAsync(CancellationToken cancellationToken = default)
{
    // ✅ ConfigureAwait(false) w library code
    var data = await _apiClient.GetDataAsync().ConfigureAwait(false);
    
    // ✅ UI context w ViewModels - nie używaj ConfigureAwait(false)
    await _service.ProcessAsync();
    
    return Result.Success();
}

// ❌ Unikaj async void (oprócz event handlers)
public async void ButtonClicked() // Bad
```

### Collections
```csharp
// ✅ ObservableCollection dla bindingów XAML
public ObservableCollection<AwizacjaDto> Awizacje { get; } = new();

// ✅ List<T> dla internal operations
private readonly List<AwizacjaDto> _awizacjaCache = new();

// ✅ Proper collection updates
private void UpdateAwizacjeList(IEnumerable<AwizacjaDto> newAwizacje)
{
    Awizacje.Clear();
    foreach (var awizacja in newAwizacje)
    {
        Awizacje.Add(awizacja);
    }
}
```

## Security Guidelines

### Input Validation
```csharp
// ✅ Validate all inputs
public async Task ProcessScanAsync(string scanInput)
{
    if (string.IsNullOrWhiteSpace(scanInput))
    {
        ErrorMessage = "Wprowadź kod do skanowania";
        return;
    }
    
    if (scanInput.Length > 100)
    {
        ErrorMessage = "Kod jest zbyt długi";
        return;
    }
    
    // Process valid input
}
```

### API Communication
```csharp
// ✅ Używaj HttpClient z timeout
private static readonly HttpClient _httpClient = new()
{
    Timeout = TimeSpan.FromSeconds(30)
};

// ✅ Validate responses
var response = await _httpClient.GetAsync(url);
if (!response.IsSuccessStatusCode)
{
    throw new HttpRequestException($"API call failed: {response.StatusCode}");
}
```

## Documentation Standards

### XML Documentation
```csharp
/// <summary>
/// Przetwarza zeskanowany kod i określa jego typ (GS1, SSCC, DS).
/// </summary>
/// <param name="scanInput">Zeskanowany kod do analizy</param>
/// <returns>Wynik przetwarzania z typem kodu i sparsowanymi danymi</returns>
/// <exception cref="ArgumentException">Gdy scanInput jest null lub pusty</exception>
public ReceiveScanResult ProcessScan(string scanInput)
{
    // Implementation
}
```

### Code Comments
```csharp
// ✅ Komentarze wyjaśniające DLACZEGO, nie CO
// Claim dostawy żeby uniknąć konfliktów z innymi użytkownikami
await _receiveService.ClaimAwizacjaAsync(request);

// ✅ TODO comments z datą i autorem  
// TODO: Przenieść na WeakReferenceMessenger - LD 2024-01-15

// ❌ Unikaj oczywistych komentarzy
// Ustawia loading na true
IsLoading = true; // Bad comment
```

---

## Messaging Best Practices

### Message Classes Organization
```csharp
// ✅ Organizuj wiadomości w folderze Messages/
namespace WmsApp.Messages
{
    // Wiadomości dla skanowania
    public class LkScannedMessage : ValueChangedMessage<string>
    {
        public LkScannedMessage(string value) : base(value) { }
    }
    
    public class GS1ScannedMessage : ValueChangedMessage<string>
    {
        public GS1ScannedMessage(string value) : base(value) { }
    }
    
    // Wiadomości dla stanu
    public class ScanProcessedMessage
    {
        public string ProcessedCode { get; }
        public bool Success { get; }
        public string? ErrorMessage { get; }
        
        public ScanProcessedMessage(string processedCode, bool success, string? errorMessage = null)
        {
            ProcessedCode = processedCode;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }
}
```

### Threading Safety
```csharp
// ✅ KRYTYCZNE: Zawsze przekieruj UI operations na główny wątek
private void OnScanReceived(object recipient, LkScannedMessage message)
{
    // ✅ UI updates na głównym wątku
    MainThread.BeginInvokeOnMainThread(() => {
        CurrentLK = message.Value;
        ProcessLkCommand.NotifyCanExecuteChanged();
    });
}

// ❌ NIGDY nie modyfikuj UI bezpośrednio z background thread
private void OnScanReceived(object recipient, LkScannedMessage message)
{
    CurrentLK = message.Value; // ❌ Może spowodować crash!
}
```

### Memory Management
```csharp
// ✅ ViewModeli powinny implementować IDisposable
public partial class ReceivesSelectionViewModel : ObservableObject, IDisposable
{
    private bool _disposed = false;
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            // ✅ Wyrejestruj wszystkie wiadomości
            WeakReferenceMessenger.Default.UnregisterAll(this);
            _disposed = true;
        }
    }
}
```

---

**Data ostatniej aktualizacji:** 10.09.2024
