# PRD Backend – <PERSON><PERSON><PERSON>wy” (API .NET, JSON)

## 1. Cel i zakres
Celem modułu jest obsługa procesu rejestracji dostawy w magazynie w oparciu o:
- wybór dostawy (LK = list_control.id) i blokadę wybranego zlecenia do realizacji,
- generowanie nośników DS (palet) z numeracją z docnumber (name='nrpalety') i powiązaniem z list_control,
- rejestrację pozycji przyjęcia w tabeli etykiety (insert-only),
- weryfikację drukarek oraz druk etykiet DS (Code 128: „DS{nr}”),
- przetwarzanie skanów GS1 (AIs 00, 02, 10, 17, 37) i logikę „Nośnik kompletny / (Auto)”.

Docelowa komunikacja: JSON (zastępuje starsze PHP/XML/Compact Framework).

## 2. Słowniczek i aktorzy
- LK: identyfikator dostawy (list_control.id), prezento<PERSON><PERSON> jako „LK{ID}” (bez zer wiodących).
- DS: numer nośnika/palety pochodzący z docnumber(name='nrpalety'), zapisywany w listcontrol_palety.paleta_id oraz w etykiety.paleta_id.
- „Realizujący”: pracownik przypisany do danego list_control w trakcie obsługi (list_control.realizujacy_pracownik_id).
- Pracownik: identyfikowany przez pracownicy.id, przekazywany do API przy operacjach.

## 3. Założenia i reguły biznesowe
- Lista dostaw (Widok 1) pochodzi z list_control, filtrowana po docin_id_man IS NULL (i dodatkowe filtry magazyn/system wg potrzeb projektu). 
- LK wprowadzamy jako „LK{ID}”, bez akceptacji zer wiodących; błędny/nieistniejący ID → komunikat błędu.
- Generowanie DS:
  - blokada współbieżności przez list_control.realizujacy_pracownik_id (tylko jeden operator może generować DS i rejestrować dla danego LK),
  - dla każdej palety pobierany jest kolejny numer z docnumber('nrpalety'),
  - tworzymy rekord w palety i w listcontrol_palety (paleta_id = nr nośnika),
  - opcjonalny druk (Code 128: „DS{nr}”), walidacja IP względem listy drukarek.
- Rejestracja (Widok 2):
  - Parsujemy skany GS1 (00, 02, 10, 17, 37) po stronie API,
  - Możliwe przyjęcie bez awizacji (jeśli SSCC z AI 00 nie odnosi się do pozycji awizacji),
  - Wpisy pozycji trafiają tylko jako INSERT do etykiety (bez UPDATE),
  - etykiety.paleta_id = bieżący DS (lub nowy przy trybie „(Auto)”),
  - miejsce dostawy niezmienialne przez użytkownika: z list_control.miejsce_id,
  - typ palety może być edytowany także w Widoku 2,
  - niezgodności ilości względem awizacji: confirm; brak wymaganych danych (np. partia/data przy wymaganych flagach kodu): blokada.
- Koniec sesji: kończy rejestrację dla LK (zwalnia blokadę); warunki definitywnego zamknięcia dostawy – wszystkie pozycje awizowane przyjęte.

## 4. Architektura i wzorce

### 4.1 Clean Architecture (zgodnie z ARCHITECTURE.md)
Moduł dostaw integruje się z istniejącą Clean Architecture:
- **Wms.Domain**: encje dostawy (Receive, ReceiveItem, Pallet)
- **Wms.Application**: use-cases (CreateReceive, GeneratePallets, RegisterItem)
- **Wms.Infrastructure**: repozytoria, EF Core mappings
- **Wms.Api**: kontrolery REST zgodnie z istniejącymi wzorcami

### 4.2 Konwencje API (zgodnie z istniejącym systemem)
- **Odpowiedzi**: ProblemDetails dla błędów, DTO dla sukcesów
- **Walidacja**: FluentValidation dla wszystkich DTOs
- **Autoryzacja**: JWT Bearer tokens (zgodnie z istniejącym auth)
- **Błędy**: GlobalExceptionMiddleware z komunikatami w języku polskim

### 4.3 Wzorce odpowiedzi API
```csharp path=null start=null
// Sukces - zwracamy DTO bezpośrednio
public class ReceiveListResponse
{
    public List<ReceiveDto> Receives { get; set; } = [];
    public int TotalCount { get; set; }
}

// Błąd - ProblemDetails (automatyczne przez middleware)
// Walidacja - ValidationProblemDetails (FluentValidation)
```

## 5. Endpointy API

### 5.1. Lista dostaw do wyboru
```csharp path=null start=null
[HttpGet]
[Route("api/v1/receives")]
public async Task<ActionResult<ReceiveListResponse>> GetAvailableReceives(
    [FromQuery] GetAvailableReceivesQuery query)
```
**Parametry:**
- `magazynId` (optional): filtrowanie po magazynie  
- `limit` (default: 100): maksymalna liczba wyników
- `includeAssigned` (default: false): czy włączyć już przypisane

**Filtry:** `list_control.docin_id_man IS NULL` + opcjonalnie `realizujacy_pracownik_id IS NULL`

**Odpowiedź:**
```csharp path=null start=null
public class ReceiveListResponse
{
    public List<ReceiveDto> Receives { get; set; } = [];
    public int TotalCount { get; set; }
}

public class ReceiveDto
{
    public int Id { get; set; }
    public string Lk => $"LK{Id}";
    public string DokumentDostawy { get; set; } = string.Empty;
    public string SystemNazwa { get; set; } = string.Empty;
    public string MiejsceDostawy { get; set; } = string.Empty;
    public DateTime Data { get; set; }
    public bool IsAssigned { get; set; }
}
```

### 5.2. Zajęcie dostawy (blokada)
```csharp path=null start=null
[HttpPost]
[Route("api/v1/receives/{receiveId}/claim")]
public async Task<ActionResult<ClaimReceiveResponse>> ClaimReceive(
    int receiveId, 
    [FromBody] ClaimReceiveCommand command)
```
**Request:**
```csharp path=null start=null
public class ClaimReceiveCommand
{
    public int PracownikId { get; set; }
}

// FluentValidation
public class ClaimReceiveCommandValidator : AbstractValidator<ClaimReceiveCommand>
{
public ClaimReceiveCommandValidator()
    {
        RuleFor(x => x.PracownikId).GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
    }
}
```
**Logika:** Atomowe ustawienie `list_control.realizujacy_pracownik_id` jeśli NULL  
**Błędy:** 
- 404: Dostawa nie istnieje
- 409: Dostawa już zajęta przez innego pracownika
- 400: Niepoprawne dane wejściowe

### 4.3. Generowanie DS (nośników)
POST /api/dostawy/{listControlId}/palety/generuj
Body:
```json path=null start=null
{
  "pracownikId": 101,
  "typPaletyId": 2,
  "ilosc": 10,
  "drukowac": true,
  "drukarkaIp": "************"
}
```
Działanie:
- Walidacja: claim należy do pracownika; ilosc 1..100; drukarkaIp: akceptuj skan z prefiksem „IP” (np. „IP172.7.1.44”); usuń prefiks i zweryfikuj IPv4; drukarkaIp musi być obecna na liście drukarek jeśli drukowac=true.
- Iteracyjnie pobierz nr z docnumber('nrpalety') → wstaw do palety → wstaw do listcontrol_palety (paleta_id = pobrany nr), ustaw wydruk domyślnie 1.
- Jeśli drukowac=true: wywołaj druk DS dla każdego numeru.
Odpowiedź (skrócona):
```json path=null start=null
{
  "status": "success",
  "data": {
    "palety": [ { "paletaId": 650001 }, { "paletaId": 650002 } ]
  }
}
```

### 4.4. Lista drukarek
GET /api/drukarki
- Zwraca listę dostępnych drukarek etykiet (z bazy/konfiguracji).

### 4.5. Druk etykiet DS
POST /api/druk/ds
Body:
```json path=null start=null
{
  "drukarkaIp": "************",
  "ds": [650001, 650002]
}
```
Działanie: weryfikacja IP względem listy drukarek; wydruk Code 128 z treścią „DS{nr}”.

### 4.6. Parsowanie skanu GS1
POST /api/dostawy/{listControlId}/skan
Body:
```json path=null start=null
{
  "pracownikId": 101,
  "scan": "{surowy_ciag_ze_skanera}"
}
```
Wynik parsowania (wybrane AIs zgodnie z wymaganiami):
- 00 → etykieta_klient (SSCC)
- 02 → wyszukaj po kody.ean i/lub kody.id; wynik: kodId do wstawienia w etykiety.kod_id
- 10 → lot (etykiety.lot)
- 17 → data_waznosci (etykiety.data_waznosci)
- 37 → ilosc = kody.ilosc_w_opakowaniu × wartość_AI_37

Odpowiedź (przykład):
```json path=null start=null
{
  "status": "success",
  "data": {
    "sscc": "000123456789012345",
    "kodId": 78901,
    "lot": "L250908A",
    "dataWaznosci": "2026-03-01",
    "ilosc": 96,
    "prefilled": true,
    "awizowane": true
  }
}
```
Uwagi:
- Jeśli SSCC nie jest awizowane → dopuszczamy przyjęcie bez awizacji (awizowane=false).
- Gdy z 02/lookup wynika wiele kandydatów, zwróć listę opcji do wyboru.

### 4.7. Utworzenie nowego nośnika (dla trybu „(Auto)”) – pojedynczy DS
POST /api/dostawy/{listControlId}/nosniki
Body:
```json path=null start=null
{
  "pracownikId": 101,
  "typPaletyId": 2,
  "drukowac": false,
  "drukarkaIp": null
}
```
- Zwraca nowy paletaId (DS) po atomowym pobraniu z docnumber i insertach w palety oraz listcontrol_palety.

### 4.8. Dodanie pozycji przyjęcia (INSERT do etykiety)
POST /api/dostawy/{listControlId}/pozycje
Body (przykład):
```json path=null start=null
{
  "pracownikId": 101,
  "paletaId": 650001,
  "kodId": 78901,
  "lot": "L250908A",
  "dataWaznosci": "2026-03-01",
  "ilosc": 96,
  "sscc": "000123456789012345",
  "certyfikat": "ABC-12345"
}
```
Zasady:
- Insert-only do etykiety; ustaw: etykiety.paleta_id = paletaId, etykiety.kod_id = kodId, etykiety.lot, etykiety.data_waznosci, etykiety.ilosc, etykiety.certyfikat (opcjonalnie). Data przyjęcia pochodzi z serwera (etykiety.ts).
- Uzupełnij kontekst: etykiety.system_id (z list_control.listcontrol_system_id), etykiety.magazyn (z kontekstu magazynu), etykiety.listcontrol_id = listControlId.
- Walidacje:
  - wymagania z kody (partia/data) → blokada gdy brak,
  - ilość vs awizacja → jeśli przekroczenie lub niespójność → status=confirm (wymagane potwierdzenie przez klienta),
  - miejsce dostawy tylko z list_control.miejsce_id (niezmienialne przez UI).

### 4.9. Podgląd pozycji bieżącego nośnika
GET /api/dostawy/{listControlId}/nosniki/{paletaId}/pozycje
- Zwraca listę utworzonych etykiet (pozycji) dla DS.

### 4.10. Nośnik kompletny
POST /api/dostawy/{listControlId}/nosniki/{paletaId}/complete
- Oznacza bieżący nośnik jako zamknięty w kontekście sesji (bez zmian w etykiety – insert-only już wykonane). Klient przechodzi w tryb „(Auto)”.

### 4.11. Zakończenie sesji rejestracji
POST /api/dostawy/{listControlId}/koniec
- Zwalnia blokadę (realizujacy_pracownik_id = NULL, jeśli przypisany do tego pracownika).
- Sprawdza warunek zamknięcia (wszystkie pozycje awizowane przyjęte) – opcjonalnie zwraca sugestię zmiany statusu dostawy (bez automatycznego zamykania na tym etapie).

### 4.12. Pozycje awizacji do wyboru
GET /api/dostawy/{listControlId}/awizacja/pozycje
- Zwraca listę pozycji awizacji (źródło: awizacje_dostaw_head/awizacje_dostaw_dane powiązane z LK przez list_control).
- Pola (przykład): pozycja_zamowienia, kodId/kod, lot (jeśli dostępny), data_waznosci (jeśli dostępna), ilosc.
- Służy do ręcznego wyboru towaru w Widoku 2 (przełącznik „Awizacja” | „Kartoteka”).

## 5. Modele danych i mapowanie
- list_control: używane pola kluczowe: id (LK), listcontrol_system_id, miejsce_id, docin_id_man, wd_przyjecie_head_id (na przyszłość), realizujacy_pracownik_id (NOWE).
- listcontrol_palety: listcontrol_id, paleta_id (DS), wydruk.
- palety: id (auto) – Tworzymy rekord; powiązanie logiczne przez DS (paleta_id w innych tabelach wskazuje numer DS z docnumber, a nie palety.id); decyzja: zapisywać nr DS także w palety.pal_docin_id lub osobnym polu – do dyskusji przy implementacji.
- etykiety: insert-only; pola: system_id, magazyn, kod_id, paleta_id (DS), lot, data_waznosci, ilosc, listcontrol_id, sscc (jeśli dostępny), statusy domyślne (wg istniejących wartości).
- kody: źródło wymagań (wymagana_partia, wymagana_data_waznosci, wymagana_dataprod, ilosc_w_opakowaniu, status_jakosci_domyslny).
- awizacje_dostaw_head / awizacje_dostaw_dane: źródło prefillingu przy SSCC; dopuszczamy brak dopasowania (przyjęcie bez awizacji).
- docnumber: name='nrpalety' (pobieranie i atomowy increment).
- drukarki: źródło listy drukarek (DB/konfiguracja) – weryfikacja po IP.

## 6. Integracja z istniejącymi wzorcami

### 6.1 Walidacja kodów (CodeValidators z ARCHITECTURE.md)
```csharp path=null start=null
// Wykorzystanie istniejących validatorów
public static class ReceiveCodeValidators
{
    // Z istniejącego CodeValidators
    public static bool IsValidSSCC(string code) => CodeValidators.SsccPattern.IsMatch(code);
    public static bool IsValidDS(string code) => CodeValidators.DsPattern.IsMatch(code);
    
    // Nowy dla LK
    public static readonly Regex LkPattern = new(@"^LK\d{1,8}$");
    public static bool IsValidLK(string code) => LkPattern.IsMatch(code);
}
```

### 6.2 Obsługa błędów (zgodnie z GlobalExceptionMiddleware)
```csharp path=null start=null
// Własne wyjątki domenowe (wzorzec z ARCHITECTURE.md)
public class ReceiveNotFoundException : DomainException
{
public ReceiveNotFoundException(int receiveId)
        : base($"Dostawa LK{deliveryId} nie została znaleziona") { }
}

public class ReceiveAlreadyClaimedException : DomainException
{
public ReceiveAlreadyClaimedException(int receiveId, int currentWorkerId)
        : base($"Dostawa LK{deliveryId} jest już obsługiwana przez pracownika {currentWorkerId}") { }
}
```

### 6.3 GS1 Parsing (Application Layer)
```csharp path=null start=null
public class Gs1Parser
{
    // AIs: 00 (SSCC), 02 (GTIN), 10 (Lot), 17 (Expiry), 37 (Count)
    public Gs1ParseResult ParseScan(string scanData) { /* implementacja */ }
}
```

## 7. Migracje DB (minimalny zakres)
- list_control: dodać kolumnę realizujacy_pracownik_id INT UNSIGNED NULL; indeks.
- Zapewnienie wpisu w docnumber dla name='nrpalety'.
- (Jeśli brak) źródło listy drukarek (tabela lub konfiguracja) – minimalnie IP.
- etykiety.certyfikat VARCHAR(64) NULL (jeśli wymagane przez proces/kontrahenta).

## 8. Bezpieczeństwo i transakcje
- Operacje generujące DS (docnumber + inserty) wykonywać w transakcji.
- Claim/zwolnienie claim’u w transakcji lub z kontrolą warunku (WHERE realizujacy_pracownik_id IS NULL / =pracownikId).

## 9. Kryteria akceptacyjne (wybór)
- Lista dostaw zwraca tylko docin_id_man IS NULL, poprawne dane, brak zajętych (jeśli tak zdecydujemy w filtrze).
- Claim działa wyłącznie raz; drugi operator otrzymuje 409.
- Generowanie 10 DS tworzy 10 wpisów w palety i listcontrol_palety z kolejnymi numerami z docnumber.
- Generowanie DS: `drukarkaIp` z prefiksem „IP” (np. „IP172.7.1.44”) jest akceptowane (prefiks usunięty) i walidowane jako IPv4; nieznane IP → błąd.
- Skan GS1 00/02/10/17/37 zwraca poprawne pola zgodnie z mapowaniem; brak awizacji nie blokuje przyjęcia.
- Insert pozycji tworzy wpis w etykiety z poprawnym kontekstem (system_id, magazyn, listcontrol_id, paleta_id=DS); jeżeli przekazano „certyfikat” – jest zapisany (etykiety.certyfikat).
- Odpowiedź po utworzeniu pozycji zawiera serwerowy znacznik czasu „dataPrzyjecia” (etykiety.ts) do prezentacji na UI.
- Endpoint awizacji (`GET /api/dostawy/{id}/awizacja/pozycje`) zwraca listę pozycji do ręcznego wyboru.
- „Nośnik kompletny” kończy pracę na bieżącym DS; kolejny insert po „Auto” trafia na nowy DS.
- „Koniec” zwalnia claim; gdy awizacja kompletna – API sygnalizuje możliwość zamknięcia dostawy.

