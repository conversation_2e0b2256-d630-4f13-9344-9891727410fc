using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WmsApp.Models;
using WmsApp.Models.Pallets;
using WmsApp.Services;
using Microsoft.Extensions.Logging;

namespace WmsApp.ViewModels;

public partial class MovePalletViewModel : ObservableObject
{
    private readonly IWmsApiService _apiService;
    private readonly CodeValidationService _codeValidationService;
    private readonly ILogger<MovePalletViewModel> _logger;

    public MovePalletViewModel(
        IWmsApiService apiService, 
        CodeValidationService codeValidationService,
        ILogger<MovePalletViewModel> logger)
    {
        _apiService = apiService;
        _codeValidationService = codeValidationService;
        _logger = logger;
        
        ResetToInitialState();
    }

    #region Observable Properties

    [ObservableProperty]
    private MovePalletStep _currentStep = MovePalletStep.ScanPallet;

    [ObservableProperty]
    private string _currentStepTitle = "Krok 1/2";

    [ObservableProperty]
    private string _currentInstruction = "Zeskanuj kod palety do przeniesienia";

    [ObservableProperty]
    private bool _isPalletInfoVisible = false;

    [ObservableProperty]
    private string _scannedPalletCode = string.Empty;

    [ObservableProperty]
    private string _currentPalletLocation = string.Empty;

    [ObservableProperty]
    private string _manualCodeInput = string.Empty;

    [ObservableProperty]
    private bool _isManualInputVisible = true;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private bool _isMessageVisible = false;

    [ObservableProperty]
    private string _statusMessage = string.Empty;

    [ObservableProperty]
    private Color _messageBackgroundColor = Colors.Gray;

    #endregion

    #region Private Fields
    
    private PalletInfo? _currentPalletInfo;
    private string _targetLocationCode = string.Empty;
    
    #endregion

    #region Commands

    [RelayCommand]
    private async Task ProcessManualCode()
    {
        if (string.IsNullOrWhiteSpace(ManualCodeInput))
        {
            ShowErrorMessage("Wprowadź kod do zeskanowania");
            return;
        }

        await ProcessScannedCode(ManualCodeInput.Trim());
        ManualCodeInput = string.Empty;
    }

    [RelayCommand]
    private async Task Cancel()
    {
        await Shell.Current.GoToAsync("///options");
    }

    #endregion

    #region Public Methods

    public void ResetToInitialState()
    {
        CurrentStep = MovePalletStep.ScanPallet;
        UpdateStepDisplay();
        IsPalletInfoVisible = false;
        ScannedPalletCode = string.Empty;
        CurrentPalletLocation = string.Empty;
        ManualCodeInput = string.Empty;
        IsLoading = false;
        HideMessage();
        _currentPalletInfo = null;
        _targetLocationCode = string.Empty;
    }

    public async Task ProcessScannedCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return;

        HideMessage();

        try
        {
            switch (CurrentStep)
            {
                case MovePalletStep.ScanPallet:
                    await ProcessPalletCode(code);
                    break;
                
                case MovePalletStep.ScanLocation:
                    await ProcessLocationCode(code);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scanned code: {Code}", code);
            ShowErrorMessage("Wystąpił błąd podczas przetwarzania kodu");
        }
    }

    #endregion

    #region Private Methods

    private async Task ProcessPalletCode(string palletCode)
    {
        // Walidacja formatu kodu palety
        var scanResult = _codeValidationService.ValidateCode(palletCode);
        if (!scanResult.IsValid || (scanResult.Type != ScanCodeType.SSCC && scanResult.Type != ScanCodeType.DS))
        {
            ShowErrorMessage(scanResult.ErrorMessage ?? "Nieprawid\u0142owy kod palety. Wymagany format SSCC (18 cyfr) lub DS (DS + 4-9 cyfr)");
            return;
        }

        IsLoading = true;

        try
        {
            // Pobierz informacje o palecie z API
            var response = await _apiService.GetPalletAsync(palletCode);

            if (!response.IsSuccessStatusCode || response.Content == null)
            {
                ShowErrorMessage("Paleta nie została znaleziona w systemie");
                return;
            }

            _currentPalletInfo = response.Content;
            ScannedPalletCode = palletCode;
            CurrentPalletLocation = _currentPalletInfo.CurrentLocation?.Code ?? "Nieznana lokalizacja";

            // Przejdź do następnego kroku
            CurrentStep = MovePalletStep.ScanLocation;
            UpdateStepDisplay();
            IsPalletInfoVisible = true;

            ShowSuccessMessage("Paleta zeskanowana pomyślnie");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pallet info for code: {PalletCode}", palletCode);
            ShowErrorMessage("Błąd podczas pobierania informacji o palecie");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task ProcessLocationCode(string locationCode)
    {
        // Walidacja formatu lokalizacji
        var scanResult = _codeValidationService.ValidateCode(locationCode);
        if (!scanResult.IsValid || scanResult.Type != ScanCodeType.Location)
        {
            ShowErrorMessage(scanResult.ErrorMessage ?? "Nieprawidłowy kod lokalizacji. Wymagany format: MP-H-R-M-P (np. MP-1-145-002-1)");
            return;
        }

        // Sprawdź czy paleta nie jest już w tej lokalizacji
        if (CurrentPalletLocation == locationCode)
        {
            ShowWarningMessage("Paleta już znajduje się w tej lokalizacji. Zeskanuj kod nowej palety.");
            await Task.Delay(3000); // Pokazuj komunikat przez 3 sekundy
            ResetToInitialState();
            return;
        }

        _targetLocationCode = locationCode;
        IsLoading = true;

        try
        {
            // Sprawdź czy lokalizacja istnieje
            var locationResponse = await _apiService.GetLocationAsync(locationCode);

            if (!locationResponse.IsSuccessStatusCode)
            {
                ShowErrorMessage("Lokalizacja nie istnieje lub jest nieaktywna");
                CurrentStep = MovePalletStep.ScanLocation;
                UpdateStepDisplay();
                return;
            }

            // Wykonaj ruch palety
            var moveRequest = new MovePalletRequest
            {
                PalletCode = ScannedPalletCode,
                ToLocationCode = locationCode
            };

            var moveResponse = await _apiService.MovePalletAsync(ScannedPalletCode, moveRequest);

            if (moveResponse.IsSuccessStatusCode && moveResponse.Content != null)
            {
                var result = moveResponse.Content;
                
                ShowSuccessMessage($"Paleta przeniesiona pomyślnie!\nZ: {result.FromLocationCode}\nDo: {result.ToLocationCode}");
                
                _logger.LogInformation("Pallet {PalletCode} moved from {From} to {To}", 
                    result.PalletCode, result.FromLocationCode, result.ToLocationCode);

                // Po 2 sekundach resetuj do stanu początkowego
                await Task.Delay(2000);
                ResetToInitialState();
            }
            else
            {
                ShowErrorMessage("Błąd podczas przenoszenia palety");
                CurrentStep = MovePalletStep.ScanLocation;
                UpdateStepDisplay();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving pallet {PalletCode} to {LocationCode}", 
                ScannedPalletCode, locationCode);
            ShowErrorMessage("Wystąpił błąd podczas przenoszenia palety");
            CurrentStep = MovePalletStep.ScanLocation;
            UpdateStepDisplay();
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void UpdateStepDisplay()
    {
        switch (CurrentStep)
        {
            case MovePalletStep.ScanPallet:
                CurrentStepTitle = "Krok 1/2";
                CurrentInstruction = "Zeskanuj kod palety do przeniesienia";
                break;
            
            case MovePalletStep.ScanLocation:
                CurrentStepTitle = "Krok 2/2";
                CurrentInstruction = "Zeskanuj kod docelowej lokalizacji";
                break;
        }
    }

    private void ShowSuccessMessage(string message)
    {
        StatusMessage = message;
        MessageBackgroundColor = Color.FromArgb("#28a745"); // Green
        IsMessageVisible = true;
    }

    private void ShowErrorMessage(string message)
    {
        StatusMessage = message;
        MessageBackgroundColor = Color.FromArgb("#dc3545"); // Red
        IsMessageVisible = true;
    }

    private void ShowWarningMessage(string message)
    {
        StatusMessage = message;
        MessageBackgroundColor = Color.FromArgb("#ffc107"); // Yellow/Orange
        IsMessageVisible = true;
    }

    private void HideMessage()
    {
        IsMessageVisible = false;
        StatusMessage = string.Empty;
    }

    #endregion
}

public enum MovePalletStep
{
    ScanPallet,
    ScanLocation
}
