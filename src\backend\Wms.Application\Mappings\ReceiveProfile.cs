using AutoMapper;
using Wms.Application.DTOs.Receives;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;

namespace Wms.Application.Mappings;

public class ReceiveProfile : Profile
{
    public ReceiveProfile()
    {
        // ListControl -> ReceiveDto
        CreateMap<ListControl, ReceiveDto>()
            .ForMember(dest => dest.DokumentDostawy, opt => opt.MapFrom(src => src.DokumentDostawy ?? string.Empty))
            .ForMember(dest => dest.SystemNazwa, opt => opt.MapFrom(src => src.SystemEntity != null ? src.SystemEntity.Nazwa : "WMS"))
            .ForMember(dest => dest.MiejsceDostawy, opt => opt.MapFrom(src => src.MiejsceDostawy != null ? src.MiejsceDostawy.Code : string.Empty))
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Ts))
            .ForMember(dest => dest.IsAssigned, opt => opt.MapFrom(src => src.IsAssigned))
            .ForMember(dest => dest.RealizujacyPracownikImieNazwisko, opt => opt.MapFrom(src => src.RealizujacyPracownik != null ? src.RealizujacyPracownik.ImieNazwisko : null))
            .ForMember(dest => dest.AwizacjeId, opt => opt.MapFrom(src => src.AwizacjeId))
            .ForMember(dest => dest.NumerZamowienia, opt => opt.Ignore()); // Will be filled separately if needed

        // ListControl -> ReceiveDetailsDto
        CreateMap<ListControl, ReceiveDetailsDto>()
            .IncludeBase<ListControl, ReceiveDto>()
            .ForMember(dest => dest.Carriers, opt => opt.MapFrom(src => src.ListControlPallets))
            .ForMember(dest => dest.ExpectedItems, opt => opt.Ignore()) // Will be mapped separately
            .ForMember(dest => dest.CompletedItemsCount, opt => opt.Ignore())
            .ForMember(dest => dest.TotalExpectedItemsCount, opt => opt.Ignore())
            .ForMember(dest => dest.IsComplete, opt => opt.Ignore());

        // ListControlPallet -> CarrierDto
        CreateMap<ListControlPallet, CarrierDto>()
            .ForMember(dest => dest.DataUtworzenia, opt => opt.MapFrom(src => DateTime.UtcNow)) // Default creation time
            .ForMember(dest => dest.ItemsCount, opt => opt.Ignore()) // Will be calculated separately
            .ForMember(dest => dest.IsCompleted, opt => opt.Ignore()); // Will be calculated separately

        // AwizacjaDane -> ExpectedItemDto
        CreateMap<AwizacjaDane, ExpectedItemDto>()
            .ForMember(dest => dest.KodNazwa, opt => opt.MapFrom(src => src.KodEntity != null ? src.KodEntity.KodNazwa : src.Kod))
            .ForMember(dest => dest.Kod, opt => opt.MapFrom(src => src.Kod))
            .ForMember(dest => dest.Ean, opt => opt.MapFrom(src => src.KodEntity != null ? src.KodEntity.Ean : null))
            .ForMember(dest => dest.IloscAwizowana, opt => opt.MapFrom(src => src.Ilosc ?? 0))
            .ForMember(dest => dest.IloscPrzyjeta, opt => opt.Ignore()) // Will be calculated from labels
            .ForMember(dest => dest.DataWaznosci, opt => opt.MapFrom(src => src.DataWaznosci))
            .ForMember(dest => dest.Lot, opt => opt.MapFrom(src => src.Lot))
            .ForMember(dest => dest.EtykietaKlient, opt => opt.MapFrom(src => src.EtykietaKlient))
            .ForMember(dest => dest.Blloc, opt => opt.MapFrom(src => src.Blloc))

            .ForMember(dest => dest.SystemId, opt => opt.MapFrom(src => src.AwizacjaHead != null ? src.AwizacjaHead.SystemId : 0))
            .ForMember(dest => dest.KodId, opt => opt.MapFrom(src => src.KodEntity != null ? src.KodEntity.Id : 0));

        // Label -> ReceiveItemDto
        CreateMap<Label, ReceiveItemDto>()
            .ForMember(dest => dest.PaletaId, opt => opt.MapFrom(src => src.PaletaId ?? 0))
            .ForMember(dest => dest.KodNazwa, opt => opt.MapFrom(src => "Product Name")) // Will be filled from separate lookup
            .ForMember(dest => dest.Ean, opt => opt.MapFrom(src => src.Gtin))
            .ForMember(dest => dest.DataPrzyjecia, opt => opt.MapFrom(src => src.Ts))
            .ForMember(dest => dest.Certyfikat, opt => opt.MapFrom(src => src.Blloc))
            .ForMember(dest => dest.KodId, opt => opt.MapFrom(src => src.KodId ?? 0));
    }
}
