using FluentAssertions;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.ValueObjects;

public class LKCodeTests
{
    [Fact]
    public void FromString_ValidLKCode_ShouldCreateSuccessfully()
    {
        // Arrange
        var validCode = "LK123";

        // Act
        var result = LKCode.FromString(validCode);

        // Assert
        result.Value.Should().Be("LK123");
        result.Id.Should().Be(123);
    }

    [Theory]
    [InlineData("LK1")]
    [InlineData("**********")] // 8 cyfr - maksimum
    [InlineData("LK12345")]
    public void FromString_ValidFormats_ShouldCreateSuccessfully(string validCode)
    {
        // Act & Assert
        var result = LKCode.FromString(validCode);
        result.Value.Should().Be(validCode);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("LK")]
    [InlineData("123")]
    [InlineData("**********9")] // 9 cyfr - za dużo
    [InlineData("LKabc")]
    [InlineData("lk123")] // małe litery
    public void FromString_InvalidFormats_ShouldThrowArgumentException(string invalidCode)
    {
        // Act & Assert
        Action act = () => LKCode.FromString(invalidCode);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void FromId_ValidId_ShouldCreateSuccessfully()
    {
        // Arrange
        var validId = 12345;

        // Act
        var result = LKCode.FromId(validId);

        // Assert
        result.Value.Should().Be("LK12345");
        result.Id.Should().Be(12345);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(100000000)] // Więcej niż 8 cyfr
    public void FromId_InvalidId_ShouldThrowArgumentException(int invalidId)
    {
        // Act & Assert
        Action act = () => LKCode.FromId(invalidId);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("LK123", true)]
    [InlineData("LK1", true)]
    [InlineData("**********", true)]
    [InlineData("", false)]
    [InlineData("123", false)]
    [InlineData("LKabc", false)]
    [InlineData(null, false)]
    public void IsValid_ShouldReturnExpectedResult(string code, bool expected)
    {
        // Act
        var result = LKCode.IsValid(code);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ToString_ShouldReturnValue()
    {
        // Arrange
        var lkCode = LKCode.FromString("LK123");

        // Act
        var result = lkCode.ToString();

        // Assert
        result.Should().Be("LK123");
    }

    [Fact]
    public void ImplicitOperator_ShouldReturnValue()
    {
        // Arrange
        var lkCode = LKCode.FromString("LK123");

        // Act
        string result = lkCode;

        // Assert
        result.Should().Be("LK123");
    }

    [Fact]
    public void ExplicitOperator_ShouldCreateFromString()
    {
        // Arrange
        var codeString = "LK123";

        // Act
        var result = (LKCode)codeString;

        // Assert
        result.Value.Should().Be("LK123");
        result.Id.Should().Be(123);
    }

    [Fact]
    public void Equality_SameCodes_ShouldBeEqual()
    {
        // Arrange
        var code1 = LKCode.FromString("LK123");
        var code2 = LKCode.FromString("LK123");

        // Act & Assert
        code1.Should().Be(code2);
        (code1 == code2).Should().BeTrue();
    }

    [Fact]
    public void Equality_DifferentCodes_ShouldNotBeEqual()
    {
        // Arrange
        var code1 = LKCode.FromString("LK123");
        var code2 = LKCode.FromString("LK456");

        // Act & Assert
        code1.Should().NotBe(code2);
        (code1 != code2).Should().BeTrue();
    }
}
