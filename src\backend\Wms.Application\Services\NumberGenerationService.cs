using Microsoft.Extensions.Logging;
using Wms.Application.Interfaces;

namespace Wms.Application.Services;

/// <summary>
/// Serwis do atomowego generowania numerów z tabeli docnumber
/// </summary>
public class NumberGenerationService
{
    private readonly IDocNumberRepository _docNumberRepository;
    private readonly ILogger<NumberGenerationService> _logger;

    public const string PALLET_NUMBER_NAME = "nrpalety";
    public const string LABEL_NUMBER_NAME = "nretykiety";

    public NumberGenerationService(
        IDocNumberRepository docNumberRepository,
        ILogger<NumberGenerationService> logger)
    {
        _docNumberRepository = docNumberRepository;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera następny numer palety z docnumber('nrpalety')
    /// </summary>
    public async Task<int> GetNextPalletNumberAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var nextNumber = await _docNumberRepository.GetNextNumberAsync(PALLET_NUMBER_NAME, cancellationToken);
            _logger.LogDebug("Generated next pallet number: {PalletNumber}", nextNumber);
            return nextNumber;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate next pallet number");
            throw;
        }
    }

    /// <summary>
    /// Pobiera następny numer etykiety z docnumber('nretykiety')
    /// </summary>
    public async Task<int> GetNextLabelNumberAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var nextNumber = await _docNumberRepository.GetNextNumberAsync(LABEL_NUMBER_NAME, cancellationToken);
            _logger.LogDebug("Generated next label number: {LabelNumber}", nextNumber);
            return nextNumber;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate next label number");
            throw;
        }
    }

    /// <summary>
    /// Upewnia się, że wpisy dla numeracji palet i etykiet istnieją
    /// </summary>
    public async Task EnsureNumberingEntriesExistAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Upewnij się, że wpis dla palet istnieje
            await _docNumberRepository.CreateIfNotExistsAsync(PALLET_NUMBER_NAME, 650000, cancellationToken);
            _logger.LogDebug("Ensured pallet numbering entry exists");

            // Upewnij się, że wpis dla etykiet istnieje
            await _docNumberRepository.CreateIfNotExistsAsync(LABEL_NUMBER_NAME, 1, cancellationToken);
            _logger.LogDebug("Ensured label numbering entry exists");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure numbering entries exist");
            throw;
        }
    }
}
