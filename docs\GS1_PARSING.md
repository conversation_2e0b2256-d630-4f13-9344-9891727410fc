# Przewodnik: Parsowanie GS1-128 w WMS (GS1 + prefiks IZ)

Ten dokument opisuje, jak w naszym systemie WMS realizowane jest parsowanie skanów GS1-128, w tym obsługa prefiksu „IZ” (kontekst dostaw), separatora grupowego FNC1 oraz najczęściej używanych Application Identifierów (AI): 00, 01, 02, 10, 17, 37.

Najważniejsze wnioski praktyczne:
- API oczekuje surowego ciągu GS1-128: opcjonalny prefiks „IZ”, opcjonalny kod startowy ]C1, a następnie ciąg AIs i danych bez nawiasów.
- Separator FNC1 musi występować po polach o zmiennej długości (np. AI 10 – Lot), a nie musi po polach o stałej długości (np. AI 00, 01, 02, 17).
- GTIN (AI 01 lub 02) musi mieć prawidłową cyfrę kontrolną.

## Spis treści
- Wymagany format skanu dla API
- Obsługiwane Application Identifier (AI)
- Prefiks IZ i kod startowy ]C1
- Przykłady działających skanów (curl)
- Struktura zapytania i odpowiedzi API
- Najczęstsze problemy i diagnoza
- Uwagi implementacyjne (parser i DTO)

## Testowanie przez API Proxy

- W środowisku developerskim testy wykonujemy przez API Proxy pod adresem: http://127.0.0.1:8080
- Proxy przekazuje żądania do backendu Wms.Api (np. http://127.0.0.1:8081)
- W logach Proxy zobaczysz wpisy typu:
  [POST] Proxying: /api/v1/auth/login-scan -> http://127.0.0.1:8081/api/v1/auth/login-scan
- Ustaw BASE_URL tak, aby wskazywał API Proxy (patrz przykłady poniżej)

## Wymagany format skanu dla API

API rozpoznaje GS1 po wzorcu: opcjonalny „IZ”, opcjonalny „]C1”, a następnie cyfry rozpoczynające AI. Nie używamy wariantu z nawiasami okrągłymi (np. „(01)”), ponieważ taki zapis nie przechodzi wstępnej detekcji w API i jest traktowany jako „Unknown”.

Prawidłowe (przykłady):
- IZ]C10105901234123457…
- ]C10012345678901234567810ABC123\u001D17…
- 0105901234123457…

Nieprawidłowe dla API (przykład testowy, ale nie do użycia w żądaniach API):
- IZ]C1(01)05901234123457(10)BATCH(17)240101

Separator grupowy FNC1 musi być obecny (jako znak ASCII 29) po polach zmiennych. W JSON przekazujemy go jako sekwencję „\u001d” (małe d).

## Obsługiwane Application Identifier (AI)

- 00 – SSCC (18 cyfr, stała długość)
- 01 – GTIN jednostki handlowej (8/12/13/14 cyfr; w praktyce 14 – GTIN-14)
- 02 – GTIN zawartości (14 cyfr, stała długość)
- 10 – Lot/Batch (do 20 znaków, zmienna długość; wymaga FNC1, jeśli kolejne AI następuje dalej)
- 17 – Data ważności (YYMMDD, stała długość 6)
- 37 – Ilość (1–8 cyfr, zmienna długość; FNC1 opcjonalny, zależnie od pozycji)

Uwaga: dla AI 10 (zmienna długość) separator FNC1 jest wymagany, jeśli po nim występuje kolejne pole.

## Prefiks IZ i kod startowy ]C1

- Prefiks „IZ” (bez względu na wielkość liter) sygnalizuje kontekst dostawy i jest wykrywany przez parser. W odpowiedzi API zobaczysz wtedy HasIzPrefix = true.
- Kod startowy EAN-128 „]C1” jest akceptowany, ale nie jest wymagany.

## Przykłady działających skanów (curl)

Przed testami ustaw token JWT jako zmienną środowiskową:

```bash path=null start=null
export AUTH_TOKEN={{JWT_TOKEN}}
BASE_URL=http://127.0.0.1:8080
```

1) SSCC + Lot + Data + Ilość (bez prefiksu IZ)

```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "]C100000123456789012345020123456789012310ABC123\u001d17250930370005",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

2) SSCC + Lot + Data + Ilość (z prefiksem IZ)

```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C10012345678901234567810ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

3) GTIN z AI 01 (GTIN-14) + Lot + Data + Ilość (z prefiksem IZ)

Uwaga: „05901234123457” ma poprawną cyfrę kontrolną.

```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C1010590123412345710ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

4) GTIN z AI 02 (GTIN-14) + Lot + Data + Ilość (z prefiksem IZ)

Pamiętaj o prawidłowej cyfrze kontrolnej. Przykładowa składnia (podmień GTIN-14 na poprawny):

```bash path=null start=null
curl -s -X POST "$BASE_URL/api/v1/receives/scan" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scanData": "IZ]C102{GTIN14}10ABC123\u001d17260315370010",
    "listControlId": null,
    "deviceId": "CLI",
    "context": "delivery"
  }'
```

## Struktura zapytania i odpowiedzi API

Endpoint: POST /api/v1/Receives/scan
Nagłówki: Authorization: Bearer {{JWT_TOKEN}}, Content-Type: application/json

Request (ParseScanRequest):
```json path=null start=null
{
  "scanData": ")C1...",         
  "listControlId": 12345,        
  "deviceId": "MC3300-001",     
  "context": "delivery"         
}
```

Response (ParseReceiveScanResponse):
```json path=null start=null
{
  "isSuccess": true,
  "rawScanData": "...",
  "scanType": "GS1",
  "errorMessage": null,
  "hasIzPrefix": true,
  "parsedData": {
    "sscc": "000123456789012345",
    "gtin": "05901234123457",
    "lot": "ABC123",
    "expiryDate": "2026-03-15",
    "quantity": 10,
    "parsedFieldsCount": 4
  },
  "formSuggestion": {
    "kodId": null,
    "kodValue": null,
    "kodNazwa": null,
    "lot": "ABC123",
    "expiryDate": "2026-03-15",
    "suggestedQuantity": null,
    "packagingUnit": null,
    "isFromAdvice": false,
    "warningMessage": null,
    "hasSuggestions": true
  }
}
```

Uwagi:
- expiryDate w „parsedData” może mieć format prezentacyjny (dd.MM.yyyy) po stronie domeny, natomiast w „formSuggestion” jest normalizowana do yyyy-MM-dd.
- parsedFieldsCount to liczba rozpoznanych pól (SSCC/GTIN/Lot/Data/Ilość).

## Najczęstsze problemy i diagnoza

- „scanType: Unknown / Nierozpoznany typ kodu”
  - Przyczyna: użyto zapisu z nawiasami (np. „(01)”), który nie przechodzi detekcji GS1 w API.
  - Rozwiązanie: podawaj surowy ciąg GS1 (opcjonalnie „IZ” + „]C1”), bez nawiasów.

- „GTIN = null” mimo użycia AI 01/02
  - Przyczyna 1: błędna cyfra kontrolna GTIN.
  - Przyczyna 2: w przypadku AI 10 brak separatora FNC1 i parser „zjada” kolejne pole.
  - Rozwiązanie: użyj poprawnego GTIN-14 i dodaj „\u001d” po polu o zmiennej długości.

- „Nie znaleziono AI na pozycji X”
  - Przyczyna: brak separatora FNC1 po polu o zmiennej długości lub błędna kolejność znaków.
  - Rozwiązanie: upewnij się, że po AI 10 (Lot) i innych zmiennych polach występuje FNC1 (\u001d), jeśli po nich jest kolejne AI.

## Uwagi implementacyjne (parser i DTO)

- GS1Parser (Wms.Domain):
  - Obsługa AI: 00 (SSCC), 01 (GTIN), 02 (GTIN zawartości), 10 (Lot), 17 (Expiry), 37 (Quantity), 15 (Best Before)
  - Wykrywa prefiks IZ oraz kod startowy ]C1
  - Usuwa znaki kontrolne poza separatorem FNC1
  - Dla AI 10 i innych wspierających FNC1 usuwa separator z wartości

- Mapowanie do odpowiedzi API (Wms.Application):
  - GS1ParseResult -> GS1ScanData (SSCC/GTIN/Lot/ExpiryDate/Quantity/ParsedFieldsCount)
  - HasIzPrefix jest przenoszone do odpowiedzi API
  - FormSuggestion bazuje na rozpoznanych danych i (docelowo) dopasowaniach do kodów towarów

## Dobre praktyki

- Zawsze testuj skany z poprawnym GTIN-14 (cyfra kontrolna!).
- Po polu AI 10 zawsze dodawaj FNC1, jeśli występuje po nim kolejne pole.
- Nie używaj nawiasów (to zapis „drukowany”), tylko surową postać łańcucha skanu dla API.
- W środowisku JSON używaj „\u001d” dla FNC1.

---

Ostatnia aktualizacja: 2025-09-10
Autor: Zespół WMS

