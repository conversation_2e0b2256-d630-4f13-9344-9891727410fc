using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Wms.Application.Interfaces;
using Wms.Infrastructure.Services.DataWedge;

namespace Wms.Infrastructure.Extensions;

/// <summary>
/// Rozszerzenia DI dla serwisów DataWedge
/// </summary>
public static class DataWedgeServiceExtensions
{
    /// <summary>
    /// Dodaje serwisy DataWedge do DI container
    /// </summary>
    public static IServiceCollection AddDataWedgeServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Konfiguracja DataWedge
        services.Configure<DataWedgeOptions>(
            configuration.GetSection(DataWedgeOptions.SectionName));

        // Rejestracja serwisu
        services.AddSingleton<IScanInputService, DataWedgeService>();
        
        // Hosted service do automatycznego uruchomienia
        services.AddHostedService<DataWedgeHostedService>();

        return services;
    }
}

/// <summary>
/// Hosted service do zarządzania DataWedgeService
/// </summary>
public class DataWedgeHostedService : BackgroundService
{
    private readonly IScanInputService _scanInputService;
    private readonly ILogger<DataWedgeHostedService> _logger;

    public DataWedgeHostedService(
        IScanInputService scanInputService,
        ILogger<DataWedgeHostedService> logger)
    {
        _scanInputService = scanInputService;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Uruchamianie DataWedge HostedService...");
            
            // Subskrybuj eventy skanowania
            _scanInputService.ScanReceived += OnScanReceived;
            
            // Uruchom nasłuchiwanie
            await _scanInputService.StartListeningAsync();
            
            // Czekaj do zatrzymania
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("DataWedge HostedService został zatrzymany");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd w DataWedge HostedService");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Zatrzymywanie DataWedge HostedService...");
            
            // Unsubscribe eventy
            _scanInputService.ScanReceived -= OnScanReceived;
            
            // Zatrzymaj nasłuchiwanie
            await _scanInputService.StopListeningAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas zatrzymywania DataWedge HostedService");
        }
        finally
        {
            await base.StopAsync(cancellationToken);
        }
    }

    /// <summary>
    /// Obsługa eventów skanowania
    /// </summary>
    private async void OnScanReceived(object? sender, ScanReceivedEventArgs e)
    {
        try
        {
            _logger.LogInformation("HostedService otrzymał skan: {ScanData} z {DeviceInfo}", 
                e.ProcessedData.CleanData, e.DeviceInfo);

            // Automatyczne przetwarzanie skanów w kontekście dostaw
            if (e.ProcessedData.CleanData.StartsWith("IZ", StringComparison.OrdinalIgnoreCase) ||
                e.ProcessedData.CleanData.StartsWith("]C1"))
            {
                _logger.LogInformation("Wykryto skan GS1, przetwarzam w kontekście dostaw...");
                
                var result = await _scanInputService.ProcessScanForDeliveryAsync(
                    e.ProcessedData.CleanData);
                    
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Skan przetworzony pomyślnie: {ScanType}, HasIzPrefix={HasIzPrefix}",
                        result.ScanType, result.HasIzPrefix);
                }
                else
                {
                    _logger.LogWarning("Błąd przetwarzania skanu: {ErrorMessage}", result.ErrorMessage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd w obsłudze skanu przez HostedService");
        }
    }
}
