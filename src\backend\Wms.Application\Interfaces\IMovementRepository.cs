using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

public interface IMovementRepository
{
    Task<Movement> CreateAsync(Movement movement);
    Task<IEnumerable<Movement>> GetByLabelIdAsync(int labelId);
    Task<Movement?> GetLastMovementForLabelAsync(int labelId);
    Task<IEnumerable<Movement>> GetByUserIdAsync(int userId, DateTime? fromDate = null);
    Task<IEnumerable<Movement>> GetRecentMovementsAsync(int limit = 100);
}
