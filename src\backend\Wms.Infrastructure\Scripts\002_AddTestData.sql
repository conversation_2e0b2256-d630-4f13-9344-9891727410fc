-- <PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON><PERSON><PERSON> testowe dane dla palety ********* i lokalizacje
-- Do uruchomienia na serwerze produkcyjnym dla celów testowych

-- Dodanie testowych lokalizacji
INSERT IGNORE INTO miejsca (id, hala, regal, miejsce, poziom, is_visible, is_picking_location, max_pojemnosc) VALUES
(1, 1, '145', 2, '1', 1, 1, 5),
(2, 1, '146', 3, '2', 1, 0, 10),
(3, 2, '200', 1, '1', 1, 1, 8);

-- <PERSON><PERSON><PERSON> testowej palety
INSERT IGNORE INTO paleta (id, SSCC, paleta_number, created_at) VALUES
(1, NULL, '*********', NOW()),
(2, '123456789012345678', NULL, NOW()),
(3, NULL, '**********', NOW());

-- <PERSON><PERSON><PERSON> testowych etykiet
INSERT IGNORE INTO etykiety (
    id, system_id, sscc, etykieta_klient, paleta_id, miejscep, 
    ilosc, data_waznosci, lot, active, ts
) VALUES
-- Etykieta dla palety *********
(1, 1, '*********', '*********', 1, 3, 75, DATE_ADD(CURDATE(), INTERVAL 45 DAY), 'BATCH003', 1, NOW()),
-- Etykieta dla palety SSCC
(2, 2, '123456789012345678', 'TEST-001', 2, 1, 100, DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'BATCH001', 1, NOW()),
-- Etykieta dla palety **********
(3, 3, '**********', '**********', 3, 2, 50, DATE_ADD(CURDATE(), INTERVAL 15 DAY), 'BATCH002', 1, NOW());

-- Aktualizacja powiązań paleta -> etykiety
UPDATE paleta SET main_label_id = 1 WHERE id = 1;
UPDATE paleta SET main_label_id = 2 WHERE id = 2;
UPDATE paleta SET main_label_id = 3 WHERE id = 3;

-- Sprawdzenie danych
SELECT 'LOKALIZACJE:' as info;
SELECT id, CONCAT('MP-', hala, '-', regal, '-', miejsce, '-', poziom) as kod, 
       is_visible, max_pojemnosc FROM miejsca WHERE id IN (1,2,3);

SELECT 'PALETY:' as info;
SELECT p.id, p.SSCC, p.paleta_number, e.etykieta_klient, 
       CONCAT('MP-', m.hala, '-', m.regal, '-', m.miejsce, '-', m.poziom) as lokalizacja
FROM paleta p 
LEFT JOIN etykiety e ON p.id = e.paleta_id AND e.active = 1
LEFT JOIN miejsca m ON e.miejscep = m.id
WHERE p.id IN (1,2,3);
