using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Queries;
using Wms.Application.Interfaces;
using Wms.Domain.Exceptions;

namespace Wms.Application.Features.Receives.Handlers;

public class GetReceiveDetailsHandler : IRequestHandler<GetReceiveDetailsQuery, ReceiveDetailsDto?>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly ILabelRepository _labelRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetReceiveDetailsHandler> _logger;

    public GetReceiveDetailsHandler(
        IReceiveRepository receiveRepository,
        IPalletRepository palletRepository,
        ILabelRepository labelRepository,
        IMapper mapper,
        ILogger<GetReceiveDetailsHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _palletRepository = palletRepository;
        _labelRepository = labelRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<ReceiveDetailsDto?> Handle(GetReceiveDetailsQuery request, CancellationToken cancellationToken)
    {
        var receive = await _receiveRepository.GetWithDetailsAsync(request.ReceiveId, cancellationToken);
        if (receive == null)
        {
            _logger.LogWarning("Dostawa {ReceiveId} nie została znaleziona", request.ReceiveId);
            return null;
        }

        var receiveDto = _mapper.Map<ReceiveDetailsDto>(receive);

        // Pobierz pozycje oczekiwane z awizacji
        var expectedItems = await _receiveRepository.GetExpectedItemsAsync(request.ReceiveId, cancellationToken);
        receiveDto.ExpectedItems = _mapper.Map<List<ExpectedItemDto>>(expectedItems);

        // Pobierz nośniki i policz pozycje dla każdego
        var carriers = await _palletRepository.GetCarriersForReceiveAsync(request.ReceiveId, cancellationToken);
        var carrierDtos = new List<CarrierDto>();

        foreach (var carrier in carriers)
        {
            var carrierDto = _mapper.Map<CarrierDto>(carrier);
            
            // Policz pozycje na nośniku
            var carrierItems = await _palletRepository.GetCarrierItemsAsync(carrier.PaletaId, cancellationToken);
            carrierDto.ItemsCount = carrierItems.Count();
            carrierDto.IsCompleted = carrierItems.Any(); // Można dodać bardziej złożoną logikę

            carrierDtos.Add(carrierDto);
        }

        receiveDto.Carriers = carrierDtos;

        // Oblicz podsumowanie
        receiveDto.TotalExpectedItemsCount = expectedItems.Count();
        receiveDto.CompletedItemsCount = carrierDtos.Sum(c => c.ItemsCount);
        receiveDto.IsComplete = receiveDto.TotalExpectedItemsCount > 0 && 
                                receiveDto.CompletedItemsCount >= receiveDto.TotalExpectedItemsCount;

        _logger.LogDebug("Pobrano szczegóły dostawy LK{ReceiveId}: {CarriersCount} nośników, {ExpectedCount} oczekiwanych pozycji", 
            request.ReceiveId, receiveDto.Carriers.Count, receiveDto.TotalExpectedItemsCount);

        return receiveDto;
    }
}

public class GetReceiveExpectedItemsHandler : IRequestHandler<GetReceiveExpectedItemsQuery, List<ExpectedItemDto>>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly ILabelRepository _labelRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetReceiveExpectedItemsHandler> _logger;

    public GetReceiveExpectedItemsHandler(
        IReceiveRepository receiveRepository,
        ILabelRepository labelRepository,
        IMapper mapper,
        ILogger<GetReceiveExpectedItemsHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _labelRepository = labelRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<ExpectedItemDto>> Handle(GetReceiveExpectedItemsQuery request, CancellationToken cancellationToken)
    {
        var expectedItems = await _receiveRepository.GetExpectedItemsAsync(request.ListControlId, cancellationToken);
        var expectedItemDtos = _mapper.Map<List<ExpectedItemDto>>(expectedItems);

        // Wzbogacenie danymi z kartoteki 'kody' po (system_id, kod)
        var systemId = expectedItems.FirstOrDefault()?.AwizacjaHead?.SystemId ?? 0;
        if (systemId > 0)
        {
            var codes = expectedItemDtos
                .Select(e => e.Kod)
                .Where(c => !string.IsNullOrWhiteSpace(c))
                .Select(c => c!)
                .Distinct()
                .ToList();

            if (codes.Count > 0)
            {
                var kodLookup = await _receiveRepository.GetKodLookupAsync(systemId, codes, cancellationToken);
                foreach (var dto in expectedItemDtos)
                {
                    if (!string.IsNullOrWhiteSpace(dto.Kod) && kodLookup.TryGetValue(dto.Kod!, out var info))
                    {
                        dto.KodId = info.KodId;
                        dto.KodNazwa = info.KodNazwa;
                        dto.Ean = info.Ean;
                    }
                }
            }
        }

        // Pobierz sumy ilości przyjętych towarów pogrupowane po kodzie towaru
        var receivedQuantities = await _labelRepository.GetReceivedQuantitiesByCodeAsync(request.ListControlId);

        // Dla każdej pozycji ustaw ilość przyjętą na podstawie kodu towaru
        foreach (var expectedItem in expectedItemDtos)
        {
            if (!string.IsNullOrEmpty(expectedItem.Kod) && receivedQuantities.ContainsKey(expectedItem.Kod))
            {
                expectedItem.IloscPrzyjeta = receivedQuantities[expectedItem.Kod];
            }
        }

        _logger.LogDebug("Pobrano {Count} oczekiwanych pozycji dla dostawy LK{ReceiveId}", 
            expectedItemDtos.Count, request.ListControlId);

        return expectedItemDtos;
    }
}

public class GetReceiveItemsHandler : IRequestHandler<GetReceiveItemsQuery, List<ReceiveItemDto>>
{
    private readonly ILabelRepository _labelRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetReceiveItemsHandler> _logger;

    public GetReceiveItemsHandler(
        ILabelRepository labelRepository,
        IMapper mapper,
        ILogger<GetReceiveItemsHandler> logger)
    {
        _labelRepository = labelRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<ReceiveItemDto>> Handle(GetReceiveItemsQuery request, CancellationToken cancellationToken)
    {
        IEnumerable<Domain.Entities.Label> labels;

        if (request.PaletaId.HasValue)
        {
            // Pobierz pozycje tylko z określonej palety
            labels = await _labelRepository.GetByPalletIdAsync(request.PaletaId.Value);
        }
        else
        {
            // TODO: Potrzeba dodania metody GetByListControlIdAsync do ILabelRepository
            // Na razie zwracamy pustą listę
            labels = new List<Domain.Entities.Label>();
        }

        var receiveItemDtos = _mapper.Map<List<ReceiveItemDto>>(labels);

        _logger.LogDebug("Pobrano {Count} pozycji dla dostawy LK{ReceiveId}{PalletFilter}", 
            receiveItemDtos.Count, request.ListControlId, 
            request.PaletaId.HasValue ? $" i palety {request.PaletaId.Value}" : "");

        return receiveItemDtos;
    }
}

public class GetCarrierItemsHandler : IRequestHandler<GetCarrierItemsQuery, List<ReceiveItemDto>>
{
    private readonly IPalletRepository _palletRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierItemsHandler> _logger;

    public GetCarrierItemsHandler(
        IPalletRepository palletRepository,
        IMapper mapper,
        ILogger<GetCarrierItemsHandler> logger)
    {
        _palletRepository = palletRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<ReceiveItemDto>> Handle(GetCarrierItemsQuery request, CancellationToken cancellationToken)
    {
        var labels = await _palletRepository.GetCarrierItemsAsync(request.PaletaId, cancellationToken);
        var receiveItemDtos = _mapper.Map<List<ReceiveItemDto>>(labels);

        _logger.LogDebug("Pobrano {Count} pozycji z nośnika DS{PalletId:D8} dla dostawy LK{ReceiveId}", 
            receiveItemDtos.Count, request.PaletaId, request.ListControlId);

        return receiveItemDtos;
    }
}

public class GetReceiveCarriersHandler : IRequestHandler<GetReceiveCarriersQuery, List<CarrierDto>>
{
    private readonly IPalletRepository _palletRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetReceiveCarriersHandler> _logger;

    public GetReceiveCarriersHandler(
        IPalletRepository palletRepository,
        IMapper mapper,
        ILogger<GetReceiveCarriersHandler> logger)
    {
        _palletRepository = palletRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<CarrierDto>> Handle(GetReceiveCarriersQuery request, CancellationToken cancellationToken)
    {
        var carriers = await _palletRepository.GetCarriersForReceiveAsync(request.ListControlId, cancellationToken);
        var carrierDtos = new List<CarrierDto>();

        foreach (var carrier in carriers)
        {
            var carrierDto = _mapper.Map<CarrierDto>(carrier);
            
            // Policz pozycje na nośniku
            var carrierItems = await _palletRepository.GetCarrierItemsAsync(carrier.PaletaId, cancellationToken);
            carrierDto.ItemsCount = carrierItems.Count();
            carrierDto.IsCompleted = carrierItems.Any();

            carrierDtos.Add(carrierDto);
        }

        _logger.LogDebug("Pobrano {Count} nośników dla dostawy LK{ReceiveId}", 
            carrierDtos.Count, request.ListControlId);

        return carrierDtos;
    }
}
