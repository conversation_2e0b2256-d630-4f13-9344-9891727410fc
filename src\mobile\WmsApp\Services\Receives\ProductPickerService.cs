using Microsoft.Extensions.Logging;
using WmsApp.Models.Receives;
using WmsApp.ViewModels.Receives;
using WmsApp.Views.Receives;

namespace WmsApp.Services.Receives;

public interface IProductPickerService
{
    Task<ProductSelection?> PickAsync(string lk);
}

public class ProductPickerService : IProductPickerService
{
    private readonly IServiceProvider _services;

    public ProductPickerService(IServiceProvider services)
    {
        _services = services;
    }

    public async Task<ProductSelection?> PickAsync(string lk)
    {
        var logger = _services.GetService<ILogger<ProductPickerService>>();
        logger?.LogInformation("DEBUG: ProductPickerService.PickAsync wywołane z LK={LK}", lk);
        
        var tcs = new TaskCompletionSource<ProductSelection?>();

        var vm = _services.GetRequiredService<ProductPickerViewModel>();
        logger?.LogInformation("DEBUG: Otrzymano ProductPickerViewModel");
        
        vm.Init(lk, tcs);
        logger?.LogInformation("DEBUG: Wywołano Init na ViewModelu");

        var page = _services.GetRequiredService<ProductPickerPage>();
        page.BindingContext = vm;
        logger?.LogInformation("DEBUG: Ustawiono BindingContext strony");

        // Otwórz jako modal
        try
        {
            await Application.Current!.MainPage!.Navigation.PushModalAsync(page);
            logger?.LogInformation("DEBUG: Otwarto modal");

            // Poczekaj na rezultat
            logger?.LogInformation("DEBUG: Czekanie na rezultat...");
            var result = await tcs.Task;
            logger?.LogInformation("DEBUG: Otrzymano rezultat: {Result}", result?.KodValue ?? "null");

            // Zamknij modal (jeśli jeszcze otwarty)
            // Sprawdź czy modal jest nadal w stosie nawigacji
            if (Application.Current?.MainPage?.Navigation?.ModalStack?.Contains(page) == true)
            {
                await Application.Current.MainPage.Navigation.PopModalAsync();
                logger?.LogInformation("DEBUG: Zamknięto modal");
            }
            else
            {
                logger?.LogInformation("DEBUG: Modal już zamknięty");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Błąd podczas obsługi modala");
            
            // Spróbuj zamknąć modal w przypadku błędu
            try
            {
                if (Application.Current?.MainPage?.Navigation?.ModalStack?.Contains(page) == true)
                {
                    await Application.Current.MainPage.Navigation.PopModalAsync();
                }
            }
            catch { /* Ignoruj błędy przy zamykaniu */ }
            
            throw;
        }
    }
}

