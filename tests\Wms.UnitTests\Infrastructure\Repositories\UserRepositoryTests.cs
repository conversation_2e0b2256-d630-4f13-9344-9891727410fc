using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class UserRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly UserRepository _userRepository;

    public UserRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _userRepository = new UserRepository(_context);

        SeedTestData();
    }

    [Fact]
    public async Task GetByCardNumberAsync_WithValidCardNumber_ShouldReturnUser()
    {
        // Arrange
        var cardNumber = "1234567890";

        // Act
        var result = await _userRepository.GetByCardNumberAsync(cardNumber);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.NumerKarty.Should().Be(cardNumber);
        result.ImieNazwisko.Should().Be("Jan Kowalski");
        result.Stanowisko.Should().Be("Operator");
        result.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GetByCardNumberAsync_WithInvalidCardNumber_ShouldReturnNull()
    {
        // Arrange
        var invalidCardNumber = "invalid-card";

        // Act
        var result = await _userRepository.GetByCardNumberAsync(invalidCardNumber);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByCardNumberAsync_WithNullCardNumber_ShouldReturnNull()
    {
        // Arrange
        string nullCardNumber = null!;

        // Act
        var result = await _userRepository.GetByCardNumberAsync(nullCardNumber);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByIdAsync_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var userId = 1;

        // Act
        var result = await _userRepository.GetByIdAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(userId);
        result.ImieNazwisko.Should().Be("Jan Kowalski");
        result.Login.Should().Be("jan.kowalski");
        result.JednostkaId.Should().Be(1);
    }

    [Fact]
    public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var invalidUserId = 999;

        // Act
        var result = await _userRepository.GetByIdAsync(invalidUserId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByIdAsync_WithZeroId_ShouldReturnNull()
    {
        // Arrange
        var zeroId = 0;

        // Act
        var result = await _userRepository.GetByIdAsync(zeroId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnAllUsers()
    {
        // Act
        var result = await _userRepository.GetActiveUsersAsync();

        // Assert
        result.Should().NotBeNull();
        var userList = result.ToList();
        userList.Should().HaveCount(3);
        userList.Should().Contain(u => u.ImieNazwisko == "Jan Kowalski");
        userList.Should().Contain(u => u.ImieNazwisko == "Anna Nowak");
        userList.Should().Contain(u => u.ImieNazwisko == "Piotr Wiśniewski");
    }

    [Fact]
    public async Task GetActiveUsersAsync_WithEmptyDatabase_ShouldReturnEmptyCollection()
    {
        // Arrange - Clear the database
        _context.Users.RemoveRange(_context.Users);
        await _context.SaveChangesAsync();

        // Act
        var result = await _userRepository.GetActiveUsersAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData("1234567890", "Jan Kowalski")]
    [InlineData("0987654321", "Anna Nowak")]
    [InlineData("1122334455", "Piotr Wiśniewski")]
    public async Task GetByCardNumberAsync_WithDifferentCardNumbers_ShouldReturnCorrectUsers(string cardNumber, string expectedName)
    {
        // Act
        var result = await _userRepository.GetByCardNumberAsync(cardNumber);

        // Assert
        result.Should().NotBeNull();
        result!.ImieNazwisko.Should().Be(expectedName);
        result.NumerKarty.Should().Be(cardNumber);
    }

    [Theory]
    [InlineData(1, "Jan Kowalski", "jan.kowalski")]
    [InlineData(2, "Anna Nowak", "anna.nowak")]
    [InlineData(3, "Piotr Wiśniewski", "piotr.wisniewski")]
    public async Task GetByIdAsync_WithDifferentIds_ShouldReturnCorrectUsers(int userId, string expectedName, string expectedLogin)
    {
        // Act
        var result = await _userRepository.GetByIdAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result!.ImieNazwisko.Should().Be(expectedName);
        result.Login.Should().Be(expectedLogin);
        result.Id.Should().Be(userId);
    }

    [Fact]
    public async Task GetByCardNumberAsync_WithCaseVariations_ShouldBeExact()
    {
        // Arrange - Add user with specific card number
        var user = new User
        {
            Id = 10,
            Login = "test.user",
            ImieNazwisko = "Test User",
            Haslo = "test123",
            NumerKarty = "AbC123",
            Stanowisko = "Tester",
            JednostkaId = 1
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Act
        var exactMatch = await _userRepository.GetByCardNumberAsync("AbC123");
        var lowerCase = await _userRepository.GetByCardNumberAsync("abc123");
        var upperCase = await _userRepository.GetByCardNumberAsync("ABC123");

        // Assert
        exactMatch.Should().NotBeNull();
        exactMatch!.ImieNazwisko.Should().Be("Test User");
        
        // Entity Framework is case-insensitive by default for string comparisons
        // This behavior depends on database collation
        lowerCase?.ImieNazwisko.Should().BeOneOf("Test User", null);
        upperCase?.ImieNazwisko.Should().BeOneOf("Test User", null);
    }

    [Fact]
    public async Task GetByCardNumberAsync_WithWhitespace_ShouldNotMatch()
    {
        // Arrange
        var cardWithSpaces = " 1234567890 ";

        // Act
        var result = await _userRepository.GetByCardNumberAsync(cardWithSpaces);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnUsersInConsistentOrder()
    {
        // Act
        var result1 = await _userRepository.GetActiveUsersAsync();
        var result2 = await _userRepository.GetActiveUsersAsync();

        // Assert
        var list1 = result1.ToList();
        var list2 = result2.ToList();
        
        list1.Should().HaveCount(list2.Count);
        for (int i = 0; i < list1.Count; i++)
        {
            list1[i].Id.Should().Be(list2[i].Id);
        }
    }

    private void SeedTestData()
    {
        var users = new[]
        {
            new User
            {
                Id = 1,
                Login = "jan.kowalski",
                ImieNazwisko = "Jan Kowalski",
                Haslo = "password123",
                NumerKarty = "1234567890",
                Stanowisko = "Operator",
                Email = "<EMAIL>",
                JednostkaId = 1
            },
            new User
            {
                Id = 2,
                Login = "anna.nowak",
                ImieNazwisko = "Anna Nowak",
                Haslo = "password123",
                NumerKarty = "0987654321",
                Stanowisko = "Supervisor",
                Email = "<EMAIL>",
                JednostkaId = 2
            },
            new User
            {
                Id = 3,
                Login = "piotr.wisniewski",
                ImieNazwisko = "Piotr Wiśniewski",
                Haslo = "password123",
                NumerKarty = "1122334455",
                Stanowisko = "Manager",
                Email = "<EMAIL>",
                JednostkaId = 1
            }
        };

        _context.Users.AddRange(users);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
