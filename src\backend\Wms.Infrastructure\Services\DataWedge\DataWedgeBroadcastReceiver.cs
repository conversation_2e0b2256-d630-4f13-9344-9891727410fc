using Microsoft.Extensions.Logging;

namespace Wms.Infrastructure.Services.DataWedge;

/// <summary>
/// Przykład implementacji BroadcastReceiver dla MAUI Android
/// Ten kod należy umieścić w projekcie MAUI w folderze Platforms/Android
/// </summary>
public static class DataWedgeBroadcastReceiverExample
{
    /// <summary>
    /// Przykład kodu BroadcastReceiver dla Android MAUI
    /// </summary>
    public static string GetBroadcastReceiverCode()
    {
        return @"
// Ten kod należy umieścić w: src/mobile/WmsApp/Platforms/Android/DataWedgeBroadcastReceiver.cs

using Android.Content;
using AndroidX.LocalBroadcastManager.Content;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;

namespace WmsApp.Platforms.Android;

[BroadcastReceiver(Enabled = true, Exported = false)]
[IntentFilter(new[] { ""com.wms.receives.SCAN"" })]
public class DataWedgeBroadcastReceiver : BroadcastReceiver
{
    private static ILogger? _logger;
    
    public override void OnReceive(Context? context, Intent? intent)
    {
        if (intent?.Action != ""com.wms.receives.SCAN"")
            return;

        try
        {
            // Pobierz dane z Intent extras
            var scanData = intent.GetStringExtra(""com.symbol.datawedge.data_string"");
            var labelType = intent.GetStringExtra(""com.symbol.datawedge.label_type"");
            var source = intent.GetStringExtra(""com.symbol.datawedge.source"");
            var timestamp = intent.GetStringExtra(""com.symbol.datawedge.timestamp"");

            _logger?.LogInformation(""Otrzymano skan DataWedge: Data={ScanData}, Type={LabelType}, Source={Source}"",
                scanData, labelType, source);

            if (!string.IsNullOrEmpty(scanData))
            {
                // Wyślij event do głównej aplikacji
                MessagingCenter.Send<string, ScanReceivedMessage>(""DataWedgeReceiver"", ""ScanReceived"", 
                    new ScanReceivedMessage(scanData, labelType, source));
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, ""Błąd w DataWedgeBroadcastReceiver"");
        }
    }
    
    public static void SetLogger(ILogger logger)
    {
        _logger = logger;
    }
}

public record ScanReceivedMessage(string ScanData, string? LabelType, string? Source);

// W MauiProgram.cs dodać:
// builder.Services.AddSingleton<DataWedgeService>();

// W MainPage.xaml.cs lub ViewModel subskrybować:
// MessagingCenter.Subscribe<string, ScanReceivedMessage>(this, ""ScanReceived"", OnScanReceived);

// Przykład obsługi:
private void OnScanReceived(string sender, ScanReceivedMessage message)
{
    MainThread.BeginInvokeOnMainThread(async () =>
    {
        try
        {
            // Przetwórz skan przez serwis
            var dataWedgeService = ServiceHelper.GetService<IScanInputService>();
            await dataWedgeService.ProcessScanForDeliveryAsync(message.ScanData);
        }
        catch (Exception ex)
        {
            // Handle error
        }
    });
}
";
    }
}

/// <summary>
/// Konfiguracja DataWedge dla MAUI
/// </summary>
public static class DataWedgeConfiguration
{
    /// <summary>
    /// Przykład konfiguracji profilu DataWedge
    /// </summary>
    public static DataWedgeProfileConfig GetProfileConfig()
    {
        return new DataWedgeProfileConfig
        {
            ProfileName = "WmsReceives",
            Package = "com.wms.receives",
            Activities = new[] { "*" },
            OutputConfig = new DataWedgeOutputConfig
            {
                OutputMode = "Intent",
                IntentAction = "com.wms.receives.SCAN",
                IntentDelivery = "Broadcast",
                IntentExtras = new Dictionary<string, object>
                {
                    ["com.symbol.datawedge.data_string"] = "",
                    ["com.symbol.datawedge.label_type"] = "",
                    ["com.symbol.datawedge.source"] = ""
                }
            },
            ScannerConfig = new DataWedgeScannerConfig
            {
                ScannerEnabled = true,
                ScannerSelection = "auto",
                DecoderParams = new Dictionary<string, object>
                {
                    // Włącz najważniejsze kodery
                    ["decoder_code128"] = true,
                    ["decoder_code39"] = true,
                    ["decoder_ean8"] = true,
                    ["decoder_ean13"] = true,
                    ["decoder_upca"] = true,
                    ["decoder_upce"] = true,
                    ["decoder_gs1_128"] = true, // Ważne dla GS1
                    ["decoder_qrcode"] = true,
                    ["decoder_datamatrix"] = true,
                    ["decoder_pdf417"] = true
                }
            }
        };
    }
    
    /// <summary>
    /// Generuje XML konfiguracji profilu DataWedge
    /// </summary>
    public static string GenerateDataWedgeProfileXml(string profileName, string packageName)
    {
        return $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<profile version=""17.3"" schemaVersion=""17.2"" name=""{profileName}"">
    <application name=""{packageName}"" package=""{packageName}"">
        <activity name=""*"" />
    </application>
    
    <plugin name=""barcode"">
        <param name=""configure_all_scanners"" value=""true"" />
        <param name=""scanner_selection"" value=""auto"" />
        <param name=""scanner_input_enabled"" value=""true"" />
        
        <!-- Dekodery -->
        <param name=""decoder_code128"" value=""true"" />
        <param name=""decoder_gs1_128"" value=""true"" />
        <param name=""decoder_ean13"" value=""true"" />
        <param name=""decoder_ean8"" value=""true"" />
        <param name=""decoder_upca"" value=""true"" />
        <param name=""decoder_code39"" value=""true"" />
        <param name=""decoder_qrcode"" value=""true"" />
        <param name=""decoder_datamatrix"" value=""true"" />
    </plugin>
    
    <plugin name=""intent"">
        <param name=""intent_output_enabled"" value=""true"" />
        <param name=""intent_action"" value=""com.wms.receives.SCAN"" />
        <param name=""intent_delivery"" value=""2"" />
        <param name=""intent_use_content_provider"" value=""false"" />
        
        <!-- Intent extras -->
        <bundle>
            <param name=""com.symbol.datawedge.data_string"" />
            <param name=""com.symbol.datawedge.label_type"" />
            <param name=""com.symbol.datawedge.source"" />
        </bundle>
    </plugin>
</profile>";
    }
}

/// <summary>
/// Modele konfiguracji DataWedge
/// </summary>
public record DataWedgeProfileConfig
{
    public string ProfileName { get; init; } = string.Empty;
    public string Package { get; init; } = string.Empty;
    public string[] Activities { get; init; } = Array.Empty<string>();
    public DataWedgeOutputConfig? OutputConfig { get; init; }
    public DataWedgeScannerConfig? ScannerConfig { get; init; }
}

public record DataWedgeOutputConfig
{
    public string OutputMode { get; init; } = string.Empty;
    public string IntentAction { get; init; } = string.Empty;
    public string IntentDelivery { get; init; } = string.Empty;
    public Dictionary<string, object> IntentExtras { get; init; } = new();
}

public record DataWedgeScannerConfig
{
    public bool ScannerEnabled { get; init; }
    public string ScannerSelection { get; init; } = string.Empty;
    public Dictionary<string, object> DecoderParams { get; init; } = new();
}
