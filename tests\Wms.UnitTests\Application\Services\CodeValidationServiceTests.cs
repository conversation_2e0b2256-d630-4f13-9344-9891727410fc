using FluentAssertions;
using Wms.Application.Services;

namespace Wms.UnitTests.Application.Services;

public class CodeValidationServiceTests
{
    private readonly CodeValidationService _codeValidationService;

    public CodeValidationServiceTests()
    {
        _codeValidationService = new CodeValidationService();
    }

    #region SSCC Tests

    [Theory]
    [InlineData("123456789012345675")] // Valid SSCC with correct check digit
    [InlineData("000000000000000000")] // All zeros (special case)
    public void ValidateSSCC_WithValidCodes_ShouldReturnTrue(string ssccCode)
    {
        // Act
        var result = _codeValidationService.ValidateSSCC(ssccCode);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("")] // Empty string
    [InlineData("   ")] // Whitespace
    [InlineData(null)] // Null
    public void ValidateSSCC_WithEmptyOrNullInput_ShouldReturnFalse(string ssccCode)
    {
        // Act
        var result = _codeValidationService.ValidateSSCC(ssccCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("12345678901234567")] // 17 digits - too short
    [InlineData("1234567890123456789")] // 19 digits - too long
    [InlineData("123456")] // Way too short
    public void ValidateSSCC_WithIncorrectLength_ShouldReturnFalse(string ssccCode)
    {
        // Act
        var result = _codeValidationService.ValidateSSCC(ssccCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("12345678901234567A")] // Contains letter
    [InlineData("1234567890123456-5")] // Contains hyphen
    [InlineData("123456789012345 75")] // Contains space
    [InlineData("123456789012345.75")] // Contains dot
    public void ValidateSSCC_WithNonDigitCharacters_ShouldReturnFalse(string ssccCode)
    {
        // Act
        var result = _codeValidationService.ValidateSSCC(ssccCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("123456789012345674")] // Invalid check digit (should be 5)
    [InlineData("123456789012345670")] // Invalid check digit
    public void ValidateSSCC_WithIncorrectCheckDigit_ShouldReturnFalse(string ssccCode)
    {
        // Act
        var result = _codeValidationService.ValidateSSCC(ssccCode);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region DS Code Tests

    [Theory]
    [InlineData("DS1234")] // 4 digits - minimum
    [InlineData("DS12345")] // 5 digits
    [InlineData("DS123456")] // 6 digits
    [InlineData("DS1234567")] // 7 digits
    [InlineData("DS12345678")] // 8 digits
    [InlineData("DS123456789")] // 9 digits - maximum
    public void ValidateDS_WithValidCodes_ShouldReturnTrue(string dsCode)
    {
        // Act
        var result = _codeValidationService.ValidateDS(dsCode);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("")] // Empty string
    [InlineData("   ")] // Whitespace
    [InlineData(null)] // Null
    public void ValidateDS_WithEmptyOrNullInput_ShouldReturnFalse(string dsCode)
    {
        // Act
        var result = _codeValidationService.ValidateDS(dsCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("DS123")] // 3 digits - too short
    [InlineData("DS1234567890")] // 10 digits - too long
    [InlineData("DS")] // No digits
    public void ValidateDS_WithIncorrectLength_ShouldReturnFalse(string dsCode)
    {
        // Act
        var result = _codeValidationService.ValidateDS(dsCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("ds1234")] // Lowercase prefix
    [InlineData("Ds1234")] // Mixed case prefix
    [InlineData("1234")] // No DS prefix
    [InlineData("XS1234")] // Wrong prefix
    public void ValidateDS_WithIncorrectPrefix_ShouldReturnFalse(string dsCode)
    {
        // Act
        var result = _codeValidationService.ValidateDS(dsCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("DS123A")] // Contains letter in number part
    [InlineData("DS12-34")] // Contains hyphen
    [InlineData("DS 1234")] // Contains space
    public void ValidateDS_WithNonDigitCharacters_ShouldReturnFalse(string dsCode)
    {
        // Act
        var result = _codeValidationService.ValidateDS(dsCode);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region Location Code Tests

    [Theory]
    [InlineData("MP-1-RMP-010-1")] // Standard format
    [InlineData("MP-12-149-099-6")] // Different values
    [InlineData("MP-123-A1B-001-Z")] // Alphanumeric regal and poziom
    [InlineData("MP-1-1-1-1")] // All minimum values
    public void ValidateLocationCode_WithValidCodes_ShouldReturnTrue(string locationCode)
    {
        // Act
        var result = _codeValidationService.ValidateLocationCode(locationCode);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("")] // Empty string
    [InlineData("   ")] // Whitespace
    [InlineData(null)] // Null
    public void ValidateLocationCode_WithEmptyOrNullInput_ShouldReturnFalse(string locationCode)
    {
        // Act
        var result = _codeValidationService.ValidateLocationCode(locationCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("MP-1234-RMP-010-1")] // Hala too long (4 digits)
    [InlineData("MP-1-RMP-1000-1")] // Miejsce too long (4 digits)
    [InlineData("MP-1-RMP-010-AB")] // Poziom too long (2 characters)
    public void ValidateLocationCode_WithFieldsTooLong_ShouldReturnFalse(string locationCode)
    {
        // Act
        var result = _codeValidationService.ValidateLocationCode(locationCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("MP-RMP-010-1")] // Missing hala
    [InlineData("MP-1-010-1")] // Missing regal
    [InlineData("MP-1-RMP-1")] // Missing miejsce
    [InlineData("MP-1-RMP-010")] // Missing poziom
    [InlineData("1-RMP-010-1")] // Missing MP prefix
    public void ValidateLocationCode_WithMissingFields_ShouldReturnFalse(string locationCode)
    {
        // Act
        var result = _codeValidationService.ValidateLocationCode(locationCode);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("XP-1-RMP-010-1")] // Wrong prefix
    [InlineData("mp-1-RMP-010-1")] // Lowercase prefix
    [InlineData("MP_1_RMP_010_1")] // Wrong separators
    [InlineData("MP/1/RMP/010/1")] // Wrong separators
    public void ValidateLocationCode_WithIncorrectFormat_ShouldReturnFalse(string locationCode)
    {
        // Act
        var result = _codeValidationService.ValidateLocationCode(locationCode);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region ParseLocationCode Tests

    [Fact]
    public void ParseLocationCode_WithValidCode_ShouldReturnCorrectParts()
    {
        // Arrange
        var locationCode = "MP-12-RMP-010-6";

        // Act
        var result = _codeValidationService.ParseLocationCode(locationCode);

        // Assert
        result.hala.Should().Be(12);
        result.regal.Should().Be("RMP");
        result.miejsce.Should().Be(10);
        result.poziom.Should().Be("6");
    }

    [Fact]
    public void ParseLocationCode_WithAlphanumericRegal_ShouldReturnCorrectParts()
    {
        // Arrange
        var locationCode = "MP-1-A1B2C-123-Z";

        // Act
        var result = _codeValidationService.ParseLocationCode(locationCode);

        // Assert
        result.hala.Should().Be(1);
        result.regal.Should().Be("A1B2C");
        result.miejsce.Should().Be(123);
        result.poziom.Should().Be("Z");
    }

    [Fact]
    public void ParseLocationCode_WithLeadingZerosInMiejsce_ShouldParseCorrectly()
    {
        // Arrange
        var locationCode = "MP-1-RMP-010-1";

        // Act
        var result = _codeValidationService.ParseLocationCode(locationCode);

        // Assert
        result.hala.Should().Be(1);
        result.regal.Should().Be("RMP");
        result.miejsce.Should().Be(10); // Leading zeros stripped by int.Parse
        result.poziom.Should().Be("1");
    }

    [Fact]
    public void ParseLocationCode_WithInvalidCode_ShouldThrowArgumentException()
    {
        // Arrange
        var invalidLocationCode = "INVALID-CODE";

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => _codeValidationService.ParseLocationCode(invalidLocationCode));
        exception.Message.Should().Contain("Invalid location code format");
        exception.Message.Should().Contain(invalidLocationCode);
        exception.Message.Should().Contain("Expected: MP-H-R-M-P");
    }

    [Theory]
    [InlineData("")] // Empty string
    [InlineData("   ")] // Whitespace
    [InlineData(null)] // Null
    [InlineData("MP-1-RMP-010")] // Missing poziom
    [InlineData("1-RMP-010-1")] // Missing MP prefix
    public void ParseLocationCode_WithInvalidInput_ShouldThrowArgumentException(string invalidLocationCode)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => _codeValidationService.ParseLocationCode(invalidLocationCode));
    }

    [Fact]
    public void ParseLocationCode_WithMinimumValues_ShouldReturnCorrectParts()
    {
        // Arrange
        var locationCode = "MP-1-A-1-X";

        // Act
        var result = _codeValidationService.ParseLocationCode(locationCode);

        // Assert
        result.hala.Should().Be(1);
        result.regal.Should().Be("A");
        result.miejsce.Should().Be(1);
        result.poziom.Should().Be("X");
    }

    [Fact]
    public void ParseLocationCode_WithMaximumValues_ShouldReturnCorrectParts()
    {
        // Arrange
        var locationCode = "MP-999-ABCDEFGH-999-9";

        // Act
        var result = _codeValidationService.ParseLocationCode(locationCode);

        // Assert
        result.hala.Should().Be(999);
        result.regal.Should().Be("ABCDEFGH");
        result.miejsce.Should().Be(999);
        result.poziom.Should().Be("9");
    }

    #endregion

    #region Interface Tests

    [Fact]
    public void CodeValidationService_ShouldImplementICodeValidationService()
    {
        // Assert
        _codeValidationService.Should().BeAssignableTo<ICodeValidationService>();
    }

    [Fact]
    public void ICodeValidationService_ShouldHaveAllRequiredMethods()
    {
        // Arrange
        var serviceType = typeof(ICodeValidationService);

        // Assert
        serviceType.GetMethod("ValidateSSCC").Should().NotBeNull();
        serviceType.GetMethod("ValidateDS").Should().NotBeNull();
        serviceType.GetMethod("ValidateLocationCode").Should().NotBeNull();
        serviceType.GetMethod("ParseLocationCode").Should().NotBeNull();
    }

    #endregion
}
