using Wms.Domain.Exceptions;

namespace Wms.Domain.Services.GS1;

/// <summary>
/// Reprezentuje Application Identifier (AI) według standardu GS1-128
/// </summary>
public record ApplicationIdentifier
{
    public string Code { get; private init; }
    public string Description { get; private init; }
    public int LengthOfAI { get; private init; }
    public DataType DataType { get; private init; }
    public int MaxDataLength { get; private init; }
    public bool SupportsFNC1 { get; private init; } // Czy obsługuje separator FNC1
    public bool IsVariableLength { get; private init; }

    private ApplicationIdentifier(string code, string description, int lengthOfAI, 
        DataType dataType, int maxDataLength, bool supportsFNC1)
    {
        Code = code;
        Description = description;
        LengthOfAI = lengthOfAI;
        DataType = dataType;
        MaxDataLength = maxDataLength;
        SupportsFNC1 = supportsFNC1;
        IsVariableLength = supportsFNC1;
    }

    /// <summary>
    /// Tworzy nowy AI
    /// </summary>
    public static ApplicationIdentifier Create(string code, string description, int lengthOfAI, 
        DataType dataType, int maxDataLength, bool supportsFNC1)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new InvalidGS1FormatException(code ?? "null", "Kod AI nie może być pusty");

        if (lengthOfAI < 1 || lengthOfAI > 4)
            throw new InvalidGS1FormatException(code, "Długość AI musi być między 1 a 4 znakami");

        if (maxDataLength < 1)
            throw new InvalidGS1FormatException(code, "Maksymalna długość danych musi być większa od 0");

        return new ApplicationIdentifier(code, description, lengthOfAI, dataType, maxDataLength, supportsFNC1);
    }

    /// <summary>
    /// Sprawdza czy podany kod może być tym AI (obsługuje wzorce z 'd')
    /// </summary>
    public bool Matches(string candidateCode)
    {
        if (string.IsNullOrEmpty(candidateCode)) return false;

        // Sprawdź dokładne dopasowanie
        if (candidateCode == Code) return true;

        // Sprawdź wzorzec z 'd' (np. "310d" pasuje do "3101", "3102", etc.)
        if (Code.EndsWith("d") && candidateCode.Length == LengthOfAI)
        {
            var pattern = Code[..^1]; // Usuń 'd' z końca
            if (candidateCode.StartsWith(pattern) && candidateCode.Length == LengthOfAI)
            {
                // Sprawdź czy ostatni znak to cyfra (dla wzorców z 'd')
                return char.IsDigit(candidateCode[^1]);
            }
        }

        return false;
    }

    /// <summary>
    /// Pobiera liczbę miejsc dziesiętnych z kodu AI (dla wzorców z 'd')
    /// </summary>
    public int GetDecimalPlaces(string actualCode)
    {
        if (!Code.EndsWith("d")) return 0;
        
        if (actualCode.Length != LengthOfAI) return 0;
        
        var lastChar = actualCode[^1];
        return char.IsDigit(lastChar) ? int.Parse(lastChar.ToString()) : 0;
    }

    public override string ToString() => $"{Code} [{Description}]";
}

/// <summary>
/// Typ danych AI
/// </summary>
public enum DataType
{
    Numeric,
    Alphanumeric
}
