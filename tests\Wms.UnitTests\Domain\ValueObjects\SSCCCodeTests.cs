using FluentAssertions;
using Wms.Domain.Exceptions;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.ValueObjects;

public class SSCCCodeTests
{
    [Theory]
    [InlineData("123456789012345678")]
    [InlineData("000000000000000001")]
    [InlineData("999999999999999999")]
    public void Create_ValidCode_ShouldSucceed(string validCode)
    {
        // Act
        var result = SSCCCode.Create(validCode);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(validCode);
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("1")]
    [InlineData("12345678901234567")]
    [InlineData("1234567890123456789")]
    [InlineData("12345678901234567A")]
    [InlineData("ABCDEFGHIJKLMNOPQR")]
    public void Create_InvalidCode_ShouldThrowException(string invalidCode)
    {
        // Act & Assert
        var exception = Assert.Throws<InvalidSSCCCodeException>(() => SSCCCode.Create(invalidCode));
        exception.Message.Should().Be($"Nieprawidłowy kod SSCC: '{invalidCode}'. Wymagany format: 18 cyfr");
    }

    [Fact]
    public void TryCreate_ValidCode_ShouldReturnTrueAndCode()
    {
        // Arrange
        var validCode = "123456789012345678";

        // Act
        var result = SSCCCode.TryCreate(validCode, out var ssccCode);

        // Assert
        result.Should().BeTrue();
        ssccCode.Should().NotBeNull();
        ssccCode!.Value.Should().Be(validCode);
    }

    [Theory]
    [InlineData("")]
    [InlineData("12345678901234567")]
    [InlineData("ABCDEFGHIJKLMNOPQR")]
    public void TryCreate_InvalidCode_ShouldReturnFalseAndNull(string invalidCode)
    {
        // Act
        var result = SSCCCode.TryCreate(invalidCode, out var ssccCode);

        // Assert
        result.Should().BeFalse();
        ssccCode.Should().BeNull();
    }

    [Theory]
    [InlineData("123456789012345678", true)]
    [InlineData("000000000000000001", true)]
    [InlineData("999999999999999999", true)]
    [InlineData("", false)]
    [InlineData("12345678901234567", false)]
    [InlineData("1234567890123456789", false)]
    [InlineData("12345678901234567A", false)]
    [InlineData("ABCDEFGHIJKLMNOPQR", false)]
    [InlineData(null, false)]
    public void IsValid_ShouldReturnExpectedResult(string code, bool expected)
    {
        // Act
        var result = SSCCCode.IsValid(code);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Equals_SameCodes_ShouldReturnTrue()
    {
        // Arrange
        var code1 = SSCCCode.Create("123456789012345678");
        var code2 = SSCCCode.Create("123456789012345678");

        // Act & Assert
        code1.Equals(code2).Should().BeTrue();
        (code1 == code2).Should().BeTrue();
        (code1 != code2).Should().BeFalse();
    }

    [Fact]
    public void Equals_DifferentCodes_ShouldReturnFalse()
    {
        // Arrange
        var code1 = SSCCCode.Create("123456789012345678");
        var code2 = SSCCCode.Create("876543210987654321");

        // Act & Assert
        code1.Equals(code2).Should().BeFalse();
        (code1 == code2).Should().BeFalse();
        (code1 != code2).Should().BeTrue();
    }

    [Fact]
    public void Equals_NullComparison_ShouldReturnFalse()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act & Assert
        code.Equals(null).Should().BeFalse();
        (code == null).Should().BeFalse();
        (code != null).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_SameCodes_ShouldReturnSameHashCode()
    {
        // Arrange
        var code1 = SSCCCode.Create("123456789012345678");
        var code2 = SSCCCode.Create("123456789012345678");

        // Act & Assert
        code1.GetHashCode().Should().Be(code2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var result = code.ToString();

        // Assert
        result.Should().Be("123456789012345678");
    }

    [Fact]
    public void ImplicitConversion_FromSSCCCodeToString_ShouldWork()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        string result = code;

        // Assert
        result.Should().Be("123456789012345678");
    }

    [Fact]
    public void ExplicitConversion_FromStringToSSCCCode_ShouldWork()
    {
        // Arrange
        var codeString = "123456789012345678";

        // Act
        var result = (SSCCCode)codeString;

        // Assert
        result.Value.Should().Be(codeString);
    }

    [Fact]
    public void ExplicitConversion_FromInvalidStringToSSCCCode_ShouldThrowException()
    {
        // Arrange
        var invalidCode = "INVALID";

        // Act & Assert
        Assert.Throws<InvalidSSCCCodeException>(() => (SSCCCode)invalidCode);
    }

    [Fact]
    public void ParsedValue_ShouldReturnCorrectNumericValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(123456789012345678L);
    }

    [Fact]
    public void ParsedValue_WithLeadingZeros_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = SSCCCode.Create("000000000000000123");

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(123L);
    }

    [Theory]
    [InlineData("123456789012345678", "123456789012345678", 0)]
    [InlineData("000000000000000001", "000000000000000002", -1)]
    [InlineData("000000000000000002", "000000000000000001", 1)]
    [InlineData("999999999999999999", "000000000000000001", 1)]
    public void CompareTo_ShouldReturnExpectedResult(string code1, string code2, int expected)
    {
        // Arrange
        var ssccCode1 = SSCCCode.Create(code1);
        var ssccCode2 = SSCCCode.Create(code2);

        // Act
        var result = ssccCode1.CompareTo(ssccCode2);

        // Assert
        if (expected == 0)
            result.Should().Be(0);
        else if (expected > 0)
            result.Should().BePositive();
        else
            result.Should().BeNegative();
    }

    [Fact]
    public void CompareTo_WithNull_ShouldReturn1()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var result = code.CompareTo(null);

        // Assert
        result.Should().Be(1);
    }

    [Theory]
    [InlineData("000000000000000001", "000000000000000002")]
    [InlineData("123456789012345678", "999999999999999999")]
    public void ComparisonOperators_ShouldWorkCorrectly(string smaller, string larger)
    {
        // Arrange
        var smallerCode = SSCCCode.Create(smaller);
        var largerCode = SSCCCode.Create(larger);

        // Act & Assert
        (smallerCode < largerCode).Should().BeTrue();
        (largerCode > smallerCode).Should().BeTrue();
        (smallerCode <= largerCode).Should().BeTrue();
        (largerCode >= smallerCode).Should().BeTrue();
        (smallerCode >= largerCode).Should().BeFalse();
        (largerCode <= smallerCode).Should().BeFalse();
    }

    [Fact]
    public void ApplicationIdentifier_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var appId = code.ApplicationIdentifier;

        // Assert
        appId.Should().Be(1L);
    }

    [Fact]
    public void CompanyPrefix_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var companyPrefix = code.CompanyPrefix;

        // Assert
        companyPrefix.Should().Be(2345678L);
    }

    [Fact]
    public void SerialReference_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var serialRef = code.SerialReference;

        // Assert
        serialRef.Should().Be(901234567L);
    }

    [Fact]
    public void CheckDigit_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var checkDigit = code.CheckDigit;

        // Assert
        checkDigit.Should().Be(8);
    }

    [Theory]
    [InlineData("123456789012345670", 0)] // 0
    [InlineData("123456789012345671", 1)] // 1
    [InlineData("123456789012345672", 2)] // 2
    [InlineData("123456789012345679", 9)] // 9
    public void CheckDigit_VariousValues_ShouldReturnCorrectDigit(string code, int expectedCheckDigit)
    {
        // Arrange
        var ssccCode = SSCCCode.Create(code);

        // Act
        var checkDigit = ssccCode.CheckDigit;

        // Assert
        checkDigit.Should().Be(expectedCheckDigit);
    }

    [Fact]
    public void FormattedValue_ShouldReturnFormattedString()
    {
        // Arrange
        var code = SSCCCode.Create("123456789012345678");

        // Act
        var formatted = code.FormattedValue;

        // Assert
        formatted.Should().Be("(00) 1 2345678 901234567 8");
    }

    [Fact]
    public void FormattedValue_WithDifferentStructure_ShouldReturnCorrectFormat()
    {
        // Arrange
        var code = SSCCCode.Create("000000000000000001");

        // Act
        var formatted = code.FormattedValue;

        // Assert
        formatted.Should().Be("(00) 0 0000000 000000000 1");
    }
}
