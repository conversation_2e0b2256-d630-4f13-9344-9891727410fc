<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="xml:space" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  
  <!-- Metadata headers -->
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Komunikaty błędów GS1 -->
  <data name="GS1_InvalidFormat" xml:space="preserve">
    <value>Nieprawidłowy format kodu GS1-128</value>
  </data>
  <data name="GS1_EmptyScanData" xml:space="preserve">
    <value>Puste dane skanu</value>
  </data>
  <data name="GS1_InvalidApplicationIdentifier" xml:space="preserve">
    <value>Nieprawidłowy identyfikator aplikacji (AI): {0}</value>
  </data>
  <data name="GS1_InvalidSSCCLength" xml:space="preserve">
    <value>Nieprawidłowa długość kodu SSCC. Oczekiwano 18 cyfr, otrzymano {0}</value>
  </data>
  <data name="GS1_InvalidGTINLength" xml:space="preserve">
    <value>Nieprawidłowa długość kodu GTIN. Oczekiwano 14 cyfr, otrzymano {0}</value>
  </data>
  <data name="GS1_InvalidLotNumber" xml:space="preserve">
    <value>Nieprawidłowy numer partii: {0}</value>
  </data>
  <data name="GS1_InvalidExpiryDate" xml:space="preserve">
    <value>Nieprawidłowa data ważności: {0}. Oczekiwano format YYMMDD</value>
  </data>
  <data name="GS1_InvalidQuantity" xml:space="preserve">
    <value>Nieprawidłowa ilość: {0}. Musi być liczbą większą od zera</value>
  </data>
  <data name="GS1_MissingFNC1Separator" xml:space="preserve">
    <value>Brak separatora FNC1 w kodzie GS1-128</value>
  </data>
  <data name="GS1_ParseError" xml:space="preserve">
    <value>Błąd parsowania kodu GS1: {0}</value>
  </data>
  <data name="GS1_UnsupportedAI" xml:space="preserve">
    <value>Nieobsługiwany identyfikator aplikacji (AI): {0}</value>
  </data>
  
  <!-- Komunikaty błędów skanowania -->
  <data name="Scan_InvalidFormat" xml:space="preserve">
    <value>Nieprawidłowy format skanu</value>
  </data>
  <data name="Scan_EmptyData" xml:space="preserve">
    <value>Pusty kod skanowania</value>
  </data>
  <data name="Scan_UnknownType" xml:space="preserve">
    <value>Nierozpoznany typ kodu</value>
  </data>
  <data name="Scan_ProcessingError" xml:space="preserve">
    <value>Wystąpił błąd podczas przetwarzania skanu</value>
  </data>
  <data name="Scan_ValidationFailed" xml:space="preserve">
    <value>Walidacja skanu nieudana: {0}</value>
  </data>
  
  <!-- Komunikaty błędów dostaw -->
  <data name="Receive_NotFound" xml:space="preserve">
    <value>Nie znaleziono dostawy o ID: {0}</value>
  </data>
  <data name="Receive_NotAssigned" xml:space="preserve">
    <value>Dostawa musi być przypisana do tego pracownika</value>
  </data>
  <data name="Receive_AlreadyClaimed" xml:space="preserve">
    <value>Dostawa jest już przypisana do innego pracownika</value>
  </data>
  <data name="Receive_CannotRelease" xml:space="preserve">
    <value>Nie można zwolnić dostawy - brak uprawnień</value>
  </data>
  <data name="Receive_CarrierNotFound" xml:space="preserve">
    <value>Nośnik DS{0:D8} nie należy do tej dostawy</value>
  </data>
  <data name="Receive_ItemCreationFailed" xml:space="preserve">
    <value>Nie udało się utworzyć pozycji dostawy</value>
  </data>
  
  <!-- Komunikaty walidacji -->
  <data name="Validation_RequiredField" xml:space="preserve">
    <value>Pole {0} jest wymagane</value>
  </data>
  <data name="Validation_InvalidLength" xml:space="preserve">
    <value>Pole {0} musi mieć długość od {1} do {2} znaków</value>
  </data>
  <data name="Validation_InvalidValue" xml:space="preserve">
    <value>Nieprawidłowa wartość pola {0}</value>
  </data>
  <data name="Validation_QuantityMustBePositive" xml:space="preserve">
    <value>Ilość musi być liczbą dodatnią</value>
  </data>
  <data name="Validation_DateInPast" xml:space="preserve">
    <value>Data {0} nie może być z przeszłości</value>
  </data>
  <data name="Validation_DateTooFarInFuture" xml:space="preserve">
    <value>Data {0} jest zbyt odległa w przyszłości</value>
  </data>
  
  <!-- Komunikaty dotyczące produktów -->
  <data name="Product_NotFound" xml:space="preserve">
    <value>Nie znaleziono produktu o kodzie: {0}</value>
  </data>
  <data name="Product_ExpiredWarning" xml:space="preserve">
    <value>Produkt przeterminowany</value>
  </data>
  <data name="Product_ExpiringSoonWarning" xml:space="preserve">
    <value>Produkt wygasa za {0} dni</value>
  </data>
  <data name="Product_UnreasonableExpiryDate" xml:space="preserve">
    <value>Nieprawdopodobna data ważności</value>
  </data>
  
  <!-- Komunikaty dotyczące prefiksu IZ -->
  <data name="IZ_PrefixDetected" xml:space="preserve">
    <value>Wykryto prefiks IZ - skan w trybie rejestracji dostaw</value>
  </data>
  <data name="IZ_PrefixMissing" xml:space="preserve">
    <value>Ostrzeżenie: skan nie pochodzi z trybu dostaw (brak prefiksu IZ)</value>
  </data>
  
  <!-- Komunikaty sukcesu -->
  <data name="Success_ScanProcessed" xml:space="preserve">
    <value>Skan przetworzony pomyślnie</value>
  </data>
  <data name="Success_ReceiveClaimed" xml:space="preserve">
    <value>Dostawa została przypisana pomyślnie</value>
  </data>
  <data name="Success_ReceiveReleased" xml:space="preserve">
    <value>Dostawa została zwolniona pomyślnie</value>
  </data>
  <data name="Success_ItemCreated" xml:space="preserve">
    <value>Pozycja dostawy została utworzona pomyślnie</value>
  </data>
  
  <!-- Komunikaty informacyjne -->
  <data name="Info_DataWedgeStarted" xml:space="preserve">
    <value>DataWedge gotowy do odbierania skanów</value>
  </data>
  <data name="Info_DataWedgeStopped" xml:space="preserve">
    <value>DataWedge zatrzymany</value>
  </data>
  <data name="Info_ScanReceived" xml:space="preserve">
    <value>Otrzymano skan z urządzenia</value>
  </data>
</root>
