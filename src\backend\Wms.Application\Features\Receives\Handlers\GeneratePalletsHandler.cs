using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Exceptions;
using Wms.Domain.Services;

namespace Wms.Application.Features.Receives.Handlers;

public class GeneratePalletsHandler : IRequestHandler<GeneratePalletsCommand, GeneratePalletsResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly PalletGenerationService _palletGenerationService;
    private readonly NumberGenerationService _numberGenerationService;
    private readonly IMapper _mapper;
    private readonly ILogger<GeneratePalletsHandler> _logger;

    public GeneratePalletsHandler(
        IReceiveRepository receiveRepository,
        IPalletRepository palletRepository,
        IUnitOfWork unitOfWork,
        PalletGenerationService palletGenerationService,
        NumberGenerationService numberGenerationService,
        IMapper mapper,
        ILogger<GeneratePalletsHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _palletRepository = palletRepository;
        _unitOfWork = unitOfWork;
        _palletGenerationService = palletGenerationService;
        _numberGenerationService = numberGenerationService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<GeneratePalletsResponse> Handle(GeneratePalletsCommand request, CancellationToken cancellationToken)
    {
        // Sprawdź czy dostawa istnieje i czy jest przypisana do użytkownika
        // Używamy GetByIdAsync dla walidacji (AsNoTracking)
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
        if (receive == null)
        {
            throw new ReceiveNotFoundException(request.ListControlId);
        }

        if (!receive.IsAssigned || receive.RealizujacyPracownikId != request.PracownikId)
        {
            return new GeneratePalletsResponse
            {
                Success = false,
                Message = "Dostawa musi być przypisana do tego pracownika"
            };
        }

        try
        {
            await _unitOfWork.ExecuteInTransactionAsync(async () =>
            {
                var generatedCarriers = new List<CarrierDto>();

                for (int i = 0; i < request.Ilosc; i++)
                {
                    // Generuj nowy numer DS z docnumber
                    var paletaId = await _numberGenerationService.GetNextPalletNumberAsync(cancellationToken);
                    var dsCode = _palletGenerationService.CreateDSCodeFromNumber(paletaId);

                    // Utwórz paletę
                    var pallet = await _palletRepository.CreatePalletAsync(request.TypPaletyId, cancellationToken);
                    
                    // Utwórz powiązanie list_control_pallet
                    var listControlPallet = await _palletRepository.CreateListControlPalletAsync(
                        request.ListControlId, 
                        paletaId, 
                        request.Drukowac, 
                        cancellationToken);

                    // Mapuj na DTO
                    var carrierDto = _mapper.Map<CarrierDto>(listControlPallet);
                    generatedCarriers.Add(carrierDto);
                }

                // TODO: Jeśli request.Drukowac, wywołaj serwis drukowania
                if (request.Drukowac && !string.IsNullOrEmpty(request.DrukarkaIp))
                {
                    _logger.LogInformation("Drukowanie {Count} etykiet DS na drukarce {PrinterIp}", 
                        request.Ilosc, request.DrukarkaIp);
                    // Placeholder for printing service call
                }
            });

            _logger.LogInformation("Wygenerowano {Count} palet DS dla dostawy LK{ReceiveId} przez pracownika {UserId}", 
                request.Ilosc, request.ListControlId, request.PracownikId);

            return new GeneratePalletsResponse
            {
                Success = true,
                Message = $"Pomyślnie wygenerowano {request.Ilosc} palet DS",
                GeneratedCount = request.Ilosc
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas generowania palet dla dostawy LK{ReceiveId}", request.ListControlId);
            throw;
        }
    }
}
