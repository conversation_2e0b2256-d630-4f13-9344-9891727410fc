import { test, expect } from '@playwright/test';

test('WMS API health check', async ({ page }) => {
  // Navigate to the health endpoint
  await page.goto('/api/v1/health');

  // Check that the response contains expected health information
  await expect(page.locator('body')).toContainText('status');
});

test('WMS API login endpoint', async ({ request }) => {
  // Test the login API endpoint directly
  const response = await request.post('/api/v1/auth/login-scan', {
    data: {
      cardNumber: '1234567',
      deviceId: 'Playwright-Test'
    }
  });

  expect(response.ok()).toBeTruthy();
  expect(response.status()).toBe(200);

  const responseBody = await response.json();
  expect(responseBody).toHaveProperty('token');
  expect(responseBody).toHaveProperty('user');
});