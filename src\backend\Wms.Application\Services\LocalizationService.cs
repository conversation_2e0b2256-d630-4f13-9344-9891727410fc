using System.Globalization;
using System.Resources;
using Microsoft.Extensions.Logging;
using Wms.Application.Interfaces;

namespace Wms.Application.Services;

/// <summary>
/// Serwis lokalizacji dla obsługi polskich komunikatów błędów i informacji
/// </summary>
public class LocalizationService : ILocalizationService
{
    private readonly ResourceManager _resourceManager;
    private readonly ILogger<LocalizationService> _logger;
    private readonly CultureInfo _defaultCulture;

    public LocalizationService(ILogger<LocalizationService> logger)
    {
        _logger = logger;
        _defaultCulture = new CultureInfo("pl-PL");
        
        // Inicjalizuj ResourceManager dla pliku Strings.resx
        _resourceManager = new ResourceManager(
            "Wms.Application.Resources.Strings",
            typeof(LocalizationService).Assembly);
    }

    /// <summary>
    /// Pobiera zlokalizowany komunikat
    /// </summary>
    public string GetString(string key, CultureInfo? culture = null)
    {
        try
        {
            var targetCulture = culture ?? _defaultCulture;
            var message = _resourceManager.GetString(key, targetCulture);
            
            if (string.IsNullOrEmpty(message))
            {
                _logger.LogWarning("Nie znaleziono zasobu dla klucza: {Key} w kulturze: {Culture}", 
                    key, targetCulture.Name);
                return $"[{key}]"; // Fallback - pokaż klucz w nawiasach
            }

            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania zasobu: {Key}", key);
            return $"[{key}]";
        }
    }

    /// <summary>
    /// Pobiera zlokalizowany komunikat z formatowaniem parametrów
    /// </summary>
    public string GetString(string key, params object[] args)
    {
        return GetString(key, _defaultCulture, args);
    }

    /// <summary>
    /// Pobiera zlokalizowany komunikat z formatowaniem parametrów dla określonej kultury
    /// </summary>
    public string GetString(string key, CultureInfo? culture, params object[] args)
    {
        try
        {
            var template = GetString(key, culture);
            
            if (args == null || args.Length == 0)
                return template;

            return string.Format(template, args);
        }
        catch (FormatException ex)
        {
            _logger.LogError(ex, "Błąd formatowania komunikatu dla klucza: {Key} z argumentami: {Args}", 
                key, string.Join(", ", args));
            return GetString(key, culture); // Zwróć bez formatowania
        }
    }

    /// <summary>
    /// Sprawdza czy zasób istnieje
    /// </summary>
    public bool HasResource(string key, CultureInfo? culture = null)
    {
        try
        {
            var targetCulture = culture ?? _defaultCulture;
            var message = _resourceManager.GetString(key, targetCulture);
            return !string.IsNullOrEmpty(message);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Ustawia domyślną kulturę dla aplikacji
    /// </summary>
    public void SetDefaultCulture(CultureInfo culture)
    {
        CultureInfo.DefaultThreadCurrentCulture = culture;
        CultureInfo.DefaultThreadCurrentUICulture = culture;
        _logger.LogInformation("Ustawiono domyślną kulturę aplikacji na: {Culture}", culture.Name);
    }
}

/// <summary>
/// Klasy pomocnicze dla typowanych kluczy zasobów
/// </summary>
public static class ResourceKeys
{
    /// <summary>
    /// Klucze komunikatów GS1
    /// </summary>
    public static class GS1
    {
        public const string InvalidFormat = "GS1_InvalidFormat";
        public const string EmptyScanData = "GS1_EmptyScanData";
        public const string InvalidApplicationIdentifier = "GS1_InvalidApplicationIdentifier";
        public const string InvalidSSCCLength = "GS1_InvalidSSCCLength";
        public const string InvalidGTINLength = "GS1_InvalidGTINLength";
        public const string InvalidLotNumber = "GS1_InvalidLotNumber";
        public const string InvalidExpiryDate = "GS1_InvalidExpiryDate";
        public const string InvalidQuantity = "GS1_InvalidQuantity";
        public const string MissingFNC1Separator = "GS1_MissingFNC1Separator";
        public const string ParseError = "GS1_ParseError";
        public const string UnsupportedAI = "GS1_UnsupportedAI";
    }

    /// <summary>
    /// Klucze komunikatów skanowania
    /// </summary>
    public static class Scan
    {
        public const string InvalidFormat = "Scan_InvalidFormat";
        public const string EmptyData = "Scan_EmptyData";
        public const string UnknownType = "Scan_UnknownType";
        public const string ProcessingError = "Scan_ProcessingError";
        public const string ValidationFailed = "Scan_ValidationFailed";
    }

    /// <summary>
    /// Klucze komunikatów dostaw
    /// </summary>
    public static class Receive
    {
        public const string NotFound = "Receive_NotFound";
        public const string NotAssigned = "Receive_NotAssigned";
        public const string AlreadyClaimed = "Receive_AlreadyClaimed";
        public const string CannotRelease = "Receive_CannotRelease";
        public const string CarrierNotFound = "Receive_CarrierNotFound";
        public const string ItemCreationFailed = "Receive_ItemCreationFailed";
    }

    /// <summary>
    /// Klucze komunikatów walidacji
    /// </summary>
    public static class Validation
    {
        public const string RequiredField = "Validation_RequiredField";
        public const string InvalidLength = "Validation_InvalidLength";
        public const string InvalidValue = "Validation_InvalidValue";
        public const string QuantityMustBePositive = "Validation_QuantityMustBePositive";
        public const string DateInPast = "Validation_DateInPast";
        public const string DateTooFarInFuture = "Validation_DateTooFarInFuture";
    }

    /// <summary>
    /// Klucze komunikatów produktów
    /// </summary>
    public static class Product
    {
        public const string NotFound = "Product_NotFound";
        public const string ExpiredWarning = "Product_ExpiredWarning";
        public const string ExpiringSoonWarning = "Product_ExpiringSoonWarning";
        public const string UnreasonableExpiryDate = "Product_UnreasonableExpiryDate";
    }

    /// <summary>
    /// Klucze komunikatów prefiksu IZ
    /// </summary>
    public static class IZ
    {
        public const string PrefixDetected = "IZ_PrefixDetected";
        public const string PrefixMissing = "IZ_PrefixMissing";
    }

    /// <summary>
    /// Klucze komunikatów sukcesu
    /// </summary>
    public static class Success
    {
        public const string ScanProcessed = "Success_ScanProcessed";
        public const string ReceiveClaimed = "Success_ReceiveClaimed";
        public const string ReceiveReleased = "Success_ReceiveReleased";
        public const string ItemCreated = "Success_ItemCreated";
    }

    /// <summary>
    /// Klucze komunikatów informacyjnych
    /// </summary>
    public static class Info
    {
        public const string DataWedgeStarted = "Info_DataWedgeStarted";
        public const string DataWedgeStopped = "Info_DataWedgeStopped";
        public const string ScanReceived = "Info_ScanReceived";
    }
}
