# WMS Backend - Publish and Deploy Script
# Użycie: .\publish-and-deploy.ps1 [dev|prod]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "prod")]
    [string]$Environment = "dev",
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

$ErrorActionPreference = "Stop"

# Konfiguracja środowisk
$Config = @{
    dev = @{
        Server = "************"  # Zastąp swoim IP serwera dev
        User = "wms-dev"
        Path = "/var/www/wms-backend"
        Configuration = "Development"
    }
    prod = @{
        Server = "*************"  # Z Twojej ARCHITECTURE.md
        User = "wms-prod"
        Path = "/var/www/wms-backend"
        Configuration = "Release"
    }
}

$EnvConfig = $Config[$Environment]
$PublishPath = "./publish-$Environment"

Write-Host "🚀 Wdrażanie WMS Backend do środowiska: $Environment" -ForegroundColor Green

# 1. Czyszczenie poprzedniej publikacji
Write-Host "🧹 Czyszczenie poprzedniej publikacji..." -ForegroundColor Yellow
if (Test-Path $PublishPath) {
    Remove-Item -Path $PublishPath -Recurse -Force
}

# 2. Publikacja backendu
Write-Host "📦 Publikacja backendu (konfiguracja: $($EnvConfig.Configuration))..." -ForegroundColor Yellow
dotnet publish ./src/backend/Wms.Api `
    --configuration $EnvConfig.Configuration `
    --output $PublishPath `
    --runtime linux-x64 `
    --self-contained false `
    --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Błąd podczas publikacji!"
    exit 1
}

$PublishSize = (Get-ChildItem -Path $PublishPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "✅ Publikacja zakończona. Rozmiar: $($PublishSize.ToString('F1')) MB" -ForegroundColor Green

# 3. Wyświetl co zostanie wysłane
Write-Host "📁 Pliki do wdrożenia:" -ForegroundColor Cyan
Get-ChildItem -Path $PublishPath -Name "Wms.*" | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
Write-Host "   + $(((Get-ChildItem -Path $PublishPath).Count - 4)) dodatkowych plików" -ForegroundColor Gray

# 4. Potwierdzenie wdrożenia
if (-not $Force) {
    $Deploy = Read-Host "🤔 Wdrożyć na serwer $($EnvConfig.Server)? (y/N)"
    if ($Deploy -ne "y") {
        Write-Host "⏹️  Wdrożenie anulowane" -ForegroundColor Yellow
        exit 0
    }
} else {
    Write-Host "✅ Wdrożenie wymuszone parametrem -Force" -ForegroundColor Green
}

# 5. Wysłanie na serwer
Write-Host "🌐 Wysyłanie na serwer $($EnvConfig.Server)..." -ForegroundColor Yellow

# Backup poprzedniej wersji na serwerze
$BackupCmd = @"
mkdir -p $($EnvConfig.Path)-backup
cp -r $($EnvConfig.Path)/* $($EnvConfig.Path)-backup/ 2>/dev/null || true
"@

Write-Host "💾 Tworzenie backup poprzedniej wersji..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $BackupCmd

# Zatrzymanie usługi przeniesione później - tylko jeśli wykryto zmiany

# Porównanie plików i przesłanie tylko zmienionych
Write-Host "🔍 Porównywanie plików z serwerem..." -ForegroundColor Yellow

# Funkcja do pobierania informacji o plikach z serwera
$GetRemoteFilesCmd = @"
cd $($EnvConfig.Path) 2>/dev/null || exit 0
find . -type f -exec stat -c '%n|%s|%Y' {} \; 2>/dev/null | sed 's|^\./||' | sort
"@

# Pobierz listę plików z serwera
$RemoteFiles = @{}
try {
    $RemoteFilesList = ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $GetRemoteFilesCmd
    if ($RemoteFilesList) {
        $RemoteFilesList -split "`n" | ForEach-Object {
            if ($_ -match '^(.+)\|(\d+)\|(\d+)$') {
                $remoteRel = $matches[1] -replace '\\','/'
                $RemoteFiles[$remoteRel] = @{
                    Size = [long]$matches[2]
                    ModTime = [long]$matches[3]
                }
            }
        }
    }
    Write-Host "📋 Znaleziono $($RemoteFiles.Count) plików na serwerze" -ForegroundColor Cyan
    # Debug: pokaż kilka przykładowych pozycji z serwera
    if ($RemoteFiles.Count -gt 0) {
        ($RemoteFiles.Keys | Select-Object -First 3) | ForEach-Object {
            $rf = $RemoteFiles[$_]
            Write-Host "🔍 Debug - Remote: '$_' size=$($rf.Size) mtime=$($rf.ModTime)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "⚠️  Nie można pobrać listy plików z serwera - przesyłanie wszystkich plików" -ForegroundColor Yellow
}

# Porównaj pliki lokalne z zdalnymi
$FilesToUpload = @()
$SkippedFiles = @()
$HashCheckCandidates = @()
$TotalLocalFiles = 0

# Debug: Pokaż ścieżkę publikacji
$FullPublishPath = (Resolve-Path $PublishPath).Path
Write-Host "🔍 Debug - Ścieżka publikacji: '$FullPublishPath'" -ForegroundColor Gray

Get-ChildItem -Path $PublishPath -Recurse -File | ForEach-Object {
    $TotalLocalFiles++
    # Poprawne obliczenie ścieżki względnej
    $RelativePath = $_.FullName.Substring($FullPublishPath.Length + 1).Replace('\', '/')
    $LocalSize = $_.Length
    $LocalModTime = [long]([DateTimeOffset]$_.LastWriteTime).ToUnixTimeSeconds()

    # Debug: Pokaż pierwsze 3 pliki
    if ($TotalLocalFiles -le 3) {
        Write-Host "🔍 Debug - Plik $TotalLocalFiles`: '$($_.FullName)' -> '$RelativePath'" -ForegroundColor Gray
    }

    if ($RemoteFiles.ContainsKey($RelativePath)) {
        $RemoteFile = $RemoteFiles[$RelativePath]
        if ($RemoteFile.Size -ne $LocalSize) {
            # Rozmiar inny -> na pewno do wysłania
            $FilesToUpload += @{
                LocalPath = $_.FullName
                RelativePath = $RelativePath
                Size = $LocalSize
            }
        } elseif ($RemoteFile.ModTime -eq $LocalModTime) {
            # Rozmiar i czas modyfikacji taki sam -> pomiń
            $SkippedFiles += $RelativePath
        } else {
            # Rozmiar ten sam, ale czas inny -> sprawdzimy hash w drugim etapie
            $HashCheckCandidates += [pscustomobject]@{
                LocalPath    = $_.FullName
                RelativePath = $RelativePath
                Size         = $LocalSize
            }
        }
    } else {
        # Brak na serwerze
        $FilesToUpload += @{
            LocalPath = $_.FullName
            RelativePath = $RelativePath
            Size = $LocalSize
        }
    }
}

# Drugi etap: porównanie po hashach dla plików o tej samej wielkości, ale innej dacie modyfikacji
if ($HashCheckCandidates.Count -gt 0) {
    Write-Host "🔑 Weryfikacja zmian po SHA256 dla $($HashCheckCandidates.Count) plików..." -ForegroundColor Yellow

    # Oblicz lokalne hashe
    $LocalHashes = @{}
    foreach ($c in $HashCheckCandidates) {
        try {
            $h = (Get-FileHash -Algorithm SHA256 -LiteralPath $c.LocalPath).Hash.ToLower()
            $LocalHashes[$c.RelativePath] = $h
        } catch {
            Write-Host "⚠️  Błąd hashowania lokalnego: $($c.RelativePath) -> traktuję jako zmieniony" -ForegroundColor Yellow
            $FilesToUpload += @{
                LocalPath    = $c.LocalPath
                RelativePath = $c.RelativePath
                Size         = $c.Size
            }
        }
    }

    # Zbierz listę plików do hashowania na serwerze
    $EscapedPaths = @()
    foreach ($c in $HashCheckCandidates) {
        if (-not $LocalHashes.ContainsKey($c.RelativePath)) { continue }
        $EscapedPaths += '"' + ($c.RelativePath -replace '"', '\"') + '"'
    }

    if ($EscapedPaths.Count -gt 0) {
        $RemoteFilesArg = $EscapedPaths -join ' '
        $RemoteHashCmd = @"
cd $($EnvConfig.Path) 2>/dev/null || exit 1
for f in $RemoteFilesArg; do
  if [ -f "`$f" ]; then
    if command -v sha256sum >/dev/null 2>&1; then
      sha256sum "`$f"
    elif command -v shasum >/dev/null 2>&1; then
      shasum -a 256 "`$f"
    elif command -v openssl >/dev/null 2>&1; then
      openssl dgst -sha256 -r "`$f"
    else
      echo "NOHASH `'$f`'"
    fi
  fi
done
"@

        try {
            $RemoteHashResult = ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $RemoteHashCmd
            $RemoteHashes = @{}
            if ($RemoteHashResult) {
                $RemoteHashResult -split "`n" | ForEach-Object {
                    if ($_ -match '^([0-9a-fA-F]{64})\s+\*?(.+)$') {
                        $RemoteHashes[$matches[2]] = $matches[1].ToLower()
                    }
                }
            }

            foreach ($c in $HashCheckCandidates) {
                if (-not $LocalHashes.ContainsKey($c.RelativePath)) { continue }
                $localHash = $LocalHashes[$c.RelativePath]
                if ($RemoteHashes.ContainsKey($c.RelativePath)) {
                    if ($RemoteHashes[$c.RelativePath] -eq $localHash) {
                        $SkippedFiles += $c.RelativePath
                    } else {
                        $FilesToUpload += @{
                            LocalPath    = $c.LocalPath
                            RelativePath = $c.RelativePath
                            Size         = $c.Size
                        }
                    }
                } else {
                    # Brak pliku lub błąd pobierania hasha -> traktuj jako zmieniony
                    $FilesToUpload += @{
                        LocalPath    = $c.LocalPath
                        RelativePath = $c.RelativePath
                        Size         = $c.Size
                    }
                }
            }
        } catch {
            Write-Host "⚠️  Nie udało się obliczyć hashy na serwerze - wyślemy pliki oznaczone do weryfikacji" -ForegroundColor Yellow
            foreach ($c in $HashCheckCandidates) {
                $FilesToUpload += @{
                    LocalPath    = $c.LocalPath
                    RelativePath = $c.RelativePath
                    Size         = $c.Size
                }
            }
        }
    }
}

Write-Host "📊 Analiza plików:" -ForegroundColor Cyan
Write-Host "   📁 Plików lokalnych: $TotalLocalFiles" -ForegroundColor White
Write-Host "   📤 Do przesłania: $($FilesToUpload.Count)" -ForegroundColor Green
Write-Host "   ⏭️  Pominięto (bez zmian): $($SkippedFiles.Count)" -ForegroundColor Gray

if ($FilesToUpload.Count -eq 0) {
    Write-Host "✅ Wszystkie pliki są aktualne - brak zmian do przesłania!" -ForegroundColor Green
    Write-Host "🔄 Sprawdzanie czy aplikacja działa..." -ForegroundColor Yellow
    
    # Sprawdź status aplikacji
    $StatusCmd = @"
if systemctl is-active --quiet wms-api; then
  echo "✅ WMS API już działa"
  curl -s http://127.0.0.1:5000/health 2>/dev/null | head -3 || echo "API nie odpowiada lokalnie"
else
  echo "❌ WMS API nie działa - wymagany restart"
  exit 1
fi
"@
    
    try {
        ssh "root@$($EnvConfig.Server)" $StatusCmd
        Write-Host "✅ Aplikacja działa poprawnie - wdrożenie zakończone!" -ForegroundColor Green
        Remove-Item -Path $PublishPath -Recurse -Force
        exit 0
    } catch {
        Write-Host "⚠️  Aplikacja wymaga restartu mimo braku zmian w plikach" -ForegroundColor Yellow
    }
}

# Zatrzymanie usługi tylko jeśli mamy zmiany do wdrożenia
Write-Host "⏸️  Zatrzymywanie aplikacji..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" "pkill -f 'Wms.Api' || true"

# Przesyłanie tylko zmienionych plików
Write-Host "📤 Przesyłanie $($FilesToUpload.Count) zmienionych plików..." -ForegroundColor Yellow

# Utwórz katalogi na serwerze
$DirsToCreate = $FilesToUpload | ForEach-Object { 
    (Split-Path $_.RelativePath -Parent) -replace '\\','/'
} | Where-Object { $_ -and $_ -ne '.' } | Sort-Object -Unique

if ($DirsToCreate) {
    $CreateDirsCmd = "cd $($EnvConfig.Path) && mkdir -p " + ($DirsToCreate -join ' ')
    ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $CreateDirsCmd
}

# Przesyłaj pliki pojedynczo z informacją o postępie
$UploadedSize = 0
$FilesToUpload | ForEach-Object {
    $Progress = [math]::Round(($FilesToUpload.IndexOf($_) + 1) / $FilesToUpload.Count * 100)
    $SizeMB = $_.Size / 1MB
    Write-Host "   [$Progress%] $($_.RelativePath) ($($SizeMB.ToString('F1')) MB)" -ForegroundColor White
    
    scp -p "$($_.LocalPath)" "$($EnvConfig.User)@$($EnvConfig.Server):$($EnvConfig.Path)/$($_.RelativePath)"
    $UploadedSize += $_.Size
}

$UploadedSizeMB = $UploadedSize / 1MB
Write-Host "✅ Przesłano $($FilesToUpload.Count) plików ($($UploadedSizeMB.ToString('F1')) MB)" -ForegroundColor Green

# Ustawienie uprawnień przez wms-dev
$SetPermissionsCmd = @"
cd $($EnvConfig.Path)
# Sprawdzenie czy plik wykonywalny istnieje
if [ -f "Wms.Api" ]; then
  chmod +x Wms.Api
  echo "✅ Plik wykonywalny Wms.Api znaleziony i oznaczony jako wykonywalny"
else
  echo "❌ Nie znaleziono pliku wykonywalnego Wms.Api"
  ls -la | grep Wms
  exit 1
fi
"@

Write-Host "🔄 Ustawianie uprawnień..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $SetPermissionsCmd

# Restart usługi systemd przez root
$SystemdCmd = @"
# Restart usługi systemd
echo "🔄 Restartowanie usługi WMS API przez systemd..."
systemctl restart wms-api
sleep 3

# Sprawdzenie statusu usługi
if systemctl is-active --quiet wms-api; then
  echo "✅ WMS API uruchomione pomyślnie przez systemd"
  echo "📋 Status usługi:"
  systemctl status wms-api --no-pager -l | head -10
  echo "🌐 Sprawdzenie portu 5000:"
  ss -tlnp | grep :5000 || echo "Port 5000 nie nasłuchuje"
  echo "🧪 Test lokalnego API:"
  curl -s http://127.0.0.1:5000/health 2>/dev/null | head -3 || echo "API nie odpowiada lokalnie"
else
  echo "❌ Błąd uruchamiania usługi WMS API"
  echo "📋 Logi systemd:"
  journalctl -u wms-api -n 20 --no-pager || echo "Brak dostępu do logów systemd"
  exit 1
fi
"@

Write-Host "🔑 Restart usługi przez root (potwierdz haslem):" -ForegroundColor Yellow
ssh "root@$($EnvConfig.Server)" $SystemdCmd

Write-Host "✅ Wdrożenie zakończone!" -ForegroundColor Green
Write-Host "🌐 API dostępne pod: https://$($EnvConfig.Server)/api/v1" -ForegroundColor Cyan

# Cleanup lokalnego publish
Remove-Item -Path $PublishPath -Recurse -Force
Write-Host "🧹 Lokalne pliki publikacji usunięte" -ForegroundColor Gray
