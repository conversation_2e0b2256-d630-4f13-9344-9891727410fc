# XAML Binding Optimization - Ściągawka 🚀

## ⚡ <PERSON><PERSON><PERSON>ki Start

### 1. Konfiguracja projektu (jednokrotnie)
```xml
<!-- W WmsApp.csproj -->
<MauiEnableXamlCBindingWithSourceCompilation>true</MauiEnableXamlCBindingWithSourceCompilation>
```

### 2. <PERSON><PERSON><PERSON> dodawaj x:DataType

```xml
<!-- ✅ Strona -->
<ContentPage x:DataType="viewmodels:MyViewModel">
    <Label Text="{Binding MyProperty}" />
</ContentPage>

<!-- ✅ DataTemplate -->
<DataTemplate x:DataType="models:MyModel">
    <Label Text="{Binding MyProperty}" />
</DataTemplate>
```

## 🔧 Najczęstsze przypadki

### Picker z właściwościami
```xml
<!-- ❌ STARY SPOSÓB -->
<Picker ItemDisplayBinding="{Binding Name}" />

<!-- ✅ NOWY SPOSÓB -->
<Picker ItemsSource="{Binding Items}">
    <Picker.ItemDisplayBinding>
        <Binding Path="Name" x:DataType="models:MyItem" />
    </Picker.ItemDisplayBinding>
</Picker>
```

### Collections ze string
```xml
<CollectionView ItemsSource="{Binding StringList}">
    <CollectionView.ItemTemplate>
        <DataTemplate x:DataType="x:String">
            <Label Text="{Binding}" />
        </DataTemplate>
    </CollectionView.ItemTemplate>
</CollectionView>
```

### Enums z konwerterami  
```xml
<Picker ItemsSource="{Binding Environments}">
    <Picker.ItemDisplayBinding>
        <Binding Path="." 
                 Converter="{StaticResource EnumConverter}" 
                 x:DataType="{x:Type models:MyEnum}" />
    </Picker.ItemDisplayBinding>
</Picker>
```

### RelativeSource bindings
```xml
<!-- Działa automatycznie z MauiEnableXamlCBindingWithSourceCompilation=true -->
<DataTemplate x:DataType="models:Item">
    <Grid>
        <Grid.GestureRecognizers>
            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MyViewModel}}, Path=SelectCommand}"
                                  CommandParameter="{Binding .}" />
        </Grid.GestureRecognizers>
    </Grid>
</DataTemplate>
```

## 🔍 Weryfikacja

### Build check
```bash
dotnet build --verbosity normal 2>&1 | grep -E "XC0022|XC0025"
```

**Oczekiwany wynik:** Brak output'u (0 ostrzeżeń)

### Ostrzeżenia do wyeliminowania
- **XC0022:** `Binding could be compiled to improve runtime performance` → Dodaj `x:DataType`
- **XC0025:** `Binding was not compiled because it has an explicitly set Source property` → Dodaj konfigurację projektu

## 📝 Checklist nowych plików XAML

- [ ] `x:DataType` na poziomie strony/UserControl  
- [ ] `x:DataType` dla wszystkich DataTemplate
- [ ] Namespace imports dla modeli
- [ ] Build bez ostrzeżeń XC0022/XC0025
- [ ] Test na urządzeniu/emulatorze

---

💡 **Pro tip:** Zawsze dodawaj `x:DataType` przed pisaniem bindingów - oszczędzisz czas na debugowaniu!
