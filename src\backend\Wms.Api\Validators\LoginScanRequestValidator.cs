using FluentValidation;
using Wms.Application.DTOs.Auth;

namespace Wms.Api.Validators;

public class LoginScanRequestValidator : AbstractValidator<LoginScanRequest>
{
    public LoginScanRequestValidator()
    {
        RuleFor(x => x.CardNumber)
            .NotEmpty()
            .WithMessage("Card number is required")
            .MinimumLength(4)
            .WithMessage("Card number must be at least 4 characters")
            .MaximumLength(45)
            .WithMessage("Card number cannot exceed 45 characters");

        RuleFor(x => x.DeviceId)
            .MaximumLength(100)
            .WithMessage("Device ID cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.DeviceId));
    }
}
