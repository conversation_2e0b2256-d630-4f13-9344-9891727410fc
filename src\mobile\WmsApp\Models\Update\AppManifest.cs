using System.Text.Json.Serialization;

namespace WmsApp.Models.Update;

/// <summary>
/// Model reprezentujący manifest aplikacji pobierany z serwera
/// </summary>
public record AppManifest
{
    /// <summary>
    /// Wersja aplik<PERSON>ji (np. "1.0.5")
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; init; } = string.Empty;

    /// <summary>
    /// Kod wersji (build number)
    /// </summary>
    [JsonPropertyName("versionCode")]
    public int VersionCode { get; init; }

    /// <summary>
    /// URL do pobrania APK
    /// </summary>
    [JsonPropertyName("downloadUrl")]
    public string DownloadUrl { get; init; } = string.Empty;

    /// <summary>
    /// SHA-256 hash pliku APK dla weryfikacji
    /// </summary>
    [JsonPropertyName("sha256")]
    public string Sha256 { get; init; } = string.Empty;

    /// <summary>
    /// Rozmiar pliku APK w bajtach
    /// </summary>
    [JsonPropertyName("fileSize")]
    public long FileSize { get; init; }

    /// <summary>
    /// Opis zmian w tej wersji
    /// </summary>
    [JsonPropertyName("releaseNotes")]
    public string ReleaseNotes { get; init; } = string.Empty;

    /// <summary>
    /// Czy aktualizacja jest wymagana (blokująca)
    /// </summary>
    [JsonPropertyName("required")]
    public bool Required { get; init; }

    /// <summary>
    /// Minimalna wersja Android wymagana dla tej aplikacji
    /// </summary>
    [JsonPropertyName("minSdkVersion")]
    public int MinSdkVersion { get; init; } = 29; // Android 10

    /// <summary>
    /// Data wydania w formacie ISO 8601
    /// </summary>
    [JsonPropertyName("releaseDate")]
    public string ReleaseDate { get; init; } = string.Empty;
}

/// <summary>
/// Model reprezentujący informacje o dostępnej aktualizacji
/// </summary>
public record UpdateInfo
{
    /// <summary>
    /// Czy aktualizacja jest dostępna
    /// </summary>
    public bool IsAvailable { get; init; }

    /// <summary>
    /// Aktualna wersja aplikacji
    /// </summary>
    public string CurrentVersion { get; init; } = string.Empty;

    /// <summary>
    /// Nowa dostępna wersja
    /// </summary>
    public string NewVersion { get; init; } = string.Empty;

    /// <summary>
    /// Czy aktualizacja jest wymagana
    /// </summary>
    public bool IsRequired { get; init; }

    /// <summary>
    /// Manifest z szczegółowymi informacjami o aktualizacji
    /// </summary>
    public AppManifest? Manifest { get; init; }

    /// <summary>
    /// Komunikat o błędzie (jeśli wystąpił)
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// Model reprezentujący postęp pobierania
/// </summary>
public record DownloadProgress
{
    /// <summary>
    /// Liczba pobranych bajtów
    /// </summary>
    public long BytesDownloaded { get; init; }

    /// <summary>
    /// Całkowity rozmiar pliku
    /// </summary>
    public long TotalBytes { get; init; }

    /// <summary>
    /// Procent ukończenia (0-100)
    /// </summary>
    public int PercentageComplete => TotalBytes > 0 ? (int)((BytesDownloaded * 100) / TotalBytes) : 0;

    /// <summary>
    /// Status pobierania
    /// </summary>
    public DownloadStatus Status { get; init; }

    /// <summary>
    /// Komunikat o błędzie (jeśli wystąpił)
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// Status pobierania APK
/// </summary>
public enum DownloadStatus
{
    NotStarted,
    InProgress,
    Completed,
    Failed,
    Cancelled
}
