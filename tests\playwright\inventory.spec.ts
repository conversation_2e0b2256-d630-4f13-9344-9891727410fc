import { test, expect } from '@playwright/test';

test.describe('WMS Inventory API Tests', () => {
  let authToken: string;

  // Setup: Login and get authentication token
  test.beforeEach(async ({ request }) => {
    const loginResponse = await request.post('/api/v1/auth/login-scan', {
      data: {
        cardNumber: '1234567',
        deviceId: 'Playwright-Inventory-Test'
      }
    });

    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.token;
    expect(authToken).toBeDefined();
  });

  test('should get inventory sessions', async ({ request }) => {
    const response = await request.get('/api/v1/inventory/sessions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();

    if (response.status() === 200) {
      const sessions = await response.json();
      console.log(`Found ${sessions.length} inventory sessions`);

      // Check if we have at least some sessions
      expect(Array.isArray(sessions)).toBeTruthy();

      if (sessions.length > 0) {
        const firstSession = sessions[0];
        expect(firstSession).toHaveProperty('id');
        expect(firstSession).toHaveProperty('data');
        expect(firstSession).toHaveProperty('opis');
      }
    }
  });

  test('should scan SSCC code for inventory', async ({ request }) => {
    // First get an inventory session
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (sessionsResponse.ok()) {
      const sessions = await sessionsResponse.json();

      if (sessions && sessions.length > 0) {
        const sessionId = sessions[0].id;

        // Test SSCC scan
        const scanResponse = await request.post('/api/v1/inventory/scan', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            sessionId: sessionId,
            scanData: '123456789012345678', // Test SSCC
            deviceId: 'Playwright-Test'
          }
        });

        expect(scanResponse.ok()).toBeTruthy();

        if (scanResponse.status() === 200) {
          const scanResult = await scanResponse.json();
          console.log('SSCC scan result:', scanResult);

          expect(scanResult).toHaveProperty('success');
          expect(scanResult).toHaveProperty('message');
        }
      }
    }
  });

  test('should scan DS code for inventory', async ({ request }) => {
    // First get an inventory session
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (sessionsResponse.ok()) {
      const sessions = await sessionsResponse.json();

      if (sessions && sessions.length > 0) {
        const sessionId = sessions[0].id;

        // Test DS code scan
        const scanResponse = await request.post('/api/v1/inventory/scan', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            sessionId: sessionId,
            scanData: 'DS123456', // Test DS code
            deviceId: 'Playwright-Test'
          }
        });

        expect(scanResponse.ok()).toBeTruthy();

        if (scanResponse.status() === 200) {
          const scanResult = await scanResponse.json();
          console.log('DS code scan result:', scanResult);

          expect(scanResult).toHaveProperty('success');
          expect(scanResult).toHaveProperty('message');
        }
      }
    }
  });

  test('should handle invalid scan codes', async ({ request }) => {
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (sessionsResponse.ok()) {
      const sessions = await sessionsResponse.json();

      if (sessions && sessions.length > 0) {
        const sessionId = sessions[0].id;

        // Test invalid scan code
        const scanResponse = await request.post('/api/v1/inventory/scan', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            sessionId: sessionId,
            scanData: 'INVALID_CODE', // Invalid code
            deviceId: 'Playwright-Test'
          }
        });

        // Should return error or specific response for invalid codes
        expect([200, 400, 404]).toContain(scanResponse.status());

        if (scanResponse.ok()) {
          const scanResult = await scanResponse.json();
          console.log('Invalid code scan result:', scanResult);

          // Should indicate error or no results
          expect(scanResult).toHaveProperty('success');
          if (scanResult.success === false) {
            expect(scanResult).toHaveProperty('message');
          }
        }
      }
    }
  });

  test('should test inventory workflow simulation', async ({ request }) => {
    console.log('🔍 Starting inventory workflow simulation...');

    // 1. Get available inventory sessions
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    expect(sessionsResponse.ok()).toBeTruthy();
    const sessions = await sessionsResponse.json();
    console.log(`📋 Found ${sessions.length} inventory sessions`);

    if (sessions.length > 0) {
      const sessionId = sessions[0].id;
      console.log(`🎯 Using session ID: ${sessionId}`);

      // 2. Simulate scanning multiple items
      const testCodes = [
        '123456789012345678', // SSCC
        'DS123456',           // DS code
        'DS789012'            // Another DS code
      ];

      for (const code of testCodes) {
        console.log(`📱 Scanning code: ${code}`);

        const scanResponse = await request.post('/api/v1/inventory/scan', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            sessionId: sessionId,
            scanData: code,
            deviceId: 'Playwright-Workflow-Test'
          }
        });

        console.log(`✅ Scan response status: ${scanResponse.status()}`);

        if (scanResponse.ok()) {
          const result = await scanResponse.json();
          console.log(`📊 Scan result: ${result.message || 'Success'}`);
        }
      }

      console.log('🎉 Inventory workflow simulation completed');
    }
  });
});