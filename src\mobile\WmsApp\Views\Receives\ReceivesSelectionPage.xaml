<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.Receives.ReceivesSelectionPage"
             x:Name="ReceivesSelectionPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Receives"
             xmlns:models="clr-namespace:WmsApp.Models.Receives"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:ReceivesSelectionViewModel"
             Title="{loc:Translate Key=ReceivesSelection_Title}"
             Shell.NavBarIsVisible="True">

    <Grid>
        <!-- Main Content -->
        <ScrollView>
        <StackLayout Padding="20" Spacing="15">
            
            <!-- Header -->
            <Frame BackgroundColor="{StaticResource Primary}" HasShadow="True" CornerRadius="8">
                <StackLayout>
<Label Text="{loc:Translate Key=ReceivesSelection_HeaderMain}" 
                           TextColor="White" 
                           FontSize="20" 
                           FontAttributes="Bold" 
                           HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=ReceivesSelection_HeaderSubtitle}"
                           TextColor="White"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </StackLayout>
            </Frame>

            <!-- LK Input Section -->
            <Frame BackgroundColor="White" HasShadow="True" CornerRadius="8">
                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=ReceivesSelection_LK_Label}" FontSize="16" FontAttributes="Bold" />
                    
                    <Entry x:Name="LkEntry"
                           Text="{Binding LkInput}"
Placeholder="{loc:Translate Key=ReceivesSelection_LK_Placeholder}"
                           FontSize="16"
                           HeightRequest="50"
                           BackgroundColor="LightGray" />
                    
<Button Text="{loc:Translate Key=ReceivesSelection_LK_Confirm_Button}"
                            Command="{Binding ValidateLKCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White"
                            FontSize="16"
                            HeightRequest="50" />
                </StackLayout>
            </Frame>

            <!-- Lista Dostaw Section -->
            <Frame BackgroundColor="White" HasShadow="True" CornerRadius="8">
                <StackLayout Spacing="10">
                    <Grid ColumnDefinitions="*,Auto">
<Label Text="{loc:Translate Key=ReceivesSelection_List_Label}" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               Grid.Column="0" />
<Button Text="{loc:Translate Key=ReceivesSelection_Refresh_Button}" 
                                Command="{Binding LoadReceivesCommand}"
                                BackgroundColor="{StaticResource Secondary}"
                                TextColor="White"
                                FontSize="12"
                                Padding="15,5"
                                Grid.Column="1" />
                    </Grid>

                    <CollectionView ItemsSource="{Binding AvailableReceives}"
                                    HeightRequest="200"
                                    BackgroundColor="LightGray">
                        <CollectionView.ItemTemplate>
<DataTemplate x:DataType="models:ReceiveDto">
                                <Grid Padding="10" ColumnDefinitions="*,Auto" BackgroundColor="White" Margin="0,1">
                                    <StackLayout Grid.Column="0" Spacing="2">
                                        <Label Text="{Binding LK}" FontAttributes="Bold" FontSize="14" />
                                        <Label Text="{Binding NumerZamowienia}" FontSize="12" />
                                        <Label Text="{Binding SystemNazwa}" FontSize="12" TextColor="Gray" />
                                        <Label Text="{Binding MiejsceDostawy}" FontSize="11" TextColor="Gray" />
                                        <Label Text="{Binding Data, StringFormat='{0:dd.MM.yyyy}'}" FontSize="11" TextColor="DarkBlue" />

                                        <!-- Informacja o dokumencie dostawy -->
                                        <Label Text="{Binding DokumentDostawy, StringFormat='Dokument: {0}'}"
                                               FontSize="10"
                                               TextColor="DarkBlue"
                                               IsVisible="{Binding DokumentDostawy, Converter={StaticResource StringNotNullOrEmptyConverter}}" />

                                        <!-- Informacja o awizacji -->
                                        <Label Text="{Binding AwizacjeId, StringFormat='Awizacja: {0}'}"
                                               FontSize="10"
                                               TextColor="Green"
                                               IsVisible="{Binding MaAwizacje}" />

                                        <!-- Informacja o pracowniku realizującym -->
                                        <Label FontSize="10" TextColor="DarkGreen">
                                            <Label.Text>
                                                <MultiBinding StringFormat="{}Realizuje: {0}">
                                                    <Binding Path="RealizujacyPracownikImieNazwisko" TargetNullValue="Nieprzypisany" />
                                                </MultiBinding>
                                            </Label.Text>
                                        </Label>
                                    </StackLayout>

                                    <!-- Przycisk Realizuj lub informacja o statusie -->
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Button Text="Realizuj"
                                                Clicked="OnRealizujClicked"
                                                BackgroundColor="{StaticResource Primary}"
                                                TextColor="White"
                                                FontSize="10"
                                                Padding="10,5"
                                                IsVisible="{Binding IsOccupied, Converter={StaticResource InverseBoolConverter}}" />

                                        <Label Text="Zajęta"
                                               FontSize="10"
                                               TextColor="Red"
                                               VerticalOptions="Center"
                                               HorizontalOptions="Center"
                                               IsVisible="{Binding IsOccupied}" />
                                    </StackLayout>
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </StackLayout>
            </Frame>

            <!-- Akcje -->
            <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
<Button Text="{loc:Translate Key=ReceivesSelection_GenerateDS_Button}" 
                        Command="{Binding ShowGenerateDsModalCommand}"
                        BackgroundColor="{StaticResource Tertiary}"
                        TextColor="White"
                        FontSize="14"
                        HeightRequest="50"
                        Grid.Column="0" />
                
<Button Text="{loc:Translate Key=ReceivesSelection_ListReceives_Button}"
                        Command="{Binding LoadReceivesCommand}"
                        BackgroundColor="{StaticResource Secondary}"
                        TextColor="White"
                        FontSize="14"
                        HeightRequest="50"
                        Grid.Column="1" />
            </Grid>

            <!-- Error/Success Messages -->
            <Label Text="{Binding ErrorMessage}" 
                   TextColor="Red" 
                   FontSize="14" 
                   IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="LightPink"
                   Padding="10" />
                   
            <Label Text="{Binding SuccessMessage}" 
                   TextColor="Green" 
                   FontSize="14" 
                   IsVisible="{Binding SuccessMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="LightGreen"
                   Padding="10" />

            <!-- Loading Indicator -->
            <ActivityIndicator IsVisible="{Binding IsLoading}" 
                               IsRunning="{Binding IsLoading}" 
                               Color="{StaticResource Primary}"
                               HeightRequest="50" />

            <!-- Generated DS Display -->
            <Frame IsVisible="{Binding IsGeneratedDsVisible}" 
                   BackgroundColor="LightGreen" 
                   HasShadow="True" 
                   CornerRadius="8">
                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=ReceivesSelection_GeneratedDS_Header}" FontAttributes="Bold" />
                    <CollectionView ItemsSource="{Binding GeneratedDsCodes}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <Label Text="{Binding}" 
                                       FontFamily="Courier" 
                                       FontSize="14" 
                                       Padding="5" 
                                       BackgroundColor="White" 
                                       Margin="0,1" />
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
<Button Text="{loc:Translate Key=ReceivesSelection_ProceedToRegistration_Button}" 
                            Command="{Binding ProceedToRegistrationCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White" />
                </StackLayout>
            </Frame>

        </StackLayout>
        </ScrollView>

        <!-- Modal Overlay -->
        <Grid IsVisible="{Binding IsGenerateDsModalVisible}"
          BackgroundColor="#80000000" 
          VerticalOptions="Fill" 
          HorizontalOptions="Fill">
        <Frame BackgroundColor="White" 
               HasShadow="True" 
               CornerRadius="10" 
               Margin="20"
               VerticalOptions="Center"
               HorizontalOptions="Fill">
            <StackLayout Spacing="15">
<Label Text="{loc:Translate Key=Receives_GenerateDS_Title}" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" />

                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=Receives_GenerateDS_TypPalety_Label}" FontSize="14" />
                    <Picker ItemsSource="{Binding TypyPalet}">
                        <Picker.ItemDisplayBinding>
                            <Binding Path="Nazwa" x:DataType="models:TypPaletyDto" />
                        </Picker.ItemDisplayBinding>
                        <Picker.SelectedItem>
                            <Binding Path="SelectedTypPaletyId" Converter="{StaticResource IdToTypPaletyConverter}" />
                        </Picker.SelectedItem>
                    </Picker>
                </StackLayout>

                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=Receives_GenerateDS_Count_Label}" FontSize="14" />
                    <Entry Text="{Binding IloscPalet}" 
                           Keyboard="Numeric"
                           FontSize="14"
                           BackgroundColor="LightGray" />
                </StackLayout>

                <StackLayout Spacing="10">
<Label Text="{loc:Translate Key=Receives_GenerateDS_Printer_Label}" FontSize="14" />
                    <Grid ColumnDefinitions="*,Auto">
                        <Entry Text="{Binding SelectedDrukarkaNazwa}" 
                               Placeholder="Zeskanuj IP drukarki lub wybierz"
                               FontSize="14"
                               BackgroundColor="LightGray"
                               Grid.Column="0" />
<Picker ItemsSource="{Binding Drukarki}"
                                SelectedItem="{Binding SelectedDrukarkaNazwa}"
                                Title="{loc:Translate Key=Common_Select}"
                                FontSize="12"
                                Grid.Column="1" />
                    </Grid>
                    <CheckBox IsChecked="{Binding CzyDrukowac}" />
<Label Text="{loc:Translate Key=Receives_GenerateDS_Print_Checkbox_Label}" FontSize="12" />
                </StackLayout>

                <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
<Button Text="{loc:Translate Key=Common_Cancel}" 
                            Command="{Binding CloseGenerateDsModalCommand}"
                            BackgroundColor="Gray"
                            TextColor="White"
                            Grid.Column="0" />
<Button Text="{loc:Translate Key=Common_Generate}" 
                            Command="{Binding GenerateDsCommand}"
                            BackgroundColor="{StaticResource Primary}"
                            TextColor="White"
                            Grid.Column="1" />
                </Grid>
            </StackLayout>
        </Frame>
        </Grid>
    </Grid>

</ContentPage>
