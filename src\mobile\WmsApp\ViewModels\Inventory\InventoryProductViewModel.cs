using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WmsApp.Models.Inventory;
using WmsApp.Services.Inventory;

namespace WmsApp.ViewModels.Inventory;

/// <summary>
/// ViewModel dla inwentaryzacji produktowej
/// </summary>
public partial class InventoryProductViewModel : ObservableObject
{
    private readonly IInventoryService _inventoryService;
    private readonly ILogger<InventoryProductViewModel> _logger;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private ObservableCollection<InventoryDto> activeInventories = new();

    [ObservableProperty]
    private InventoryDto? selectedInventory;

    [ObservableProperty]
    private InventorySessionDto? currentSession;

    [ObservableProperty]
    private string scanInput = string.Empty;

    [ObservableProperty]
    private bool isSessionActive;

    public InventoryProductViewModel(
        IInventoryService inventoryService,
        ILogger<InventoryProductViewModel> logger)
    {
        _inventoryService = inventoryService;
        _logger = logger;
    }

    /// <summary>
    /// Inicjalizacja ViewModelu
    /// </summary>
    [RelayCommand]
    private async Task InitializeAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            _logger.LogInformation("Inicjalizacja inwentaryzacji produktowej");

            await LoadActiveInventoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas inicjalizacji inwentaryzacji produktowej");
            ErrorMessage = "Błąd podczas ładowania inwentaryzacji";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// Ładowanie aktywnych inwentaryzacji produktowych
    /// </summary>
    private async Task LoadActiveInventoriesAsync()
    {
        try
        {
            var inventories = await _inventoryService.GetActiveInventoriesAsync(InventoryType.Product);
            
            ActiveInventories.Clear();
            foreach (var inventory in inventories)
            {
                ActiveInventories.Add(inventory);
            }

            _logger.LogInformation("Załadowano {Count} aktywnych inwentaryzacji produktowych", inventories.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania aktywnych inwentaryzacji");
            throw;
        }
    }

    /// <summary>
    /// Wybór inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task SelectInventoryAsync(InventoryDto inventory)
    {
        try
        {
            _logger.LogInformation("Wybrano inwentaryzację {InventoryId}: {Opis}", inventory.InwentaryzacjaId, inventory.Opis);
            
            SelectedInventory = inventory;
            
            // Rozpocznij sesję inwentaryzacji
            await StartInventorySessionAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru inwentaryzacji");
            ErrorMessage = "Błąd podczas wyboru inwentaryzacji";
        }
    }

    /// <summary>
    /// Rozpoczęcie sesji inwentaryzacji
    /// </summary>
    private async Task StartInventorySessionAsync()
    {
        try
        {
            if (SelectedInventory == null)
            {
                ErrorMessage = "Nie wybrano inwentaryzacji";
                return;
            }

            _logger.LogInformation("Rozpoczynanie sesji inwentaryzacji {InventoryId}", SelectedInventory.InwentaryzacjaId);

            var deviceId = DeviceInfo.Name ?? "Unknown";
            var session = await _inventoryService.StartInventorySessionAsync(SelectedInventory.InwentaryzacjaId, deviceId);
            CurrentSession = session;
            IsSessionActive = true;

            // Wyczyść ewentualne komunikaty o błędach
            ErrorMessage = string.Empty;

            _logger.LogInformation("Rozpoczęto sesję inwentaryzacji {SessionId}", CurrentSession.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas rozpoczynania sesji inwentaryzacji");
            ErrorMessage = ex.Message ?? "Błąd podczas rozpoczynania sesji inwentaryzacji";
        }
    }

    partial void OnSelectedInventoryChanged(InventoryDto? value)
    {
        if (value is not null)
        {
            // uruchom uruchomienie sesji gdy użytkownik wybierze element na liście
            _ = StartInventorySessionAsync();
        }
    }

    /// <summary>
    /// Przetwarzanie skanu
    /// </summary>
    [RelayCommand]
    private async Task ProcessScanAsync()
    {
        try
        {
            if (CurrentSession == null || !IsSessionActive)
            {
                ErrorMessage = "Brak aktywnej sesji inwentaryzacji";
                return;
            }

            if (string.IsNullOrWhiteSpace(ScanInput))
            {
                ErrorMessage = "Wprowadź kod do skanowania";
                return;
            }

            _logger.LogInformation("Przetwarzanie skanu: {ScanData}", ScanInput);

            var deviceId = DeviceInfo.Name ?? "Unknown";
            var result = await _inventoryService.ProcessScanAsync(CurrentSession.Id, ScanInput.Trim(), deviceId);

            if (result.IsSuccess)
            {
                await Shell.Current.DisplayAlert("Sukces", result.Message, "OK");
                ScanInput = string.Empty; // Wyczyść pole po udanym skanie
            }
            else
            {
                ErrorMessage = result.Message;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanu");
            ErrorMessage = "Błąd podczas przetwarzania skanu";
        }
    }

    /// <summary>
    /// Zakończenie sesji inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task EndSessionAsync()
    {
        try
        {
            if (CurrentSession == null)
            {
                return;
            }

            var result = await Shell.Current.DisplayAlert("Potwierdzenie", 
                "Czy na pewno chcesz zakończyć sesję inwentaryzacji?", "Tak", "Nie");

            if (!result)
            {
                return;
            }

            _logger.LogInformation("Kończenie sesji inwentaryzacji {SessionId}", CurrentSession.Id);

            await _inventoryService.EndInventorySessionAsync(CurrentSession.Id);
            
            CurrentSession = null;
            IsSessionActive = false;
            SelectedInventory = null;
            ScanInput = string.Empty;
            ErrorMessage = string.Empty;

            _logger.LogInformation("Zakończono sesję inwentaryzacji");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas kończenia sesji inwentaryzacji");
            ErrorMessage = "Błąd podczas kończenia sesji inwentaryzacji";
        }
    }

    /// <summary>
    /// Odświeżenie listy inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await LoadActiveInventoriesAsync();
    }

    /// <summary>
    /// Powrót do wyboru typu inwentaryzacji
    /// </summary>
    [RelayCommand]
    private async Task GoBackAsync()
    {
        try
        {
            // Jeśli jest aktywna sesja, zapytaj o potwierdzenie
            if (IsSessionActive)
            {
                var result = await Shell.Current.DisplayAlert("Potwierdzenie", 
                    "Masz aktywną sesję inwentaryzacji. Czy na pewno chcesz wyjść?", "Tak", "Nie");

                if (!result)
                {
                    return;
                }

                // Zakończ sesję przed wyjściem
                if (CurrentSession != null)
                {
                    await _inventoryService.EndInventorySessionAsync(CurrentSession.Id);
                }
            }

            await Shell.Current.GoToAsync("//inventoryselection");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas powrotu do wyboru inwentaryzacji");
        }
    }
}
