using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Features.Pallets.Queries;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.Pallets.Handlers;

public class GetTypyPaletHandler : IRequestHandler<GetTypyPaletQuery, List<TypPaletyLookupDto>>
{
    private readonly IPalletRepository _palletRepository;
    private readonly ILogger<GetTypyPaletHandler> _logger;

    public GetTypyPaletHandler(IPalletRepository palletRepository, ILogger<GetTypyPaletHandler> logger)
    {
        _palletRepository = palletRepository;
        _logger = logger;
    }

    public async Task<List<TypPaletyLookupDto>> Handle(GetTypyPaletQuery request, CancellationToken cancellationToken)
    {
        var types = await _palletRepository.GetTypyPaletAsync(cancellationToken);
        var result = types.Select(t => new TypPaletyLookupDto
        {
            Id = t.Id,
            Nazwa = t.GetDisplayName(),
            Opis = t.Opis
        }).ToList();

        _logger.LogDebug("Pobrano {Count} typów palet", result.Count);
        return result;
    }
}

