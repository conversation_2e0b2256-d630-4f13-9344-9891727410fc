using WmsApp.ViewModels;

namespace WmsApp.Views;

public partial class EmulatorScannerPage : ContentPage
{
    public EmulatorScannerPage(EmulatorScannerViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public EmulatorScannerViewModel VM => (EmulatorScannerViewModel)BindingContext;

    protected override void OnAppearing()
    {
        base.OnAppearing();
        
        // Fokus na pole wprowadzania przy otwieraniu strony
        ScanInputEntry.Focus();
    }
}
