using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;

namespace Wms.Api.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
[Produces("application/json")]
public class LocationsController : BaseApiController
{
    private readonly IPalletService _palletService;
    private readonly ILogger<LocationsController> _logger;

    public LocationsController(IPalletService palletService, ILogger<LocationsController> logger)
    {
        _palletService = palletService;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera informacje o lokalizacji
    /// </summary>
    /// <param name="locationCode">Kod lokalizacji (format MP-H-R-M-P)</param>
    /// <returns>Szczegółowe informacje o lokalizacji</returns>
    [HttpGet("{locationCode}")]
    [ProducesResponseType(typeof(LocationInfo), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<LocationInfo>> GetLocation([FromRoute] string locationCode)
    {
        try
        {
            var result = await _palletService.GetLocationInfoAsync(locationCode);
            
            if (result == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Location Not Found",
                    Detail = $"Location with code {locationCode} was not found",
                    Status = 404
                });
            }

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid location code format: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Invalid Input",
                Detail = ex.Message,
                Status = 400
            });
        }
    }
}
