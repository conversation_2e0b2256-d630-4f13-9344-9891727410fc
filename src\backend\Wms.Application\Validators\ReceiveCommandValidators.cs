using FluentValidation;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Services;

namespace Wms.Application.Validators;

public class ClaimReceiveCommandValidator : AbstractValidator<ClaimReceiveCommand>
{
    public ClaimReceiveCommandValidator()
    {
        RuleFor(x => x.ReceiveId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
        
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
    }
}

public class ReleaseReceiveCommandValidator : AbstractValidator<ReleaseReceiveCommand>
{
    public ReleaseReceiveCommandValidator()
    {
        RuleFor(x => x.ReceiveId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
        
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
    }
}

public class GeneratePalletsCommandValidator : AbstractValidator<GeneratePalletsCommand>
{
    public GeneratePalletsCommandValidator()
    {
        RuleFor(x => x.ListControlId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
            
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
            
        RuleFor(x => x.TypPaletyId)
            .GreaterThan(0)
            .WithMessage("ID typu palety musi być większe od 0");
            
        RuleFor(x => x.Ilosc)
            .InclusiveBetween(1, 100)
            .WithMessage("Ilość palet musi być między 1 a 100");

        When(x => x.Drukowac, () =>
        {
            RuleFor(x => x.DrukarkaIp)
                .NotEmpty()
                .WithMessage("IP drukarki jest wymagane gdy drukowanie jest włączone")
                .Must(BeValidPrinterIp)
                .WithMessage("Nieprawidłowe IP drukarki");
        });
    }

    private static bool BeValidPrinterIp(string? ip)
    {
        if (string.IsNullOrWhiteSpace(ip))
            return false;
            
        // Remove IP prefix if present
        var cleanIp = ip.StartsWith("IP", StringComparison.OrdinalIgnoreCase) 
            ? ip[2..] 
            : ip;
            
        return System.Net.IPAddress.TryParse(cleanIp, out var ipAddress) &&
               ipAddress.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
    }
}

public class ParseGS1ScanCommandValidator : AbstractValidator<ParseGS1ScanCommand>
{
    public ParseGS1ScanCommandValidator()
    {
        RuleFor(x => x.ListControlId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
            
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
            
        RuleFor(x => x.Scan)
            .NotEmpty()
            .WithMessage("Dane skanu nie mogą być puste");
    }
}

public class CreateReceiveItemCommandValidator : AbstractValidator<CreateReceiveItemCommand>
{
    public CreateReceiveItemCommandValidator()
    {
        RuleFor(x => x.ListControlId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
            
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
            
        RuleFor(x => x.PaletaId)
            .GreaterThan(0)
            .WithMessage("ID palety musi być większe od 0");
            
        RuleFor(x => x.KodId)
            .GreaterThan(0)
            .WithMessage("ID kodu produktu musi być większe od 0");
            
        RuleFor(x => x.Ilosc)
            .GreaterThan(0)
            .WithMessage("Ilość musi być większa od 0");

        When(x => !string.IsNullOrEmpty(x.Sscc), () =>
        {
            RuleFor(x => x.Sscc)
                .Must(sscc => Wms.Domain.ValueObjects.SSCCCode.IsValid(sscc))
                .WithMessage("Nieprawidłowy format kodu SSCC");
        });
    }
}

public class CreateCarrierCommandValidator : AbstractValidator<CreateCarrierCommand>
{
    public CreateCarrierCommandValidator()
    {
        RuleFor(x => x.ListControlId)
            .GreaterThan(0)
            .WithMessage("ID dostawy musi być większe od 0");
            
        RuleFor(x => x.PracownikId)
            .GreaterThan(0)
            .WithMessage("ID pracownika musi być większe od 0");
            
        RuleFor(x => x.TypPaletyId)
            .GreaterThan(0)
            .WithMessage("ID typu palety musi być większe od 0");

        When(x => x.Drukowac, () =>
        {
            RuleFor(x => x.DrukarkaIp)
                .NotEmpty()
                .WithMessage("IP drukarki jest wymagane gdy drukowanie jest włączone");
        });
    }
}
