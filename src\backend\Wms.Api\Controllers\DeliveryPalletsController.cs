using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Commands;
using Wms.Domain.Exceptions;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler zarządzania nośnikami (paletami DS) w dostawach
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/dostawy")]
[Authorize]
[Produces("application/json")]
public class DeliveryPalletsController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<DeliveryPalletsController> _logger;

    public DeliveryPalletsController(IMediator mediator, ILogger<DeliveryPalletsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Generuje nowe nośniki DS dla dostawy (tryb batch)
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="request">Parametry generowania nośników</param>
    /// <returns>Lista wygenerowanych nośników</returns>
    [HttpPost("{id:int}/nosniki")]
    [ProducesResponseType(typeof(GeneratePalletsResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<GeneratePalletsResponse>> GeneratePallets(
        [FromRoute] int id,
        [FromBody] GeneratePalletsRequest request)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new GeneratePalletsCommand
            {
                ListControlId = id,
                PracownikId = userId,
                TypPaletyId = request.TypPaletyId,
                Ilosc = request.Ilosc,
                Drukowac = request.Drukowac,
                DrukarkaIp = request.DrukarkaIp
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Nie można wygenerować nośników",
                    Detail = result.Message,
                    Status = 400
                });
            }

            _logger.LogInformation("Wygenerowano {Count} nośników DS dla dostawy LK{ReceiveId} przez pracownika {UserId}", 
                result.GeneratedCount, id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Conflict(new ProblemDetails
            {
                Title = "Brak uprawnień",
                Detail = ex.Message,
                Status = 409
            });
        }
        catch (PalletGenerationException ex)
        {
            _logger.LogWarning("Błąd generowania nośników: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Błąd generowania nośników",
                Detail = ex.Message,
                Status = 400
            });
        }
    }

    /// <summary>
    /// Tworzy pojedynczy nośnik DS dla dostawy (tryb Auto)
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="request">Parametry tworzenia nośnika</param>
    /// <returns>Utworzony nośnik</returns>
    [HttpPost("{id:int}/nosnik")]
    [ProducesResponseType(typeof(CarrierDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<CarrierDto>> CreateCarrier(
        [FromRoute] int id,
        [FromBody] CreateCarrierRequest request)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new CreateCarrierCommand
            {
                ListControlId = id,
                PracownikId = userId,
                TypPaletyId = request.TypPaletyId,
                Drukowac = request.Drukowac,
                DrukarkaIp = request.DrukarkaIp
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("Utworzono nośnik DS{PalletId:D8} dla dostawy LK{ReceiveId} przez pracownika {UserId}", 
                result.PaletaId, id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Conflict(new ProblemDetails
            {
                Title = "Brak uprawnień",
                Detail = ex.Message,
                Status = 409
            });
        }
    }

    /// <summary>
    /// Parsuje dane ze skanu GS1
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="request">Dane skanu do sparsowania</param>
    /// <returns>Sparsowane dane GS1</returns>
    [HttpPost("{id:int}/skan")]
    [ProducesResponseType(typeof(GS1ParseResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<GS1ParseResponse>> ParseGS1Scan(
        [FromRoute] int id,
        [FromBody] ParseGS1ScanRequest request)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new ParseGS1ScanCommand
            {
                ListControlId = id,
                PracownikId = userId,
                Scan = request.Scan
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Błąd parsowania GS1",
                    Detail = result.Message,
                    Status = 400
                });
            }

            _logger.LogDebug("Sparsowano dane GS1 dla dostawy LK{ReceiveId}: SSCC={Sscc}, EAN={Ean}", 
                id, result.Sscc, result.Ean);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (InvalidGS1FormatException ex)
        {
            _logger.LogWarning("Nieprawidłowy format GS1: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy format GS1",
                Detail = ex.Message,
                Status = 400
            });
        }
    }

    /// <summary>
    /// Dodaje pozycję do dostawy na określonym nośniku
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="request">Dane pozycji do dodania</param>
    /// <returns>Utworzona pozycja</returns>
    [HttpPost("{id:int}/pozycje")]
    [ProducesResponseType(typeof(ReceiveItemDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ReceiveItemDto>> CreateReceiveItem(
        [FromRoute] int id,
        [FromBody] CreateReceiveItemRequest request)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new CreateReceiveItemCommand
            {
                ListControlId = id,
                PracownikId = userId,
                PaletaId = request.PaletaId,
                KodId = request.KodId,
                Lot = request.Lot,
                DataProd = request.DataProd,
                DataWaznosci = request.DataWaznosci,
                Ilosc = request.Ilosc,
                Sscc = request.Sscc,
                Certyfikat = request.Certyfikat
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("Utworzono pozycję dla dostawy LK{ReceiveId} na nośniku DS{PalletId:D8}: Kod={KodId}, Ilość={Quantity}", 
                id, request.PaletaId, request.KodId, request.Ilosc);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Nieprawidłowe dane pozycji: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowe dane pozycji",
                Detail = ex.Message,
                Status = 400
            });
        }
    }

    /// <summary>
    /// Kończy pracę z nośnikiem (oznacza jako ukończony)
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="paletaId">ID palety</param>
    /// <returns>Wynik operacji</returns>
    [HttpPost("{id:int}/nosniki/{paletaId:int}/complete")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<bool>> CompleteCarrier(
        [FromRoute] int id,
        [FromRoute] int paletaId)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new CompleteCarrierCommand
            {
                ListControlId = id,
                PaletaId = paletaId,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Nie można ukończyć nośnika",
                    Detail = "Nośnik nie może być ukończony - sprawdź czy zawiera pozycje",
                    Status = 400
                });
            }

            _logger.LogInformation("Ukończono nośnik DS{PalletId:D8} dla dostawy LK{ReceiveId} przez pracownika {UserId}", 
                paletaId, id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }
}

/// <summary>
/// Request model dla generowania nośników
/// </summary>
public class GeneratePalletsRequest
{
    public int TypPaletyId { get; set; }
    public int Ilosc { get; set; }
    public bool Drukowac { get; set; }
    public string? DrukarkaIp { get; set; }
}

/// <summary>
/// Request model dla tworzenia nośnika
/// </summary>
public class CreateCarrierRequest
{
    public int TypPaletyId { get; set; }
    public bool Drukowac { get; set; }
    public string? DrukarkaIp { get; set; }
}

/// <summary>
/// Request model dla parsowania GS1
/// </summary>
public class ParseGS1ScanRequest
{
    public string Scan { get; set; } = string.Empty;
}

/// <summary>
/// Request model dla tworzenia pozycji
/// </summary>
public class CreateReceiveItemRequest
{
    public int PaletaId { get; set; }
    public int KodId { get; set; }
    public string? Lot { get; set; }
    public DateOnly? DataProd { get; set; }
    public DateOnly? DataWaznosci { get; set; }
    public decimal Ilosc { get; set; }
    public string? Sscc { get; set; }
    public string? Certyfikat { get; set; }
}
