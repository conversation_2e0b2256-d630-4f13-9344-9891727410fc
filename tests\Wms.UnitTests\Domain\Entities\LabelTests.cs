using FluentAssertions;
using Wms.Domain.Entities;

namespace Wms.UnitTests.Domain.Entities;

public class LabelTests
{
    [Fact]
    public void Label_Creation_WithValidData_ShouldCreateLabelSuccessfully()
    {
        // Arrange
        var sscc = "123456789012345678";
        var etykietaKlient = "ETK001";
        var gtin = "1234567890123";
        var ilosc = 25.5m;
        var kartony = 5;

        // Act
        var label = new Label
        {
            Sscc = sscc,
            EtykietaKlient = etykietaKlient,
            Gtin = gtin,
            Ilosc = ilosc,
            Kartony = kartony,
            CreatedAt = DateTime.UtcNow
        };

        // Assert
        label.Should().NotBeNull();
        label.Sscc.Should().Be(sscc);
        label.EtykietaKlient.Should().Be(etykietaKlient);
        label.Gtin.Should().Be(gtin);
        label.Ilosc.Should().Be(ilosc);
        label.Kartony.Should().Be(kartony);
        label.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Label_DefaultValues_ShouldBeSetCorrectly()
    {
        // Arrange & Act
        var label = new Label();

        // Assert
        label.SystemId.Should().Be(0);
        label.Magazyn.Should().Be(0);
        label.Miejscep.Should().Be(0);
        label.StatusIdOld.Should().Be(1);
        label.StatusId.Should().Be(1);
        label.PrzeznczenieId.Should().Be(1);
        label.Nretykiety.Should().Be(0);
        label.EdycjaEt.Should().Be(1);
        label.Ts.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("123456789012345678")]  // 18 digits - valid
    [InlineData("")]
    [InlineData(null)]
    public void Label_Sscc_ShouldAcceptValidValues(string ssccValue)
    {
        // Arrange & Act
        var label = new Label
        {
            Sscc = ssccValue
        };

        // Assert
        label.Sscc.Should().Be(ssccValue);
    }

    [Fact]
    public void Label_HasSSCC_WhenSsccIsNotEmpty_ShouldReturnTrue()
    {
        // Arrange
        var label = new Label
        {
            Sscc = "123456789012345678"
        };

        // Act & Assert
        label.HasSSCC.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Label_HasSSCC_WhenSsccIsEmptyOrNull_ShouldReturnFalse(string ssccValue)
    {
        // Arrange
        var label = new Label
        {
            Sscc = ssccValue
        };

        // Act & Assert
        label.HasSSCC.Should().BeFalse();
    }

    [Theory]
    [InlineData(1, true)]
    [InlineData(0, false)]
    [InlineData(null, false)]
    public void Label_IsActive_ShouldReturnCorrectValue(int? activeValue, bool expectedResult)
    {
        // Arrange
        var label = new Label
        {
            Active = activeValue
        };

        // Act & Assert
        label.IsActive.Should().Be(expectedResult);
    }

    [Fact]
    public void Label_GetDisplayCode_WithSscc_ShouldReturnSscc()
    {
        // Arrange
        var sscc = "123456789012345678";
        var label = new Label
        {
            Id = 123,
            Sscc = sscc,
            EtykietaKlient = "ETK001"
        };

        // Act
        var displayCode = label.GetDisplayCode();

        // Assert
        displayCode.Should().Be(sscc);
    }

    [Fact]
    public void Label_GetDisplayCode_WithoutSsccButWithEtykietaKlient_ShouldReturnEtykietaKlient()
    {
        // Arrange
        var etykietaKlient = "ETK001";
        var label = new Label
        {
            Id = 123,
            Sscc = null,
            EtykietaKlient = etykietaKlient
        };

        // Act
        var displayCode = label.GetDisplayCode();

        // Assert
        displayCode.Should().Be(etykietaKlient);
    }

    [Fact]
    public void Label_GetDisplayCode_WithoutSsccAndEtykietaKlient_ShouldReturnId()
    {
        // Arrange
        var id = 123;
        var label = new Label
        {
            Id = id,
            Sscc = null,
            EtykietaKlient = null
        };

        // Act
        var displayCode = label.GetDisplayCode();

        // Assert
        displayCode.Should().Be(id.ToString());
    }

    [Fact]
    public void Label_GetDisplayCode_WithEmptyStrings_ShouldReturnEmptyString()
    {
        // Arrange
        var id = 123;
        var label = new Label
        {
            Id = id,
            Sscc = "",
            EtykietaKlient = ""
        };

        // Act
        var displayCode = label.GetDisplayCode();

        // Assert
        displayCode.Should().Be(""); // Because ?? operator doesn't check for empty strings, only null
    }

    [Fact]
    public void Label_Dates_CanBeSet()
    {
        // Arrange
        var dataProd = DateOnly.FromDateTime(DateTime.Now.AddDays(-30));
        var dataWaznosci = DateOnly.FromDateTime(DateTime.Now.AddDays(60));

        // Act
        var label = new Label
        {
            Dataprod = dataProd,
            DataWaznosci = dataWaznosci
        };

        // Assert
        label.Dataprod.Should().Be(dataProd);
        label.DataWaznosci.Should().Be(dataWaznosci);
    }

    [Fact]
    public void Label_ForeignKeys_CanBeSet()
    {
        // Arrange
        var paletaId = 100;
        var miejscepId = 200;
        var kodId = 300;

        // Act
        var label = new Label
        {
            PaletaId = paletaId,
            Miejscep = miejscepId,
            KodId = kodId
        };

        // Assert
        label.PaletaId.Should().Be(paletaId);
        label.Miejscep.Should().Be(miejscepId);
        label.KodId.Should().Be(kodId);
    }

    [Fact]
    public void Label_OptionalProperties_CanBeNull()
    {
        // Arrange & Act
        var label = new Label
        {
            EtykietaKlient = null,
            Active = null,
            KodId = null,
            PaletaId = null,
            Kartony = null,
            Dataprod = null,
            DataWaznosci = null,
            Ilosc = null,
            Status = null,
            Blloc = null,
            AkcjaId = null,
            StatusPrism = null,
            Lot = null,
            Sscc = null,
            Gtin = null,
            DocinId = null,
            DocoutId = null,
            DeliveryId = null,
            ListcontrolId = null,
            StatusId2 = null
        };

        // Assert
        label.EtykietaKlient.Should().BeNull();
        label.Active.Should().BeNull();
        label.KodId.Should().BeNull();
        label.PaletaId.Should().BeNull();
        label.Kartony.Should().BeNull();
        label.Dataprod.Should().BeNull();
        label.DataWaznosci.Should().BeNull();
        label.Ilosc.Should().BeNull();
        label.Status.Should().BeNull();
        label.Blloc.Should().BeNull();
        label.AkcjaId.Should().BeNull();
        label.StatusPrism.Should().BeNull();
        label.Lot.Should().BeNull();
        label.Sscc.Should().BeNull();
        label.Gtin.Should().BeNull();
        label.DocinId.Should().BeNull();
        label.DocoutId.Should().BeNull();
        label.DeliveryId.Should().BeNull();
        label.ListcontrolId.Should().BeNull();
        label.StatusId2.Should().BeNull();
    }

    [Fact]
    public void Label_NavigationProperties_CanBeSet()
    {
        // Arrange
        var pallet = new Pallet { Id = 1 };
        var location = new Location { Id = 1 };
        var label = new Label { Id = 1 };

        // Act
        label.Pallet = pallet;
        label.Location = location;

        // Assert
        label.Pallet.Should().NotBeNull();
        label.Pallet.Id.Should().Be(pallet.Id);
        label.Location.Should().NotBeNull();
        label.Location.Id.Should().Be(location.Id);
    }

    [Fact]
    public void Label_Ts_ShouldBeSetToCurrentDateTime()
    {
        // Arrange & Act
        var label = new Label();

        // Assert
        label.Ts.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Label_UpdatedAt_WhenModified_ShouldReflectChange()
    {
        // Arrange
        var label = new Label
        {
            CreatedAt = DateTime.UtcNow.AddHours(-1)
        };
        var updateTime = DateTime.UtcNow;

        // Act
        label.UpdatedAt = updateTime;

        // Assert
        label.UpdatedAt.Should().Be(updateTime);
        label.UpdatedAt.Should().BeAfter(label.CreatedAt);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    [InlineData(1)]
    [InlineData(100)]
    public void Label_Kartony_ShouldAcceptValidValues(int kartonyValue)
    {
        // Arrange & Act
        var label = new Label
        {
            Kartony = kartonyValue
        };

        // Assert
        label.Kartony.Should().Be(kartonyValue);
    }

    [Theory]
    [InlineData(0.0)]
    [InlineData(1.5)]
    [InlineData(1000.25)]
    public void Label_Ilosc_ShouldAcceptValidValues(decimal iloscValue)
    {
        // Arrange & Act
        var label = new Label
        {
            Ilosc = iloscValue
        };

        // Assert
        label.Ilosc.Should().Be(iloscValue);
    }
}
