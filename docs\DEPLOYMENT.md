# Wdrażanie Aplikacji WMS na Serwer Developerski

## 📋 Spis Treści
1. [Przygotowanie Serwera](#przygotowanie-serwera)
2. [Instalacja .NET 9.0](#instalacja-net-90)
3. [Konfiguracja Apache](#konfiguracja-apache)
4. [Wdrażanie Aplikacji](#wdrażanie-aplikacji)
5. [Konfiguracja Usługi Systemd](#konfiguracja-usługi-systemd)
6. [Testowanie i Weryfikacja](#testowanie-i-weryfikacja)
7. [Rozwiązywanie Problemów](#rozwiązywanie-problemów)

---

## Przygotowanie Serwera

### Informacje o Serwerze Developerskim
- **IP:** ***********
- **System:** Debian GNU/Linux 10 (buster)
- **Architektura:** x86_64 (amd64)
- **Użytkownicy:**
  - `root` - administrator serwera
  - `wms-dev` - użytkownik aplikacji WMS

### Struktura Katalogów
```
/var/www/wms-backend/          # Główny katalog aplikacji
├── Wms.Api                    # Plik wykonywalny Linux
├── Wms.Api.dll               # Biblioteka .NET
├── Wms.Api.deps.json         # Zależności
├── appsettings.json          # Konfiguracja
└── [inne pliki aplikacji]

/etc/systemd/system/           # Usługi systemd
├── wms-api.service           # Definicja usługi WMS API

/etc/apache2/                 # Konfiguracja Apache
├── sites-available/
│   ├── 000-default.conf      # Konfiguracja HTTP
│   └── default-ssl.conf      # Konfiguracja HTTPS
└── sites-enabled/            # Aktywne strony

/etc/ssl/                     # Certyfikaty SSL
├── certs/wms-selfsigned.crt  # Certyfikat SSL
└── private/wms-selfsigned.key # Klucz prywatny
```

---

## Instalacja .NET 9.0

### Krok 1: Dodanie Repozytorium Microsoft
```bash
# Zaloguj się jako root
ssh root@***********

# Pobierz pakiet konfiguracyjny Microsoft
wget https://packages.microsoft.com/config/debian/10/packages-microsoft-prod.deb -O packages-microsoft-prod.deb

# Zainstaluj pakiet
dpkg -i packages-microsoft-prod.deb

# Aktualizuj listę pakietów
apt-get update
```

### Krok 2: Instalacja ASP.NET Core Runtime 9.0
```bash
# Zainstaluj ASP.NET Core Runtime
apt-get install -y aspnetcore-runtime-9.0

# Weryfikacja instalacji
dotnet --version
dotnet --list-runtimes
```

**Oczekiwany wynik:**
```
Microsoft.AspNetCore.App 9.0.0 [/usr/share/dotnet/shared/Microsoft.AspNetCore.App]
Microsoft.NETCore.App 9.0.0 [/usr/share/dotnet/shared/Microsoft.NETCore.App]
```

---

## Konfiguracja Apache

### Krok 1: Włączenie Modułów Apache
```bash
# Włącz niezbędne moduły
a2enmod proxy
a2enmod proxy_http  
a2enmod ssl
a2enmod rewrite
a2enmod headers

# Restart Apache
systemctl restart apache2
```

### Krok 2: Konfiguracja HTTP (port 80)
Edytuj `/etc/apache2/sites-available/000-default.conf`:

```apache
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www

    # Logi
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    # Reverse proxy dla WMS API
    <Location "/api">
        ProxyPass "http://127.0.0.1:5000/api"
        ProxyPassReverse "http://127.0.0.1:5000/api"
        ProxyPreserveHost On
        ProxyAddHeaders On
        RequestHeader set X-Forwarded-Proto "http"
        RequestHeader set X-Forwarded-For expr=%{REMOTE_ADDR}
    </Location>

    # Health check endpoint
    <Location "/health">
        ProxyPass "http://127.0.0.1:5000/health"
        ProxyPassReverse "http://127.0.0.1:5000/health"
        ProxyPreserveHost On
    </Location>

    # Swagger UI
    <Location "/swagger">
        ProxyPass "http://127.0.0.1:5000/swagger"
        ProxyPassReverse "http://127.0.0.1:5000/swagger"
        ProxyPreserveHost On
    </Location>

    # OpenAPI
    <Location "/openapi">
        ProxyPass "http://127.0.0.1:5000/openapi"
        ProxyPassReverse "http://127.0.0.1:5000/openapi"
        ProxyPreserveHost On
    </Location>

    # Pozostała konfiguracja...
</VirtualHost>
```

### Krok 3: Tworzenie Certyfikatu SSL
```bash
# Utwórz konfigurację certyfikatu
cat > /tmp/cert.conf << 'EOF'
[req]
default_bits = 2048
prompt = no
distinguished_name = req_distinguished_name
req_extensions = v3_req

[req_distinguished_name]
C=PL
ST=Poland
L=Warsaw
O=WMS System
CN=***********

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
IP.1 = ***********
DNS.1 = localhost
DNS.2 = wms-api
EOF

# Wygeneruj certyfikat
openssl req -new -x509 -days 365 -nodes \
  -config /tmp/cert.conf \
  -keyout /etc/ssl/private/wms-selfsigned.key \
  -out /etc/ssl/certs/wms-selfsigned.crt

# Ustaw odpowiednie uprawnienia
chmod 600 /etc/ssl/private/wms-selfsigned.key
chmod 644 /etc/ssl/certs/wms-selfsigned.crt
```

### Krok 4: Konfiguracja HTTPS (port 443)
Edytuj `/etc/apache2/sites-available/default-ssl.conf`:

```apache
<IfModule mod_ssl.c>
    <VirtualHost _default_:443>
        ServerAdmin webmaster@localhost
        DocumentRoot /var/www

        # Konfiguracja SSL
        SSLEngine on
        SSLCertificateFile    /etc/ssl/certs/wms-selfsigned.crt
        SSLCertificateKeyFile /etc/ssl/private/wms-selfsigned.key

        # Bezpieczeństwo SSL
        SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
        SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
        SSLHonorCipherOrder off
        SSLSessionTickets off

        # Reverse proxy dla WMS API (identyczny jak HTTP)
        <Location "/api">
            ProxyPass "http://127.0.0.1:5000/api"
            ProxyPassReverse "http://127.0.0.1:5000/api"
            ProxyPreserveHost On
            ProxyAddHeaders On
            RequestHeader set X-Forwarded-Proto "https"
            RequestHeader set X-Forwarded-For expr=%{REMOTE_ADDR}
            RequestHeader set X-Forwarded-Port "443"
        </Location>

        # Health check endpoint
        <Location "/health">
            ProxyPass "http://127.0.0.1:5000/health"
            ProxyPassReverse "http://127.0.0.1:5000/health"
            ProxyPreserveHost On
        </Location>

        # Swagger UI
        <Location "/swagger">
            ProxyPass "http://127.0.0.1:5000/swagger"
            ProxyPassReverse "http://127.0.0.1:5000/swagger"
            ProxyPreserveHost On
        </Location>

        # OpenAPI
        <Location "/openapi">
            ProxyPass "http://127.0.0.1:5000/openapi"
            ProxyPassReverse "http://127.0.0.1:5000/openapi"
            ProxyPreserveHost On
        </Location>

        # Logi
        ErrorLog ${APACHE_LOG_DIR}/error.log
        CustomLog ${APACHE_LOG_DIR}/access.log combined

    </VirtualHost>
</IfModule>
```

### Krok 5: Włączenie Stron i Restart
```bash
# Włącz stronę SSL
a2ensite default-ssl

# Test konfiguracji
apache2ctl configtest

# Restart Apache
systemctl restart apache2

# Sprawdź status
systemctl status apache2
```

---

## Wdrażanie Aplikacji

### Krok 1: Przygotowanie Katalogu
```bash
# Utwórz katalog aplikacji
mkdir -p /var/www/wms-backend

# Ustaw właściciela
chown wms-dev:users /var/www/wms-backend
chmod 755 /var/www/wms-backend
```

### Krok 2: Użycie Skryptu Wdrożeniowego

Skrypt `scripts/publish-and-deploy.ps1` automatyzuje proces wdrażania:

```powershell
# Z katalogu głównego projektu
pwsh -File ./scripts/publish-and-deploy.ps1 dev
```

**Co robi skrypt:**
1. Publikuje aplikację z runtime `linux-x64`
2. Przesyła pliki na serwer przez SCP
3. Ustawia uprawnienia wykonania
4. Restartuje usługę systemd

### Krok 3: Ręczne Wdrożenie (alternatywnie)
```bash
# Na maszynie developerskiej
dotnet publish ./src/backend/Wms.Api \
    --configuration Development \
    --output ./publish-dev \
    --runtime linux-x64 \
    --self-contained false

# Przesłanie plików
scp -r ./publish-dev/* wms-dev@***********:/var/www/wms-backend/

# Na serwerze
ssh wms-dev@***********
cd /var/www/wms-backend
chmod +x Wms.Api
```

---

## Konfiguracja Usługi Systemd

### Krok 1: Utworzenie Definicji Usługi
Utwórz plik `/etc/systemd/system/wms-api.service`:

```ini
[Unit]
Description=WMS API Service
After=network.target

[Service]
Type=simple
User=wms-dev
Group=users
WorkingDirectory=/var/www/wms-backend
ExecStart=/var/www/wms-backend/Wms.Api --urls http://127.0.0.1:5000
Restart=on-failure
RestartSec=10

# Zmienne środowiskowe
Environment=ASPNETCORE_ENVIRONMENT=Development
Environment=ASPNETCORE_URLS=http://127.0.0.1:5000

# Logowanie
StandardOutput=journal
StandardError=journal

# Limity zasobów
MemoryLimit=512M

[Install]
WantedBy=multi-user.target
```

### Krok 2: Włączenie i Uruchomienie Usługi
```bash
# Przeładuj konfigurację systemd
systemctl daemon-reload

# Włącz automatyczne uruchamianie
systemctl enable wms-api

# Uruchom usługę
systemctl start wms-api

# Sprawdź status
systemctl status wms-api
```

### Krok 3: Zarządzanie Usługą
```bash
# Start
systemctl start wms-api

# Stop
systemctl stop wms-api

# Restart
systemctl restart wms-api

# Status
systemctl status wms-api

# Logi
journalctl -u wms-api -f

# Logi (ostatnie 50 linii)
journalctl -u wms-api -n 50
```

---

## Testowanie i Weryfikacja

### Sprawdzenie Statusu Usług
```bash
# Status Apache
systemctl status apache2

# Status WMS API
systemctl status wms-api

# Porty nasłuchujące
ss -tlnp | grep -E ':(80|443|5000)'
```

### Testowanie Endpointów

#### 1. Health Check
```bash
# HTTP
curl http://***********/health

# HTTPS (ignoruj certyfikat)
curl -k https://***********/health

# API Health
curl -k https://***********/api/v1.0/health
```

**Oczekiwany wynik:**
```json
{
  "status": "Healthy",
  "timestamp": "2025-09-03T10:34:36Z",
  "version": "1.0.0",
  "environment": "Development"
}
```

#### 2. API Endpoints
```bash
# Auth endpoint (test walidacji)
curl -k -X POST https://***********/api/v1.0/auth/login-scan \
  -H "Content-Type: application/json" \
  -d '{"CardNumber": "123456"}'

# OpenAPI (jeśli w Development)
curl -k https://***********/openapi/v1.0.json
```

### Weryfikacja w Przeglądarce
1. Przejdź do `https://***********/health`
2. Kliknij "Zaawansowane" → "Przejdź do *********** (niebezpieczne)"
3. Powinieneś zobaczyć: `Healthy`

## Dostępne Endpointy

### Health Check
- **Prosty:** `https://***********/health`
- **API:** `https://***********/api/v1.0/health`
- **Szczegółowy:** `https://***********/api/v1.0/health/detailed`

### API Endpoints
- **Auth:** `https://***********/api/v1.0/auth/login-scan`
- **Pallets:** `https://***********/api/v1.0/pallets`
- **Locations:** `https://***********/api/v1.0/locations`

### Development Tools
- **OpenAPI:** `https://***********/openapi/v1.0.json`
- **Swagger:** `https://***********/swagger` (jeśli dostępne)

---

## Rozwiązywanie Problemów

### Problemy z Certyfikatem SSL
**Problem:** `net::ERR_CERT_AUTHORITY_INVALID`
**Rozwiązanie:** 
- To normalne zachowanie dla samopodpisanego certyfikatu
- W przeglądarce: "Zaawansowane" → "Przejdź do strony"
- W curl: użyj flagi `-k`

### API Zwraca 404
**Problem:** `https://***********/api/v1/health` zwraca 404
**Rozwiązanie:**
- Użyj `v1.0` zamiast `v1`: `https://***********/api/v1.0/health`
- API używa wersji 1.0, nie 1

### Usługa WMS API nie uruchamia się
```bash
# Sprawdź logi
journalctl -u wms-api -n 50

# Sprawdź plik wykonywalny
ls -la /var/www/wms-backend/Wms.Api

# Sprawdź uprawnienia
chmod +x /var/www/wms-backend/Wms.Api

# Test ręcznego uruchomienia
cd /var/www/wms-backend
./Wms.Api --urls http://127.0.0.1:5000
```

### Apache 503 Service Unavailable
**Przyczyna:** WMS API nie działa na porcie 5000
**Rozwiązanie:**
```bash
# Restart usługi
systemctl restart wms-api

# Sprawdź czy nasłuchuje na porcie 5000
ss -tlnp | grep :5000

# Test lokalny
curl http://127.0.0.1:5000/health
```

### Problemy z Uprawnieniami
```bash
# Ustaw właściciela
chown -R wms-dev:users /var/www/wms-backend

# Ustaw uprawnienia katalogu
chmod 755 /var/www/wms-backend

# Ustaw uprawnienia pliku wykonywalnego
chmod +x /var/www/wms-backend/Wms.Api
```

### Diagnostyka Połączeń
```bash
# Sprawdź porty
ss -tlnp | grep -E ':(80|443|5000)'

# Test Apache lokanie
curl -I http://127.0.0.1/health
curl -k -I https://127.0.0.1/health

# Test API lokalnie
curl http://127.0.0.1:5000/health

# Sprawdź logi Apache
tail -f /var/log/apache2/error.log
tail -f /var/log/apache2/access.log
```

---

## Automatyzacja Wdrażania

### Skrypt PowerShell
Plik `scripts/publish-and-deploy.ps1` obsługuje:
- Kompilację dla Linux (`--runtime linux-x64`)
- Przesyłanie przez SCP
- Restart usługi systemd
- Weryfikację działania

### Przykład Użycia
```powershell
# Wdrożenie na środowisko dev
.\scripts\publish-and-deploy.ps1 dev

# Z automatycznym potwierdzeniem
echo "y" | pwsh -File ./scripts/publish-and-deploy.ps1 dev
```

### CI/CD Pipeline (przyszłość)
Można rozszerzyć o:
- GitHub Actions
- Automatyczne testy
- Backup bazy danych
- Rolling deployment

---

## Bezpieczeństwo

### Firewall (jeśli używany)
```bash
# Otwórz porty HTTP/HTTPS
ufw allow 80
ufw allow 443

# Port 5000 powinien być dostępny tylko lokalnie
# (reverse proxy przez Apache)
```

### Certyfikat Produkcyjny
Dla środowiska produkcyjnego:
1. **Let's Encrypt** (darmowy, wymaga domeny)
2. **Certyfikat CA** (płatny, dla IP lub domeny)
3. **Wildcard cert** (dla wielu subdomen)

### Monitoring
```bash
# Sprawdzanie logów aplikacji
journalctl -u wms-api --since "1 hour ago"

# Monitoring zasobów
htop
free -h
df -h
```

---

## Backup i Recovery

### Backup Aplikacji
```bash
# Backup bieżącej wersji
tar -czf /tmp/wms-backup-$(date +%Y%m%d_%H%M%S).tar.gz \
  -C /var/www/wms-backend .

# Automatyczny backup przed wdrożeniem
# (obsługiwane przez skrypt deploy)
```

### Recovery
```bash
# Przywróć z backupu
cd /var/www/wms-backend
tar -xzf /tmp/wms-backup-YYYYMMDD_HHMMSS.tar.gz

# Restart usługi
systemctl restart wms-api
```

---

**Data utworzenia:** 2025-09-03  
**Wersja dokumentu:** 1.0  
**Autor:** AI Assistant  
**Status:** Aktualne dla serwera ***********
