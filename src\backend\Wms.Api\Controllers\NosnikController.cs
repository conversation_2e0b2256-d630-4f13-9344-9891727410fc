using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Application.DTOs.Receives;
using AutoMapper;
using Wms.Domain.Entities;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler do zarządzania nośnikami (SSCC/DS)
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/nosniki")]
[Authorize]
[Produces("application/json")]
public class NosnikController : BaseApiController
{
    private readonly ILabelRepository _labelRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly IKodRepository _kodRepository;
    private readonly IPalletService _palletService;
    private readonly IMapper _mapper;
    private readonly ILogger<NosnikController> _logger;

    public NosnikController(
        ILabelRepository labelRepository,
        IPalletRepository palletRepository,
        IKodRepository kodRepository,
        IPalletService palletService,
        IMapper mapper,
        ILogger<NosnikController> logger)
    {
        _labelRepository = labelRepository;
        _palletRepository = palletRepository;
        _kodRepository = kodRepository;
        _palletService = palletService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera pozycje na nośniku na podstawie kodu (SSCC lub DS)
    /// </summary>
    /// <param name="nosnikCode">Kod nośnika (SSCC lub DS)</param>
    /// <returns>Lista pozycji na nośniku</returns>
    [HttpGet("{nosnikCode}/positions")]
    [ProducesResponseType(typeof(GetNosnikPositionsResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<GetNosnikPositionsResponse>> GetNosnikPositions([FromRoute] string nosnikCode)
    {
        _logger.LogInformation("Rozpoczęcie pobierania pozycji dla nośnika {NosnikCode} przez użytkownika {UserId}",
            nosnikCode, GetCurrentUserId());

        if (string.IsNullOrWhiteSpace(nosnikCode))
        {
            _logger.LogWarning("Otrzymano pusty kod nośnika");
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy kod nośnika",
                Detail = "Kod nośnika jest wymagany",
                Status = 400
            });
        }

        try
        {
            _logger.LogDebug("Pobieranie pozycji dla nośnika {NosnikCode}", nosnikCode);

            // Znajdź paletę na podstawie kodu nośnika
            int? paletaId = await FindPaletaIdByCodeAsync(nosnikCode);

            if (!paletaId.HasValue)
            {
                _logger.LogWarning("Nie znaleziono palety dla kodu nośnika {NosnikCode}. Sprawdzono SSCC i DS.", nosnikCode);
                return Ok(new GetNosnikPositionsResponse
                {
                    Positions = Array.Empty<NosnikPositionDto>(),
                    TotalSztuk = 0,
                    TotalOpakowan = 0,
                    Warnings = new[] { "Nie znaleziono pozycji na nośniku" }
                });
            }

            _logger.LogDebug("Znaleziono paletę {PaletaId} dla nośnika {NosnikCode}", paletaId.Value, nosnikCode);

            // Pobierz wszystkie etykiety z tej palety
            var labels = await _palletRepository.GetCarrierItemsAsync(paletaId.Value);
            _logger.LogDebug("Pobrano {LabelsCount} etykiet dla palety {PaletaId}", labels.Count(), paletaId.Value);

            var positions = new List<NosnikPositionDto>();
            decimal totalSztuk = 0;
            decimal totalOpakowan = 0;

            foreach (var label in labels.Where(l => l.Active == 1))
            {
                var position = new NosnikPositionDto
                {
                    Id = label.Id,
                    TowarKod = await GetKodValueAsync(label.KodId),
                    TowarNazwa = await GetKodNazwaAsync(label.KodId),
                    Partia = label.Lot,
                    DataWaznosci = label.DataWaznosci?.ToDateTime(TimeOnly.MinValue),
                    DataProdukcji = label.Dataprod?.ToDateTime(TimeOnly.MinValue) ?? DateTime.MinValue,
                    IloscSztuk = label.Ilosc ?? 0,
                    IloscOpakowan = await CalculateOpakowan(label.Ilosc ?? 0, label.KodId),
                    Certyfikat = label.Blloc,
                    DataPrzyjecia = label.Ts
                };

                positions.Add(position);
                totalSztuk += position.IloscSztuk;
                totalOpakowan += position.IloscOpakowan;
            }

            var response = new GetNosnikPositionsResponse
            {
                Positions = positions.ToArray(),
                TotalSztuk = totalSztuk,
                TotalOpakowan = totalOpakowan,
                Warnings = positions.Count == 0 ? new[] { "Brak pozycji na nośniku" } : Array.Empty<string>()
            };

            _logger.LogInformation("Pobrano {Count} pozycji dla nośnika {NosnikCode}, łącznie {TotalSztuk} sztuk",
                positions.Count, nosnikCode, totalSztuk);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania pozycji nośnika {NosnikCode}", nosnikCode);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas pobierania pozycji nośnika",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Oznacza nośnik jako kompletny
    /// </summary>
    /// <param name="request">Żądanie oznaczenia nośnika jako kompletny</param>
    /// <returns>Wynik operacji</returns>
    [HttpPost("complete")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<object>> CompleteNosnik([FromBody] CompleteNosnikRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.NosnikCode))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy kod nośnika",
                Detail = "Kod nośnika jest wymagany",
                Status = 400
            });
        }

        try
        {
            _logger.LogInformation("Oznaczanie nośnika {NosnikCode} jako kompletny przez {DeviceId}",
                request.NosnikCode, request.DeviceId);

            // W przyszłości tutaj można dodać logikę oznaczania nośnika jako kompletny
            // Na razie zwracamy sukces

            return Ok(new { success = true, message = $"Nośnik {request.NosnikCode} został oznaczony jako kompletny" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas oznaczania nośnika jako kompletny");
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas oznaczania nośnika jako kompletny",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Usuwa etykietę po ID (tylko te z active = null)
    /// </summary>
    /// <param name="labelId">ID etykiety do usunięcia</param>
    /// <returns>Wynik operacji</returns>
    [HttpDelete("labels/{labelId:int}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<object>> DeleteLabel([FromRoute] int labelId)
    {
        try
        {
            _logger.LogInformation("Próba usunięcia etykiety {LabelId} przez użytkownika {UserId}",
                labelId, GetCurrentUserId());

            // Pobierz etykietę z bazy
            var labels = await _labelRepository.GetByPalletIdAsync(labelId); // Używamy tej metody bo nie ma GetByIdAsync
            var label = labels.FirstOrDefault(l => l.Id == labelId);

            if (label == null)
            {
                _logger.LogWarning("Nie znaleziono etykiety {LabelId}", labelId);
                return NotFound(new ProblemDetails
                {
                    Title = "Etykieta nie znaleziona",
                    Detail = $"Etykieta o ID {labelId} nie istnieje",
                    Status = 404
                });
            }

            // Sprawdź czy etykieta może być usunięta (active = null)
            if (label.Active != null)
            {
                _logger.LogWarning("Próba usunięcia aktywnej etykiety {LabelId} (active = {Active})",
                    labelId, label.Active);
                return Conflict(new ProblemDetails
                {
                    Title = "Nie można usunąć etykiety",
                    Detail = "Można usuwać tylko etykiety z active = null",
                    Status = 409
                });
            }

            // TODO: Implementacja usuwania etykiety
            // Na razie tylko logujemy - rzeczywiste usuwanie wymaga dodania metody w repository
            _logger.LogInformation("Etykieta {LabelId} może być usunięta (active = null)", labelId);

            return Ok(new {
                success = true,
                message = $"Etykieta {labelId} została oznaczona do usunięcia",
                labelId = labelId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas usuwania etykiety {LabelId}", labelId);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas usuwania etykiety",
                Status = 500
            });
        }
    }

    #region Private Methods

    private async Task<int?> FindPaletaIdByCodeAsync(string code)
    {
        _logger.LogDebug("Wyszukiwanie palety dla kodu {Code}", code);
        var label = await FindLabelByCodeAsync(code);
        if (label != null)
        {
            _logger.LogDebug("Znaleziono etykietę {LabelId} dla kodu {Code}, paleta {PaletaId}",
                label.Id, code, label.PaletaId);
        }
        else
        {
            _logger.LogDebug("Nie znaleziono etykiety dla kodu {Code}", code);
        }
        return label?.PaletaId;
    }

    private async Task<Label?> FindLabelByCodeAsync(string code)
    {
        _logger.LogDebug("Rozpoczęcie wyszukiwania etykiety dla kodu {Code}", code);

        // Skopiowana logika z PalletService.FindLabelByCodeAsync
        if (IsSSCC(code))
        {
            _logger.LogDebug("Kod {Code} rozpoznany jako SSCC, wyszukiwanie po SSCC", code);
            var label = await _labelRepository.GetBySSCCAsNoTrackingAsync(code);
            _logger.LogDebug("Wynik wyszukiwania SSCC: {Found}", label != null ? "znaleziono" : "nie znaleziono");
            return label;
        }

        if (IsDS(code))
        {
            _logger.LogDebug("Kod {Code} rozpoznany jako DS, wyszukiwanie po paleta_id i etykieta_klient", code);
            var numericPart = code.Substring(2);
            _logger.LogDebug("Część numeryczna kodu DS: {NumericPart}", numericPart);

            if (int.TryParse(numericPart, out int palletId))
            {
                _logger.LogDebug("Parsowanie części numerycznej udane: {PalletId}, wyszukiwanie po paleta_id", palletId);
                var label = await _labelRepository.GetByPalletIdDirectAsync(palletId);
                if (label != null)
                {
                    _logger.LogDebug("Znaleziono etykietę {LabelId} po paleta_id {PalletId}", label.Id, palletId);
                    return label;
                }
                _logger.LogDebug("Nie znaleziono etykiety po paleta_id {PalletId}", palletId);
            }
            else
            {
                _logger.LogWarning("Nie udało się sparsować części numerycznej kodu DS: {NumericPart}", numericPart);
            }

            _logger.LogDebug("Wyszukiwanie po etykieta_klient dla kodu {Code}", code);
            var clientLabel = await _labelRepository.GetByClientCodeAsNoTrackingAsync(code);
            _logger.LogDebug("Wynik wyszukiwania po etykieta_klient: {Found}", clientLabel != null ? "znaleziono" : "nie znaleziono");
            return clientLabel;
        }

        _logger.LogWarning("Kod {Code} nie został rozpoznany jako SSCC ani DS", code);
        return null;
    }

    private static bool IsSSCC(string code)
    {
        return !string.IsNullOrWhiteSpace(code) && 
               code.Length == 18 && 
               code.All(char.IsDigit);
    }

    private static bool IsDS(string code)
    {
        return !string.IsNullOrWhiteSpace(code) && 
               code.StartsWith("DS", StringComparison.OrdinalIgnoreCase) && 
               code.Length > 2;
    }

    private async Task<string> GetKodValueAsync(int? kodId)
    {
        if (!kodId.HasValue) return "UNKNOWN";

        try
        {
            var kod = await _kodRepository.GetByIdAsync(kodId.Value);
            return kod?.KodValue ?? $"KOD-{kodId}";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Błąd podczas pobierania kodu dla ID {KodId}", kodId);
            return $"KOD-{kodId}";
        }
    }

    private async Task<string> GetKodNazwaAsync(int? kodId)
    {
        if (!kodId.HasValue) return "Nieznany produkt";

        try
        {
            var kod = await _kodRepository.GetByIdAsync(kodId.Value);
            return kod?.KodNazwa ?? $"Produkt {kodId}";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Błąd podczas pobierania nazwy kodu dla ID {KodId}", kodId);
            return $"Produkt {kodId}";
        }
    }

    private async Task<decimal> CalculateOpakowan(decimal ilosc, int? kodId)
    {
        if (!kodId.HasValue) return 0;

        try
        {
            var kod = await _kodRepository.GetByIdAsync(kodId.Value);
            if (kod?.IloscWOpakowaniu > 0)
            {
                return Math.Round(ilosc / kod.IloscWOpakowaniu.Value, 2);
            }

            // Domyślna kalkulacja jeśli brak informacji o opakowaniu
            return Math.Round(ilosc / 12, 2);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Błąd podczas obliczania opakowań dla kodu {KodId}", kodId);
            return Math.Round(ilosc / 12, 2);
        }
    }

    #endregion
}
