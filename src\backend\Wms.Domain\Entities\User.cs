using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class User : BaseEntity
{
    public int Id { get; set; }
    public string Login { get; set; } = null!;
    public int JednostkaId { get; set; } = 2;
    public string ImieNazwisko { get; set; } = null!;
    public string <PERSON><PERSON> { get; set; } = null!;
    public string? TelefonKom { get; set; }
    public string? Email { get; set; }
    public string? Telefon { get; set; }
    public string Stanowisko { get; set; } = null!;
    public string? NumerKarty { get; set; }
    public int Pin { get; set; } = 1234;
    public string? NumerRfid { get; set; }
    public bool IsActive { get; set; } = true; // Nowe pole - można dodać do tabeli
    
    // Navigation properties
    public ICollection<Session> Sessions { get; set; } = new List<Session>();
    public ICollection<Movement> Movements { get; set; } = new List<Movement>(); // zmianym.pracownik_id
}
