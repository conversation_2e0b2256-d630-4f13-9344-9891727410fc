using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.ApplicationModel;
using System.Collections.ObjectModel;
using System.Linq;
using WmsApp.Models.Receives;
using WmsApp.Services.Receives;
using WmsApp.Services;
using WmsApp.Converters;

using CommunityToolkit.Mvvm.Messaging;
using WmsApp.Messages;

namespace WmsApp.ViewModels.Receives;

/// <summary>
/// ViewModel dla widoku rejestracji dostaw
/// Obsługuje skanowanie GS1/DS, rejestrację pozycji na nośniki, podgląd i zakończenie sesji
/// </summary>
public partial class ReceivesRegistrationViewModel : ObservableObject, IQueryAttributable
{
    private readonly IReceiveService _receiveService;
    private readonly ReceiveCodeValidationService _validationService;
    private readonly ILogger<ReceivesRegistrationViewModel> _logger;

    #region Observable Properties - Session State

    [ObservableProperty]
    private string currentLK = string.Empty;

    [ObservableProperty]
    private string currentNosnik = "(Auto)";

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private string successMessage = string.Empty;

    [ObservableProperty]
    private string scanInput = string.Empty;

    [ObservableProperty]
    private bool isScanInputFocused = true;

    #endregion

#region Observable Properties - Item Form

    [ObservableProperty]
    private string towarQuery = string.Empty;

    [ObservableProperty]
    private string towarKod = string.Empty;

    [ObservableProperty]
    private string towarNazwa = string.Empty;

    [ObservableProperty]
    private int? selectedKodId;

    [ObservableProperty]
    private string partia = string.Empty;

    [ObservableProperty]
    private DateTime? dataWaznosci;

    [ObservableProperty]
    private DateTime? dataProdukcji;

    [ObservableProperty]
    private string certyfikat = string.Empty;

    [ObservableProperty]
    private int? selectedStanJakosciId;

    [ObservableProperty]
    private int? selectedTypPaletyId;

    [ObservableProperty]
    private TypPaletyDto? selectedTypPaleta;

    partial void OnSelectedTypPaletaChanged(TypPaletyDto? value)
    {
        SelectedTypPaletyId = value?.Id;
    }

    [ObservableProperty]
    private decimal iloscSztuk;

    [ObservableProperty]
    private decimal iloscOpakowan;

    [ObservableProperty]
    private decimal iloscWOpakowaniu = 1;

    [ObservableProperty]
    private bool isTowarSourceAwizacja = true; // true = Awizacja, false = Kartoteka

    [ObservableProperty]
    private bool isConfirmationModalVisible;

    [ObservableProperty]
    private string confirmationMessage = string.Empty;

    [ObservableProperty]
    private bool isKodSelectionModalVisible;

    #endregion

    #region Observable Properties - Preview Modal

    [ObservableProperty]
    private bool isPreviewModalVisible;

    [ObservableProperty]
    private decimal previewTotalSztuk;

    [ObservableProperty]
    private decimal previewTotalOpakowan;

    [ObservableProperty]
    private string[] previewWarnings = Array.Empty<string>();

    [ObservableProperty]
    private int selectedPreviewTabIndex = 0; // 0 = Pozycje nośnika, 1 = Pozycje awizacji

    #endregion

    public ObservableCollection<AwizacjaGroupedItem> AwizacjaGroupedItems { get; } = new();

    #region Collections

    public ObservableCollection<KodDto> KodCandidates { get; } = new();
    public ObservableCollection<TypPaletyDto> TypyPalet { get; } = new();
    public ObservableCollection<AwizacjaPositionDto> AwizacjaPositions { get; } = new();
    public ObservableCollection<KodDto> KartotekaSearchResults { get; } = new();
    public ObservableCollection<NosnikPositionDto> PreviewPositions { get; } = new();

    #endregion

    #region State Management

    private RegisterPositionRequest? _pendingRequest;

    #endregion

    private readonly IProductPickerService _productPickerService;

    public ReceivesRegistrationViewModel(
        IReceiveService receiveService,
        ReceiveCodeValidationService validationService,
        ILogger<ReceivesRegistrationViewModel> logger,
        IProductPickerService productPickerService)
    {
        _receiveService = receiveService;
        _validationService = validationService;
        _logger = logger;
        _productPickerService = productPickerService;

// Subskrybcja skanów (WeakReferenceMessenger)
        WeakReferenceMessenger.Default.Register<Gs1ScannedMessage>(this, (r, m) => OnGS1Scanned(m.Value));
        WeakReferenceMessenger.Default.Register<SsccScannedMessage>(this, (r, m) => OnSSCCScanned(m.Value));
        WeakReferenceMessenger.Default.Register<DsScannedMessage>(this, (r, m) => OnDSScanned(m.Value));

        // Kalkulacja automatyczna ilości
        PropertyChanged += OnPropertyChangedHandler;
    }

    #region IQueryAttributable

    private bool _hasInitialized = false;

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        _logger.LogInformation("DEBUG: ApplyQueryAttributes wywołane z {Count} parametrami", query.Count);
        foreach (var kvp in query)
        {
            _logger.LogInformation("DEBUG: Parametr {Key} = {Value}", kvp.Key, kvp.Value);
        }

        if (query.ContainsKey("lk"))
        {
            var newLK = query["lk"].ToString() ?? string.Empty;

            // Jeśli to ten sam LK i już zainiicjalizowano, nie rób nic
            if (CurrentLK == newLK && _hasInitialized)
            {
                _logger.LogInformation("DEBUG: Ten sam LK ({LK}) już załadowany, pomijam ponowną inicjalizację", newLK);
                return;
            }

            CurrentLK = newLK;
            _logger.LogInformation("DEBUG: Ustawiono CurrentLK na: '{CurrentLK}'", CurrentLK);
            _hasInitialized = true;
            _ = Task.Run(InitializeAsync);
        }
        else
        {
            _logger.LogWarning("DEBUG: Brak parametru 'lk' w query attributes");
        }
    }

    #endregion

    #region Commands - Main Actions

    [RelayCommand]
    private async Task ProcessScanAsync()
    {
        if (string.IsNullOrWhiteSpace(ScanInput))
        {
            ErrorMessage = "Wprowadź kod do skanowania";
            return;
        }

        try
        {
            ErrorMessage = string.Empty;
            var scanResult = _validationService.ProcessScan(ScanInput.Trim());

            switch (scanResult.Type)
            {
                case ReceiveScanCodeType.GS1:
                    await ProcessGS1ScanAsync(scanResult.Code);
                    break;
                case ReceiveScanCodeType.SSCC:
                    ProcessSSCCScan(scanResult.Code);
                    break;
                case ReceiveScanCodeType.DS:
                    ProcessDSScan(scanResult.Code);
                    break;
                default:
                    ErrorMessage = $"Nierozpoznany typ kodu: {scanResult.ErrorMessage ?? "Nieznany"}";
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanu: {ScanInput}", ScanInput);
            ErrorMessage = $"Błąd przetwarzania kodu: {ex.Message}";
        }
        finally
        {
            // Reset scan input and focus
            ScanInput = string.Empty;
            IsScanInputFocused = true;
        }
    }

    [RelayCommand]
    private async Task RegisterPositionAsync()
    {
        try
        {
            if (!ValidateForm())
            {
                return;
            }

            IsLoading = true;
            ErrorMessage = string.Empty;

            var request = CreateRegisterPositionRequest();
            var response = await _receiveService.RegisterPositionAsync(request);

            if (response.RequiresConfirmation)
            {
                // Pokaż modal potwierdzenia
                _pendingRequest = request;
                ConfirmationMessage = response.ValidationWarning ?? "Czy kontynuować rejestrację?";
                IsConfirmationModalVisible = true;
                return;
            }

            await ProcessRegistrationSuccess(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas rejestracji pozycji");
            ErrorMessage = $"Błąd rejestracji: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ConfirmRegistrationAsync()
    {
        if (_pendingRequest == null) return;

        try
        {
            IsLoading = true;
            IsConfirmationModalVisible = false;

            var response = await _receiveService.ConfirmRegisterPositionAsync(_pendingRequest);
            await ProcessRegistrationSuccess(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas potwierdzenia rejestracji");
            ErrorMessage = $"Błąd potwierdzenia: {ex.Message}";
        }
        finally
        {
            _pendingRequest = null;
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void CancelConfirmation()
    {
        IsConfirmationModalVisible = false;
        _pendingRequest = null;
        SuccessMessage = "Rejestracja została anulowana";
    }

    [RelayCommand]
    private async Task ShowPreviewAsync()
    {
        if (CurrentNosnik == "(Auto)")
        {
            ErrorMessage = "Brak aktywnego nośnika do podglądu";
            return;
        }

        try
        {
            IsLoading = true;

            // Załaduj pozycje nośnika
            var positions = await _receiveService.GetNosnikPositionsAsync(CurrentNosnik);

            PreviewPositions.Clear();
            foreach (var position in positions.Positions)
            {
                PreviewPositions.Add(position);
            }

            PreviewTotalSztuk = positions.TotalSztuk;
            PreviewTotalOpakowan = positions.TotalOpakowan;
            PreviewWarnings = positions.Warnings;

            // Załaduj pozycje awizacji jeśli są w trybie awizacji
            if (IsTowarSourceAwizacja)
            {
                await LoadAwizacjaPositionsAsync();
            }

            // Resetuj zakładkę na pierwszą (pozycje nośnika)
            SelectedPreviewTabIndex = 0;

            IsPreviewModalVisible = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania podglądu nośnika");
            ErrorMessage = $"Błąd podglądu: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task CompleteNosnikAsync()
    {
        if (CurrentNosnik == "(Auto)")
        {
            ErrorMessage = "Brak aktywnego nośnika do zamknięcia";
            return;
        }

        try
        {
            IsLoading = true;
            var deviceId = DeviceInfo.Current.Name;

            await _receiveService.CompleteNosnikAsync(CurrentNosnik, deviceId);

            SuccessMessage = $"Nośnik {CurrentNosnik} został oznaczony jako kompletny";
            CurrentNosnik = "(Auto)"; // Reset do trybu auto

            _logger.LogInformation("Oznaczono nośnik {Nosnik} jako kompletny", CurrentNosnik);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas zamykania nośnika");
            ErrorMessage = $"Błąd zamykania nośnika: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task FinishSessionAsync()
    {
        try
        {
            IsLoading = true;
            var deviceId = DeviceInfo.Current.Name;

            var result = await _receiveService.FinishReceiveSessionAsync(CurrentLK, deviceId);

            if (result.CanClose)
            {
                SuccessMessage = result.ClosureMessage ?? "Sesja została zakończona";

                // Zwolnij claim i wróć do selekcji
                await _receiveService.ReleaseReceiveAsync(CurrentLK, deviceId);
                await Shell.Current.GoToAsync("//receivesselection");
            }
            else
            {
                var warnings = string.Join("\n", result.Warnings);
                ErrorMessage = $"Nie można zakończyć sesji:\n{warnings}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas kończenia sesji");
            ErrorMessage = $"Błąd zakończenia sesji: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    #endregion

    #region Commands - Item Selection

[RelayCommand]
    private async Task OpenProductPickerAsync()
    {
        try
        {
            _logger.LogInformation("DEBUG: OpenProductPickerAsync - rozpoczęcie");

            var result = await _productPickerService.PickAsync(CurrentLK);

            _logger.LogInformation("DEBUG: Otrzymano wynik z pickera: {Result}",
                result != null ? $"KodId={result.KodId}, Kod={result.KodValue}" : "null");

            if (result is null)
            {
                _logger.LogInformation("DEBUG: Wynik jest null, kończenie");
                return;
            }

            // Ustaw w formularzu
            SelectedKodId = result.KodId;
            TowarKod = result.KodValue;
            TowarNazwa = result.KodNazwa;

            _logger.LogInformation("DEBUG: Ustawiono w formularzu - KodId={KodId}, Kod={Kod}, Nazwa={Nazwa}",
                SelectedKodId, TowarKod, TowarNazwa);

            if (result.PackagingUnit.HasValue && result.PackagingUnit > 0)
            {
                IloscWOpakowaniu = result.PackagingUnit.Value;
                if (IloscOpakowan > 0)
                    IloscSztuk = IloscOpakowan * IloscWOpakowaniu;

                _logger.LogInformation("DEBUG: Ustawiono ilości - IloscWOpakowaniu={Ilosc}", IloscWOpakowaniu);
            }

            if (result.CzyWymaganaPartia && string.IsNullOrWhiteSpace(Partia))
            {
                ErrorMessage = "Ten towar wymaga podania partii";
                _logger.LogInformation("DEBUG: Towar wymaga partii");
            }

            if (result.CzyWymaganaDataWaznosci && !DataWaznosci.HasValue)
            {
                ErrorMessage = string.IsNullOrEmpty(ErrorMessage) ?
                    "Ten towar wymaga podania daty ważności" :
                    ErrorMessage + "\nTen towar wymaga podania daty ważności";
                _logger.LogInformation("DEBUG: Towar wymaga daty ważności");
            }

            _logger.LogInformation("DEBUG: OpenProductPickerAsync - zakończone pomyślnie");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru towaru w pickerze");
            ErrorMessage = $"Błąd wyboru towaru: {ex.Message}";
        }
    }

    // Zachowujemy starą metodę wyszukiwania (opcjonalny fallback)
    [RelayCommand]
    private async Task SearchTowarAsync()
    {
        var query = TowarQuery;
        if (string.IsNullOrWhiteSpace(query) || query.Length < 2)
        {
            return;
        }

        try
        {
            KartotekaSearchResults.Clear();

            var results = await _receiveService.SearchKodyAsync(query, 10);
            foreach (var result in results)
            {
                KartotekaSearchResults.Add(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyszukiwania towaru");
            ErrorMessage = $"Błąd wyszukiwania: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task SelectKodAsync(KodDto kod)
    {
        await SetSelectedKodAsync(kod);
        IsKodSelectionModalVisible = false;
    }

    [RelayCommand]
    private async Task SelectAwizacjaPositionAsync(AwizacjaPositionDto position)
    {
        try
        {
            // Pobierz kartotekę po (kod, systemId)
            var kod = await _receiveService.GetKodByCodeAsync(position.TowarKod, position.SystemId);
            await SetSelectedKodAsync(kod);

            // Jeśli pozycja ma SSCC, ustaw jako current nosnik
            if (!string.IsNullOrWhiteSpace(position.SSCC))
            {
                CurrentNosnik = position.SSCC;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru pozycji awizacji");
            ErrorMessage = $"Błąd wyboru pozycji: {ex.Message}";
        }
    }

    [RelayCommand]
    private void ToggleTowarSource()
    {
        IsTowarSourceAwizacja = !IsTowarSourceAwizacja;
        ClearItemForm();

        if (IsTowarSourceAwizacja)
        {
            _ = Task.Run(LoadAwizacjaPositionsAsync);
        }
        else
        {
            KartotekaSearchResults.Clear();
        }
    }

    #endregion

    #region Commands - Quantity Shortcuts

    [RelayCommand]
    private async Task SelectAwizacjaGroupItemAsync(AwizacjaGroupedItem item)
    {
        try
        {
            var kod = await _receiveService.GetKodByCodeAsync(item.TowarKod, item.SystemId);
            await SetSelectedKodAsync(kod);

            if (!string.IsNullOrWhiteSpace(item.SSCC))
            {
                CurrentNosnik = item.SSCC;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru zgrupowanej pozycji awizacji");
            ErrorMessage = $"Błąd wyboru pozycji: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task QuickConfirmAsync()
    {
        // Enter w polu ilość = szybkie zatwierdzenie i pozostanie na bieżącym nośniku
        if (ValidateForm())
        {
            await RegisterPositionAsync();

            // Po udanej rejestracji pozostań na tym samym nośniku
            if (string.IsNullOrEmpty(ErrorMessage))
            {
                ClearItemForm();
                IsScanInputFocused = true;
            }
        }
    }

    #endregion

    #region Private Methods - Scan Processing

    private async Task ProcessGS1ScanAsync(string gs1Code)
    {
        try
        {
            // Pobranie ID dostawy z CurrentLK
            var listControlId = ParseLkToId(CurrentLK);

            // Użycie nowego endpointu
            var parsed = await _receiveService.ParseReceiveScanAsync(gs1Code, listControlId);

            if (!parsed.IsSuccess)
            {
                ErrorMessage = parsed.ErrorMessage ?? "Błąd parsowania kodu GS1";
                return;
            }

            // Ostrzeżenie o braku prefiksu IZ
            if (!parsed.HasIzPrefix)
            {
                var warning = "UWAGA: Nie wykryto prefiksu 'IZ' w kodzie GS1. Kod może nie być z Polski.";
                ErrorMessage = warning;
                _logger.LogWarning("Brak prefiksu IZ w kodzie GS1");
            }

            // Obsługa sugestii formularza
            if (parsed.FormSuggestion?.HasSuggestions == true)
            {
                await ApplyFormSuggestions(parsed.FormSuggestion);
            }

            // Obsługa parsowanych danych GS1
            if (parsed.ParsedData != null)
            {
                await ApplyGS1ParsedData(parsed.ParsedData, !parsed.HasIzPrefix);
            }

            // Wyświetlenie ostrzeżeń z sugestii
            if (!string.IsNullOrEmpty(parsed.FormSuggestion?.WarningMessage))
            {
                var existingError = ErrorMessage;
                ErrorMessage = string.IsNullOrEmpty(existingError) ?
                    parsed.FormSuggestion.WarningMessage :
                    $"{existingError}\n{parsed.FormSuggestion.WarningMessage}";
            }

            _logger.LogInformation("Przetworzono kod GS1. Sukces: {IsSuccess}, Typ: {ScanType}, IzPrefix: {HasIzPrefix}",
                parsed.IsSuccess, parsed.ScanType, parsed.HasIzPrefix);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania GS1");
            ErrorMessage = $"Błąd parsowania GS1: {ex.Message}";
        }
    }

    private async Task ApplyFormSuggestions(ReceiveFormSuggestion suggestion)
    {
        // Ustawienie kodu produktu
        if (suggestion.KodId.HasValue)
        {
            try
            {
                var kod = await _receiveService.GetKodByIdAsync(suggestion.KodId.Value);
                await SetSelectedKodAsync(kod);
                SuccessMessage = $"Wybrano towar: {kod.Nazwa}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Błąd podczas pobierania kodu {KodId}", suggestion.KodId);
                ErrorMessage = $"Nie można pobrać danych towaru (ID: {suggestion.KodId})";
            }
        }

        // Ustawienie partii
        if (!string.IsNullOrEmpty(suggestion.Lot))
        {
            Partia = suggestion.Lot;
        }

        // Ustawienie daty ważności
        if (!string.IsNullOrEmpty(suggestion.ExpiryDate) && DateTime.TryParse(suggestion.ExpiryDate, out var expiry))
        {
            DataWaznosci = expiry;
        }

        // Ustawienie ilości
        if (suggestion.SuggestedQuantity.HasValue)
        {
            IloscSztuk = suggestion.SuggestedQuantity.Value;

            // Przeliczenie na opakowania jeśli mamy packaging unit
            if (suggestion.PackagingUnit.HasValue && suggestion.PackagingUnit.Value > 0)
            {
                IloscWOpakowaniu = suggestion.PackagingUnit.Value;
                IloscOpakowan = Math.Round(IloscSztuk / IloscWOpakowaniu, 3);
            }
        }
    }

    private async Task ApplyGS1ParsedData(GS1ScanData gs1Data, bool showIzWarning)
    {
        // SSCC (AI 00)
        if (!string.IsNullOrWhiteSpace(gs1Data.SSCC))
        {
            CurrentNosnik = gs1Data.SSCC;
            var successMsg = $"Ustawiono nośnik: {gs1Data.SSCC}";
            if (showIzWarning)
            {
                successMsg += " (UWAGA: brak prefiksu IZ)";
            }
            SuccessMessage = successMsg;
        }

        // GTIN/EAN (AI 02) - jeśli nie został już obsłużony przez FormSuggestion
        if (!string.IsNullOrWhiteSpace(gs1Data.GTIN) && !SelectedKodId.HasValue)
        {
            try
            {
                var kody = await _receiveService.SearchKodyAsync(gs1Data.GTIN, 10);
                if (kody.Length == 1)
                {
                    await SetSelectedKodAsync(kody[0]);
                }
                else if (kody.Length > 1)
                {
                    // Pokaż selektor kodów
                    KodCandidates.Clear();
                    foreach (var candidate in kody)
                    {
                        KodCandidates.Add(candidate);
                    }
                    IsKodSelectionModalVisible = true;
                }
                else
                {
                    // Brak kandydatów dla GTIN
                    var errorMsg = $"Nie znaleziono towaru o kodzie GTIN: {gs1Data.GTIN}";
                    if (string.IsNullOrEmpty(ErrorMessage))
                    {
                        ErrorMessage = errorMsg;
                    }
                    else
                    {
                        ErrorMessage += "\n" + errorMsg;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Błąd podczas wyszukiwania GTIN: {GTIN}", gs1Data.GTIN);
            }
        }

        // Partia, data ważności, ilość - jeśli nie zostały już ustawione przez FormSuggestion
        if (!string.IsNullOrWhiteSpace(gs1Data.Lot) && string.IsNullOrEmpty(Partia))
        {
            Partia = gs1Data.Lot;
        }

        if (!string.IsNullOrEmpty(gs1Data.ExpiryDate) && !DataWaznosci.HasValue)
        {
            if (DateTime.TryParse(gs1Data.ExpiryDate, out var expiry))
            {
                DataWaznosci = expiry;
            }
        }

        if (gs1Data.Quantity.HasValue && IloscSztuk == 0 && IloscWOpakowaniu > 0)
        {
            IloscOpakowan = gs1Data.Quantity.Value;
            IloscSztuk = IloscOpakowan * IloscWOpakowaniu;
        }
    }

    private static int? ParseLkToId(string lk)
    {
        if (string.IsNullOrWhiteSpace(lk)) return null;
        var s = lk.Trim().ToUpperInvariant();
        if (s.StartsWith("LK")) s = s.Substring(2);
        return int.TryParse(s, out var id) ? id : null;
    }

    private async void ProcessSSCCScan(string ssccCode)
    {
        CurrentNosnik = ssccCode;
        _logger.LogInformation("Ustawiono nośnik SSCC: {SSCC}", ssccCode);

        // Spróbuj automatycznie wypełnić formularz danymi z awizacji
        try
        {
            var awizacjaData = await _receiveService.GetAwizacjaLabelDataAsync(ssccCode);

            if (awizacjaData != null)
            {
                // Wypełnij formularz danymi z awizacji
                if (awizacjaData.KodId.HasValue)
                {
                    var kod = await _receiveService.GetKodByIdAsync(awizacjaData.KodId.Value);
                    await SetSelectedKodAsync(kod);
                }

                Partia = awizacjaData.Lot ?? string.Empty;
                Certyfikat = awizacjaData.Blloc ?? string.Empty;

                if (!string.IsNullOrEmpty(awizacjaData.DataWaznosci) && DateTime.TryParse(awizacjaData.DataWaznosci, out var dataWaznosci))
                {
                    DataWaznosci = dataWaznosci;
                }

                if (awizacjaData.Ilosc.HasValue)
                {
                    IloscSztuk = (int)awizacjaData.Ilosc.Value;
                }

                var successMsg = $"Automatycznie wypełniono formularz z awizacji dla SSCC: {ssccCode}";
                if (!string.IsNullOrEmpty(awizacjaData.WarningMessage))
                {
                    successMsg += $"\nOstrzeżenie: {awizacjaData.WarningMessage}";
                }

                SuccessMessage = successMsg;
                _logger.LogInformation("Automatycznie wypełniono formularz z awizacji dla SSCC: {SSCC}, Kod: {Kod}",
                    ssccCode, awizacjaData.Kod);
            }
            else
            {
                SuccessMessage = $"Ustawiono nośnik SSCC: {ssccCode}\nNie znaleziono danych w awizacji - wypełnij formularz ręcznie";
                _logger.LogWarning("Nie znaleziono danych awizacji dla SSCC: {SSCC}", ssccCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania danych awizacji dla SSCC: {SSCC}", ssccCode);
            SuccessMessage = $"Ustawiono nośnik SSCC: {ssccCode}\nBłąd pobierania danych z awizacji - wypełnij formularz ręcznie";
        }
    }

    private void ProcessDSScan(string dsCode)
    {
        CurrentNosnik = dsCode;
        SuccessMessage = $"Ustawiono nośnik DS: {dsCode}";
        _logger.LogInformation("Ustawiono nośnik DS: {DS}", dsCode);
    }

    #endregion

    #region Private Methods - Form Management

private async Task SetSelectedKodAsync(KodDto kod)
    {
        // Upewnij się, że metoda jest asynchroniczna (CS1998)
        await Task.CompletedTask;
        SelectedKodId = kod.Id;
        TowarKod = kod.Kod;
        TowarNazwa = kod.Nazwa;
        IloscWOpakowaniu = kod.IloscWOpakowaniu;

        // Automatyczne przeliczenie ilości
        if (IloscOpakowan > 0)
        {
            IloscSztuk = IloscOpakowan * IloscWOpakowaniu;
        }

        // Walidacja wymagań
        if (kod.CzyWymaganaPartia && string.IsNullOrWhiteSpace(Partia))
        {
            ErrorMessage = "Ten towar wymaga podania partii";
        }

        if (kod.CzyWymaganaDataWaznosci && !DataWaznosci.HasValue)
        {
            ErrorMessage = "Ten towar wymaga podania daty ważności";
        }

        SuccessMessage = $"Wybrano towar: {kod.Nazwa}";
    }

    private bool ValidateForm()
    {
        ErrorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(CurrentLK))
        {
            ErrorMessage = "Brak aktywnej dostawy (LK)";
            return false;
        }

        if (!SelectedKodId.HasValue)
        {
            ErrorMessage = "Wybierz towar";
            return false;
        }

        if (!ReceiveCodeValidationService.IsValidQuantity(IloscSztuk))
        {
            ErrorMessage = "Wprowadź prawidłową ilość sztuk";
            return false;
        }

        return true;
    }

    private RegisterPositionRequest CreateRegisterPositionRequest()
    {
        return new RegisterPositionRequest
        {
            LK = CurrentLK,
            NosnikCode = CurrentNosnik == "(Auto)" ? null : CurrentNosnik,
            TypPaletyId = CurrentNosnik == "(Auto)" ? SelectedTypPaletyId : null,
            KodId = SelectedKodId!.Value,
            Partia = string.IsNullOrWhiteSpace(Partia) ? null : Partia.Trim(),
            DataWaznosci = DataWaznosci,
            DataProdukcji = DataProdukcji ?? default,
            Certyfikat = string.IsNullOrWhiteSpace(Certyfikat) ? null : Certyfikat.Trim(),
            StanJakosciId = SelectedStanJakosciId,
            IloscSztuk = IloscSztuk,
            DeviceId = DeviceInfo.Current.Name
        };
    }

private async Task ProcessRegistrationSuccess(RegisterPositionResponse response)
    {
        // Upewnij się, że metoda jest asynchroniczna (CS1998)
        await Task.CompletedTask;
        CurrentNosnik = response.NosnikCode;

        var actionText = response.WasCreated ? "utworzono nowy nośnik" : "dodano pozycję";
        SuccessMessage = $"Pomyślnie zarejestrowano pozycję ({actionText}): {response.NosnikCode}";

        ClearItemForm();
        IsScanInputFocused = true;

        _logger.LogInformation("Pomyślnie zarejestrowano pozycję na nośniku {Nosnik}", response.NosnikCode);
    }

    private void ClearItemForm()
    {
        TowarKod = string.Empty;
        TowarNazwa = string.Empty;
        SelectedKodId = null;
        Partia = string.Empty;
        DataWaznosci = null;
        DataProdukcji = null;
        Certyfikat = string.Empty;
        IloscSztuk = 0;
        IloscOpakowan = 0;
        IloscWOpakowaniu = 1;
        ErrorMessage = string.Empty;
    }

    private async Task InitializeAsync()
    {
        try
        {
            await LoadTypyPaletAsync();
            await LoadAwizacjaPositionsAsync();

            _logger.LogInformation("Zainicjalizowano rejestrację dla LK: {LK}", CurrentLK);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas inicjalizacji");
            ErrorMessage = "Błąd inicjalizacji widoku rejestracji";
        }
    }

    private async Task LoadTypyPaletAsync()
    {
        try
        {
            var typy = await _receiveService.GetTypyPaletAsync();

            MainThread.BeginInvokeOnMainThread(() =>
            {
                TypyPalet.Clear();
                foreach (var t in typy)
                {
                    if (t.Id != 0 && !string.Equals(t.Nazwa, "BRAK", StringComparison.OrdinalIgnoreCase))
                        TypyPalet.Add(t);
                }
                // Nie ustawiaj domyślnego typu - użytkownik wybiera ręcznie
                SelectedTypPaleta = null;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania typów palet");
        }
    }

    private async Task LoadAwizacjaPositionsAsync()
    {
        try
        {
            var positions = await _receiveService.GetAwizacjaPositionsAsync(CurrentLK);

            MainThread.BeginInvokeOnMainThread(() =>
            {
                AwizacjaPositions.Clear();
                foreach (var position in positions)
                {
                    AwizacjaPositions.Add(position);
                }

                // Grupowanie pozycji: po niepustych polach z zestawu {kod, lot, blloc, etykieta_klient}
                // Zawsze uwzględniamy kod (TowarKod), a z pozostałych pól dodajemy tylko te niepuste
                AwizacjaGroupedItems.Clear();
                var grouped = positions
                    .GroupBy(p => new
                    {
                        p.SystemId,
                        Kod = p.TowarKod,
                        Lot = string.IsNullOrWhiteSpace(p.Lot) ? null : p.Lot,
                        Blloc = string.IsNullOrWhiteSpace(p.Blloc) ? null : p.Blloc,
                        EtykietaKlient = string.IsNullOrWhiteSpace(p.EtykietaKlient) ? null : p.EtykietaKlient
                    })
                    .Select(g => new AwizacjaGroupedItem
                    {
                        SystemId = g.Key.SystemId,
                        TowarKod = g.Key.Kod,
                        TowarNazwa = g.First().TowarNazwa,
                        IloscAwizowana = g.Sum(x => x.IloscAwizowana),
                        IloscPrzyjeta = g.Sum(x => x.IloscPrzyjeta),
                        SSCC = g.Key.EtykietaKlient,
                        Lot = g.Key.Lot,
                        EtykietaKlient = g.Key.EtykietaKlient,
                        Blloc = g.Key.Blloc
                    });
                foreach (var gi in grouped)
                    AwizacjaGroupedItems.Add(gi);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania pozycji awizacji");
        }
    }

    #endregion

    #region Event Handlers

    private void OnPropertyChangedHandler(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // Automatyczne przeliczanie ilości
        if (e.PropertyName == nameof(IloscOpakowan) || e.PropertyName == nameof(IloscWOpakowaniu))
        {
            if (IloscOpakowan > 0 && IloscWOpakowaniu > 0)
            {
                IloscSztuk = IloscOpakowan * IloscWOpakowaniu;
            }
        }
        else if (e.PropertyName == nameof(IloscSztuk) && IloscWOpakowaniu > 0)
        {
            IloscOpakowan = Math.Round(IloscSztuk / IloscWOpakowaniu, 3);
        }
    }

    private void OnGS1Scanned(string gs1Code)
    {
        ScanInput = gs1Code;
        _ = Task.Run(ProcessScanAsync);
    }

    private void OnSSCCScanned(string ssccCode)
    {
        ScanInput = ssccCode;
        _ = Task.Run(ProcessScanAsync);
    }

    private void OnDSScanned(string dsCode)
    {
        ScanInput = dsCode;
        _ = Task.Run(ProcessScanAsync);
    }

    #endregion

    #region Modal Commands

    [RelayCommand]
    private void ClosePreviewModal()
    {
        IsPreviewModalVisible = false;
    }

    [RelayCommand]
    private void SwitchToCarrierTab()
    {
        SelectedPreviewTabIndex = 0;
    }

    [RelayCommand]
    private async Task SwitchToDeliveryTabAsync()
    {
        SelectedPreviewTabIndex = 1;
        // Załaduj pozycje awizacji jeśli jeszcze nie zostały załadowane
        if (AwizacjaGroupedItems.Count == 0 && IsTowarSourceAwizacja)
        {
            await LoadAwizacjaPositionsAsync();
        }
    }

    [RelayCommand]
    private async Task DeletePositionAsync(int positionId)
    {
        System.Diagnostics.Debug.WriteLine($"[DELETE] DeletePositionAsync wywołane dla pozycji: {positionId}");
        System.Diagnostics.Debug.WriteLine($"[DELETE] Używany serwis: {_receiveService.GetType().Name}");
        
        try
        {
            System.Diagnostics.Debug.WriteLine($"[DELETE] Pokazuję dialog potwierdzenia dla pozycji: {positionId}");
            
            var result = await Application.Current.MainPage.DisplayAlert(
                "Potwierdzenie",
                $"Czy na pewno chcesz usunąć pozycję {positionId}?",
                "Tak",
                "Nie");

            System.Diagnostics.Debug.WriteLine($"[DELETE] Wynik dialogu: {result}");

            if (!result) 
            {
                System.Diagnostics.Debug.WriteLine($"[DELETE] Użytkownik anulował usuwanie pozycji: {positionId}");
                return;
            }

            IsLoading = true;
            System.Diagnostics.Debug.WriteLine($"[DELETE] Rozpoczynam usuwanie pozycji: {positionId}");

            // Wywołaj API do usunięcia pozycji
            await _receiveService.DeleteLabelAsync(positionId);
            System.Diagnostics.Debug.WriteLine($"[DELETE] Pozycja {positionId} usunięta z API");

            // Odśwież podgląd
            System.Diagnostics.Debug.WriteLine($"[DELETE] Odświeżam podgląd po usunięciu pozycji: {positionId}");
            await ShowPreviewAsync();

            SuccessMessage = $"Pozycja {positionId} została usunięta";
            System.Diagnostics.Debug.WriteLine($"[DELETE] Sukces - pozycja {positionId} została usunięta");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas usuwania pozycji {PositionId}", positionId);
            System.Diagnostics.Debug.WriteLine($"[DELETE] BŁĄD podczas usuwania pozycji {positionId}: {ex.Message}");
            ErrorMessage = $"Błąd usuwania: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void CloseKodSelectionModal()
    {
        IsKodSelectionModalVisible = false;
    }

    [RelayCommand]
    private void TestDeleteCommand(int positionId)
    {
        System.Diagnostics.Debug.WriteLine($"[TEST] TestDeleteCommand wywołane dla pozycji: {positionId}");
        System.Diagnostics.Debug.WriteLine($"[TEST] Używany serwis: {_receiveService.GetType().Name}");
    }

    [RelayCommand]
    private void SimpleDeleteTest()
    {
        System.Diagnostics.Debug.WriteLine($"[SIMPLE] SimpleDeleteTest wywołane - przycisk działa!");
        System.Diagnostics.Debug.WriteLine($"[SIMPLE] Używany serwis: {_receiveService.GetType().Name}");
    }

    #endregion

    #region Dispose

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
WeakReferenceMessenger.Default.Unregister<Gs1ScannedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<SsccScannedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<DsScannedMessage>(this);
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    #endregion
}
