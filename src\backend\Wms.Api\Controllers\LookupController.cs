using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using Wms.Application.DTOs.Pallets;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Pallets.Queries;
using Wms.Application.Features.Kody.Queries;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler endpointów lookup dla aplikacji mobilnej
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/lookup")]
[Authorize]
[Produces("application/json")]
public class LookupController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<LookupController> _logger;

    public LookupController(IMediator mediator, ILogger<LookupController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Zwraca listę typów palet dla wyboru w UI
    /// </summary>
    [HttpGet("typy-palet")]
    [ProducesResponseType(typeof(List<TypPaletyLookupDto>), 200)]
    public async Task<ActionResult<List<TypPaletyLookupDto>>> GetTypyPalet()
    {
        var result = await _mediator.Send(new GetTypyPaletQuery());
        _logger.LogDebug("Zwrócono {Count} typów palet (lookup)", result.Count);
        return Ok(result);
    }

    /// <summary>
    /// Zwraca szczegóły kodu towaru po ID (kompatybilne z mobile)
    /// </summary>
    [HttpGet("kody/{id:int}")]
    [ProducesResponseType(typeof(KodDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<KodDto>> GetKodById([FromRoute] int id)
    {
        var result = await _mediator.Send(new GetKodByIdQuery(id));
        if (result == null)
        {
            return NotFound();
        }
        return Ok(result);
    }

    /// <summary>
    /// Wyszukuje kody po części nazwy lub kodu
    /// </summary>
    [HttpGet("kody/search")]
    [ProducesResponseType(typeof(List<KodDto>), 200)]
    public async Task<ActionResult<List<KodDto>>> SearchKody([FromQuery] string query, [FromQuery] int limit = 10)
    {
        var result = await _mediator.Send(new SearchKodyQuery(query, limit));
        return Ok(result);
    }

    /// <summary>
    /// Zwraca szczegóły kodu po wartości 'kod' (opcjonalnie filtrowane systemId)
    /// </summary>
    [HttpGet("kody/by-code/{kod}")]
    [ProducesResponseType(typeof(KodDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<KodDto>> GetKodByCode([FromRoute] string kod, [FromQuery] int? systemId = null)
    {
        var result = await _mediator.Send(new GetKodByCodeQuery(kod, systemId));
        if (result == null) return NotFound();
        return Ok(result);
    }
}

