namespace Wms.Domain.Exceptions;

/// <summary>
/// Bazowy wyjątek domenowy dla systemu WMS
/// </summary>
public abstract class DomainException : Exception
{
    protected DomainException(string message) : base(message) { }
    protected DomainException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Wyjątek gdy dostawa nie została znaleziona
/// </summary>
public class ReceiveNotFoundException : DomainException
{
    public int ReceiveId { get; }

    public ReceiveNotFoundException(int receiveId) 
        : base($"Dostawa LK{receiveId} nie została znaleziona")
    {
        ReceiveId = receiveId;
    }
}

/// <summary>
/// Wyjątek gdy dostawa jest już zajęta przez innego pracownika
/// </summary>
public class ReceiveAlreadyClaimedException : DomainException
{
    public int ReceiveId { get; }
    public int CurrentUserId { get; }

    public ReceiveAlreadyClaimedException(int receiveId, int currentUserId)
        : base($"Dostawa LK{receiveId} jest już zajęta przez pracownika {currentUserId}")
    {
        ReceiveId = receiveId;
        CurrentUserId = currentUserId;
    }
}

/// <summary>
/// Wyjątek gdy próbuje się zwolnić dostawę nie będąc jej właścicielem
/// </summary>
public class UnauthorizedReceiveReleaseException : DomainException
{
    public int ReceiveId { get; }
    public int RequestingUserId { get; }
    public int OwnerUserId { get; }

    public UnauthorizedReceiveReleaseException(int receiveId, int requestingUserId, int ownerUserId)
        : base($"Pracownik {requestingUserId} nie może zwolnić dostawy LK{receiveId} zajętej przez pracownika {ownerUserId}")
    {
        ReceiveId = receiveId;
        RequestingUserId = requestingUserId;
        OwnerUserId = ownerUserId;
    }
}

/// <summary>
/// Wyjątek przy nieprawidłowym formacie GS1
/// </summary>
public class InvalidGS1FormatException : DomainException
{
    public string RawData { get; }

    public InvalidGS1FormatException(string rawData, string details)
        : base($"Nieprawidłowy format GS1: {details}")
    {
        RawData = rawData;
    }
}

/// <summary>
/// Wyjątek przy nieprawidłowym kodzie SSCC
/// </summary>
public class InvalidSSCCException : DomainException
{
    public string InvalidSSCC { get; }

    public InvalidSSCCException(string invalidSSCC)
        : base($"Nieprawidłowy kod SSCC: {invalidSSCC}")
    {
        InvalidSSCC = invalidSSCC;
    }
}

/// <summary>
/// Wyjątek przy nieprawidłowym kodzie DS
/// </summary>
public class InvalidDSCodeException : DomainException
{
    public string InvalidCode { get; }

    public InvalidDSCodeException(string invalidCode)
        : base($"Nieprawidłowy kod DS: {invalidCode}")
    {
        InvalidCode = invalidCode;
    }
}

/// <summary>
/// Wyjątek przy nieprawidłowym kodzie LK
/// </summary>
public class InvalidLKCodeException : DomainException
{
    public string InvalidCode { get; }

    public InvalidLKCodeException(string invalidCode)
        : base($"Nieprawidłowy kod LK: {invalidCode}")
    {
        InvalidCode = invalidCode;
    }
}

/// <summary>
/// Wyjątek przy błędach generowania palet DS
/// </summary>
public class PalletGenerationException : DomainException
{
    public int ReceiveId { get; }

    public PalletGenerationException(int receiveId, string message)
        : base($"Błąd generowania palet dla dostawy LK{receiveId}: {message}")
    {
        ReceiveId = receiveId;
    }
}

/// <summary>
/// Wyjątek przy błędach druku etykiet
/// </summary>
public class PrintingException : DomainException
{
    public string PrinterIp { get; }

    public PrintingException(string printerIp, string message)
        : base($"Błąd druku na drukarce {printerIp}: {message}")
    {
        PrinterIp = printerIp;
    }

    public PrintingException(string printerIp, string message, Exception innerException)
        : base($"Błąd druku na drukarce {printerIp}: {message}", innerException)
    {
        PrinterIp = printerIp;
    }
}
