<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.MainPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:MainViewModel"
             Title="{loc:Translate Key=Main_Title}"
             BackgroundColor="{StaticResource PageBackgroundColor}">

    <ScrollView Padding="20">
        <StackLayout Spacing="20">
            
            <!-- Header z tytułem -->


            <!-- Menu Grid 2x4 -->
            <Grid RowDefinitions="Auto,Auto,Auto,Auto" 
                  ColumnDefinitions="*,*" 
                  RowSpacing="15" 
                  ColumnSpacing="15">

                <!-- Przyjęcie (niebieski) -->
                <Frame Grid.Row="0" Grid.Column="0" 
                       BackgroundColor="{StaticResource AcceptanceColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToAcceptanceCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="🚚" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Acceptance}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Wydania (zielony) -->
                <Frame Grid.Row="0" Grid.Column="1" 
                       BackgroundColor="{StaticResource IssuesColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToIssuesCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="📤" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Issues}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Operacje (fioletowy) -->
                <Frame Grid.Row="1" Grid.Column="0" 
                       BackgroundColor="{StaticResource OperationsColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToOperationsCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="📋" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Operations}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Zadania (pomarańczowy) -->
                <Frame Grid.Row="1" Grid.Column="1" 
                       BackgroundColor="{StaticResource TasksColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToTasksCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="📄" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Tasks}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Inwentaryzacja (żółty) -->
                <Frame Grid.Row="2" Grid.Column="0" 
                       BackgroundColor="{StaticResource InventoryColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToInventoryCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="❓" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Inventory}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Raporty (ciemnozielony) -->
                <Frame Grid.Row="2" Grid.Column="1" 
                       BackgroundColor="{StaticResource ReportsColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToReportsCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="📊" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Reports}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Przemieszczenia (fioletowy) -->
                <Frame Grid.Row="3" Grid.Column="0" 
                       BackgroundColor="{StaticResource MovementsColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToMovementsCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="🔄" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Movements}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

                <!-- Ustawienia (szary) -->
                <Frame Grid.Row="3" Grid.Column="1" 
                       BackgroundColor="{StaticResource SettingsColor}" 
                       CornerRadius="15" 
                       HasShadow="True"
                       Padding="15"
                       HeightRequest="120">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToSettingsCommand}" />
                    </Frame.GestureRecognizers>
                    <StackLayout VerticalOptions="Center" Spacing="8">
                        <Label Text="⚙️" 
                               FontSize="32" 
                               HorizontalOptions="Center" />
<Label Text="{loc:Translate Key=Main_Tile_Settings}" 
                               TextColor="White"
                               FontSize="14"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                    </StackLayout>
                </Frame>

            </Grid>

            <!-- Logout Button -->
            <Button Text="🚪 Wyloguj" 
                    Command="{Binding LogoutCommand}"
                    BackgroundColor="{StaticResource Danger}"
                    TextColor="White"
                    FontSize="16"
                    FontAttributes="Bold"
                    CornerRadius="10"
                    HeightRequest="50" 
                    Margin="0,20,0,0" />

        </StackLayout>
    </ScrollView>

</ContentPage>
