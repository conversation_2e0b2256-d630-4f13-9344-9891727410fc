import { test, expect } from '@playwright/test';

test.describe('WMS Inventory Simple Tests', () => {
  let authToken: string;

  test.beforeEach(async ({ request }) => {
    const response = await request.post('/api/v1/auth/login-scan', {
      data: {
        cardNumber: '1234567',
        deviceId: 'Playwright-Simple-Test'
      }
    });

    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    authToken = data.token;
  });

  test('should successfully authenticate and get inventory sessions', async ({ request }) => {
    console.log('🔍 Testing inventory API endpoints...');

    // Test getting inventory sessions
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    console.log(`📊 Sessions response status: ${sessionsResponse.status()}`);

    if (sessionsResponse.ok()) {
      const sessions = await sessionsResponse.json();
      console.log(`📋 Found ${sessions.length} inventory sessions`);

      expect(Array.isArray(sessions)).toBeTruthy();

      if (sessions.length > 0) {
        console.log(`✅ First session ID: ${sessions[0].id}`);
        console.log(`📝 First session description: ${sessions[0].opis}`);

        expect(sessions[0]).toHaveProperty('id');
        expect(sessions[0]).toHaveProperty('opis');
      } else {
        console.log('⚠️ No active inventory sessions found');
      }
    } else {
      console.log('❌ Failed to get inventory sessions');
    }
  });

  test('should test inventory scanning workflow', async ({ request }) => {
    console.log('🎯 Testing inventory scanning workflow...');

    // Get sessions first
    const sessionsResponse = await request.get('/api/v1/inventory/sessions', {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    expect(sessionsResponse.ok()).toBeTruthy();

    const sessions = await sessionsResponse.json();

    if (sessions && sessions.length > 0) {
      const sessionId = sessions[0].id;
      console.log(`🎯 Using session: ${sessionId} - ${sessions[0].opis}`);

      // Test SSCC scan
      console.log('📱 Testing SSCC scan: 123456789012345678');

      const ssccScanResponse = await request.post('/api/v1/inventory/scan', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          sessionId: sessionId,
          scanData: '123456789012345678',
          deviceId: 'Playwright-Test'
        }
      });

      console.log(`✅ SSCC scan response: ${ssccScanResponse.status()}`);

      if (ssccScanResponse.ok()) {
        const result = await ssccScanResponse.json();
        console.log(`📊 SSCC result: ${JSON.stringify(result, null, 2)}`);
        expect(result).toHaveProperty('success');
      }

      // Test DS code scan
      console.log('📱 Testing DS scan: DS123456');

      const dsScanResponse = await request.post('/api/v1/inventory/scan', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          sessionId: sessionId,
          scanData: 'DS123456',
          deviceId: 'Playwright-Test'
        }
      });

      console.log(`✅ DS scan response: ${dsScanResponse.status()}`);

      if (dsScanResponse.ok()) {
        const result = await dsScanResponse.json();
        console.log(`📊 DS result: ${JSON.stringify(result, null, 2)}`);
        expect(result).toHaveProperty('success');
      }

      console.log('🎉 Inventory scanning workflow completed successfully');
    } else {
      console.log('⚠️ Skipping scan tests - no inventory sessions available');
    }
  });
});