using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class LocationRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly LocationRepository _locationRepository;

    public LocationRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _locationRepository = new LocationRepository(_context);

        SeedTestData();
    }

    [Theory]
    [InlineData("MP-001-A-001-1", 1, "A", 1, "1")]
    [InlineData("MP-002-RMP-010-4", 2, "RMP", 10, "4")]
    [InlineData("MP-123-B-099-6", 123, "B", 99, "6")]
    public async Task GetByCodeAsync_WithValidLocationCode_ShouldReturnLocation(
        string locationCode, int expectedHala, string expectedRegal, int expectedMiejsce, string expectedPoziom)
    {
        // Act
        var result = await _locationRepository.GetByCodeAsync(locationCode);

        // Assert
        result.Should().NotBeNull();
        result!.Hala.Should().Be(expectedHala);
        result.Regal.Should().Be(expectedRegal);
        result.Miejsce.Should().Be(expectedMiejsce);
        result.Poziom.Should().Be(expectedPoziom);
        result.Code.Should().Be(locationCode);
    }

    [Theory]
    [InlineData("INVALID")]
    [InlineData("MP-1-A-1")] // Missing level
    [InlineData("1-A-1-1")] // Missing MP prefix
    [InlineData("MP-A-A-1-1")] // Invalid hall format
    [InlineData("MP-1-A-B-1")] // Invalid place format
    [InlineData("")]
    [InlineData(null)]
    public async Task GetByCodeAsync_WithInvalidLocationCode_ShouldReturnNull(string invalidLocationCode)
    {
        // Act
        var result = await _locationRepository.GetByCodeAsync(invalidLocationCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByCoordinatesAsync_WithValidCoordinates_ShouldReturnLocation()
    {
        // Arrange
        var hala = 1;
        var regal = "A";
        var miejsce = 1;
        var poziom = "1";

        // Act
        var result = await _locationRepository.GetByCoordinatesAsync(hala, regal, miejsce, poziom);

        // Assert
        result.Should().NotBeNull();
        result!.Hala.Should().Be(hala);
        result.Regal.Should().Be(regal);
        result.Miejsce.Should().Be(miejsce);
        result.Poziom.Should().Be(poziom);
    }

    [Fact]
    public async Task GetByCoordinatesAsync_WithInvalidCoordinates_ShouldReturnNull()
    {
        // Arrange
        var hala = 999;
        var regal = "INVALID";
        var miejsce = 999;
        var poziom = "INVALID";

        // Act
        var result = await _locationRepository.GetByCoordinatesAsync(hala, regal, miejsce, poziom);

        // Assert
        result.Should().BeNull();
    }

    [Theory]
    [InlineData(1, 1)]
    [InlineData(2, 2)]
    [InlineData(3, 123)]
    public async Task GetByIdAsync_WithValidId_ShouldReturnLocation(int locationId, int expectedHala)
    {
        // Act
        var result = await _locationRepository.GetByIdAsync(locationId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(locationId);
        result.Hala.Should().Be(expectedHala);
    }

    [Fact]
    public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var invalidId = 999;

        // Act
        var result = await _locationRepository.GetByIdAsync(invalidId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetVisibleLocationsAsync_ShouldReturnOnlyVisibleLocations()
    {
        // Act
        var result = await _locationRepository.GetVisibleLocationsAsync();

        // Assert
        result.Should().NotBeNull();
        var locations = result.ToList();
        locations.Should().HaveCount(3); // Only locations with Widoczne = 1 (IDs 1, 2, 3)
        locations.Should().AllSatisfy(l => l.Widoczne.Should().Be(1));
        locations.Should().Contain(l => l.Id == 1);
        locations.Should().Contain(l => l.Id == 2);
        locations.Should().Contain(l => l.Id == 3);
        locations.Should().NotContain(l => l.Id == 4); // Hidden location
    }

    [Fact]
    public async Task GetVisibleLocationsAsync_ShouldReturnLocationsInCorrectOrder()
    {
        // Act
        var result = await _locationRepository.GetVisibleLocationsAsync();

        // Assert
        var locations = result.ToList();
        locations.Should().HaveCount(3);
        
        // Should be ordered by Hala, then Regal, then Miejsce, then Poziom
        locations[0].Should().Match<Location>(l => 
            l.Hala == 1 && l.Regal == "A" && l.Miejsce == 1 && l.Poziom == "1");
        locations[1].Should().Match<Location>(l => 
            l.Hala == 2 && l.Regal == "RMP" && l.Miejsce == 10 && l.Poziom == "4");
        locations[2].Should().Match<Location>(l => 
            l.Hala == 123 && l.Regal == "B" && l.Miejsce == 99 && l.Poziom == "6");
    }

    [Fact]
    public async Task GetCurrentPalletCountAsync_WithLabelsInLocation_ShouldReturnCorrectCount()
    {
        // Arrange - Add some labels to location
        var locationId = 1;
        SeedLabelsForPalletCount(locationId);

        // Act
        var result = await _locationRepository.GetCurrentPalletCountAsync(locationId);

        // Assert
        result.Should().Be(2); // 2 distinct pallets in location 1
    }

    [Fact]
    public async Task GetCurrentPalletCountAsync_WithNoLabelsInLocation_ShouldReturnZero()
    {
        // Arrange
        var emptyLocationId = 999;

        // Act
        var result = await _locationRepository.GetCurrentPalletCountAsync(emptyLocationId);

        // Assert
        result.Should().Be(0);
    }

    [Fact]
    public async Task GetCurrentPalletCountAsync_WithInactiveLabels_ShouldNotCountThem()
    {
        // Arrange - Add inactive labels to location
        var locationId = 2;
        var inactiveLabels = new[]
        {
            new Label
            {
                Id = 100,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = 10,
                Active = 0, // Inactive
                Sscc = "inactive1"
            },
            new Label
            {
                Id = 101,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = 11,
                Active = 0, // Inactive
                Sscc = "inactive2"
            }
        };

        _context.Labels.AddRange(inactiveLabels);
        await _context.SaveChangesAsync();

        // Act
        var result = await _locationRepository.GetCurrentPalletCountAsync(locationId);

        // Assert
        result.Should().Be(0); // No active labels
    }

    [Fact]
    public async Task GetCurrentPalletCountAsync_WithMultipleLabelsForSamePallet_ShouldCountPalletOnce()
    {
        // Arrange - Add multiple labels for the same pallet
        var locationId = 3;
        var samePalletId = 100;
        
        var labelsForSamePallet = new[]
        {
            new Label
            {
                Id = 200,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = samePalletId,
                Active = 1,
                Sscc = "same_pallet_1"
            },
            new Label
            {
                Id = 201,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = samePalletId, // Same pallet
                Active = 1,
                Sscc = "same_pallet_2"
            }
        };

        _context.Labels.AddRange(labelsForSamePallet);
        await _context.SaveChangesAsync();

        // Act
        var result = await _locationRepository.GetCurrentPalletCountAsync(locationId);

        // Assert
        result.Should().Be(1); // Only one distinct pallet
    }

    [Theory]
    [InlineData(2, "RMP", 10, "4")]
    [InlineData(123, "B", 99, "6")]
    public async Task GetByCoordinatesAsync_WithValidCoordinatesFromTestData_ShouldWork(int hala, string regal, int miejsce, string? poziom)
    {
        // Act
        var result = await _locationRepository.GetByCoordinatesAsync(hala, regal, miejsce, poziom);

        // Assert
        result.Should().NotBeNull();
        result!.Poziom.Should().Be(poziom);
    }
    
    [Fact]
    public async Task GetByCoordinatesAsync_WithNullLevel_ShouldWork()
    {
        // Arrange - Add a location with null poziom
        var locationWithNullLevel = new Location
        {
            Id = 10,
            Hala = 5,
            Regal = "NULL",
            Miejsce = 1,
            Poziom = null, // Null level
            Widoczne = 1,
            MaxPojemnosc = 5
        };
        
        _context.Locations.Add(locationWithNullLevel);
        await _context.SaveChangesAsync();
        
        // Act
        var result = await _locationRepository.GetByCoordinatesAsync(5, "NULL", 1, null);

        // Assert
        result.Should().NotBeNull();
        result!.Poziom.Should().BeNull();
        result.Hala.Should().Be(5);
        result.Regal.Should().Be("NULL");
        result.Miejsce.Should().Be(1);
    }

    [Fact]
    public async Task GetByCodeAsync_WithComplexLocationCode_ShouldParseCorrectly()
    {
        // Arrange
        var complexLocationCode = "MP-12-ABC123-456-Z";

        // Add a location with these coordinates
        var complexLocation = new Location
        {
            Id = 100,
            Hala = 12,
            Regal = "ABC123",
            Miejsce = 456,
            Poziom = "Z",
            Widoczne = 1,
            MaxPojemnosc = 5
        };

        _context.Locations.Add(complexLocation);
        await _context.SaveChangesAsync();

        // Act
        var result = await _locationRepository.GetByCodeAsync(complexLocationCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(100);
        result.Hala.Should().Be(12);
        result.Regal.Should().Be("ABC123");
        result.Miejsce.Should().Be(456);
        result.Poziom.Should().Be("Z");
    }

    private void SeedTestData()
    {
        var locations = new[]
        {
            new Location
            {
                Id = 1,
                Hala = 1,
                Regal = "A",
                Miejsce = 1,
                Poziom = "1",
                Widoczne = 1,
                Zbiorka = 0,
                MaxPojemnosc = 10
            },
            new Location
            {
                Id = 2,
                Hala = 2,
                Regal = "RMP",
                Miejsce = 10,
                Poziom = "4",
                Widoczne = 1,
                Zbiorka = 1,
                MaxPojemnosc = 5
            },
            new Location
            {
                Id = 3,
                Hala = 123,
                Regal = "B",
                Miejsce = 99,
                Poziom = "6",
                Widoczne = 1,
                Zbiorka = 0,
                MaxPojemnosc = 20
            },
            new Location
            {
                Id = 4,
                Hala = 1,
                Regal = "HIDDEN",
                Miejsce = 1,
                Poziom = "1",
                Widoczne = 0, // Hidden location
                Zbiorka = 0,
                MaxPojemnosc = 1
            }
        };

        _context.Locations.AddRange(locations);
        _context.SaveChanges();
    }

    private void SeedLabelsForPalletCount(int locationId)
    {
        var labels = new[]
        {
            new Label
            {
                Id = 1,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = 1,
                Active = 1,
                Sscc = "test_sscc_1"
            },
            new Label
            {
                Id = 2,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = 1, // Same pallet as above
                Active = 1,
                Sscc = "test_sscc_2"
            },
            new Label
            {
                Id = 3,
                SystemId = 1,
                Miejscep = locationId,
                PaletaId = 2, // Different pallet
                Active = 1,
                Sscc = "test_sscc_3"
            }
        };

        _context.Labels.AddRange(labels);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
