using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WmsApp.Models;
using WmsApp.Services;
using WmsApp.Models.Auth;

namespace WmsApp.ViewModels;

public partial class LoginViewModel : ObservableObject
{
    private readonly IApiConfigurationService _apiConfigurationService;
    private readonly IAuthService _authService;
    private readonly ILocalizationService _localization;
    private IWmsApiService? _currentApiService;

    [ObservableProperty]
    private string cardNumber = string.Empty;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private bool isLoggedIn;

    [ObservableProperty]
    private ApiEnvironment selectedEnvironment;

    [ObservableProperty]
    private bool showEnvironmentSelector = true;
    
    [ObservableProperty]
    private string currentEnvironmentInfo = string.Empty;

public ObservableCollection<ApiEnvironment> AvailableEnvironments { get; }

public ObservableCollection<LanguageItem> AvailableLanguages { get; } = new()
{
    new LanguageItem("pl", "Polski"),
    new LanguageItem("en", "English"),
};

private LanguageItem _selectedLanguage = new("pl", "Polski");
public LanguageItem SelectedLanguage
{
    get => _selectedLanguage;
    set
    {
        if (SetProperty(ref _selectedLanguage, value))
        {
            _localization.SetCulture(value.Code);
        }
    }
}

public LoginViewModel(IApiConfigurationService apiConfigurationService, IAuthService authService, ILocalizationService localization)
{
    _apiConfigurationService = apiConfigurationService;
    _authService = authService;
    _localization = localization;
        
        AvailableEnvironments = new ObservableCollection<ApiEnvironment>
        {
            ApiEnvironment.Mock,
            ApiEnvironment.Localhost,
            ApiEnvironment.AndroidEmulator,
            ApiEnvironment.DevelopmentServer,
            ApiEnvironment.ProductionServer,
            ApiEnvironment.AndroidHost,
            ApiEnvironment.TC21Proxy
        };
        
        SelectedEnvironment = _apiConfigurationService.CurrentEnvironment;
        IsLoggedIn = false;

        // Ustaw język z zapisanej kultury
        var current = _localization.CurrentCulture.TwoLetterISOLanguageName.ToLowerInvariant();
        var match = AvailableLanguages.FirstOrDefault(l => l.Code == current) ?? AvailableLanguages[0];
        _selectedLanguage = match; // Set without triggering SetCulture again
        OnPropertyChanged(nameof(SelectedLanguage));
        
        // Initialize API service and diagnostics
        UpdateApiService();
    }

    [RelayCommand]
    private async Task ScanCardAsync()
    {
        try
        {
            ErrorMessage = string.Empty;
            
            // For now, just simulate scanning - implement later with barcode service
            await Task.Delay(1000); // Simulate scan time
            
            // Simulate scanned code
            CardNumber = "123456789";
            
            await LoginAsync();
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Scanning failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task LoginAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(CardNumber))
            {
                ErrorMessage = "Please enter or scan a card number";
                return;
            }

            IsLoading = true;
            ErrorMessage = string.Empty;

            // DEMO MODE: Skip API call and allow any card number
            if (IsValidDemoCardNumber(CardNumber))
            {
                IsLoggedIn = true;
                CardNumber = string.Empty;
                
                // Navigate to main page
                await Shell.Current.GoToAsync("//main");
                return;
            }

            // Try API call with current service
            try
            {
                if (_currentApiService == null)
                {
                    ErrorMessage = "API service not initialized";
                    return;
                }

                var baseUrl = _apiConfigurationService.CurrentEnvironment.GetBaseUrl();
                System.Diagnostics.Debug.WriteLine($"[WMS] Attempting login to: {baseUrl}");

                var request = new LoginScanRequest
                {
                    CardNumber = CardNumber,
                    DeviceId = DeviceInfo.Current.Name
                };

                System.Diagnostics.Debug.WriteLine($"[WMS] Sending request with card: {CardNumber}, DeviceId: {request.DeviceId}");
                System.Diagnostics.Debug.WriteLine($"[WMS] Request object: CardNumber='{request.CardNumber}', DeviceId='{request.DeviceId}'");
                
                var response = await _currentApiService.LoginScanAsync(request);
                System.Diagnostics.Debug.WriteLine($"[WMS] Response status: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"[WMS] Response.IsSuccessStatusCode: {response.IsSuccessStatusCode}");
                System.Diagnostics.Debug.WriteLine($"[WMS] Response.Content != null: {response.Content != null}");
                System.Diagnostics.Debug.WriteLine($"[WMS] Response.Error: {response.Error?.Content ?? "None"}");
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    // Zapisz token i dane użytkownika
                    await _authService.SetAuthenticationAsync(response.Content);
                    
                    System.Diagnostics.Debug.WriteLine($"[WMS] Login successful - User: {response.Content.User.FullName}, Token expires: {response.Content.ExpiresAt}");
                    
                    IsLoggedIn = true;
                    CardNumber = string.Empty;
                    
                    // Odśwież API service z nowym tokenem
                    UpdateApiService();
                    
                    // Navigate to main page
                    await Shell.Current.GoToAsync("//main");
                    return;
                }
            }
            catch (Exception apiEx)
            {
                // API failed - show helpful message with detailed error info
                var baseUrl = _apiConfigurationService.CurrentEnvironment.GetBaseUrl();
                System.Diagnostics.Debug.WriteLine($"[WMS] API Error: {apiEx}");
                
                var errorDetails = apiEx.InnerException?.Message ?? apiEx.Message;
                if (apiEx.Message.Contains("timeout") || apiEx.Message.Contains("network"))
                {
                    ErrorMessage = $"Network timeout - check WiFi connection.\nURL: {baseUrl}\nError: {errorDetails}";
                }
                else
                {
                    ErrorMessage = $"Backend API not available.\nURL: {baseUrl}\nError: {errorDetails}\nFor demo: DEMO or 123456";
                }
                return;
            }
            
            ErrorMessage = "Login failed. Please check your card number and try again.";
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Login error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            IsLoading = true;
            
            if (_currentApiService != null)
            {
                await _currentApiService.LogoutAsync();
            }
            
            // Wyczyść token i dane użytkownika
            await _authService.ClearAuthenticationAsync();
            
            IsLoggedIn = false;
            
            // Odśwież API service (bez tokena)
            UpdateApiService();
            
            await Shell.Current.GoToAsync("//login");
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Logout error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    partial void OnCardNumberChanged(string value)
    {
        ErrorMessage = string.Empty;
    }
    
    partial void OnSelectedEnvironmentChanged(ApiEnvironment value)
    {
        _apiConfigurationService.SetEnvironment(value);
        UpdateApiService();
        ErrorMessage = string.Empty;
    }
    
    private void UpdateApiService()
    {
        _currentApiService = _apiConfigurationService.CreateApiService();
        
        // Update environment info for diagnostics
        var env = _apiConfigurationService.CurrentEnvironment;
        var baseUrl = env.GetBaseUrl();
        var displayName = env.GetDisplayName();
        
        CurrentEnvironmentInfo = $"🔧 Current API: {displayName}\n📡 URL: {baseUrl}\n🔄 Service: {(_currentApiService?.GetType().Name == "MockWmsApiService" ? "Mock Data" : "Real API")}";
        
        System.Diagnostics.Debug.WriteLine($"[WMS] Updated API Service - Environment: {env}, URL: {baseUrl}, Service Type: {_currentApiService?.GetType().Name}");
    }
    
    private static bool IsValidDemoCardNumber(string cardNumber)
    {
        // Allow these demo card numbers to work without API
        var validDemoCards = new[] 
        { 
            "DEMO", 
            "123456", 
            "123456789", 
            "TEST", 
            "ADMIN", 
            "USER"
        };
        
        return validDemoCards.Contains(cardNumber.ToUpperInvariant());
    }
}
