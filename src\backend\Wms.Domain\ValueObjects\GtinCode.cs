using System.Text.RegularExpressions;
using Wms.Domain.Exceptions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący kod GTIN (Global Trade Item Number)
/// Format: 8, 12, 13, 14 cyfr zgodnie ze standardem GS1 (AI 02)
/// </summary>
public record GtinCode : IComparable<GtinCode>
{
    private static readonly Regex GtinPattern = new(@"^\d{8}$|^\d{12}$|^\d{13}$|^\d{14}$", RegexOptions.Compiled);
    
    public string Value { get; private init; }
    public int Length => Value.Length;
    public GtinType Type => Length switch
    {
        8 => GtinType.GTIN8,
        12 => GtinType.UPC,
        13 => GtinType.EAN13,
        14 => GtinType.GTIN14,
        _ => GtinType.Unknown
    };
    
    // GS1 GTIN check digit (last digit)
    public int CheckDigit => int.Parse(Value[^1..]);
    public string WithoutCheckDigit => Value[..^1];
    
    public string FormattedValue => Type switch
    {
        GtinType.GTIN8 => $"(02) {Value[0..4]} {Value[4..8]}",
        GtinType.UPC => $"(02) {Value[0..6]} {Value[6..12]}",
        GtinType.EAN13 => $"(02) {Value[0..1]} {Value[1..7]} {Value[7..12]} {Value[12]}",
        GtinType.GTIN14 => $"(02) {Value[0..1]} {Value[1..8]} {Value[8..13]} {Value[13]}",
        _ => $"(02) {Value}"
    };

    private GtinCode(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Tworzy GtinCode z ciągu cyfr
    /// </summary>
    public static GtinCode Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new InvalidGS1FormatException(value ?? "null", "Kod GTIN nie może być pusty");

        // Remove any non-digits
        var digitsOnly = Regex.Replace(value, @"[^\d]", "");
        
        if (!GtinPattern.IsMatch(digitsOnly))
            throw new InvalidGS1FormatException(value, $"Nieprawidłowy format GTIN. Oczekiwano 8, 12, 13 lub 14 cyfr, otrzymano: {digitsOnly.Length} cyfr");

        // Validate check digit
        if (!ValidateCheckDigit(digitsOnly))
            throw new InvalidGS1FormatException(value, "Nieprawidłowa cyfra kontrolna GTIN");

        return new GtinCode(digitsOnly);
    }

    /// <summary>
    /// Próbuje utworzyć GtinCode z stringa
    /// </summary>
    public static bool TryCreate(string value, out GtinCode? gtinCode)
    {
        gtinCode = null;
        
        if (!IsValid(value))
            return false;

        var digitsOnly = Regex.Replace(value, @"[^\d]", "");
        gtinCode = new GtinCode(digitsOnly);
        return true;
    }

    /// <summary>
    /// Waliduje czy string może być kodem GTIN
    /// </summary>
    public static bool IsValid(string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        var digitsOnly = Regex.Replace(value, @"[^\d]", "");
        
        if (!GtinPattern.IsMatch(digitsOnly))
            return false;

        return ValidateCheckDigit(digitsOnly);
    }

    /// <summary>
    /// Waliduje cyfrę kontrolną GTIN według algorytmu GS1
    /// </summary>
    private static bool ValidateCheckDigit(string gtin)
    {
        if (gtin.Length < 8)
            return false;

        var sum = 0;
        var multiplier = gtin.Length % 2 == 0 ? 3 : 1; // Start with 3 for even positions, 1 for odd

        // Calculate sum excluding check digit
        for (int i = 0; i < gtin.Length - 1; i++)
        {
            var digit = int.Parse(gtin[i].ToString());
            sum += digit * multiplier;
            multiplier = multiplier == 3 ? 1 : 3; // Alternate between 3 and 1
        }

        var calculatedCheckDigit = (10 - (sum % 10)) % 10;
        var providedCheckDigit = int.Parse(gtin[^1].ToString());

        return calculatedCheckDigit == providedCheckDigit;
    }

    /// <summary>
    /// Porównuje dwa kody GTIN na podstawie ich wartości numerycznych
    /// </summary>
    public int CompareTo(GtinCode? other)
    {
        if (other is null) return 1;
        
        // Porównaj najpierw długość, potem wartość
        var lengthComparison = Length.CompareTo(other.Length);
        if (lengthComparison != 0) return lengthComparison;
        
        return string.Compare(Value, other.Value, StringComparison.Ordinal);
    }

    public static bool operator <(GtinCode left, GtinCode right) => left.CompareTo(right) < 0;
    public static bool operator >(GtinCode left, GtinCode right) => left.CompareTo(right) > 0;
    public static bool operator <=(GtinCode left, GtinCode right) => left.CompareTo(right) <= 0;
    public static bool operator >=(GtinCode left, GtinCode right) => left.CompareTo(right) >= 0;

    public override string ToString() => Value;
    
    public static implicit operator string(GtinCode gtinCode) => gtinCode.Value;
    public static explicit operator GtinCode(string value) => Create(value);
}

/// <summary>
/// Rodzaje kodów GTIN
/// </summary>
public enum GtinType
{
    Unknown = 0,
    GTIN8 = 8,    // EAN-8
    UPC = 12,     // UPC-A
    EAN13 = 13,   // EAN-13
    GTIN14 = 14   // ITF-14
}
