using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;

namespace Wms.Api.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
[Produces("application/json")]
public class PalletsController : BaseApiController
{
    private readonly IPalletService _palletService;
    private readonly ILogger<PalletsController> _logger;

    public PalletsController(IPalletService palletService, ILogger<PalletsController> logger)
    {
        _palletService = palletService;
        _logger = logger;
    }

    /// <summary>
    /// Przenosi paletę do nowej lokalizacji
    /// </summary>
    /// <param name="palletCode">Kod palety (SSCC lub DS)</param>
    /// <param name="request">Dane ruchu palety</param>
    /// <returns>Informacje o wykonanym ruchu</returns>
    [HttpPost("{palletCode}/move")]
    [ProducesResponseType(typeof(MovePalletResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<MovePalletResponse>> MovePallet(
        [FromRoute] string palletCode,
        [FromBody] MovePalletRequest request)
    {
        try
        {
            // Ustawienie kodu palety z route'a
            request.PalletCode = palletCode;

            var userId = int.Parse(GetCurrentUserId());
            var deviceId = GetDeviceId();
            var ipAddress = GetClientIpAddress();

            var result = await _palletService.MovePalletAsync(request, userId, deviceId, ipAddress);

            _logger.LogInformation("Pallet {PalletCode} successfully moved to {ToLocation} by user {UserId}", 
                palletCode, request.ToLocationCode, userId);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid input for pallet move: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowe dane",
                Detail = ex.Message,
                Status = 400
            });
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning("Resource not found for pallet move: {Message}", ex.Message);
            return NotFound(new ProblemDetails
            {
                Title = "Zasób nie znaleziony",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Invalid operation for pallet move: {Message}", ex.Message);
            return Conflict(new ProblemDetails
            {
                Title = "Operacja niedozwolona",
                Detail = ex.Message,
                Status = 409
            });
        }
    }

    /// <summary>
    /// Pobiera informacje o palecie
    /// </summary>
    /// <param name="palletCode">Kod palety (SSCC lub DS)</param>
    /// <returns>Szczegółowe informacje o palecie</returns>
    [HttpGet("{palletCode}")]
    [ProducesResponseType(typeof(PalletInfo), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<PalletInfo>> GetPallet([FromRoute] string palletCode)
    {
        try
        {
            var result = await _palletService.GetPalletInfoAsync(palletCode);
            
            if (result == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Paleta nie znaleziona",
                    Detail = $"Paleta {palletCode} nie została znaleziona w systemie",
                    Status = 404
                });
            }

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid pallet code format: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowe dane",
                Detail = ex.Message,
                Status = 400
            });
        }
    }
}
