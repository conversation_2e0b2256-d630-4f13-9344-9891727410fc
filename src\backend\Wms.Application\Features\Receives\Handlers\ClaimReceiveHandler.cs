using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Interfaces;
using Wms.Domain.Exceptions;
using Wms.Domain.Services;

namespace Wms.Application.Features.Receives.Handlers;

public class ClaimReceiveHandler : IRequestHandler<ClaimReceiveCommand, ClaimReceiveResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ReceiveDomainService _receiveDomainService;
    private readonly ILogger<ClaimReceiveHandler> _logger;

    public ClaimReceiveHandler(
        IReceiveRepository receiveRepository,
        IUnitOfWork unitOfWork,
        ReceiveDomainService receiveDomainService,
        ILogger<ClaimReceiveHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _unitOfWork = unitOfWork;
        _receiveDomainService = receiveDomainService;
        _logger = logger;
    }

    public async Task<ClaimReceiveResponse> Handle(ClaimReceiveCommand request, CancellationToken cancellationToken)
    {
        var receive = await _receiveRepository.GetByIdAsync(request.ReceiveId, cancellationToken);
        
        if (receive == null)
        {
            throw new ReceiveNotFoundException(request.ReceiveId);
        }

        // Sprawdź czy można zająć dostawę
        var canClaimResult = _receiveDomainService.CanClaimReceive(receive, request.PracownikId);
        if (!canClaimResult.IsSuccess)
        {
            return new ClaimReceiveResponse
            {
                Success = false,
                Message = canClaimResult.ErrorMessage ?? "Nie można zająć dostawy"
            };
        }

        try
        {
            var claimedAt = DateTime.UtcNow;
            
            // Zajmij dostawę za pomocą atomowej operacji w repository
            var claimed = await _receiveRepository.ClaimReceiveAsync(request.ReceiveId, request.PracownikId, cancellationToken);
            
            if (!claimed)
            {
                return new ClaimReceiveResponse
                {
                    Success = false,
                    Message = "Dostawa została już zajęta przez innego pracownika"
                };
            }

            _logger.LogInformation("Repository claim sukces, wywołuję UnitOfWork.SaveChangesAsync() dla dostawy LK{ReceiveId}", request.ReceiveId);
            
            try
            {
                await _unitOfWork.SaveChangesAsync();
                _logger.LogInformation("UnitOfWork.SaveChangesAsync() zakończony pomyślnie dla LK{ReceiveId}", request.ReceiveId);
            }
            catch (Exception saveEx)
            {
                _logger.LogError(saveEx, "BŁĄD w UnitOfWork.SaveChangesAsync() dla LK{ReceiveId}: {ErrorMessage}", 
                    request.ReceiveId, saveEx.Message);
                throw;
            }
            
            // Weryfikacja czy claim rzeczywiście się zapisał w bazie
            var verifyReceive = await _receiveRepository.GetByIdAsync(request.ReceiveId, cancellationToken);
            var actualAssignedUserId = verifyReceive?.RealizujacyPracownikId;
            
            _logger.LogInformation("Dostawa LK{ReceiveId} została zajęta przez pracownika {UserId}. Weryfikacja w bazie: RealizujacyPracownikId={ActualUserId}", 
                request.ReceiveId, request.PracownikId, actualAssignedUserId);
                
            if (actualAssignedUserId != request.PracownikId)
            {
                _logger.LogError("KRYTYCZNY BŁĄD: Claim zwrócił sukces, ale w bazie RealizujacyPracownikId={ActualUserId}, oczekiwano {ExpectedUserId}", 
                    actualAssignedUserId, request.PracownikId);
            }

            return new ClaimReceiveResponse
            {
                Success = true,
                Message = $"Dostawa LK{request.ReceiveId} została pomyślnie zajęta",
                ClaimedAt = claimedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas zajmowania dostawy LK{ReceiveId} przez pracownika {UserId}", 
                request.ReceiveId, request.PracownikId);
            throw;
        }
    }
}

public class ReleaseReceiveHandler : IRequestHandler<ReleaseReceiveCommand, ClaimReceiveResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ReceiveDomainService _receiveDomainService;
    private readonly ILogger<ReleaseReceiveHandler> _logger;

    public ReleaseReceiveHandler(
        IReceiveRepository receiveRepository,
        IUnitOfWork unitOfWork,
        ReceiveDomainService receiveDomainService,
        ILogger<ReleaseReceiveHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _unitOfWork = unitOfWork;
        _receiveDomainService = receiveDomainService;
        _logger = logger;
    }

    public async Task<ClaimReceiveResponse> Handle(ReleaseReceiveCommand request, CancellationToken cancellationToken)
    {
        var receive = await _receiveRepository.GetByIdAsync(request.ReceiveId, cancellationToken);
        
        if (receive == null)
        {
            throw new ReceiveNotFoundException(request.ReceiveId);
        }

        // Sprawdź czy można zwolnić dostawę
        var canReleaseResult = _receiveDomainService.CanReleaseReceive(receive, request.PracownikId);
        if (!canReleaseResult.IsSuccess)
        {
            return new ClaimReceiveResponse
            {
                Success = false,
                Message = canReleaseResult.ErrorMessage ?? "Nie można zwolnić dostawy"
            };
        }

        try
        {
            var releasedAt = DateTime.UtcNow;
            
            // Zwolnij dostawę za pomocą atomowej operacji w repository
            var released = await _receiveRepository.ReleaseReceiveAsync(request.ReceiveId, request.PracownikId, cancellationToken);
            
            if (!released)
            {
                return new ClaimReceiveResponse
                {
                    Success = false,
                    Message = "Nie można zwolnić dostawy - może być przypisana do innego pracownika"
                };
            }

            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Dostawa LK{ReceiveId} została zwolniona przez pracownika {UserId}", 
                request.ReceiveId, request.PracownikId);

            return new ClaimReceiveResponse
            {
                Success = true,
                Message = $"Dostawa LK{request.ReceiveId} została pomyślnie zwolniona",
                ClaimedAt = releasedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas zwalniania dostawy LK{ReceiveId} przez pracownika {UserId}", 
                request.ReceiveId, request.PracownikId);
            throw;
        }
    }
}
