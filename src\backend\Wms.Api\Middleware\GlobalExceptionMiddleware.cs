using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Text.Json;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Exceptions;

namespace Wms.Api.Middleware;

public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;
    private readonly ILocalizationService? _localizationService;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, ILocalizationService? localizationService = null)
    {
        _next = next;
        _logger = logger;
        _localizationService = localizationService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var problemDetails = exception switch
        {
            // Domain exceptions for Receives module
            ReceiveNotFoundException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.4",
                Title = GetLocalizedMessage(ResourceKeys.Receive.NotFound) ?? "Dostawa nie znaleziona",
                Status = (int)HttpStatusCode.NotFound,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            ReceiveAlreadyClaimedException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.8",
                Title = GetLocalizedMessage(ResourceKeys.Receive.AlreadyClaimed) ?? "Dostawa już przypisana",
                Status = (int)HttpStatusCode.Conflict,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            UnauthorizedReceiveReleaseException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7235#section-3.1",
                Title = "Brak uprawnień do zwolnienia dostawy",
                Status = (int)HttpStatusCode.Unauthorized,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            InvalidGS1FormatException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "Nieprawidłowy format GS1",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            InvalidSSCCException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "Nieprawidłowy kod SSCC",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            InvalidDSCodeException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "Nieprawidłowy kod DS",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            PalletGenerationException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "Błąd generowania palet",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            PrintingException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.6.3",
                Title = "Błąd druku",
                Status = (int)HttpStatusCode.ServiceUnavailable,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            // General exceptions
            ArgumentException or ArgumentNullException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "Bad Request",
                Status = (int)HttpStatusCode.BadRequest,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            UnauthorizedAccessException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7235#section-3.1",
                Title = "Unauthorized",
                Status = (int)HttpStatusCode.Unauthorized,
                Detail = "Authentication required",
                Instance = context.Request.Path
            },
            KeyNotFoundException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.4",
                Title = "Not Found",
                Status = (int)HttpStatusCode.NotFound,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            InvalidOperationException => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.8",
                Title = "Conflict",
                Status = (int)HttpStatusCode.Conflict,
                Detail = exception.Message,
                Instance = context.Request.Path
            },
            _ => new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.6.1",
                Title = "Internal Server Error",
                Status = (int)HttpStatusCode.InternalServerError,
                Detail = "An error occurred while processing your request",
                Instance = context.Request.Path
            }
        };

        context.Response.StatusCode = problemDetails.Status ?? (int)HttpStatusCode.InternalServerError;

        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        var json = JsonSerializer.Serialize(problemDetails, options);
        await context.Response.WriteAsync(json);
    }

    /// <summary>
    /// Pobiera zlokalizowany komunikat lub zwraca null jeśli serwis lokalizacji jest niedostępny
    /// </summary>
    private string? GetLocalizedMessage(string key)
    {
        return _localizationService?.GetString(key);
    }
}
