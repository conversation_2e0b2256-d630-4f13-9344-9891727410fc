using WmsApp.ViewModels.Inventory;

namespace WmsApp.Views.Inventory;

public partial class InventoryProductPage : ContentPage
{
    public InventoryProductPage(InventoryProductViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (BindingContext is InventoryProductViewModel vm)
        {
            await vm.InitializeCommand.ExecuteAsync(null);
        }
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public InventoryProductViewModel VM => (InventoryProductViewModel)BindingContext;
}
