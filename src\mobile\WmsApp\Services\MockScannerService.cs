using WmsApp.Models.Receives;

using CommunityToolkit.Mvvm.Messaging;
using WmsApp.Messages;

namespace WmsApp.Services;

/// <summary>
/// Mock serwis skanowania dla emulatorów i testów
/// Pozwala na testowanie funkcjonalności skanowania bez fizycznego skanera
/// </summary>
public interface IMockScannerService
{
    /// <summary>
    /// Sprawdza czy mock scanner jest aktywny
    /// </summary>
    bool IsActive { get; }
    
    /// <summary>
    /// Symuluje skanowanie kodu
    /// </summary>
    void SimulateScan(string scanData);
    
    /// <summary>
    /// Symuluje skanowanie kodu z określonym typem
    /// </summary>
    void SimulateScan(string scanData, ReceiveScanCodeType codeType);
    
    /// <summary>
    /// Generuje przykładowy kod do testów
    /// </summary>
    string GenerateTestCode(ReceiveScanCodeType codeType);
}

public class MockScannerService : IMockScannerService
{
    private readonly IDeviceDetectionService _deviceDetection;
    private readonly Random _random = new();

    public MockScannerService(IDeviceDetectionService deviceDetection)
    {
        _deviceDetection = deviceDetection;
    }

    public bool IsActive => _deviceDetection.IsEmulator || !_deviceDetection.IsScanningAvailable;

    public void SimulateScan(string scanData)
    {
        if (!IsActive)
        {
            System.Diagnostics.Debug.WriteLine("[MockScanner] Not active on this device");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[MockScanner] Simulating scan: {scanData}");
        
        // Powiadom o przetworzonym skanie (bez określonego typu)
WeakReferenceMessenger.Default.Send(new ScanProcessedMessage(new WmsApp.Models.Receives.ReceiveScanResult
        {
            Code = scanData,
            Type = WmsApp.Models.Receives.ReceiveScanCodeType.Unknown,
            IsValid = true,
            CleanedCode = scanData
        }));
    }

    public void SimulateScan(string scanData, ReceiveScanCodeType codeType)
    {
        if (!IsActive)
        {
            System.Diagnostics.Debug.WriteLine("[MockScanner] Not active on this device");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[MockScanner] Simulating {codeType} scan: {scanData}");
        
        // Wyślij specyficzny typ skanu
// Wyślij wiadomość odpowiedniego typu
switch (codeType)
{
    case ReceiveScanCodeType.GS1:
        WeakReferenceMessenger.Default.Send(new Gs1ScannedMessage(scanData));
        break;
    case ReceiveScanCodeType.SSCC:
        WeakReferenceMessenger.Default.Send(new SsccScannedMessage(scanData));
        break;
    case ReceiveScanCodeType.DS:
        WeakReferenceMessenger.Default.Send(new DsScannedMessage(scanData));
        break;
    case ReceiveScanCodeType.LK:
        WeakReferenceMessenger.Default.Send(new LkScannedMessage(scanData));
        break;
    case ReceiveScanCodeType.PrinterIP:
        WeakReferenceMessenger.Default.Send(new PrinterIpScannedMessage(scanData));
        break;
    default:
        WeakReferenceMessenger.Default.Send(new ScanProcessedMessage(new WmsApp.Models.Receives.ReceiveScanResult { Code = scanData, Type = ReceiveScanCodeType.Unknown, IsValid = false }));
        break;
}

    }

    public string GenerateTestCode(ReceiveScanCodeType codeType)
    {
        return codeType switch
        {
            ReceiveScanCodeType.GS1 => GenerateGS1Code(),
            ReceiveScanCodeType.SSCC => GenerateSSCCCode(),
            ReceiveScanCodeType.DS => GenerateDSCode(),
            ReceiveScanCodeType.LK => GenerateLKCode(),
            ReceiveScanCodeType.PrinterIP => GeneratePrinterIPCode(),
            _ => "TESTCODE123"
        };
    }

    private string GenerateGS1Code()
    {
        // Przykładowy GS1 code z GTIN, LOT, EXP
        var gtin = $"01{_random.Next(10000000, 99999999):D8}{_random.Next(1000, 9999):D4}";
        var lot = $"10LOT{_random.Next(100, 999)}";
        var exp = $"17{DateTime.Now.AddYears(1):yyMMdd}"; // Ważny rok od teraz
        
        return $"{gtin}{lot}{exp}";
    }

    private string GenerateSSCCCode()
    {
        // 20-cyfrowy kod SSCC
        var baseCode = $"00{_random.Next(100000000, 999999999):D9}{_random.Next(1000000000, int.MaxValue):D10}";
        return baseCode.Substring(0, 20);
    }

    private string GenerateDSCode()
    {
        // Kod dokumentu spedycyjnego
        return $"DS{DateTime.Now:yyyyMMdd}{_random.Next(1000, 9999)}";
    }

    private string GenerateLKCode()
    {
        // Lista księgowy
        return $"LK{_random.Next(100, 999):D3}";
    }

    private string GeneratePrinterIPCode()
    {
        // IP drukarki
        return $"192.168.1.{_random.Next(100, 199)}";
    }
}
