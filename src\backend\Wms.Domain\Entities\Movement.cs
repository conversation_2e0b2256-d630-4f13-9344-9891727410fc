using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class Movement : BaseEntity
{
    public int Id { get; set; }
    public string Typ { get; set; } = string.Empty;
    public int DocNr { get; set; } = 0;
    public int? PracownikId { get; set; } // Foreign Key do pracownicy.id
    public DateOnly Data { get; set; } = DateOnly.FromDateTime(DateTime.Now);
    public int Etykieta { get; set; } = 0; // Foreign Key do etykiety.id
    public int? SystemId { get; set; }
    public int StareM { get; set; } = 0; // Foreign Key do miejsca.id (stara lokalizacja)
    public int NoweM { get; set; } = 0; // Foreign Key do miejsca.id (nowa lokalizacja)
    public string? DocInternal { get; set; }
    public int? Stat { get; set; } = 0;
    public DateTime Tszm { get; set; } = DateTime.UtcNow; // Timestamp zmiany
    public string Uwagi { get; set; } = string.Empty;
    
    // Navigation properties
    public User? User { get; set; } // pracownik_id -> pracownicy
    public Label? Label { get; set; } // etykieta -> etykiety
    public Location? FromLocation { get; set; } // stare_m -> miejsca
    public Location? ToLocation { get; set; } // nowe_m -> miejsca
}
