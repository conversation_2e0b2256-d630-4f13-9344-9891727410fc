using WmsApp.ViewModels;

namespace WmsApp.Views;

public partial class PalletsPage : ContentPage
{
    public PalletsPage(PalletsViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public PalletsViewModel VM => (PalletsViewModel)BindingContext;

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is PalletsViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}
