using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WmsApp.Models.Receives;
using WmsApp.Services.Receives;

using CommunityToolkit.Mvvm.Messaging;
using WmsApp.Messages;

namespace WmsApp.ViewModels.Receives;

/// <summary>
/// ViewModel dla widoku selekcji dostaw
/// Obsługuje wprowadzanie/skanowanie LK, listę dostaw i generowanie DS
/// </summary>
public partial class ReceivesSelectionViewModel : ObservableObject
{
    private readonly IReceiveService _receiveService;
    private readonly ReceiveCodeValidationService _validationService;
    private readonly ILogger<ReceivesSelectionViewModel> _logger;

    #region Observable Properties

    [ObservableProperty]
    private string lkInput = string.Empty;

    [ObservableProperty]
    private bool isLoading;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    [ObservableProperty]
    private string successMessage = string.Empty;

    [ObservableProperty]
    private ReceiveDto? selectedReceive;

    [ObservableProperty]
    private bool isGenerateDsModalVisible;

    [ObservableProperty]
    private int selectedTypPaletyId;

    [ObservableProperty]
    private int iloscPalet = 1;

    [ObservableProperty]
    private string selectedDrukarkaNazwa = string.Empty;

    [ObservableProperty]
    private bool czyDrukowac;

    [ObservableProperty]
    private string[] generatedDsCodes = Array.Empty<string>();

    [ObservableProperty]
    private bool isGeneratedDsVisible;

    #endregion

    #region Collections

    public ObservableCollection<ReceiveDto> AvailableReceives { get; } = new();
    public ObservableCollection<TypPaletyDto> TypyPalet { get; } = new();
    public ObservableCollection<string> Drukarki { get; } = new();

    #endregion

    public ReceivesSelectionViewModel(
        IReceiveService receiveService,
        ReceiveCodeValidationService validationService,
        ILogger<ReceivesSelectionViewModel> logger)
    {
        _receiveService = receiveService;
        _validationService = validationService;
        _logger = logger;

        _logger.LogInformation("🚀 ReceivesSelectionViewModel utworzony. ClaimReceiveCommand będzie dostępne.");

// Subskrypcje skanów (WeakReferenceMessenger)
        WeakReferenceMessenger.Default.Register<LkScannedMessage>(this, (r, m) => OnLKScanned(m.Value));
        WeakReferenceMessenger.Default.Register<PrinterIpScannedMessage>(this, (r, m) => OnPrinterIPScanned(m.Value));
    }

    #region Commands

    [RelayCommand]
    private async Task LoadDataAsync()
    {
        await LoadReceivesAsync();
        await LoadTypyPaletAsync();
        await LoadDrukarkiAsync();
    }

    [RelayCommand]
    private async Task LoadReceivesAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var receives = await _receiveService.GetAvailableReceivesAsync();

            AvailableReceives.Clear();
            foreach (var receive in receives)
            {
                AvailableReceives.Add(receive);
            }

            _logger.LogInformation("Załadowano {Count} dostępnych dostaw", receives.Length);

            // Loguj pierwsze 3 dostawy żeby sprawdzić IsOccupied
            for (int i = 0; i < Math.Min(3, receives.Length); i++)
            {
                var receive = receives[i];
                _logger.LogInformation("🔍 Dostawa {Index}: LK={LK}, IsOccupied={IsOccupied}", i + 1, receive.LK, receive.IsOccupied);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania listy dostaw");
            ErrorMessage = $"Nie udało się załadować listy dostaw: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task SelectReceiveAsync(ReceiveDto receive)
    {
        if (receive == null) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;

            // Spróbuj zaclaimować dostawę
            var deviceId = DeviceInfo.Current.Name;
            var claimed = await _receiveService.ClaimReceiveAsync(receive.LK, deviceId);

            if (claimed)
            {
                SelectedReceive = receive;
                LkInput = receive.LK;
                SuccessMessage = $"Dostawa {receive.LK} została przypisana do tego urządzenia";

                _logger.LogInformation("Pomyślnie zaclaimowano dostawę {LK}", receive.LK);

                // Przejdź do widoku rejestracji
                await NavigateToRegistrationAsync();
            }
            else
            {
                ErrorMessage = $"Dostawa {receive.LK} jest już zajęta przez innego operatora";
                _logger.LogWarning("Nie udało się zaclaimować dostawy {LK} - zajęta przez innego", receive.LK);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyboru dostawy {LK}", receive.LK);
            ErrorMessage = $"Błąd podczas wyboru dostawy: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ClaimReceiveAsync(ReceiveDto receive)
    {
        _logger.LogInformation("🔥🔥🔥 CLAIM RECEIVE COMMAND WYWOŁANE! Receive: {Receive}", receive?.LK ?? "NULL");

        if (receive == null)
        {
            _logger.LogWarning("ClaimReceiveAsync wywołane z null receive");
            return;
        }

        try
        {
            _logger.LogInformation("🔥 PRZYCISK REALIZUJ KLIKNIĘTY! Dostawa: {LK}, IsOccupied: {IsOccupied}", receive.LK, receive.IsOccupied);
            IsLoading = true;
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;

            // Spróbuj zaclaimować dostawę
            var deviceId = DeviceInfo.Current.Name;
            _logger.LogInformation("🚀 Wywołuję _receiveService.ClaimReceiveAsync({LK}, {DeviceId})", receive.LK, deviceId);

            var claimed = await _receiveService.ClaimReceiveAsync(receive.LK, deviceId);

            _logger.LogInformation("_receiveService.ClaimReceiveAsync zwrócił: {Claimed}", claimed);

            if (claimed)
            {
                SelectedReceive = receive;
                LkInput = receive.LK;
                SuccessMessage = $"Dostawa {receive.LK} została przypisana do tego urządzenia";

                _logger.LogInformation("Pomyślnie zaclaimowano dostawę {LK} z przycisku Realizuj", receive.LK);

                // Odśwież listę dostaw
                await LoadReceivesAsync();

                // Przejdź do widoku rejestracji
                await NavigateToRegistrationAsync();
            }
            else
            {
                ErrorMessage = $"Dostawa {receive.LK} jest już zajęta przez innego operatora";
                _logger.LogWarning("Nie udało się zaclaimować dostawy {LK} - zajęta przez innego", receive.LK);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas claimowania dostawy {LK}", receive.LK);
            ErrorMessage = $"Błąd podczas przypisywania dostawy: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
            _logger.LogInformation("ClaimReceiveAsync zakończone dla dostawy {LK}", receive?.LK);
        }
    }

    [RelayCommand]
    private async Task ValidateLKAsync()
    {
        try
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(LkInput))
            {
                ErrorMessage = "Wprowadź lub zeskanuj numer LK";
                return;
            }
            
            // Walidacja formatu LK
            var scanResult = _validationService.ProcessScan(LkInput.Trim().ToUpper());
                
            if (scanResult.Type != ReceiveScanCodeType.LK || !scanResult.IsValid)
            {
                ErrorMessage = "Nieprawidłowy format LK. Oczekiwano: LK + liczba";
                return;
            }

            // Spróbuj zaclaimować dostawę
            var deviceId = DeviceInfo.Current.Name;
            var claimed = await _receiveService.ClaimReceiveAsync(LkInput.Trim(), deviceId);

            if (claimed)
            {
                SuccessMessage = $"Dostawa {LkInput} została przypisana do tego urządzenia";
                _logger.LogInformation("Pomyślnie zaclaimowano dostawę {LK} z pola tekstowego", LkInput);
                
                // Przejdź do widoku rejestracji
                await NavigateToRegistrationAsync();
            }
            else
            {
                ErrorMessage = $"Dostawa {LkInput} jest już zajęta przez innego operatora lub nie istnieje";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas walidacji LK {LK}", LkInput);
            ErrorMessage = $"Błąd podczas walidacji LK: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task ShowGenerateDsModalAsync()
    {
        if (string.IsNullOrWhiteSpace(LkInput))
        {
            ErrorMessage = "Najpierw wybierz lub wprowadź dostawę (LK)";
            return;
        }

        await LoadTypyPaletAsync();
        await LoadDrukarkiAsync();
        
        // Reset formularza
        IloscPalet = 1;
        SelectedTypPaletyId = TypyPalet.FirstOrDefault()?.Id ?? 0;
        SelectedDrukarkaNazwa = string.Empty;
        CzyDrukowac = false;
        
        IsGenerateDsModalVisible = true;
    }

    [RelayCommand]
    private void CloseGenerateDsModal()
    {
        IsGenerateDsModalVisible = false;
        IsGeneratedDsVisible = false;
        GeneratedDsCodes = Array.Empty<string>();
    }

    [RelayCommand]
    private async Task GenerateDsAsync()
    {
        try
        {
            ErrorMessage = string.Empty;
            
            // Walidacje
            if (!ReceiveCodeValidationService.IsValidPalletCount(IloscPalet))
            {
                ErrorMessage = "Ilość palet musi być między 1 a 100";
                return;
            }

            if (SelectedTypPaletyId <= 0)
            {
                ErrorMessage = "Wybierz typ palety";
                return;
            }

            IsLoading = true;

            var result = await _receiveService.GenerateDsAsync(
                LkInput.Trim(),
                SelectedTypPaletyId,
                IloscPalet,
                !string.IsNullOrWhiteSpace(SelectedDrukarkaNazwa) ? SelectedDrukarkaNazwa : null,
                CzyDrukowac);

            if (result.GeneratedCodes.Length > 0)
            {
                GeneratedDsCodes = result.GeneratedCodes;
                IsGeneratedDsVisible = true;
                
                var printInfo = result.PrintSuccess ? "wydrukowano" : "nie udało się wydrukować";
                SuccessMessage = $"Wygenerowano {result.GeneratedCodes.Length} kodów DS" + 
                               (CzyDrukowac ? $" ({printInfo})" : "");
                
                _logger.LogInformation("Wygenerowano {Count} kodów DS dla {LK}", 
                    result.GeneratedCodes.Length, LkInput);
            }
            else
            {
                ErrorMessage = "Nie udało się wygenerować kodów DS";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas generowania DS dla {LK}", LkInput);
            ErrorMessage = $"Błąd podczas generowania DS: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ProceedToRegistrationAsync()
    {
        CloseGenerateDsModal();
        await NavigateToRegistrationAsync();
    }

    #endregion

    #region Private Methods

    private async Task LoadTypyPaletAsync()
    {
        try
        {
            var typy = await _receiveService.GetTypyPaletAsync();
            
            TypyPalet.Clear();
            foreach (var typ in typy)
            {
                TypyPalet.Add(typ);
            }
            
            if (SelectedTypPaletyId == 0 && TypyPalet.Count > 0)
            {
                SelectedTypPaletyId = TypyPalet.First().Id;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania typów palet");
        }
    }

    private async Task LoadDrukarkiAsync()
    {
        try
        {
            var drukarki = await _receiveService.GetDrukarkiAsync();
            
            Drukarki.Clear();
            foreach (var drukarka in drukarki)
            {
                Drukarki.Add(drukarka);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas ładowania listy drukarek");
        }
    }

    private async Task NavigateToRegistrationAsync()
    {
        try
        {
            var route = $"//receivesregistration?lk={Uri.EscapeDataString(LkInput.Trim())}";
            await Shell.Current.GoToAsync(route);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas nawigacji do rejestracji");
            ErrorMessage = "Błąd nawigacji do widoku rejestracji";
        }
    }

    private void OnLKScanned(string lkCode)
    {
        try
        {
            var scanResult = _validationService.ProcessScan(lkCode);
            if (scanResult.Type == ReceiveScanCodeType.LK && scanResult.IsValid)
            {
                LkInput = lkCode;
                _logger.LogInformation("Zeskanowano LK: {LK}", lkCode);
                
                // Auto-walidacja po skanie
                _ = Task.Run(async () => await ValidateLKAsync());
            }
            else
            {
                ErrorMessage = "Nieprawidłowy kod LK";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanowanego LK");
            ErrorMessage = "Błąd przetwarzania kodu LK";
        }
    }

    private void OnPrinterIPScanned(string ipCode)
    {
        try
        {
            var scanResult = _validationService.ProcessScan(ipCode);
            if (scanResult.Type == ReceiveScanCodeType.PrinterIP && scanResult.IsValid)
            {
                SelectedDrukarkaNazwa = scanResult.CleanedCode ?? ipCode;
                _logger.LogInformation("Zeskanowano IP drukarki: {IP}", SelectedDrukarkaNazwa);
                SuccessMessage = $"Wybrano drukarkę: {SelectedDrukarkaNazwa}";
            }
            else
            {
                ErrorMessage = "Nieprawidłowy kod drukarki IP";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania IP drukarki");
            ErrorMessage = "Błąd przetwarzania kodu drukarki";
        }
    }

    #endregion

    #region Dispose

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
WeakReferenceMessenger.Default.Unregister<LkScannedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<PrinterIpScannedMessage>(this);
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    #endregion
}
