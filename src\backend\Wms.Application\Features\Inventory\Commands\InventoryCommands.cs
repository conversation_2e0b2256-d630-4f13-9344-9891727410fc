using MediatR;
using Wms.Application.DTOs.Inventory;

namespace Wms.Application.Features.Inventory.Commands;

/// <summary>
/// Komenda rozpoczęcia inwentaryzacji
/// </summary>
public record StartInventoryCommand : IRequest<InventorySessionDto>
{
    public int InventoryId { get; init; }
    public int PracownikId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public InventoryType Type { get; init; }
}

/// <summary>
/// Komenda skanowania w inwentaryzacji
/// </summary>
public record ProcessInventoryScanCommand : IRequest<InventoryScanResponse>
{
    public int InventorySessionId { get; init; }
    public string ScanData { get; init; } = string.Empty;
    public int PracownikId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
}

/// <summary>
/// Komenda dodania pozycji inwentaryzacji
/// </summary>
public record CreateInventoryItemCommand : IRequest<InventoryItemDto>
{
    public int InventoryId { get; init; }
    public int PracownikId { get; init; }
    public string? EtykietaId { get; init; }
    public string? PaletaId { get; init; }
    public string? Kod { get; init; }
    public decimal IloscSpisana { get; init; }
    public int Hala { get; init; }
    public int Regal { get; init; }
    public int Miejsce { get; init; }
    public int Poziom { get; init; }
    public string? Podkod { get; init; }
    public string? Skan { get; init; }
    public string? NrSap { get; init; }
}

/// <summary>
/// Komenda aktualizacji pozycji inwentaryzacji
/// </summary>
public record UpdateInventoryItemCommand : IRequest<InventoryItemDto>
{
    public int InventoryItemId { get; init; }
    public decimal IloscSpisana { get; init; }
    public int PracownikId { get; init; }
    public int Hala { get; init; }
    public int Regal { get; init; }
    public int Miejsce { get; init; }
    public int Poziom { get; init; }
    public string? Podkod { get; init; }
    public string? Skan { get; init; }
}

/// <summary>
/// Komenda zakończenia sesji inwentaryzacji
/// </summary>
public record EndInventorySessionCommand : IRequest<bool>
{
    public int InventorySessionId { get; init; }
    public int PracownikId { get; init; }
}
