using Wms.Application.DTOs;
using Wms.Application.Features.Receives.Commands;

namespace Wms.Application.Interfaces;

/// <summary>
/// Serwis do obsługi skanów z urządzeń (DataWedge, inne skanery)
/// </summary>
public interface IScanInputService
{
    /// <summary>
    /// Wysyłane gdy otrzymano nowy skan z urządzenia
    /// </summary>
    event EventHandler<ScanReceivedEventArgs>? ScanReceived;
    
    /// <summary>
    /// Inicjalizuje serwis i rozpoczyna nasłuchiwanie skanów
    /// </summary>
    Task StartListeningAsync();
    
    /// <summary>
    /// Zatrzymuje nasłuchiwanie skanów
    /// </summary>
    Task StopListeningAsync();
    
    /// <summary>
    /// Sprawdza czy serwis jest aktywny
    /// </summary>
    bool IsListening { get; }
    
    /// <summary>
    /// Konfiguruje profil DataWedge dla aplikacji
    /// </summary>
    Task ConfigureDataWedgeProfileAsync(string profileName, string packageName);
    
    /// <summary>
    /// Przetwarza surowy skan i normalizuje dane
    /// </summary>
    ScanInputData ProcessRawScan(string rawData, string? deviceInfo = null);
    
    /// <summary>
    /// Wysyła skan do parsowania i przetwarzania
    /// </summary>
    Task<ParseReceiveScanResponse> ProcessScanForDeliveryAsync(
        string scanData, 
        int? listControlId = null, 
        string? deviceId = null);
}

/// <summary>
/// Dane eventi skanowania
/// </summary>
public class ScanReceivedEventArgs : EventArgs
{
    public string RawData { get; }
    public string? DeviceInfo { get; }
    public DateTime Timestamp { get; }
    public ScanInputData ProcessedData { get; }
    
    public ScanReceivedEventArgs(string rawData, string? deviceInfo, ScanInputData processedData)
    {
        RawData = rawData;
        DeviceInfo = deviceInfo;
        ProcessedData = processedData;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Znormalizowane dane skanu
/// </summary>
public record ScanInputData
{
    public string CleanData { get; init; } = string.Empty;
    public string OriginalData { get; init; } = string.Empty;
    public ScanSource Source { get; init; } = ScanSource.Unknown;
    public string? DeviceInfo { get; init; }
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public bool HasControlChars { get; init; }
    public bool RequiresNormalization { get; init; }
    
    /// <summary>
    /// Tworzy znormalizowane dane skanu
    /// </summary>
    public static ScanInputData Create(string rawData, ScanSource source = ScanSource.DataWedge, string? deviceInfo = null)
    {
        var cleanData = NormalizeRawData(rawData);
        var hasControlChars = rawData != cleanData;
        
        return new ScanInputData
        {
            CleanData = cleanData,
            OriginalData = rawData,
            Source = source,
            DeviceInfo = deviceInfo,
            HasControlChars = hasControlChars,
            RequiresNormalization = hasControlChars
        };
    }
    
    /// <summary>
    /// Normalizuje surowe dane ze skanera
    /// </summary>
    private static string NormalizeRawData(string rawData)
    {
        if (string.IsNullOrEmpty(rawData))
            return string.Empty;
            
        // Usuń znaki końca linii i znaki kontrolne
        var cleaned = rawData.Trim()
            .Replace("\r", "")
            .Replace("\n", "")
            .Replace("\t", "");
            
        // Usuń nieprintowalne znaki ASCII (poza FNC1 który to char 29)
        cleaned = System.Text.RegularExpressions.Regex.Replace(
            cleaned, 
            @"[\x00-\x08\x0A-\x1C\x1E-\x1F\x7F-\x9F]", 
            "");
            
        return cleaned;
    }
}

/// <summary>
/// Źródło skanu
/// </summary>
public enum ScanSource
{
    Unknown = 0,
    DataWedge = 1,     // Zebra DataWedge
    EMDK = 2,          // Zebra EMDK
    CameraApi = 3,     // Camera API scan
    Manual = 4,        // Ręczne wprowadzenie
    Keyboard = 5       // Klawiatura/wedge mode
}
