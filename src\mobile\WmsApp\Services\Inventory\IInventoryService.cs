using System.Collections.ObjectModel;
using Microsoft.Extensions.Logging;
using WmsApp.Models.Inventory;

namespace WmsApp.Services.Inventory;

/// <summary>
/// Interfejs serwisu inwentaryzacji
/// </summary>
public interface IInventoryService
{
    // Zarządzanie inwentaryzacjami
    Task<InventoryDto[]> GetActiveInventoriesAsync(InventoryType? type = null);
    Task<InventoryDetailsDto?> GetInventoryDetailsAsync(int inventoryId);
    Task<InventoryProgressDto> GetInventoryProgressAsync(int inventoryId);
    
    // Sesje inwentary<PERSON>ji
    Task<InventorySessionDto> StartInventorySessionAsync(int inventoryId, string deviceId);
    Task<bool> EndInventorySessionAsync(int sessionId);
    
    // Skanowanie i pozycje
    Task<InventoryScanResponse> ProcessScanAsync(int sessionId, string scanData, string deviceId);
    Task<InventoryItemDto?> SearchInventoryLabelAsync(int inventoryId, string? etykietaId, string? nrSap, string? paletaId);
    Task<InventoryItemDto[]> GetInventoryItemsAsync(int inventoryId, int? pracownikId = null, bool onlyCompleted = false);
    
    // Operacje na pozycjach
    Task<InventoryItemDto> CreateInventoryItemAsync(int inventoryId, InventoryItemRequest request);
    Task<InventoryItemDto> UpdateInventoryItemAsync(int itemId, InventoryItemRequest request);
    
    // Wyszukiwanie kodów produktów
    Task<ProductCodeDto?> SearchProductCodeAsync(string code, int systemId);
    
    // Opcje inwentaryzacji dla menu
    Task<InventoryOptionDto[]> GetInventoryOptionsAsync();
}

/// <summary>
/// Implementacja serwisu inwentaryzacji
/// </summary>
public class InventoryService : IInventoryService
{
    private readonly IInventoryApiClient _apiClient;
    private readonly ILogger<InventoryService> _logger;

    public InventoryService(IInventoryApiClient apiClient, ILogger<InventoryService> logger)
    {
        _apiClient = apiClient;
        _logger = logger;
    }

    public async Task<InventoryDto[]> GetActiveInventoriesAsync(InventoryType? type = null)
    {
        try
        {
            _logger.LogInformation("Pobieranie aktywnych inwentaryzacji, typ: {Type}", type);

            var response = await _apiClient.GetActiveInventoriesAsync(type);

            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content.Data ?? Array.Empty<InventoryDto>();
            }

            _logger.LogWarning("Błąd pobierania inwentaryzacji: {Error}", response.Error?.Content);
            return Array.Empty<InventoryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania aktywnych inwentaryzacji");
            throw;
        }
    }

    public async Task<InventoryDetailsDto?> GetInventoryDetailsAsync(int inventoryId)
    {
        try
        {
            _logger.LogInformation("Pobieranie szczegółów inwentaryzacji {InventoryId}", inventoryId);

var response = await _apiClient.GetInventoryDetailsAsync(inventoryId);

            if (response.IsSuccessStatusCode)
            {
                return response.Content?.Data;
            }

            _logger.LogWarning("Błąd pobierania szczegółów inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania szczegółów inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<InventoryProgressDto> GetInventoryProgressAsync(int inventoryId)
    {
        try
        {
            _logger.LogInformation("Pobieranie postępu inwentaryzacji {InventoryId}", inventoryId);

var response = await _apiClient.GetInventoryProgressAsync(inventoryId);

            if (response.IsSuccessStatusCode && response.Content?.Data != null)
            {
                return response.Content.Data;
            }

            _logger.LogWarning("Błąd pobierania postępu inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);

            return new InventoryProgressDto
            {
                InwentaryzacjaId = inventoryId,
                TotalItems = 0,
                CompletedItems = 0,
                CompletionPercentage = 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania postępu inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<InventorySessionDto> StartInventorySessionAsync(int inventoryId, string deviceId)
    {
        try
        {
            _logger.LogInformation("Rozpoczynanie sesji inwentaryzacji {InventoryId} na urządzeniu {DeviceId}",
                inventoryId, deviceId);

            var request = new StartInventoryRequest
            {
                InventoryId = inventoryId,
                DeviceId = deviceId
            };

var response = await _apiClient.StartInventorySessionAsync(request);

            if (response.IsSuccessStatusCode && response.Content?.Data != null)
            {
                return response.Content.Data;
            }

            _logger.LogError("Błąd rozpoczynania sesji inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);

            throw new InvalidOperationException($"Nie udało się rozpocząć sesji inwentaryzacji: {response.Error?.Content}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas rozpoczynania sesji inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<bool> EndInventorySessionAsync(int sessionId)
    {
        try
        {
            _logger.LogInformation("Kończenie sesji inwentaryzacji {SessionId}", sessionId);

            var response = await _apiClient.EndInventorySessionAsync(sessionId);

            if (response.IsSuccessStatusCode)
            {
                return response.Content;
            }

            _logger.LogWarning("Błąd kończenia sesji inwentaryzacji {SessionId}: {Error}",
                sessionId, response.Error?.Content);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas kończenia sesji inwentaryzacji {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<InventoryScanResponse> ProcessScanAsync(int sessionId, string scanData, string deviceId)
    {
        try
        {
            _logger.LogInformation("Przetwarzanie skanu w sesji {SessionId}: {ScanData}", sessionId, scanData);

            var request = new InventoryScanRequest
            {
                InventorySessionId = sessionId,
                ScanData = scanData,
                DeviceId = deviceId
            };

var response = await _apiClient.ProcessScanAsync(request);

            if (response.IsSuccessStatusCode && response.Content?.Data != null)
            {
                _logger.LogInformation("Pomyślnie przetworzono skan - typ: {ScanType}, wynik: {IsSuccess}",
                    response.Content.Data.ScanType, response.Content.Data.IsSuccess);

                return response.Content.Data;
            }

            _logger.LogWarning("Błąd przetwarzania skanu w sesji {SessionId}: {Error}",
                sessionId, response.Error?.Content);

            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Błąd przetwarzania skanu: {response.Error?.Content ?? "Nieznany błąd"}",
                ScanType = ScanType.Unknown,
                RequiresNewEntry = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanu w sesji {SessionId}", sessionId);

            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = "Wystąpił błąd podczas przetwarzania skanu",
                ScanType = ScanType.Unknown,
                RequiresNewEntry = false
            };
        }
    }

    public async Task<InventoryItemDto?> SearchInventoryLabelAsync(int inventoryId, string? etykietaId, string? nrSap, string? paletaId)
    {
        try
        {
            _logger.LogInformation("Wyszukiwanie etykiety w inwentaryzacji {InventoryId}", inventoryId);

var response = await _apiClient.SearchInventoryLabelAsync(
                inventoryId, etykietaId, nrSap, paletaId);

            if (response.IsSuccessStatusCode)
            {
                return response.Content?.Data;
            }

            _logger.LogWarning("Błąd wyszukiwania etykiety w inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyszukiwania etykiety w inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<InventoryItemDto[]> GetInventoryItemsAsync(int inventoryId, int? pracownikId = null, bool onlyCompleted = false)
    {
        try
        {
            _logger.LogInformation("Pobieranie pozycji inwentaryzacji {InventoryId}", inventoryId);

var response = await _apiClient.GetInventoryItemsAsync(
                inventoryId, pracownikId, onlyCompleted);

            if (response.IsSuccessStatusCode && response.Content != null)
            {
                return response.Content.Data ?? Array.Empty<InventoryItemDto>();
            }

            _logger.LogWarning("Błąd pobierania pozycji inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);
            return Array.Empty<InventoryItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania pozycji inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<InventoryItemDto> CreateInventoryItemAsync(int inventoryId, InventoryItemRequest request)
    {
        try
        {
            _logger.LogInformation("Tworzenie pozycji inwentaryzacji {InventoryId}", inventoryId);

var response = await _apiClient.CreateInventoryItemAsync(inventoryId, request);

            if (response.IsSuccessStatusCode && response.Content?.Data != null)
            {
                return response.Content.Data;
            }

            _logger.LogError("Błąd tworzenia pozycji inwentaryzacji {InventoryId}: {Error}",
                inventoryId, response.Error?.Content);

            throw new InvalidOperationException($"Nie udało się utworzyć pozycji inwentaryzacji: {response.Error?.Content}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas tworzenia pozycji inwentaryzacji {InventoryId}", inventoryId);
            throw;
        }
    }

    public async Task<InventoryItemDto> UpdateInventoryItemAsync(int itemId, InventoryItemRequest request)
    {
        try
        {
            _logger.LogInformation("Aktualizacja pozycji inwentaryzacji {ItemId}", itemId);

var response = await _apiClient.UpdateInventoryItemAsync(itemId, request);

            if (response.IsSuccessStatusCode && response.Content?.Data != null)
            {
                return response.Content.Data;
            }

            _logger.LogError("Błąd aktualizacji pozycji inwentaryzacji {ItemId}: {Error}",
                itemId, response.Error?.Content);

            throw new InvalidOperationException($"Nie udało się zaktualizować pozycji inwentaryzacji: {response.Error?.Content}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas aktualizacji pozycji inwentaryzacji {ItemId}", itemId);
            throw;
        }
    }

    public async Task<ProductCodeDto?> SearchProductCodeAsync(string code, int systemId)
    {
        try
        {
            _logger.LogInformation("Wyszukiwanie kodu produktu: {Code}", code);

            var response = await _apiClient.SearchProductCodeAsync(code, systemId);

            if (response.IsSuccessStatusCode)
            {
                return response.Content;
            }

            _logger.LogWarning("Błąd wyszukiwania kodu produktu {Code}: {Error}",
                code, response.Error?.Content);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyszukiwania kodu produktu: {Code}", code);
            throw;
        }
    }

    public async Task<InventoryOptionDto[]> GetInventoryOptionsAsync()
    {
        try
        {
            _logger.LogInformation("Pobieranie opcji inwentaryzacji");
            System.Diagnostics.Debug.WriteLine("[INVENTORY_SERVICE] GetInventoryOptionsAsync called");

            // Zwracamy statyczną listę opcji inwentaryzacji
            var options = new[]
            {
                new InventoryOptionDto
                {
                    Type = InventoryType.Product,
                    Name = "Inwentaryzacja towaru",
                    Description = "Inwentaryzacja konkretnych produktów",
                    Icon = "📦",
                    IsEnabled = true
                },
                new InventoryOptionDto
                {
                    Type = InventoryType.General,
                    Name = "Inwentaryzacja ogólna",
                    Description = "Kompleksowa inwentaryzacja z pełną kontrolą",
                    Icon = "📋",
                    IsEnabled = false // Tymczasowo wyłączone
                },
                new InventoryOptionDto
                {
                    Type = InventoryType.Location,
                    Name = "Inwentaryzacja miejsc",
                    Description = "Inwentaryzacja lokalizacji magazynowych",
                    Icon = "📍",
                    IsEnabled = false // Tymczasowo wyłączone
                },
                new InventoryOptionDto
                {
                    Type = InventoryType.GG,
                    Name = "Inwentaryzacja GG",
                    Description = "Specjalny typ inwentaryzacji",
                    Icon = "🔍",
                    IsEnabled = false // Tymczasowo wyłączone
                }
            };

            System.Diagnostics.Debug.WriteLine($"[INVENTORY_SERVICE] Returning {options.Length} options");
            foreach (var option in options)
            {
                System.Diagnostics.Debug.WriteLine($"[INVENTORY_SERVICE] Option: {option.Name}, Enabled: {option.IsEnabled}");
            }

            return options;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania opcji inwentaryzacji");
            System.Diagnostics.Debug.WriteLine($"[INVENTORY_SERVICE] ERROR: {ex.Message}");
            throw;
        }
    }
}
