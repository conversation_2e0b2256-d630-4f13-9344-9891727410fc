using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Inventory;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Inventory;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

/// <summary>
/// Repository dla inwentaryzacji z obsługą SSCC i DS
/// </summary>
public class InventoryRepository : IInventoryRepository
{
    private readonly WmsDbContext _context;
    private readonly ILogger<InventoryRepository> _logger;

    public InventoryRepository(WmsDbContext context, ILogger<InventoryRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<InventorySessionEntity?> GetSessionAsync(int sessionId)
    {
        return await _context.InventorySessions
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.IsActive);
    }

    public async Task<InventorySessionEntity?> GetSessionAsync(int sessionId, CancellationToken cancellationToken)
    {
        return await _context.InventorySessions
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.IsActive, cancellationToken);
    }

    public async Task<InventoryEntity?> FindByEtykietaIdAsync(int inventoryId, string etykietaId, int systemId)
    {
        return await _context.Inventories
            .AsNoTracking()
            .FirstOrDefaultAsync(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.EtykietaId == etykietaId &&
                i.SystemId == systemId &&
                i.Active == 1);
    }

    public async Task<IEnumerable<InventoryEntity>> FindByProductCodeAsync(int inventoryId, string productCode, int systemId)
    {
        return await _context.Inventories
            .AsNoTracking()
            .Where(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.Kod == productCode &&
                i.SystemId == systemId &&
                i.Active == 1)
            .ToListAsync();
    }

    public async Task<InventoryEntity?> FindByPaletaIdAsync(int inventoryId, int paletaId, int systemId)
    {
        return await _context.Inventories
            .AsNoTracking()
            .FirstOrDefaultAsync(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.PaletaId == paletaId &&
                i.SystemId == systemId &&
                i.Active == 1);
    }

    public async Task<IEnumerable<InventoryDto>> GetActiveInventoriesAsync(InventoryType? type, int limit = 40, CancellationToken cancellationToken = default)
    {
        var query = _context.Inventories
            .AsNoTracking()
            .Where(i => i.Active == 1);

        // Filtr typu inwentaryzacji na podstawie CurrentOperacja
        if (type.HasValue)
        {
            var operacjaValue = type switch
            {
                InventoryType.General => "19", // Inwentaryzacja Ogólna
                InventoryType.Product => "18", // Inwentaryzacja Produktowa
                InventoryType.Location => "20", // Inwentaryzacja Miejsc
                InventoryType.GG => "21", // Inwentaryzacja GG
                _ => null
            };

            if (operacjaValue != null)
            {
                query = query.Where(i =>
                    i.Status == operacjaValue || // Sprawdź pole stat
                    i.Uwaga == operacjaValue);   // Lub uwaga (backup)
            }
        }

        var inventories = await query
            .GroupBy(i => new { i.Data, i.Opis, i.InwentaryzacjaId, i.NumerWspolny, i.Proba })
            .Select(g => new
            {
                Id = g.First().Id,
                Data = g.Key.Data,
                Opis = g.Key.Opis,
                InwentaryzacjaId = g.Key.InwentaryzacjaId,
                NumerWspolny = g.Key.NumerWspolny,
                Proba = g.Key.Proba,
                Status = g.First().Status
            })
            .OrderByDescending(i => i.InwentaryzacjaId)
            .Take(limit)
            .ToListAsync(cancellationToken);

        return inventories.Select(i => new InventoryDto
        {
            Id = i.Id,
            Data = i.Data ?? DateTime.Today,
            Opis = i.Opis ?? "Inwentaryzacja",
            InwentaryzacjaId = i.InwentaryzacjaId ?? 0,
            NrWspolny = i.NumerWspolny?.ToString(),
            Proba = i.Proba.ToString(),
            Active = true,
            Type = DetermineInventoryType(i.Status)
        }).ToList();
    }

    public async Task<InventoryProgressDto> GetInventoryProgressAsync(int inventoryId, int systemId)
    {
        var query = _context.Inventories
            .AsNoTracking()
            .Where(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.SystemId == systemId &&
                i.Active == 1 &&
                i.Kod != "10101"); // Wykluczenie kodów technicznych

        var totalItems = await query.CountAsync();
        var completedItems = await query.CountAsync(i => i.IloscSpisana.HasValue);

        var percentage = totalItems > 0 ? Math.Round((decimal)completedItems / totalItems * 100, 1) : 0;

        return new InventoryProgressDto
        {
            InwentaryzacjaId = inventoryId,
            TotalItems = totalItems,
            CompletedItems = completedItems,
            CompletionPercentage = percentage
        };
    }

    public async Task<InventoryEntity> CreateAsync(InventoryEntity entity)
    {
        entity.Timestamp = DateTime.Now;
        entity.Active = 1;

        _context.Inventories.Add(entity);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Created inventory item with ID {ItemId} for inventory {InventoryId}",
            entity.Id, entity.InwentaryzacjaId);

        return entity;
    }

    public async Task<InventoryItemEntity> CreateInventoryItemAsync(
        int inventoryId,
        int pracownikId,
        string? etykietaId,
        string? paletaId,
        string? kod,
        decimal iloscSpisana,
        int hala,
        int regal,
        int miejsce,
        int poziom,
        string? podkod,
        string? skan,
        string? nrSap,
        CancellationToken cancellationToken = default)
    {
        var entity = new InventoryEntity
        {
            InwentaryzacjaId = inventoryId,
            Pracownik = pracownikId.ToString(),
            EtykietaId = etykietaId,
            PaletaId = int.TryParse(paletaId, out var pal) ? pal : 0,
            Kod = kod,
            IloscSpisana = iloscSpisana,
            Hala = hala,
            Regal = regal.ToString(),
            Miejsce = miejsce,
            Poziom = poziom.ToString(),
            Podkod = podkod,
            Skan = skan,
            NumerSap = nrSap,
            SystemId = 1,
            Data = DateTime.Now,
            Opis = "Inwentaryzacja",
            Ilosc = 0
        };

        var created = await CreateAsync(entity);
        return new InventoryItemEntity
        {
            Id = created.Id,
            InwentaryzacjaId = created.InwentaryzacjaId,
            Data = created.Data,
            Opis = created.Opis,
            Ilosc = created.Ilosc ?? 0,
            IloscSpisana = created.IloscSpisana,
            Kod = created.Kod,
            Hala = created.Hala ?? 0,
            Regal = created.Regal,
            Miejsce = created.Miejsce ?? 0,
            Poziom = created.Poziom,
            Timestamp = created.Timestamp,
            Podkod = created.Podkod,
            Skan = created.Skan,
            EtykietaId = created.EtykietaId,
            PaletaId = created.PaletaId,
            NumerSap = created.NumerSap,
            SystemId = created.SystemId,
            JednostkaMiary = created.JednostkaMiary
        };
    }

    public async Task<InventoryItemEntity> UpdateInventoryItemAsync(
        int inventoryItemId,
        decimal iloscSpisana,
        int pracownikId,
        int hala,
        int regal,
        int miejsce,
        int poziom,
        string? podkod,
        string? skan,
        CancellationToken cancellationToken = default)
    {
        var entity = await _context.Inventories.FirstOrDefaultAsync(i => i.Id == inventoryItemId, cancellationToken);
        if (entity == null)
            throw new ArgumentException($"Inventory item {inventoryItemId} not found");

        entity.IloscSpisana = iloscSpisana;
        entity.Pracownik = pracownikId.ToString();
        entity.Hala = hala;
        entity.Regal = regal.ToString();
        entity.Miejsce = miejsce;
        entity.Poziom = poziom.ToString();
        entity.Podkod = podkod;
        entity.Skan = skan;

        await _context.SaveChangesAsync(cancellationToken);

        return new InventoryItemEntity
        {
            Id = entity.Id,
            InwentaryzacjaId = entity.InwentaryzacjaId ?? 0,
            Data = entity.Data ?? DateTime.Today,
            Opis = entity.Opis,
            Ilosc = entity.Ilosc ?? 0,
            IloscSpisana = entity.IloscSpisana,
            Kod = entity.Kod,
            Hala = entity.Hala ?? 0,
            Regal = entity.Regal,
            Miejsce = entity.Miejsce ?? 0,
            Poziom = entity.Poziom,
            Timestamp = entity.Timestamp,
            Podkod = entity.Podkod,
            Skan = entity.Skan,
            EtykietaId = entity.EtykietaId,
            PaletaId = entity.PaletaId,
            NumerSap = entity.NumerSap,
            SystemId = entity.SystemId,
            JednostkaMiary = entity.JednostkaMiary
        };
    }

    public async Task<bool> EndSessionAsync(int sessionId, CancellationToken cancellationToken = default)
    {
        var session = await _context.InventorySessions.FirstOrDefaultAsync(s => s.Id == sessionId, cancellationToken);
        if (session == null) return false;
        session.IsActive = false;
        session.EndedAt = DateTime.Now;
        await _context.SaveChangesAsync(cancellationToken);
        _logger.LogInformation("Ended inventory session {SessionId}", sessionId);
        return true;
    }

    public async Task CreateLocationChangeAsync(
        string etykietaId,
        int pracownikId,
        string stareMiejsce,
        string noweMiejsce,
        int systemId,
        CancellationToken cancellationToken = default)
    {
        var movement = new Movement
        {
            Typ = "INW",
            DocNr = 0,
            PracownikId = pracownikId,
            Data = DateOnly.FromDateTime(DateTime.Now),
            Etykieta = int.TryParse(etykietaId, out var ety) ? ety : 0,
            SystemId = systemId,
            StareM = int.TryParse(stareMiejsce, out var sM) ? sM : 0,
            NoweM = int.TryParse(noweMiejsce, out var nM) ? nM : 0,
            DocInternal = "IN",
            Stat = 0,
            Tszm = DateTime.Now,
            Uwagi = "Inventory location change"
        };
        _context.Movements.Add(movement);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task CreateOperationLogAsync(
        string etykietaId,
        int pracownikId,
        string docNr,
        int systemId,
        string wozek,
        int operacId,
        CancellationToken cancellationToken = default)
    {
        var op = new InventoryOperationEntity
        {
            EtykietaId = etykietaId,
            DocType = "INW",
            DocNr = docNr,
            ImieNazwisko = null,
            TypOperacji = "INW",
            SystemId = systemId,
            Wozek = wozek,
            OperacId = operacId,
            Ilosc = "1",
            Ts = DateTime.UtcNow
        };
        _context.InventoryOperations.Add(op);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<InventoryEntity> UpdateAsync(InventoryEntity entity)
    {
        entity.Timestamp = DateTime.Now;

        _context.Inventories.Update(entity);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Updated inventory item with ID {ItemId}", entity.Id);

        return entity;
    }

    public async Task<InventorySessionEntity> CreateSessionAsync(
        int inventoryId,
        int pracownikId,
        string deviceId,
        CancellationToken cancellationToken = default)
    {
        var session = new InventorySessionEntity
        {
            InventoryId = inventoryId,
            PracownikId = pracownikId,
            DeviceId = deviceId,
            StartedAt = DateTime.Now,
            IsActive = true,
            SystemId = 1
        };

        _context.InventorySessions.Add(session);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created inventory session {SessionId} for user {UserId} and inventory {InventoryId}",
            session.Id, pracownikId, inventoryId);

        return session;
    }

    public async Task<InventoryDetailsDto?> GetInventoryDetailsAsync(int inventoryId, int systemId)
    {
        var inventory = await _context.Inventories
            .AsNoTracking()
            .Where(i => i.InwentaryzacjaId == inventoryId && i.SystemId == systemId && i.Active == 1)
            .GroupBy(i => new { i.Data, i.Opis, i.InwentaryzacjaId, i.NumerWspolny, i.Proba })
            .Select(g => new
            {
                g.Key.Data,
                g.Key.Opis,
                g.Key.InwentaryzacjaId,
                g.Key.NumerWspolny,
                g.Key.Proba,
                TotalItems = g.Count(),
                CompletedItems = g.Count(x => x.IloscSpisana.HasValue),
                FirstItem = g.First()
            })
            .FirstOrDefaultAsync();

        if (inventory == null)
            return null;

        var percentage = inventory.TotalItems > 0
            ? Math.Round((decimal)inventory.CompletedItems / inventory.TotalItems * 100, 1)
            : 0;

        return new InventoryDetailsDto
        {
            Id = inventory.FirstItem.Id,
            Data = inventory.Data ?? DateTime.Today,
            Opis = inventory.Opis ?? "Inwentaryzacja",
            InwentaryzacjaId = inventory.InwentaryzacjaId ?? 0,
            NrWspolny = inventory.NumerWspolny?.ToString(),
            Proba = inventory.Proba.ToString(),
            Active = true,
            Type = DetermineInventoryType(inventory.FirstItem.Status),
            TotalItems = inventory.TotalItems,
            CompletedItems = inventory.CompletedItems,
            CompletionPercentage = percentage
        };
    }

    public async Task<IEnumerable<InventoryEntity>> GetInventoryItemsAsync(int inventoryId, int systemId, bool onlyCompleted = false)
    {
        var query = _context.Inventories
            .AsNoTracking()
            .Where(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.SystemId == systemId &&
                i.Active == 1);

        if (onlyCompleted)
        {
            query = query.Where(i => i.IloscSpisana.HasValue);
        }

        return await query
            .OrderBy(i => i.Timestamp)
            .ToListAsync();
    }

    public async Task<IEnumerable<InventoryEntity>> GetInventoryItemsAsync(int inventoryId, int? pracownikId = null, bool onlyCompleted = false, CancellationToken cancellationToken = default)
    {
        // Brak rozróżnienia po pracownikId w obecnym modelu – zwracamy dla domyślnego systemId
        return await GetInventoryItemsAsync(inventoryId, 1, onlyCompleted);
}

    public async Task<IEnumerable<InventoryEntity>> GetInventoryDifferencesAsync(int inventoryId, int systemId)
    {
        return await _context.Inventories
            .AsNoTracking()
            .Where(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.SystemId == systemId &&
                i.Active == 1 &&
                i.IloscSpisana.HasValue &&
                i.Ilosc.HasValue &&
                Math.Abs(i.IloscSpisana.Value - i.Ilosc.Value) > 0.001m)
            .OrderByDescending(i => Math.Abs(i.IloscSpisana.Value - i.Ilosc.Value))
            .ToListAsync();
    }

    public async Task<InventoryEntity?> GetByIdAsync(int id, CancellationToken cancellationToken)
    {
        return await _context.Inventories
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == id && i.Active == 1, cancellationToken);
    }

    public async Task<InventoryProgressDto> GetInventoryProgressAsync(int inventoryId)
    {
        return await GetInventoryProgressAsync(inventoryId, 1); // Domyślny SystemId = 1
    }

    public async Task<InventoryProgressDto> GetInventoryProgressAsync(int inventoryId, CancellationToken cancellationToken)
    {
        return await GetInventoryProgressAsync(inventoryId, 1);
    }

    public async Task<InventoryItemEntity?> SearchLabelAsync(int inventoryId, string? etykietaId, string? nrSap, string? paletaId, CancellationToken cancellationToken = default)
    {
        var query = _context.Inventories
            .AsNoTracking()
            .Where(i =>
                i.InwentaryzacjaId == inventoryId &&
                i.Active == 1);

        if (!string.IsNullOrEmpty(etykietaId))
        {
            query = query.Where(i => i.EtykietaId == etykietaId);
        }

        if (!string.IsNullOrEmpty(nrSap))
        {
            query = query.Where(i => i.NumerSap == nrSap);
        }

        if (!string.IsNullOrEmpty(paletaId) && int.TryParse(paletaId, out var palId))
        {
            query = query.Where(i => i.PaletaId == palId);
        }

        var entity = await query.FirstOrDefaultAsync(cancellationToken);
        if (entity == null) return null;
        // Map to InventoryItemEntity
        return new InventoryItemEntity
        {
            Id = entity.Id,
            Data = entity.Data,
            Opis = entity.Opis,
            EtykietaId = entity.EtykietaId,
            PaletaId = entity.PaletaId,
            Kod = entity.Kod,
            Ilosc = entity.Ilosc,
            JednostkaMiary = entity.JednostkaMiary,
            MiejscepId = entity.MiejscepId,
            IloscSpisana = entity.IloscSpisana,
            Pracownik = entity.Pracownik,
            Timestamp = entity.Timestamp,
            InwentaryzacjaId = entity.InwentaryzacjaId,
            Active = entity.Active,
            Hala = entity.Hala,
            Regal = entity.Regal,
            Miejsce = entity.Miejsce,
            Poziom = entity.Poziom,
            Status = entity.Status,
            NumerSap = entity.NumerSap,
            Podkod = entity.Podkod,
            Skan = entity.Skan,
            Uwaga = entity.Uwaga,
            Nadwyzka = entity.Nadwyzka,
            SystemId = entity.SystemId,
            Proba = entity.Proba,
            NumerWspolny = entity.NumerWspolny,
            Magazyn = entity.Magazyn
        };
    }

    private InventoryType DetermineInventoryType(string? status)
    {
        return status switch
        {
            "19" => InventoryType.General,
            "18" => InventoryType.Product,
            "20" => InventoryType.Location,
            "21" => InventoryType.GG,
            _ => InventoryType.Product // Default
        };
    }
}

/// <summary>
/// Repository dla sesji inwentaryzacji
/// </summary>
public class InventorySessionRepository
{
    private readonly WmsDbContext _context;
    private readonly ILogger<InventorySessionRepository> _logger;

    public InventorySessionRepository(WmsDbContext context, ILogger<InventorySessionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<InventorySessionEntity> CreateSessionAsync(InventorySessionEntity session)
    {
        session.StartedAt = DateTime.Now;
        session.IsActive = true;

        _context.InventorySessions.Add(session);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Created inventory session {SessionId} for user {UserId}",
            session.Id, session.PracownikId);

        return session;
    }

    public async Task<InventorySessionEntity?> GetActiveSessionAsync(int userId)
    {
        return await _context.InventorySessions
            .Include(s => s.Pracownik)
            .FirstOrDefaultAsync(s =>
                s.PracownikId == userId &&
                s.IsActive &&
                !s.EndedAt.HasValue);
    }

    public async Task<bool> EndSessionAsync(int sessionId, int userId)
    {
        var session = await _context.InventorySessions
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.PracownikId == userId);

        if (session == null)
            return false;

        session.EndedAt = DateTime.Now;
        session.IsActive = false;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Ended inventory session {SessionId} for user {UserId}",
            sessionId, userId);

        return true;
    }
}