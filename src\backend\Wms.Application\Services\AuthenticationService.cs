using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Wms.Application.Configuration;
using Wms.Application.DTOs.Auth;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;

namespace Wms.Application.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IUserRepository _userRepository;
    private readonly ISessionRepository _sessionRepository;
    private readonly JwtSettings _jwtSettings;
    private readonly ILogger<AuthenticationService>? _logger;

    public AuthenticationService(
        IUserRepository userRepository, 
        ISessionRepository sessionRepository,
        IOptions<JwtSettings> jwtSettings,
        ILogger<AuthenticationService>? logger = null)
    {
        _userRepository = userRepository;
        _sessionRepository = sessionRepository;
        _jwtSettings = jwtSettings.Value;
        _logger = logger;
    }

    public async Task<LoginScanResponse?> LoginWithCardAsync(LoginScanRequest request, string? ipAddress = null)
    {
        // Znalezienie użytkownika po numerze karty
        var user = await _userRepository.GetByCardNumberAsync(request.CardNumber);

        if (user == null)
        {
            return null; // Karta nie znaleziona
        }

        // Sprawdzenie czy użytkownik jest aktywny (jeśli kolumna istnieje)
        // if (!user.IsActive)
        // {
        //     throw new UnauthorizedAccessException("User account is inactive");
        // }

        // Generowanie JWT token
        var tokenId = Guid.NewGuid().ToString();
        var token = GenerateJwtToken(user, tokenId);
        var expiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryMinutes);

        // Zapisanie sesji
        var session = new Session
        {
            UserId = user.Id,
            DeviceId = request.DeviceId ?? "unknown",
            IpAddress = ipAddress,
            LoginTime = DateTime.UtcNow,
            IsActive = true,
            JwtTokenId = tokenId
        };

        try
        {
            await _sessionRepository.CreateAsync(session);
        }
        catch (Exception ex)
        {
            // W trybie dev lub przy braku tabeli sessions w zdalnej bazie – loguj i kontynuuj
            _logger?.LogWarning(ex, "Session persistence failed. Continuing without storing session.");
        }

        return new LoginScanResponse
        {
            Token = token,
            ExpiresAt = expiresAt,
            User = new UserInfo
            {
                Id = user.Id,
                FullName = user.ImieNazwisko,
                Position = user.Stanowisko,
                Email = user.Email,
                Department = user.JednostkaId.ToString() // Temporary - można rozwinąć później
            }
        };
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            // Sprawdzenie czy sesja jest nadal aktywna
            var jwtToken = (JwtSecurityToken)validatedToken;
            var tokenId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

            if (tokenId != null)
            {
                var session = await _sessionRepository.GetByTokenIdAsync(tokenId);
                return session != null;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    public async Task LogoutAsync(int userId, string? deviceId = null)
    {
        await _sessionRepository.DeactivateSessionsAsync(userId, deviceId);
    }

    private string GenerateJwtToken(User user, string tokenId)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new(JwtRegisteredClaimNames.Jti, tokenId),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new("userId", user.Id.ToString()),
            new("cardNumber", user.NumerKarty ?? string.Empty),
            new("fullName", user.ImieNazwisko),
            new("position", user.Stanowisko)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryMinutes),
            Issuer = _jwtSettings.Issuer,
            Audience = _jwtSettings.Audience,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}
