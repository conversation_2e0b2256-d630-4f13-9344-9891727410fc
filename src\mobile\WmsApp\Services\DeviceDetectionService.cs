namespace WmsApp.Services;

/// <summary>
/// Fallback implementacja serwisu detekcji urządzeń dla platform innych niż Android
/// </summary>
public class DeviceDetectionService : IDeviceDetectionService
{
    public bool IsEmulator => true; // Assume emulator/simulator for non-Android platforms

    public bool IsZebraDevice => false; // Only Android devices can be Zebra

    public bool IsDataWedgeAvailable => false; // DataWedge only on Android

    public bool IsScanningAvailable => false; // No native scanning on non-Android

    public string DeviceTypeInfo => $"🖥️ {DeviceInfo.Platform} - Funk<PERSON>je skanowania niedostępne";
}
