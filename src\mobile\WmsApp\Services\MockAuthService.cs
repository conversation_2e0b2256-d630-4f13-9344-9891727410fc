using WmsApp.Models.Auth;

namespace WmsApp.Services;

/// <summary>
/// Mock implementacja serwisu autoryzacji dla testów i emulatorów
/// </summary>
public class MockAuthService : IAuthService
{
    private UserInfo? _currentUser;
    private string? _currentToken;
    private DateTime? _tokenExpiry;

    public bool IsAuthenticated => !string.IsNullOrEmpty(_currentToken);
    
    public string? CurrentToken => _currentToken;
    
    public UserInfo? CurrentUser => _currentUser;
    
    public DateTime? TokenExpiry => _tokenExpiry;

    public async Task SetAuthenticationAsync(LoginScanResponse loginResponse)
    {
        _currentToken = "mock_jwt_token_12345";
        _tokenExpiry = DateTime.UtcNow.AddHours(8); // 8 godzin ważności
        
        _currentUser = new UserInfo
        {
            Id = 1,
            FullName = "Mock User (Emulator)",
            Position = "Operator",
            Email = "<EMAIL>",
            Department = "Warehouse",
            IsActive = true,
            LastLogin = DateTime.UtcNow
        };

        System.Diagnostics.Debug.WriteLine($"[MOCK-AUTH] Mock authentication set for user: {_currentUser.FullName}");
        System.Diagnostics.Debug.WriteLine($"[MOCK-AUTH] Token expires at: {_tokenExpiry}");
        System.Diagnostics.Debug.WriteLine($"[MOCK-AUTH] User position: {_currentUser.Position}");
        
        await Task.CompletedTask;
    }

    public async Task ClearAuthenticationAsync()
    {
        _currentToken = null;
        _currentUser = null;
        _tokenExpiry = null;
        
        System.Diagnostics.Debug.WriteLine("[MOCK-AUTH] Authentication cleared");
        await Task.CompletedTask;
    }

    public async Task<bool> IsTokenValidAsync()
    {
        if (string.IsNullOrEmpty(_currentToken))
            return false;
            
        if (_tokenExpiry == null)
            return false;
            
        var isValid = _tokenExpiry > DateTime.UtcNow;
        
        System.Diagnostics.Debug.WriteLine($"[MOCK-AUTH] Token valid: {isValid}, expires: {_tokenExpiry}");
        return await Task.FromResult(isValid);
    }

    public string? GetAuthorizationHeader()
    {
        if (string.IsNullOrEmpty(_currentToken))
            return null;
            
        return $"Bearer {_currentToken}";
    }
}

