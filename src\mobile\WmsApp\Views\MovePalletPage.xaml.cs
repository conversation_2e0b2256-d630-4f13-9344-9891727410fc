using WmsApp.ViewModels;

namespace WmsApp.Views;

public partial class MovePalletPage : ContentPage
{
    public MovePalletPage(MovePalletViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        
        // Resetuj stronę do stanu początkowego gdy się pojawia
        if (BindingContext is MovePalletViewModel viewModel)
        {
            viewModel.ResetToInitialState();
        }
    }
}
