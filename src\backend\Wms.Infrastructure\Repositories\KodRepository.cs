using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class KodRepository : IKodRepository
{
    private readonly WmsDbContext _context;

    public KodRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Kod?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.Kody
            .AsNoTracking()
            .FirstOrDefaultAsync(k => k.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<Kod>> SearchAsync(string query, int limit = 10, int? systemId = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query)) return Enumerable.Empty<Kod>();

        var q = _context.Kody.AsNoTracking().AsQueryable();
        if (systemId.HasValue)
        {
            q = q.Where(k => k.SystemId == systemId.Value);
        }

        query = query.Trim();
        q = q.Where(k => k.KodValue.Contains(query) || k.Kod<PERSON>azwa.Contains(query));

        return await q
            .OrderBy(k => k.KodValue)
            .ThenBy(k => k.KodNazwa)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<Kod?> GetByKodValueAsync(string kodValue, int? systemId = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(kodValue)) return null;
        var q = _context.Kody.AsNoTracking().AsQueryable();
        if (systemId.HasValue)
        {
            q = q.Where(k => k.SystemId == systemId.Value);
        }
        kodValue = kodValue.Trim();
        return await q.FirstOrDefaultAsync(k => k.KodValue == kodValue, cancellationToken);
    }
}

