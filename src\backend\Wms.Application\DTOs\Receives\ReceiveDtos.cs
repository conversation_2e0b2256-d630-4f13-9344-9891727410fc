namespace Wms.Application.DTOs.Receives;

public class ReceiveDto
{
    public int Id { get; set; }
    public string Lk => $"LK{Id}";
    public string DokumentDostawy { get; set; } = string.Empty;
    public string SystemNazwa { get; set; } = string.Empty;
    public string MiejsceDostawy { get; set; } = string.Empty;
    public DateTime Data { get; set; }
    public bool IsAssigned { get; set; }
    public bool IsOccupiedByOther { get; set; } // C<PERSON> jest zajęta przez innego użytkownika
    public int? RealizujacyPracownikId { get; set; }
    public string? RealizujacyPracownikImieNazwisko { get; set; }
    public int AwizacjeId { get; set; }
    public bool MaAwizacje => AwizacjeId > 0;
    public string? NumerZamowienia { get; set; }
}

public class ReceiveListResponse
{
    public List<ReceiveDto> Receives { get; set; } = [];
    public int TotalCount { get; set; }
}

public class ReceiveDetailsDto : ReceiveDto
{
    public List<CarrierDto> Carriers { get; set; } = [];
    public List<ExpectedItemDto> ExpectedItems { get; set; } = [];
    public int CompletedItemsCount { get; set; }
    public int TotalExpectedItemsCount { get; set; }
    public bool IsComplete { get; set; }
}

public class CarrierDto
{
    public int PaletaId { get; set; }
    public string Ds => $"DS{PaletaId:D8}";
    public int ListControlId { get; set; }
    public bool Wydruk { get; set; }
    public DateTime DataUtworzenia { get; set; }
    public int ItemsCount { get; set; }
    public bool IsCompleted { get; set; }
}

public class ExpectedItemDto
{
    public int Id { get; set; }
    public int AwizacjaHeadId { get; set; }
    public int SystemId { get; set; }
    public int KodId { get; set; }
    public string? Kod { get; set; }
    public string KodNazwa { get; set; } = string.Empty;
    public string? Ean { get; set; }
    public decimal IloscAwizowana { get; set; }
    public decimal IloscPrzyjeta { get; set; }
    public string? Lot { get; set; }
    public DateOnly? DataWaznosci { get; set; }
    public string? EtykietaKlient { get; set; } // SSCC z awizacji
    public string? Blloc { get; set; } // Certyfikat z awizacji

    public bool IsReceived => IloscPrzyjeta >= IloscAwizowana;
}

public class ReceiveItemDto
{
    public int Id { get; set; }
    public int PaletaId { get; set; }
    public int KodId { get; set; }
    public string KodNazwa { get; set; } = string.Empty;
    public string? Ean { get; set; }
    public decimal Ilosc { get; set; }
    public string? Lot { get; set; }
    public DateOnly? DataProd { get; set; }
    public DateOnly? DataWaznosci { get; set; }
    public string? Sscc { get; set; }
    public string? Certyfikat { get; set; }
    public DateTime DataPrzyjecia { get; set; }
    public string Ds => $"DS{PaletaId:D8}";
}

public class ClaimReceiveResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ClaimedAt { get; set; }
}

public class GeneratePalletsResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<CarrierDto> GeneratedCarriers { get; set; } = [];
    public int GeneratedCount { get; set; }
}

public class GS1ParseResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Sscc { get; set; }
    public int? KodId { get; set; }
    public string? KodNazwa { get; set; }
    public string? Ean { get; set; }
    public string? Lot { get; set; }
    public DateOnly? DataWaznosci { get; set; }
    public decimal? Ilosc { get; set; }
    public bool IsPrefilled { get; set; }
    public bool IsExpected { get; set; } // czy pozycja jest awizowana
}

public class PrinterDto
{
    public int Id { get; set; }
    public string Nazwa { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string Lokalizacja { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

// DTO dla pozycji nośnika
public class NosnikPositionDto
{
    public int Id { get; set; }
    public string TowarKod { get; set; } = string.Empty;
    public string TowarNazwa { get; set; } = string.Empty;
    public string? Partia { get; set; }
    public DateTime? DataWaznosci { get; set; }
    public DateTime DataProdukcji { get; set; }
    public decimal IloscSztuk { get; set; }
    public decimal IloscOpakowan { get; set; }
    public string? Certyfikat { get; set; }
    public DateTime DataPrzyjecia { get; set; }
}

public class GetNosnikPositionsResponse
{
    public NosnikPositionDto[] Positions { get; set; } = Array.Empty<NosnikPositionDto>();
    public decimal TotalSztuk { get; set; }
    public decimal TotalOpakowan { get; set; }
    public string[] Warnings { get; set; } = Array.Empty<string>();
}

public class CompleteNosnikRequest
{
    public string NosnikCode { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
}
