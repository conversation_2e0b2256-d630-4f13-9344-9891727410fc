-- <PERSON><PERSON><PERSON> tabeli sessions dla ś<PERSON>zenia sesji użytkowników
-- Ta tabela jest nowa i nie istnieje w obecnej bazie danych

CREATE TABLE IF NOT EXISTS `sessions` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `UserId` int NOT NULL,
    `DeviceId` varchar(100) NOT NULL,
    `IpAddress` varchar(45) DEFAULT NULL,
    `LoginTime` datetime(6) NOT NULL,
    `LogoutTime` datetime(6) DEFAULT NULL,
    `IsActive` tinyint(1) NOT NULL DEFAULT 1,
    `JwtTokenId` varchar(50) DEFAULT NULL,
    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `UpdatedAt` datetime(6) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(6),
    `CreatedBy` varchar(100) DEFAULT NULL,
    `UpdatedBy` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`Id`),
    KEY `IX_sessions_UserId` (`UserId`),
    <PERSON>EY `IX_sessions_JwtTokenId` (`JwtTokenId`),
    KEY `IX_sessions_IsActive` (`IsActive`),
    CONSTRAINT `FK_sessions_pracownicy_UserId` 
        FOREIGN KEY (`UserId`) 
        REFERENCES `pracownicy` (`id`) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dodanie kolumny is_active do tabeli pracownicy (jeśli nie istnieje)
-- Ta kolumna będzie używana do oznaczania aktywnych użytkowników

ALTER TABLE `pracownicy` 
ADD COLUMN IF NOT EXISTS `is_active` tinyint(1) NOT NULL DEFAULT 1;
