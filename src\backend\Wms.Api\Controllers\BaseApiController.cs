using Microsoft.AspNetCore.Mvc;

namespace Wms.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public abstract class BaseApiController : ControllerBase
{
    protected string? GetClientIpAddress()
    {
        // Sprawdzanie X-Forwarded-For (Apache proxy)
        var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        // Fallback na bezpośredni adres
        return Request.HttpContext.Connection.RemoteIpAddress?.ToString();
    }

    protected string? GetDeviceId()
    {
        return Request.Headers["X-Device-Id"].FirstOrDefault();
    }

    protected string GetCurrentUserId()
    {
        return User.FindFirst("userId")?.Value 
            ?? throw new UnauthorizedAccessException("User not authenticated");
    }
}
