using MediatR;
using Wms.Application.DTOs.Inventory;

namespace Wms.Application.Features.Inventory.Queries;

/// <summary>
/// Query pobierania aktywnych inwentaryzacji
/// </summary>
public record GetActiveInventoriesQuery : IRequest<List<InventoryDto>>
{
    public InventoryType? Type { get; init; }
    public int Limit { get; init; } = 40;
}

/// <summary>
/// Query pobierania szczegółów inwentaryzacji
/// </summary>
public record GetInventoryDetailsQuery : IRequest<InventoryDetailsDto?>
{
    public int InventoryId { get; init; }
}

/// <summary>
/// Query wyszukiwania etykiety w inwentaryzacji
/// </summary>
public record SearchInventoryLabelQuery : IRequest<InventoryItemDto?>
{
    public int InventoryId { get; init; }
    public string? EtykietaId { get; init; }
    public string? NrSap { get; init; }
    public string? PaletaId { get; init; }
}

/// <summary>
/// Query pobierania pozycji inwentaryzacji
/// </summary>
public record GetInventoryItemsQuery : IRequest<List<InventoryItemDto>>
{
    public int InventoryId { get; init; }
    public int? PracownikId { get; init; }
    public bool OnlyCompleted { get; init; } = false;
}

/// <summary>
/// Query obliczania postępu inwentaryzacji
/// </summary>
public record GetInventoryProgressQuery : IRequest<InventoryProgressDto>
{
    public int InventoryId { get; init; }
}

/// <summary>
/// Query wyszukiwania kodu w kartotece
/// </summary>
public record SearchProductCodeQuery : IRequest<ProductCodeDto?>
{
    public string Code { get; init; } = string.Empty;
    public int SystemId { get; init; }
}
