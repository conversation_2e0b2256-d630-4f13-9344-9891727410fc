# WMS Backend - Publish and Deploy Script
# Użycie: .\publish-and-deploy.ps1 [dev|prod]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "prod")]
    [string]$Environment = "dev"
)

$ErrorActionPreference = "Stop"

# Konfiguracja środowisk
$Config = @{
    dev = @{
        Server = "***********"  # Zastąp swoim IP serwera dev
        User = "wms-dev"
        Path = "/var/www/wms-backend"
        Configuration = "Development"
    }
    prod = @{
        Server = "*************"  # Z Twojej ARCHITECTURE.md
        User = "wms-prod"
        Path = "/var/www/wms-backend"
        Configuration = "Release"
    }
}

$EnvConfig = $Config[$Environment]
$PublishPath = "./publish-$Environment"

Write-Host "🚀 Wdrażanie WMS Backend do środowiska: $Environment" -ForegroundColor Green

# 1. Czyszczenie poprzedniej publikacji
Write-Host "🧹 Czyszczenie poprzedniej publikacji..." -ForegroundColor Yellow
if (Test-Path $PublishPath) {
    Remove-Item -Path $PublishPath -Recurse -Force
}

# 2. Publikacja backendu
Write-Host "📦 Publikacja backendu (konfiguracja: $($EnvConfig.Configuration))..." -ForegroundColor Yellow
dotnet publish ./src/backend/Wms.Api `
    --configuration $EnvConfig.Configuration `
    --output $PublishPath `
    --runtime linux-x64 `
    --self-contained false `
    --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Błąd podczas publikacji!"
    exit 1
}

$PublishSize = (Get-ChildItem -Path $PublishPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "✅ Publikacja zakończona. Rozmiar: $($PublishSize.ToString('F1')) MB" -ForegroundColor Green

# 3. Wyświetl co zostanie wysłane
Write-Host "📁 Pliki do wdrożenia:" -ForegroundColor Cyan
Get-ChildItem -Path $PublishPath -Name "Wms.*" | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
Write-Host "   + $(((Get-ChildItem -Path $PublishPath).Count - 4)) dodatkowych plików" -ForegroundColor Gray

# 4. Potwierdzenie wdrożenia
$Deploy = Read-Host "🤔 Wdrożyć na serwer $($EnvConfig.Server)? (y/N)"
if ($Deploy -ne "y") {
    Write-Host "⏹️  Wdrożenie anulowane" -ForegroundColor Yellow
    exit 0
}

# 5. Wysłanie na serwer
Write-Host "🌐 Wysyłanie na serwer $($EnvConfig.Server)..." -ForegroundColor Yellow

# Backup poprzedniej wersji na serwerze
$BackupCmd = @"
mkdir -p $($EnvConfig.Path)-backup
cp -r $($EnvConfig.Path)/* $($EnvConfig.Path)-backup/ 2>/dev/null || true
"@

Write-Host "💾 Tworzenie backup poprzedniej wersji..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $BackupCmd

# Zatrzymanie usługi (sprawdzenie czy proces istnieje)
Write-Host "⏸️  Zatrzymywanie aplikacji..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" "pkill -f 'Wms.Api' || true"

# Wysłanie nowych plików
Write-Host "📤 Przesyłanie plików..." -ForegroundColor Yellow
scp -r "${PublishPath}/*" "$($EnvConfig.User)@$($EnvConfig.Server):$($EnvConfig.Path)/"

# Ustawienie uprawnień przez wms-dev
$SetPermissionsCmd = @"
cd $($EnvConfig.Path)
# Sprawdzenie czy plik wykonywalny istnieje
if [ -f "Wms.Api" ]; then
  chmod +x Wms.Api
  echo "✅ Plik wykonywalny Wms.Api znaleziony i oznaczony jako wykonywalny"
else
  echo "❌ Nie znaleziono pliku wykonywalnego Wms.Api"
  ls -la | grep Wms
  exit 1
fi
"@

Write-Host "🔄 Ustawianie uprawnień..." -ForegroundColor Yellow
ssh "$($EnvConfig.User)@$($EnvConfig.Server)" $SetPermissionsCmd

# Restart usługi systemd przez root
$SystemdCmd = @"
# Restart usługi systemd
echo "🔄 Restartowanie usługi WMS API przez systemd..."
systemctl restart wms-api
sleep 3

# Sprawdzenie statusu usługi
if systemctl is-active --quiet wms-api; then
  echo "✅ WMS API uruchomione pomyślnie przez systemd"
  echo "📋 Status usługi:"
  systemctl status wms-api --no-pager -l | head -10
  echo "🌐 Sprawdzenie portu 5000:"
  ss -tlnp | grep :5000 || echo "Port 5000 nie nasłuchuje"
  echo "🧪 Test lokalnego API:"
  curl -s http://127.0.0.1:5000/health 2>/dev/null | head -3 || echo "API nie odpowiada lokalnie"
else
  echo "❌ Błąd uruchamiania usługi WMS API"
  echo "📋 Logi systemd:"
  journalctl -u wms-api -n 20 --no-pager || echo "Brak dostępu do logów systemd"
  exit 1
fi
"@

Write-Host "🔑 Restart usługi przez root (potwierdz haslem):" -ForegroundColor Yellow
ssh "root@$($EnvConfig.Server)" $SystemdCmd

Write-Host "✅ Wdrożenie zakończone!" -ForegroundColor Green
Write-Host "🌐 API dostępne pod: https://$($EnvConfig.Server)/api/v1" -ForegroundColor Cyan

# Cleanup lokalnego publish
Remove-Item -Path $PublishPath -Recurse -Force
Write-Host "🧹 Lokalne pliki publikacji usunięte" -ForegroundColor Gray
