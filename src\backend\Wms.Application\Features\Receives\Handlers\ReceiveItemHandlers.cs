using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Exceptions;
using Wms.Domain.Services;
using Wms.Domain.ValueObjects;

namespace Wms.Application.Features.Receives.Handlers;

public class ParseGS1ScanHandler : IRequestHandler<ParseGS1ScanCommand, GS1ParseResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly ILogger<ParseGS1ScanHandler> _logger;

    public ParseGS1ScanHandler(
        IReceiveRepository receiveRepository,
        ILogger<ParseGS1ScanHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _logger = logger;
    }

    public async Task<GS1ParseResponse> Handle(ParseGS1ScanCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Sprawdź czy dostawa istnieje i jest przypisana do pracownika
            // Używamy GetByIdAsync dla walidacji (AsNoTracking)
            var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
            if (receive == null)
                throw new ReceiveNotFoundException(request.ListControlId);

            if (!receive.IsAssigned || receive.RealizujacyPracownikId != request.PracownikId)
            {
                return new GS1ParseResponse
                {
                    Success = false,
                    Message = "Dostawa musi być przypisana do tego pracownika"
                };
            }

            // Parsuj dane GS1 (AI: 00, 02, 10, 17, 37)
            var parseResult = ParseGS1Data(request.Scan);
            if (!parseResult.Success)
            {
                return parseResult;
            }

            // Sprawdź czy pozycja jest awizowana
            var expectedItems = await _receiveRepository.GetExpectedItemsAsync(request.ListControlId, cancellationToken);
            var isExpected = expectedItems.Any(ei => 
                (!string.IsNullOrEmpty(ei.EtykietaKlient) && ei.EtykietaKlient == parseResult.Sscc) ||
                (!string.IsNullOrEmpty(ei.Kod) && ei.Kod == parseResult.Ean));

            parseResult.IsExpected = isExpected;
            parseResult.IsPrefilled = isExpected; // Jeśli jest awizowany, automatycznie wypełnij dane

            _logger.LogInformation("Sparsowano dane GS1 dla dostawy LK{ReceiveId}: SSCC={Sscc}, EAN={Ean}, Lot={Lot}", 
                request.ListControlId, parseResult.Sscc, parseResult.Ean, parseResult.Lot);

            return parseResult;
        }
        catch (InvalidGS1FormatException ex)
        {
            _logger.LogWarning("Nieprawidłowy format GS1 dla dostawy LK{ReceiveId}: {Error}", 
                request.ListControlId, ex.Message);
            
            return new GS1ParseResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania GS1 dla dostawy LK{ReceiveId}", request.ListControlId);
            throw;
        }
    }

    private GS1ParseResponse ParseGS1Data(string scanData)
    {
        // Simplified GS1 parsing - in real implementation, use proper GS1 parser
        // Format: (00)SSCC(02)GTIN(10)LOT(17)YYMMDD(37)COUNT
        var result = new GS1ParseResponse { Success = true };

        try
        {
            if (string.IsNullOrWhiteSpace(scanData))
                throw new InvalidGS1FormatException(scanData, "Puste dane skanu");

            // Extract SSCC (AI 00)
            var ssccMatch = System.Text.RegularExpressions.Regex.Match(scanData, @"\(00\)(\d{18})");
            if (ssccMatch.Success)
            {
                result.Sscc = ssccMatch.Groups[1].Value;
            }

            // Extract GTIN (AI 02)  
            var gtinMatch = System.Text.RegularExpressions.Regex.Match(scanData, @"\(02\)(\d{14})");
            if (gtinMatch.Success)
            {
                result.Ean = gtinMatch.Groups[1].Value;
            }

            // Extract Lot (AI 10)
            var lotMatch = System.Text.RegularExpressions.Regex.Match(scanData, @"\(10\)([^()]+)");
            if (lotMatch.Success)
            {
                result.Lot = lotMatch.Groups[1].Value;
            }

            // Extract Best Before Date (AI 17) - YYMMDD
            var dateMatch = System.Text.RegularExpressions.Regex.Match(scanData, @"\(17\)(\d{6})");
            if (dateMatch.Success)
            {
                var dateStr = dateMatch.Groups[1].Value;
                if (dateStr.Length == 6)
                {
                    var year = 2000 + int.Parse(dateStr[0..2]);
                    var month = int.Parse(dateStr[2..4]);
                    var day = int.Parse(dateStr[4..6]);
                    result.DataWaznosci = new DateOnly(year, month, day);
                }
            }

            // Extract Count (AI 37)
            var countMatch = System.Text.RegularExpressions.Regex.Match(scanData, @"\(37\)(\d+)");
            if (countMatch.Success)
            {
                result.Ilosc = decimal.Parse(countMatch.Groups[1].Value);
            }

            return result;
        }
        catch (Exception ex)
        {
            throw new InvalidGS1FormatException(scanData, $"Błąd parsowania: {ex.Message}");
        }
    }
}

public class CreateReceiveItemHandler : IRequestHandler<CreateReceiveItemCommand, ReceiveItemDto>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly ILabelRepository _labelRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateReceiveItemHandler> _logger;
    private readonly NumberGenerationService _numberGenerationService;

    public CreateReceiveItemHandler(
        IReceiveRepository receiveRepository,
        IPalletRepository palletRepository,
        ILabelRepository labelRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreateReceiveItemHandler> logger,
        NumberGenerationService numberGenerationService)
    {
        _receiveRepository = receiveRepository;
        _palletRepository = palletRepository;
        _labelRepository = labelRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _numberGenerationService = numberGenerationService;
    }

    public async Task<ReceiveItemDto> Handle(CreateReceiveItemCommand request, CancellationToken cancellationToken)
    {
        // Sprawdź czy dostawa istnieje i jest przypisana do pracownika
        // Używamy GetByIdAsync dla walidacji (AsNoTracking)
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
        if (receive == null)
            throw new ReceiveNotFoundException(request.ListControlId);

        if (!receive.IsAssigned || receive.RealizujacyPracownikId != request.PracownikId)
            throw new UnauthorizedAccessException("Dostawa musi być przypisana do tego pracownika");

        // Sprawdź czy nośnik istnieje i należy do tej dostawy
        var carrier = await _palletRepository.GetCarrierAsync(request.ListControlId, request.PaletaId, cancellationToken);
        if (carrier == null)
            throw new ArgumentException($"Nośnik DS{request.PaletaId:D8} nie należy do dostawy LK{request.ListControlId}");

        Label labelResult = null!;

        await _unitOfWork.ExecuteInTransactionAsync(async () =>
        {
            // Wygeneruj numer etykiety
            var labelNumber = await _numberGenerationService.GetNextLabelNumberAsync(cancellationToken);

            // Utwórz nową etykietę z poprawnymi danymi z list_control
            labelResult = new Label
            {
                ListcontrolId = request.ListControlId,
                SystemId = receive.ListcontrolSystemId ?? 0, // Pobierz z list_control.listcontrol_system_id
                Miejscep = receive.MiejsceId, // Pobierz z list_control.miejsce_id
                PaletaId = request.PaletaId,
                KodId = request.KodId,
                Ilosc = request.Ilosc,
                Lot = request.Lot,
                Dataprod = request.DataProd,
                DataWaznosci = request.DataWaznosci,
                Sscc = request.Sscc,
                Blloc = request.Certyfikat, // Certyfikat w polu Blloc
                Nretykiety = labelNumber, // Wygenerowany numer etykiety
                Ts = DateTime.UtcNow, // CURRENT_TIMESTAMP
                Active = 1,
                Magazyn = 1 // Default warehouse - można rozszerzyć o pobieranie z konfiguracji
            };

            // Dodaj etykietę do kontekstu
            _labelRepository.Add(labelResult);

            // Zapisz zmiany do bazy danych
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Utworzono pozycję dla dostawy LK{ReceiveId} na nośniku DS{PalletId}: Kod={KodId}, Ilość={Quantity}, Etykieta={LabelNumber}",
                request.ListControlId, request.PaletaId, request.KodId, request.Ilosc, labelNumber);
        });

        var receiveItemDto = _mapper.Map<ReceiveItemDto>(labelResult);
        return receiveItemDto;
    }
}

public class CreateCarrierHandler : IRequestHandler<CreateCarrierCommand, CarrierDto>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly PalletGenerationService _palletGenerationService;
    private readonly NumberGenerationService _numberGenerationService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateCarrierHandler> _logger;

    public CreateCarrierHandler(
        IReceiveRepository receiveRepository,
        IPalletRepository palletRepository,
        IUnitOfWork unitOfWork,
        PalletGenerationService palletGenerationService,
        NumberGenerationService numberGenerationService,
        IMapper mapper,
        ILogger<CreateCarrierHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _palletRepository = palletRepository;
        _unitOfWork = unitOfWork;
        _palletGenerationService = palletGenerationService;
        _numberGenerationService = numberGenerationService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierDto> Handle(CreateCarrierCommand request, CancellationToken cancellationToken)
    {
        // Sprawdź czy dostawa istnieje i jest przypisana do pracownika
        // Używamy GetByIdAsync dla walidacji (AsNoTracking)
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
        if (receive == null)
            throw new ReceiveNotFoundException(request.ListControlId);

        if (!receive.IsAssigned || receive.RealizujacyPracownikId != request.PracownikId)
            throw new UnauthorizedAccessException("Dostawa musi być przypisana do tego pracownika");
        ListControlPallet carrierResult = null!;

        await _unitOfWork.ExecuteInTransactionAsync(async () =>
        {
            // Generuj nowy numer DS z docnumber
            var paletaId = await _numberGenerationService.GetNextPalletNumberAsync(cancellationToken);
            var dsCode = _palletGenerationService.CreateDSCodeFromNumber(paletaId);

            // Utwórz palete
            var pallet = await _palletRepository.CreatePalletAsync(request.TypPaletyId, cancellationToken);

            // Utwórz powiązanie z dostawą
            carrierResult = await _palletRepository.CreateListControlPalletAsync(
                request.ListControlId,
                paletaId,
                request.Drukowac,
                cancellationToken);

            // Zapisz zmiany do bazy danych
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Utworzono nośnik DS{PalletId:D8} dla dostawy LK{ReceiveId} przez pracownika {UserId}",
                paletaId, request.ListControlId, request.PracownikId);

            // TODO: Implementacja drukowania jeśli wymagane
            if (request.Drukowac && !string.IsNullOrEmpty(request.DrukarkaIp))
            {
                _logger.LogInformation("Wysłano zadanie druku etykiety DS{PalletId:D8} na drukarkę {PrinterIp}",
                    paletaId, request.DrukarkaIp);
            }
        });

        var carrierDto = _mapper.Map<CarrierDto>(carrierResult);
        return carrierDto;
    }
}

public class CompleteCarrierHandler : IRequestHandler<CompleteCarrierCommand, bool>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CompleteCarrierHandler> _logger;

    public CompleteCarrierHandler(
        IReceiveRepository receiveRepository,
        IPalletRepository palletRepository,
        IUnitOfWork unitOfWork,
        ILogger<CompleteCarrierHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _palletRepository = palletRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(CompleteCarrierCommand request, CancellationToken cancellationToken)
    {
        // Sprawdź czy dostawa istnieje i jest przypisana do pracownika
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
        if (receive == null)
            throw new ReceiveNotFoundException(request.ListControlId);

        if (!receive.IsAssigned || receive.RealizujacyPracownikId != request.PracownikId)
            return false;

        // Sprawdź czy nośnik istnieje
        var carrier = await _palletRepository.GetCarrierAsync(request.ListControlId, request.PaletaId, cancellationToken);
        if (carrier == null)
            return false;

        // Sprawdź czy nośnik ma jakieś pozycje
        var carrierItems = await _palletRepository.GetCarrierItemsAsync(request.PaletaId, cancellationToken);
        if (!carrierItems.Any())
            return false;

        await _unitOfWork.ExecuteInTransactionAsync(async () =>
        {
            // Oznacz nośnik jako ukończony (można dodać pole w przyszłości)
            // Lub wykonaj inne operacje finalizujące
            
            _logger.LogInformation("Ukończono nośnik DS{PalletId:D8} dla dostawy LK{ReceiveId} z {ItemCount} pozycjami", 
                request.PaletaId, request.ListControlId, carrierItems.Count());
        });

        return true;
    }
}

public class EndReceiveSessionHandler : IRequestHandler<EndReceiveSessionCommand, ClaimReceiveResponse>
{
    private readonly IReceiveRepository _receiveRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ReceiveDomainService _receiveDomainService;
    private readonly ILogger<EndReceiveSessionHandler> _logger;

    public EndReceiveSessionHandler(
        IReceiveRepository receiveRepository,
        IUnitOfWork unitOfWork,
        ReceiveDomainService receiveDomainService,
        ILogger<EndReceiveSessionHandler> logger)
    {
        _receiveRepository = receiveRepository;
        _unitOfWork = unitOfWork;
        _receiveDomainService = receiveDomainService;
        _logger = logger;
    }

    public async Task<ClaimReceiveResponse> Handle(EndReceiveSessionCommand request, CancellationToken cancellationToken)
    {
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId, cancellationToken);
        if (receive == null)
            throw new ReceiveNotFoundException(request.ListControlId);

        // Sprawdź czy może zakończyć sesję
        var canReleaseResult = _receiveDomainService.CanReleaseReceive(receive, request.PracownikId);
        if (!canReleaseResult.IsSuccess)
        {
            return new ClaimReceiveResponse
            {
                Success = false,
                Message = canReleaseResult.ErrorMessage ?? "Nie można zakończyć sesji"
            };
        }

        try
        {
            await _unitOfWork.ExecuteInTransactionAsync(async () =>
            {
                // Zwolnij dostawę
                var released = await _receiveRepository.ReleaseReceiveAsync(request.ListControlId, request.PracownikId, cancellationToken);
                if (!released)
                {
                    throw new InvalidOperationException("Nie można zwolnić dostawy");
                }

                _logger.LogInformation("Zakończono sesję pracy z dostawą LK{ReceiveId} przez pracownika {UserId}", 
                    request.ListControlId, request.PracownikId);
            });

            return new ClaimReceiveResponse
            {
                Success = true,
                Message = $"Sesja dla dostawy LK{request.ListControlId} została zakończona",
                ClaimedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas kończenia sesji dla dostawy LK{ReceiveId}", request.ListControlId);
            throw;
        }
    }
}
