# TODO Backend – <PERSON><PERSON><PERSON> "Dostawy" (.NET 9, Clean Architecture)

**Cel:** Implementacja modułu dostaw zgodnie z PRD_backend_dostawy.md i istniejącą Clean Architecture

## Faza 1 – Domain Layer (0%)
- [ ] **Encje domenowe** (0%)
- [ ] `Receive` (mapowanie `list_control`)
- [ ] `ReceivePallet` (mapowanie `listcontrol_palety`)
- [ ] `ReceiveItem` (mapowanie `etykiety` w kontekście dostaw)
  - [ ] Value Objects: `LkNumber`, `PalletCode`
- [ ] **Domain Services** (0%)
- [ ] `ReceiveDomainService` (reguły biznesowe)
  - [ ] `PalletGenerationService` (logika numeracji DS)

## Faza 2 – Infrastructure (EF Core + MySQL) (90%)
- [x] **Migracje DB** (100%)
  - [x] `list_control.realizujacy_pracownik_id INT UNSIGNED NULL` (już istnieje w WmsDbContext)
  - [ ] `etykiety.certyfikat VARCHAR(64) NULL` (do dodania gdy potrzebne)
  - [x] Indeksy dla wydajności dostaw (istniejące indeksy wystarczące)
- [x] **Entity Configurations** (100%)
  - [x] `ListControlConfiguration` (mapowanie legacy `list_control` - w WmsDbContext)
  - [x] `ListControlPalletConfiguration` (mapowanie `listcontrol_palety` - w WmsDbContext)
- [x] **Repozytoria** (zgodnie z wzorcami z ARCHITECTURE.md) (100%)
- [x] `IReceiveRepository` + implementacja (z metodami claim/release, filtrowania, pobierania awizacji)
  - [x] `IPalletRepository` + implementacja (z tworzeniem palet, nośników, zarządzaniem pozycjami)
  - [x] Rejestracja repozytoriów w DI (ServiceCollectionExtensions)

## Faza 3 – Application Layer (Use Cases) (100%)
- [x] **Commands/Queries** (CQRS pattern) (100%)
- [x] `GetAvailableReceivesQuery` + Handler
- [x] `ClaimReceiveCommand` + Handler + Validator
  - [x] `GeneratePalletsCommand` + Handler + Validator
  - [x] `ParseGs1ScanCommand` + Handler
  - [x] `CreateReceiveItemCommand` + Handler + Validator (z polem opcjonalnym `certyfikat`)
  - [x] `GetReceiveExpectedItemsQuery` + Handler (lista pozycji awizacji dla LK)
  - [x] `CreateCarrierCommand`, `CompleteCarrierCommand`, `EndReceiveSessionCommand` + Handlers
- [x] **Services** (Application) (80%)
  - [x] Logika biznesowa w handlerach zamiast oddzielnych serwisów
  - [ ] `IPrintingService` (ZPL Code 128) – placeholder w handlerach, do pełnej implementacji
  - [x] `GS1ParsingService` (AI 00,02,10,17,37) - zintegrowany w ParseGS1ScanHandler
- [x] **DTOs** (zgodnie z API contracts) (100%)
- [x] `ReceiveDto`, `ReceiveListResponse`, `ReceiveDetailsDto`
- [x] `ClaimReceiveCommand`, `GeneratePalletsCommand`, `CreateReceiveItemCommand` (z `certyfikat`), `ExpectedItemDto`
- [x] `CarrierDto`, `GS1ParseResponse`, `PrinterDto`

## Faza 4 – API Layer (ASP.NET Core) (100%)
- [x] **Kontrolery** (zgodnie z wzorcami z STYLE_GUIDE.md) (100%)
- [x] `ReceivesController` (GET listy, POST claim)
  - [x] `DeliveryPalletsController` (generowanie DS)
  - [x] `PrintersController` (lista drukarek)
  - [x] `ReceivesAwizacjaController` lub metoda w `ReceivesController`: `GET /api/dostawy/{id}/awizacja/pozycje`
- [x] **Middleware i filtry** (100%)
- [x] Obsługa `ReceiveNotFoundException` w `GlobalExceptionMiddleware`
  - [x] Walidacja JWT dla endpointów dostaw

## Etap 4 – Skan GS1 i rejestracja (0%)
- [ ] POST /api/dostawy/{id}/skan (parsowanie AIs 00, 02, 10, 17, 37). (0%)
- [ ] POST /api/dostawy/{id}/nosniki (nowy DS dla trybu „Auto”). (0%)
- [ ] POST /api/dostawy/{id}/pozycje (INSERT do etykiety z walidacjami, opcjonalne pole `certyfikat`, w odpowiedzi `dataPrzyjecia`). (0%)
- [ ] GET /api/dostawy/{id}/nosniki/{paletaId}/pozycje (podgląd). (0%)
- [ ] POST /api/dostawy/{id}/nosniki/{paletaId}/complete. (0%)
- [ ] GET /api/dostawy/{id}/awizacja/pozycje (lista pozycji awizacji do ręcznego wyboru). (0%)

## Etap 5 – Zakończenie sesji i spójność (0%)
- [ ] POST /api/dostawy/{id}/koniec (zwolnienie claim, sugestia zamknięcia). (0%)
- [ ] Agregacje ilości vs awizacja – confirm przy niespójnościach. (0%)

## Faza 5 – Testy (zgodnie z wzorcami z ARCHITECTURE.md) (60%)
- [x] **Testy jednostkowe** (xUnit + FluentAssertions) (80%)
  - [ ] Domain Services (reguły biznesowe)
  - [x] Application Handlers (use-cases) - podstawowa struktura testów dla ParseGS1ScanHandler, CreateReceiveItemHandler, GetReceiveDetailsHandler
  - [x] Repozytoria (in-memory EF Context) - ReceiveRepositoryTests, PalletRepositoryTests
- [ ] **Testy integracyjne** (0%)
  - [ ] API endpoints z TestServer
  - [ ] Transakcyjność operacji DB
  - [ ] Walidacja FluentValidation
- [ ] **Pokrycie** (min. 70% zgodnie z PRD.md) (0%)

## Faza 6 – Observability (zgodnie z ARCHITECTURE.md) (0%)
- [ ] **Logowanie** (Serilog, structured logs) (0%)
  - [ ] Operacje claim/release dostawy
  - [ ] Generowanie palet DS
  - [ ] Rejestracja pozycji przyjęcia
- [ ] **Metryki** (OpenTelemetry → Prometheus) (0%)
  - [ ] Czas odpowiedzi endpointów
  - [ ] Liczba aktywnych dostaw

---
**Postęp całościowy: 75%**

## 🎉 Ostatnio ukończone (2024-11-12):

### ✅ Warstwa API (2024-11-12)

- **Kontrolery REST API** - pełna implementacja zgodnie z clean architecture:
  - `ReceivesController` - zarządzanie dostawami (lista, claim, release, szczegóły, pozycje)
  - `DeliveryPalletsController` - zarządzanie nośnikami dostaw (generowanie, tworzenie, pozycje, finalizacja)
  - `PrintersController` - zarządzanie drukarkami (lista, szczegóły, test połączenia)
  - Wszystkie endpointy z obsługą autoryzacji JWT

- **Middleware** - rozszerzenie `GlobalExceptionMiddleware`:
  - Obsługa wyjątków domenowych: `ReceiveNotFoundException`, `ReceiveAlreadyClaimedException`
  - `InvalidGS1FormatException` z odpowiednimi statusami HTTP
  - Strukturalne logowanie błędów

### ✅ Testy jednostkowe handlerów (2024-11-12)

- **Struktura testów Application Layer**:
  - `ParseGS1ScanHandlerTests` - testy parsowania kodów GS1 z różnymi formatami
  - `CreateReceiveItemHandlerTests` - testy tworzenia pozycji dostaw z walidacją
  - `GetReceiveDetailsHandlerTests` - testy pobierania szczegółów dostaw z pozycjami i nośnikami
  - Mockowanie zależności (repozytoria, mappery, loggery)

### ✅ Repozytoria Infrastructure Layer
- **ReceiveRepository** - pełna implementacja z obsługą:
  - `GetAvailableReceivesAsync()` - filtrowanie dostępnych dostaw z opcjami warehouse i includeAssigned
  - `ClaimReceiveAsync()` / `ReleaseReceiveAsync()` - atomowe operacje claim/release z obsługą concurrency
  - `GetExpectedItemsAsync()` - pobieranie pozycji awizacji dla dostawy
  - `GetWithDetailsAsync()` - pełne detale dostawy z navi properties

- **PalletRepository** - pełna implementacja z obsługą:
  - `CreatePalletAsync()` - tworzenie nowych palet
  - `CreateListControlPalletAsync()` - tworzenie nośników (z deduplikacją)
  - `GetCarriersForReceiveAsync()` - pobieranie nośników z sortowaniem
  - `GetCarrierItemsAsync()` - pozycje na paletach

- **Dependency Injection** - rejestracja w `ServiceCollectionExtensions`

### 🧪 Testy jednostkowe
- **ReceiveRepositoryTests** (16 testów) - pełne pokrycie funkcjonalności:
  - Filtrowanie dostaw (warehouse, assigned/unassigned)
  - Operacje claim/release z różnymi scenariuszami
  - Pobieranie awizacji i szczegółów dostaw
  - Obsługa błędów i edge case’ów

- **PalletRepositoryTests** (12 testów) - pełne pokrycie funkcjonalności:
  - Tworzenie palet i nośników
  - Obsługa deduplikacji
  - Sortowanie i filtrowanie
  - Zarządzanie pozycjami na paletach

### ✅ Warstwa Application CQRS (2024-11-12)

- **Handlery zapytań** - pełna implementacja:
  - `GetAvailableReceivesHandler` - lista dostępnych dostaw z filtrowaniem
  - `GetReceiveDetailsHandler` - szczegóły dostawy z nośnikami i oczekiwanymi pozycjami
  - `GetReceiveExpectedItemsHandler` - pozycje awizacji z ilością przyjętą
  - `GetReceiveItemsHandler`, `GetCarrierItemsHandler` - pozycje na nośnikach
  - `GetPrintersHandler` - lista dostępnych drukarek

- **Handlery komend** - pełna implementacja z logiką biznesową:
  - `ClaimReceiveHandler` / `ReleaseReceiveHandler` - atomowe claim/release dostaw
  - `GeneratePalletsHandler` - generowanie wielu nośników DS jednocześnie ✅ **NAPRAWIONO NUMERACJĘ**
  - `CreateCarrierHandler` - tworzenie pojedynczego nośnika ✅ **NAPRAWIONO NUMERACJĘ**
  - `ParseGS1ScanHandler` - parsowanie kodów GS1 (AI: 00,02,10,17,37)
  - `CreateReceiveItemHandler` - tworzenie pozycji na nośnikach ✅ **NAPRAWIONO 3 BŁĘDY**
  - `CompleteCarrierHandler` / `EndReceiveSessionHandler` - finalizacja

- **Walidatory FluentValidation** - pełne pokrycie:
  - Walidacja identyfikatorów, ilości, formatów IP drukarek
  - Walidacja kodów SSCC, warunkowa walidacja flag

- **AutoMapper Profile** - mapowanie encji ↔️ DTOs z logiką biznesową

### Struktura katalogów (zgodnie z propozycją):
```
src/backend/Wms.Domain/Entities/Receives/
  ├── Entities/             # Encje, Value Objects
  └── Services/             # Serwisy domenowe

src/backend/Wms.Infrastructure/Repositories/Receives/
  ├── Repositories/         # Implementacje repozytoriów
  └── Configurations/       # EF Core configurations

src/backend/Wms.Api/Controllers/Receives/
  └── ReceivesController.cs
```

(Uwaga: zachowując Clean Architecture, rekomenduję również warstwę Application: `src/backend/Wms.Application/Receives/{Commands,Queries,DTOs}`)

## ✅ **NAPRAWIONE BŁĘDY W REJESTRACJI DOSTAW (2025-01-10)**

### 1. Błąd numeracji palet (paletaId)
**Problem:** Używano `Random.Next()` zamiast atomowej numeracji z tabeli `docnumber`
**Rozwiązanie:**
- Utworzono `DocNumber` entity mapowaną na tabelę `docnumber`
- Implementacja `DocNumberRepository` z atomową numeracją (FOR UPDATE locking)
- `NumberGenerationService` dla centralizacji logiki numeracji
- Modyfikacja `GeneratePalletsHandler` i `CreateCarrierHandler` do używania `GetNextPalletNumberAsync()`

### 2. Brak zapisu do tabeli etykiety
**Problem:** Po rejestracji pozycji nie zapisywano danych do tabeli `etykiety`
**Rozwiązanie:**
- Dodano tworzenie `Label` entity w `CreateReceiveItemHandler`
- Poprawne mapowanie pól: `SystemId` z `ListcontrolSystemId`, `Miejscep` z `MiejsceId`
- Dodano metodę `Add()` do `ILabelRepository` i implementacji

### 3. Brak numeracji etykiet (nretykiety)
**Problem:** Brak implementacji logiki numeracji etykiet
**Rozwiązanie:**
- Rozszerzenie `NumberGenerationService` o `GetNextLabelNumberAsync()`
- Dodanie stałej `LABEL_NUMBER_NAME = "nretykiety"`
- Skrypt SQL `004_AddLabelNumbering.sql` do inicjalizacji wpisu w `docnumber`

### 4. Infrastruktura i testy
**Dodano:**
- Rejestracja `IDocNumberRepository` i `NumberGenerationService` w DI
- Testy jednostkowe `DocNumberRepositoryTests` i `NumberGenerationServiceTests`
- Aktualizacja testów `CreateReceiveItemHandlerTests` z nowymi dependency
- EF Core mapping dla `DocNumber` entity w `WmsDbContext`
