using FluentAssertions;
using System.Net;
using Wms.Application.DTOs.Auth;
using Wms.Application.DTOs.Pallets;
using Wms.IntegrationTests.Infrastructure;

namespace Wms.IntegrationTests.Controllers;

public class LocationsControllerIntegrationTests : IntegrationTestBase
{
    public LocationsControllerIntegrationTests(WmsWebApplicationFactory factory) : base(factory)
    {
    }

    private async Task<string> GetAuthTokenAsync()
    {
        var loginRequest = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "integration-test-device"
        };

        var loginResponse = await Client.PostAsync(
            "/api/v1.0/auth/login-scan",
            CreateJsonContent(loginRequest));

        loginResponse.EnsureSuccessStatusCode();
        var loginData = await DeserializeResponseAsync<LoginScanResponse>(loginResponse);
        return loginData!.Token;
    }

    private async Task SetupAuthenticatedClientAsync()
    {
        var token = await GetAuthTokenAsync();
        SetAuthorizationHeader(token);
        SetDeviceIdHeader("integration-test-device");
    }

    [Fact]
    public async Task GetLocation_WithValidLocationCode_ReturnsLocationInfo()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-01-A-01-01";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
        locationInfo.Should().NotBeNull();
        locationInfo!.Code.Should().Be(locationCode);
        locationInfo.Hala.Should().Be(1);
        locationInfo.Regal.Should().Be("A");
        locationInfo.Miejsce.Should().Be(1);
        locationInfo.Poziom.Should().Be("01");
        locationInfo.IsVisible.Should().BeTrue();
        locationInfo.MaxCapacity.Should().Be(10);
        locationInfo.CurrentPalletCount.Should().BeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public async Task GetLocation_WithNonExistentLocation_ReturnsNotFound()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-99-Z-99-99";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetLocation_WithoutAuthentication_ReturnsUnauthorized()
    {
        // Arrange
        ClearHeaders();
        var locationCode = "MP-01-A-01-01";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Theory]
    [InlineData("INVALID-CODE")]
    [InlineData("")]
    [InlineData("123-456")]
    [InlineData("MP-AA-BB-CC")]
    public async Task GetLocation_WithInvalidLocationCode_ReturnsBadRequest(string locationCode)
    {
        // Arrange
        await SetupAuthenticatedClientAsync();

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("MP-01-A-01-01")] // Standard format
    [InlineData("MP-01-A-01-02")] // Another standard format
    [InlineData("MP-02-B-05-01")] // Different hall and rack
    public async Task GetLocation_WithVariousValidCodes_ReturnsCorrectData(string locationCode)
    {
        // Arrange
        await SetupAuthenticatedClientAsync();

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
            locationInfo.Should().NotBeNull();
            locationInfo!.Code.Should().Be(locationCode);
            locationInfo.IsVisible.Should().BeTrue(); // All test locations should be visible
        }
        else
        {
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }
    }

    [Fact]
    public async Task GetLocation_ChecksCurrentPalletCount()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-01-A-01-01";

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
        locationInfo.Should().NotBeNull();
        
        // Verify that current pallet count is realistic (>= 0 and <= MaxCapacity)
        locationInfo!.CurrentPalletCount.Should().BeGreaterThanOrEqualTo(0);
        locationInfo.CurrentPalletCount.Should().BeLessThanOrEqualTo(locationInfo.MaxCapacity);

        // Since we seeded data with one pallet in MP-01-A-01-01, count should be at least 1
        if (locationCode == "MP-01-A-01-01")
        {
            locationInfo.CurrentPalletCount.Should().BeGreaterThanOrEqualTo(1);
        }
    }

    [Fact]
    public async Task GetLocation_DatabaseConsistency_ReturnsAccurateData()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-01-A-01-01";

        // Get database context to verify data consistency
        using var dbContext = await GetDbContextAsync();
        var dbLocation = dbContext.Locations.First(l => l.Code == locationCode);
        var actualPalletCount = dbContext.Labels.Count(l => l.Miejscep == dbLocation.Id && l.IsActive);

        // Act
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
        locationInfo.Should().NotBeNull();
        
        // Verify API response matches database
        locationInfo!.Id.Should().Be(dbLocation.Id);
        locationInfo.Code.Should().Be(dbLocation.Code);
        locationInfo.Hala.Should().Be(dbLocation.Hala);
        locationInfo.Regal.Should().Be(dbLocation.Regal);
        locationInfo.Miejsce.Should().Be(dbLocation.Miejsce);
        locationInfo.Poziom.Should().Be(dbLocation.Poziom);
        locationInfo.IsVisible.Should().Be(dbLocation.IsVisible);
        locationInfo.IsPickingLocation.Should().Be(dbLocation.IsPickingLocation);
        locationInfo.MaxCapacity.Should().Be((int)dbLocation.MaxPojemnosc);
        locationInfo.CurrentPalletCount.Should().Be(actualPalletCount);
    }

    [Fact]
    public async Task GetLocation_MultipleLocations_ReturnsDifferentData()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var location1 = "MP-01-A-01-01";
        var location2 = "MP-01-A-01-02";
        var location3 = "MP-02-B-05-01";

        // Act
        var response1 = await Client.GetAsync($"/api/v1.0/locations/{location1}");
        var response2 = await Client.GetAsync($"/api/v1.0/locations/{location2}");
        var response3 = await Client.GetAsync($"/api/v1.0/locations/{location3}");

        // Assert
        response1.StatusCode.Should().Be(HttpStatusCode.OK);
        response2.StatusCode.Should().Be(HttpStatusCode.OK);
        response3.StatusCode.Should().Be(HttpStatusCode.OK);

        var locationInfo1 = await DeserializeResponseAsync<LocationInfo>(response1);
        var locationInfo2 = await DeserializeResponseAsync<LocationInfo>(response2);
        var locationInfo3 = await DeserializeResponseAsync<LocationInfo>(response3);

        // Verify locations are different
        locationInfo1!.Id.Should().NotBe(locationInfo2!.Id);
        locationInfo2.Id.Should().NotBe(locationInfo3!.Id);
        locationInfo1.Id.Should().NotBe(locationInfo3.Id);

        // Verify specific attributes
        locationInfo1.Hala.Should().Be(1);
        locationInfo1.Regal.Should().Be("A");
        locationInfo1.Poziom.Should().Be("01");

        locationInfo2.Hala.Should().Be(1);
        locationInfo2.Regal.Should().Be("A");
        locationInfo2.Poziom.Should().Be("02");

        locationInfo3.Hala.Should().Be(2);
        locationInfo3.Regal.Should().Be("B");
        locationInfo3.Miejsce.Should().Be(5);
        locationInfo3.IsPickingLocation.Should().BeTrue(); // Based on seed data
    }

    [Fact]
    public async Task GetLocation_AfterPalletMovement_UpdatesPalletCount()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var sourceLocation = "MP-01-A-01-01";
        var targetLocation = "MP-01-A-01-02";
        var palletCode = "123456789012345678";

        // Step 1: Get initial pallet counts
        var initialSourceResponse = await Client.GetAsync($"/api/v1.0/locations/{sourceLocation}");
        var initialTargetResponse = await Client.GetAsync($"/api/v1.0/locations/{targetLocation}");

        initialSourceResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        initialTargetResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var initialSourceInfo = await DeserializeResponseAsync<LocationInfo>(initialSourceResponse);
        var initialTargetInfo = await DeserializeResponseAsync<LocationInfo>(initialTargetResponse);

        var initialSourceCount = initialSourceInfo!.CurrentPalletCount;
        var initialTargetCount = initialTargetInfo!.CurrentPalletCount;

        // Step 2: Move pallet
        var moveRequest = new MovePalletRequest
        {
            ToLocationCode = targetLocation,
            Notes = "Location count test move"
        };

        var moveResponse = await Client.PostAsync(
            $"/api/v1.0/pallets/{palletCode}/move",
            CreateJsonContent(moveRequest));

        moveResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 3: Verify updated pallet counts
        var finalSourceResponse = await Client.GetAsync($"/api/v1.0/locations/{sourceLocation}");
        var finalTargetResponse = await Client.GetAsync($"/api/v1.0/locations/{targetLocation}");

        finalSourceResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        finalTargetResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var finalSourceInfo = await DeserializeResponseAsync<LocationInfo>(finalSourceResponse);
        var finalTargetInfo = await DeserializeResponseAsync<LocationInfo>(finalTargetResponse);

        // Assert counts have changed appropriately
        finalSourceInfo!.CurrentPalletCount.Should().Be(initialSourceCount - 1);
        finalTargetInfo!.CurrentPalletCount.Should().Be(initialTargetCount + 1);
    }

    [Fact]
    public async Task GetLocation_WithSpecialCharacters_HandledCorrectly()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        
        // Test URL encoding with special characters that might appear in location codes
        var testCodes = new[]
        {
            "MP-01-A-01-01", // Normal case
            "MP%2D01%2DA%2D01%2D01", // URL encoded hyphens
        };

        foreach (var locationCode in testCodes)
        {
            // Act
            var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");

            // Assert
            // Either it should work (if properly decoded) or return BadRequest/NotFound
            response.StatusCode.Should().BeOneOf(
                HttpStatusCode.OK, 
                HttpStatusCode.BadRequest, 
                HttpStatusCode.NotFound);
        }
    }

    [Fact]
    public async Task GetLocation_ConcurrentRequests_HandleProperly()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-01-A-01-01";

        // Act - Make multiple concurrent requests to the same location
        var tasks = Enumerable.Range(0, 5)
            .Select(_ => Client.GetAsync($"/api/v1.0/locations/{locationCode}"))
            .ToArray();

        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().AllSatisfy(response => 
            response.StatusCode.Should().Be(HttpStatusCode.OK));

        // All responses should return the same data
        var locationInfos = new List<LocationInfo>();
        foreach (var response in responses)
        {
            var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
            locationInfos.Add(locationInfo!);
        }

        locationInfos.Should().AllSatisfy(info => info.Code.Should().Be(locationCode));
        
        // All location infos should be equivalent
        var firstInfo = locationInfos[0];
        locationInfos.Skip(1).Should().AllSatisfy(info => 
            info.Should().BeEquivalentTo(firstInfo));
    }

    [Fact]
    public async Task GetLocation_PerformanceTest_RespondsQuickly()
    {
        // Arrange
        await SetupAuthenticatedClientAsync();
        var locationCode = "MP-01-A-01-01";

        // Act & Assert - Measure response time
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        var response = await Client.GetAsync($"/api/v1.0/locations/{locationCode}");
        
        stopwatch.Stop();

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should respond within 1 second
        
        var locationInfo = await DeserializeResponseAsync<LocationInfo>(response);
        locationInfo.Should().NotBeNull();
    }
}
