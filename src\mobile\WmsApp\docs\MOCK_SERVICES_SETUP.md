# Konfiguracja Mock Services dla Emulatorów

## Przegląd

Aplikacja WMS MAUI została skonfigurowana do automatycznego używania mockowanych serwisów autoryzacji i API w trybie debugowania na emulatorach Android. To umożliwia pełne testowanie aplikacji bez konieczności dostępu do backendu produkcyjnego lub fizycznych urządzeń Zebra.

## Zaimplementowane Mock Services

### 1. MockAuthService
- **Lokalizacja:** `Services/MockAuthService.cs`
- **Funkcjonalność:** Symuluje proces logowania i zarządzania sesją użytkownika
- **Zwraca:** Mockowy token JWT i informacje użytkownika
- **<PERSON>zas ważności tokena:** 8 godzin

### 2. MockWmsApiService  
- **Lokalizacja:** `Services/MockWmsApiService.cs`
- **Funkcjonalność:** Implementuje interfejs IWmsApiService z Refit
- **Obsługuje:** Login/logout, zarządzanie paletami, pobieranie lokalizacji
- **Zgodność:** Pełna kompatybilność z IApiResponse<T> z biblioteki Refit

## Automatyczna Konfiguracja

### Logika Wyboru Serwisów
Aplikacja automatycznie wybiera odpowiednie implementacje serwisów na podstawie:

1. **Tryb DEBUG** - używa mockowanych serwisów na emulatorach
2. **Detekcja emulatorów** - IDeviceDetectionService wykrywa typ urządzenia
3. **Fallback** - na prawdziwych urządzeniach używa rzeczywistych serwisów

### Konfiguracja w MauiProgram.cs

```csharp path=null start=null
// Authentication Service - warunkowa rejestracja
services.AddSingleton<IAuthService>(provider =>
{
    var deviceDetection = provider.GetRequiredService<IDeviceDetectionService>();
#if DEBUG
    if (deviceDetection.IsEmulator)
    {
        return new MockAuthService();
    }
#endif
    return new AuthService(provider.GetRequiredService<IPreferences>());
});

// WMS API Service - warunkowa rejestracja  
services.AddSingleton<IWmsApiService>(provider =>
{
    var deviceDetection = provider.GetRequiredService<IDeviceDetectionService>();
#if DEBUG
    if (deviceDetection.IsEmulator)
    {
        return new MockWmsApiService();
    }
#endif
    var apiConfig = provider.GetRequiredService<IApiConfigurationService>();
    return apiConfig.CreateApiService();
});
```

## Testowanie Mock Services

### Uruchomienie na Emulatorze
1. Uruchom emulator Android (API Level 26+)
2. Wdrożenie aplikacji: `dotnet build -f net9.0-android`
3. Zaloguj się używając dowolnego kodu (mock przyjmuje wszystkie)
4. Testuj funkcjonalności - wszystkie API calls będą symulowane

### Debug Output
Mock services generują logi diagnostyczne:
- `[MOCK-AUTH] Using MockAuthService for emulator`
- `[MOCK-API] Using MockWmsApiService for emulator` 
- Szczegółowe informacje o operacjach

## Mockie Dane

### MockAuthService
- **Użytkownik:** "Mock User (Emulator)"
- **Stanowisko:** "Operator"
- **Email:** "<EMAIL>"
- **Departament:** "Warehouse"

### MockWmsApiService  
- **Lokalizacje:** 10 mockowych lokalizacji (A-01-01-01 do A-05-02-01)
- **Palety:** 5 mockowych palet z różnymi statusami
- **API Response:** Symulowane odpowiedzi HTTP z success/error scenarios

## Rozwiązywanie Problemów

### Błędy Kompilacji
- ✅ **Rozwiązane:** Niekompatybilne właściwości UserInfo (CardNumber, Role, LK)
- ✅ **Rozwiązane:** Brak implementacji IApiResponse<T> w MockWmsApiService

### Sprawdzenie Poprawności
1. Sprawdź output debug czy używa mock services
2. Zweryfikuj czy aplikacja nie próbuje łączyć z backendem
3. Testuj wszystkie przepływy UI bez fizycznego skanera

## Status Implementacji

- ✅ **MockAuthService:** Kompletny i funkcjonalny
- ✅ **MockWmsApiService:** Kompletny z Refit compatibility  
- ✅ **Automatyczna konfiguracja:** Działająca
- ✅ **Kompilacja Android:** Pomyślna
- 🟡 **Testy na emulatorze:** Do wykonania

---

**Data utworzenia:** ${new Date().toLocaleDateString('pl-PL')}  
**Wersja:** v1.0  
**Status:** Gotowe do testowania
