namespace Wms.Application.DTOs;

/// <summary>
/// DTO zawierający dane etykiety z awizacji dla automatycznego wypełnienia formularza
/// </summary>
public class AwizacjaLabelDataDto
{
    /// <summary>
    /// Kod SSCC etykiety klienta
    /// </summary>
    public string EtykietaKlient { get; set; } = string.Empty;

    /// <summary>
    /// Kod towaru
    /// </summary>
    public string Kod { get; set; } = string.Empty;

    /// <summary>
    /// ID kodu towaru w systemie
    /// </summary>
    public int? KodId { get; set; }

    /// <summary>
    /// Nazwa towaru
    /// </summary>
    public string? KodNazwa { get; set; }

    /// <summary>
    /// Numer partii/lotu
    /// </summary>
    public string? Lot { get; set; }

    /// <summary>
    /// Data ważności w formacie yyyy-MM-dd
    /// </summary>
    public string? DataWaznosci { get; set; }

    /// <summary>
    /// Certyfikat/BLLOC
    /// </summary>
    public string? Blloc { get; set; }

    /// <summary>
    /// Ilość awizowana
    /// </summary>
    public decimal? Ilosc { get; set; }

    /// <summary>
    /// Ilość w opakowaniu (z kartoteki kodów)
    /// </summary>
    public decimal? IloscWOpakowaniu { get; set; }

    /// <summary>
    /// Czy dane pochodzą z awizacji
    /// </summary>
    public bool IsFromAdvice { get; set; } = true;

    /// <summary>
    /// Komunikat ostrzegawczy (jeśli występuje)
    /// </summary>
    public string? WarningMessage { get; set; }
}
