using System.Data.Common;
using System.Diagnostics;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Wms.Infrastructure.Interceptors;

public class PerformanceLoggingInterceptor : DbCommandInterceptor
{
    private readonly ILogger<PerformanceLoggingInterceptor> _logger;
    private readonly TimeSpan _slowQueryThreshold;

    public PerformanceLoggingInterceptor(ILogger<PerformanceLoggingInterceptor> logger)
    {
        _logger = logger;
        _slowQueryThreshold = TimeSpan.FromMilliseconds(1000); // 1 second
    }

    public override async ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
        DbCommand command,
        CommandEventData eventData,
        InterceptionResult<DbDataReader> result,
        CancellationToken cancellationToken = default)
    {
        // WAŻNE: Nie ustawiamy globalnie QueryTrackingBehavior w interceptorze!
        // Decyzja o AsNoTracking/AsTracking powinna być podejmowana per-zapytanie w repozytoriach.
        // Zobacz docs/ef-tracking-checklist.md dla szczegółowych zasad.
        return await base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
    }

    public override async ValueTask<DbDataReader> ReaderExecutedAsync(
        DbCommand command, 
        CommandExecutedEventData eventData, 
        DbDataReader result, 
        CancellationToken cancellationToken = default)
    {
        var duration = eventData.Duration;
        
        LogQueryPerformance(command, duration, eventData.Context?.Database.ProviderName);
        
        return await base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override async ValueTask<object?> ScalarExecutedAsync(
        DbCommand command, 
        CommandExecutedEventData eventData, 
        object? result, 
        CancellationToken cancellationToken = default)
    {
        var duration = eventData.Duration;
        
        LogQueryPerformance(command, duration, eventData.Context?.Database.ProviderName);
        
        return await base.ScalarExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override async ValueTask<int> NonQueryExecutedAsync(
        DbCommand command, 
        CommandExecutedEventData eventData, 
        int result, 
        CancellationToken cancellationToken = default)
    {
        var duration = eventData.Duration;
        
        LogQueryPerformance(command, duration, eventData.Context?.Database.ProviderName, result);
        
        return await base.NonQueryExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override async Task CommandFailedAsync(
        DbCommand command, 
        CommandErrorEventData eventData, 
        CancellationToken cancellationToken = default)
    {
        var duration = eventData.Duration;
        
        _logger.LogError(eventData.Exception,
            "Database query failed after {Duration}ms: {CommandText}",
            duration.TotalMilliseconds,
            SanitizeQuery(command.CommandText));

        LogQueryParameters(command);
        
        await base.CommandFailedAsync(command, eventData, cancellationToken);
    }

    private void LogQueryPerformance(DbCommand command, TimeSpan duration, string? providerName, int? affectedRows = null)
    {
        var queryType = GetQueryType(command.CommandText);
        var sanitizedQuery = SanitizeQuery(command.CommandText);
        
        var logLevel = duration >= _slowQueryThreshold ? LogLevel.Warning : LogLevel.Debug;
        
        if (affectedRows.HasValue)
        {
            _logger.Log(logLevel,
                "Database {QueryType} query executed in {Duration}ms, affected {AffectedRows} rows. Provider: {ProviderName}",
                queryType, duration.TotalMilliseconds, affectedRows, providerName ?? "Unknown");
        }
        else
        {
            _logger.Log(logLevel,
                "Database {QueryType} query executed in {Duration}ms. Provider: {ProviderName}",
                queryType, duration.TotalMilliseconds, providerName ?? "Unknown");
        }

        if (duration >= _slowQueryThreshold)
        {
            _logger.LogWarning("Slow query detected ({Duration}ms): {Query}",
                duration.TotalMilliseconds, sanitizedQuery);
            
            LogQueryParameters(command);
        }
        
        // Always log at debug level with full query details
        _logger.LogDebug("Query details - Type: {QueryType}, Duration: {Duration}ms, Query: {Query}",
            queryType, duration.TotalMilliseconds, sanitizedQuery);
    }

    private void LogQueryParameters(DbCommand command)
    {
        if (command.Parameters.Count > 0)
        {
            var parameters = new List<object>();
            foreach (DbParameter parameter in command.Parameters)
            {
                var paramValue = parameter.Value switch
                {
                    null => "NULL",
                    string s when s.Length > 100 => s[..100] + "...",
                    _ => parameter.Value.ToString()
                };
                
                parameters.Add(new { Name = parameter.ParameterName, Value = paramValue, Type = parameter.DbType });
            }
            
            _logger.LogDebug("Query parameters: {@Parameters}", parameters);
        }
    }

    private static string GetQueryType(string commandText)
    {
        if (string.IsNullOrWhiteSpace(commandText))
            return "Unknown";

        var firstWord = commandText.TrimStart().Split(' ', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault()?.ToUpperInvariant();
        
        return firstWord switch
        {
            "SELECT" => "SELECT",
            "INSERT" => "INSERT",
            "UPDATE" => "UPDATE",
            "DELETE" => "DELETE",
            "EXEC" or "EXECUTE" => "PROCEDURE",
            "CREATE" => "DDL",
            "ALTER" => "DDL",
            "DROP" => "DDL",
            _ => "Unknown"
        };
    }

    private static string SanitizeQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return "Empty query";

        // Remove excessive whitespace and newlines
        var sanitized = System.Text.RegularExpressions.Regex.Replace(query, @"\s+", " ").Trim();
        
        // Truncate very long queries
        if (sanitized.Length > 500)
        {
            sanitized = sanitized[..500] + "...";
        }
        
        return sanitized;
    }
}

// Extension method for easy registration
public static class PerformanceLoggingInterceptorExtensions
{
    public static IServiceCollection AddDatabasePerformanceLogging(this IServiceCollection services)
    {
        services.AddScoped<PerformanceLoggingInterceptor>();
        return services;
    }
}
