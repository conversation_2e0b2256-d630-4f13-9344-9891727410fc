using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class Location : BaseEntity
{
    public int Id { get; set; }
    public int Hala { get; set; } = 0;
    public string Regal { get; set; } = string.Empty;
    public int Miejsce { get; set; } = 0;
    public string? Poziom { get; set; }
    public string Wysokosc { get; set; } = string.Empty;
    public uint Widoczne { get; set; } = 1;
    public uint Zbiorka { get; set; } = 0;
    public uint MaxPojemnosc { get; set; } = 0;
    public uint StanowiskoPracyId { get; set; } = 0;
    public uint PickingFakturowany { get; set; } = 0;
    public string Producent { get; set; } = "0";
    public uint MaxUdzwigKg { get; set; } = 0;
    
    // Computed property - kod lokalizacji w formacie MP-XXX-XXX-XXX-XXX
    public string Code => $"MP-{Hala:D3}-{Regal}-{Miejsce:D3}-{Poziom ?? "00"}";
    
    // Helper properties for business logic
    public bool IsVisible => Widoczne == 1;
    public bool IsPickingLocation => Zbiorka == 1;
    public bool IsPickingBilled => PickingFakturowany == 1;
    
    // Navigation properties
    public ICollection<Label> Labels { get; set; } = new List<Label>(); // etykiety.miejscep
    public ICollection<Movement> MovementsFrom { get; set; } = new List<Movement>(); // zmianym.stare_m
    public ICollection<Movement> MovementsTo { get; set; } = new List<Movement>(); // zmianym.nowe_m
}
