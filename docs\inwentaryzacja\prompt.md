Przeanalizuj dokumentację z katalogu `/inw/` w bieżącym repozytorium i na jej podstawie stwórz nową, kompleksową dokumentację dla modułu inwentaryzacji w nowym katalogu `/docs/inwentaryzacja/`.

**Wymagania dokumentacji:**

1. **Struktura katalogów:**
   - <PERSON><PERSON><PERSON><PERSON> katalog `/docs/inwentaryzacja/` jesli nie istnieje
   - Zorganizuj dokumentację w logiczne pliki (np. README.md, WORKFLOW.md, API.md, itp.)

2. **Zakres funkcjonalności do udokumentowania:**
   - Inwentaryzacja towaru po kodzie SSCC (18-cyfrowy kod Serial Shipping Container Code)
   - Inwentaryzacja towaru po kodzie DS (format DS + 4-9 cyfr)
   - Inwentaryzacja według lokalizacji/miejsca (format MP-H-R-M-P)

3. **Elementy do uwzględnienia:**
   - Przepływy procesów (workflows) dla każdego typu inwentaryzacji
   - Specyfikacja API endpoints dla modułu inwentaryzacji
   - Modele danych i struktury DTO
   - Instrukcje użytkowania dla aplikacji mobilnej
   - Integracja z istniejącym systemem WMS
   - Obsługa skanerów Zebra MC3300 i DataWedge

4. **Format dokumentacji:**
   - Używaj języka polskiego
   - Stosuj format Markdown (.md)
   - Dodaj diagramy przepływów procesów (jeśli są w źródłowej dokumentacji)
   - Uwzględnij przykłady kodów i żądań API
   - Zachowaj spójność z istniejącą dokumentacją projektu WMS

5. **Adaptacja do bieżącego projektu:**
   - Dostosuj dokumentację do architektury Clean Architecture używanej w projekcie
   - Uwzględnij wzorce CQRS i MediatR
   - Opisz integrację z Entity Framework Core i MySQL
   - Zachowaj spójność z konwencjami nazewnictwa projektu

Przeanalizuj najpierw zawartość katalogu `/inw/`, a następnie stwórz strukturę dokumentacji, która będzie służyć jako przewodnik implementacji i użytkowania modułu inwentaryzacji w systemie WMS.