# PRD – WMS Mobile (MAUI Android) + .NET 9 Backend

Wersja dokumentu: 0.2  
Data: 2025-08-31  
Właściciel: ldomansk

## 1. Wizja i cele
System WMS do obsługi magazynu na skanerach Zebra MC3300 (Android), z backendem .NET uruchomionym w sieci lokalnej. Na start koncentrujemy się na dwóch krytycznych operacjach: logowaniu użytkownika (skanem karty) oraz zmianie lokalizacji palety (workflow „scan → scan”, bez dodatkowego potwierdzenia).

## 2. <PERSON><PERSON><PERSON> MVP (Minimal Viable Product)
- Logowanie użytkownika przez skan karty (EAN‑128/numer karty). Bez PIN.
- Zmiana lokalizacji palety: skan kodu palety → skan docelowej lokalizacji → natychmiastowy zapis (brak potwierdzenia). 
- Brak trybu offline (aplikacja działa online w LAN). 
- API REST v1. 
- Baza danych: MySQL. 
- Platforma mobilna: tylko Android (Zebra MC3300), min. Android 10, integracja skanera przez Zebra DataWedge.
- Auto‑aktualizacja aplikacji z serwera w LAN (opis w sekcji 14).

Poza MVP (backlog na później): przyjęcie towaru (PO/ASN), wydanie/picking, inwentaryzacja, druk etykiet (ZPL), powiadomienia/push, panel administracyjny, integracje ERP.

## 3. Użytkownicy i kontekst
- Persona: Magazynier pracujący w magazynie. 
- Środowisko: praca online w sieci lokalnej (LAN), komunikacja z serwerem w siedzibie (Debian + Apache). 
- Wymagania UX: interfejs PL, duże przyciski i kontrolki, maksymalnie mało „tapnięć”, preferencja skanowania zamiast wpisywania.

## 4. Platformy i urządzenia
- Urządzenia: Zebra MC3300 (Android). 
- Minimalna wersja Android: 10 (minSdk 29). 
- Integracja ze skanerem: Zebra DataWedge (Intent). 
- Sygnał po skanie: dźwięk (beep). 

## 5. Identyfikatory i kody
- Paleta:
  - SSCC (GS1‑128, AI 00, 18 cyfr) lub kod wewnętrzny z prefiksem „DS” i 8 cyfr (np. DS12345678).
- Lokalizacja:
  - Format: „MP-<hala>-<regał>-<miejsce>-<poziom>”, np. „MP-1-145-002-1”.  
  - Znaczenie: MP (prefiks miejsca paletowego) – Hala – Regał – Miejsce – Poziom.  
  - Walidacja: dokładna (patrz sekcja „Załączniki – wzorce”).
- Karta pracownika: numer karty, np. „00000123453” (skanowany z EAN‑128/GS1‑128). 

## 6. Przepływy procesów (MVP)
### 6.1 Logowanie (skan karty)
- Magazynier skanuje kartę pracownika. 
- Aplikacja wysyła żądanie do API: weryfikacja i wydanie tokenu JWT (ważny 1h). 
- Mechanizm bezczynności: jeśli na urządzeniu nie było aktywności przez 1h (od ostatniej operacji), aplikacja wymusza ponowne zalogowanie skanem karty (nie używamy refresh tokenów w MVP). 

Edge cases: 
- Nieznany numer karty → komunikat o błędzie i brak logowania. 
- Użytkownik zablokowany → komunikat o blokadzie. 

### 6.2 Zmiana lokalizacji palety (bez potwierdzenia)
- Użytkownik skanuje kod palety (SSCC/DS). 
- Aplikacja natychmiast przechodzi do skanu lokalizacji docelowej. 
- Po skanie lokalizacji aplikacja wysyła żądanie „move” do API (transakcja w DB) i od razu zapisuje zmianę. 
- Zwrotka sukcesu zawiera m.in. nową lokalizację, znacznik czasu i identyfikator ruchu. 

Edge cases: 
- Paleta nieznana w systemie → błąd (w MVP zawsze odrzucamy; paleta musi istnieć).  
  - Opcja „auto‑utworzenia palety przy pierwszym ruchu” – do rozważenia poza MVP.
- Lokalizacja nie istnieje / nieaktywna → błąd walidacji. 
- Konflikty współbieżności: zastosować optymistyczne blokowanie (wersjonowanie rekordu) i rejestrować historię konfliktów. 

## 7. Wymagania funkcjonalne (MVP)
- Logowanie skanem karty: 
  - Bez PIN. 
  - Widoczna nazwa zalogowanego użytkownika i licznik czasu bezczynności. 
  - Po 1h braku aktywności – ekran blokady z prośbą o ponowny skan karty. 
- Zmiana lokalizacji palety: 
  - Sekwencja „paleta → lokalizacja”, bez dodatkowego potwierdzenia. 
  - Reguły walidacji kodów (sekcja 5, załączniki). 
  - Zapis historii ruchów (kto, kiedy, z/do, urządzenie). 
- API REST v1 z jasnym kontraktem i kodami błędów. 
- Brak trybu offline. 

## 8. Wymagania niefunkcjonalne
- Użytkownicy: do 100 dziennie; równoległość operacji: do 30 aktywnych operatorów. 
- Wydajność: p95 czasu odpowiedzi API < 200 ms w LAN (dla operacji MVP). 
- Niezawodność: dostępność 99.5% w godzinach pracy. 
- Aplikacja mobilna: rozmiar APK < 60 MB; cold start < 2.5 s na MC3300. 

## 9. Backend i API
- Technologia: ASP.NET Core (.NET 9). 
- Styl API: REST, JSON, wersjonowanie ścieżką: /api/v1/... 
- Uwierzytelnianie: Bearer JWT (ważny 1h). 
- Transakcje: zapisy ruchów wykonywane transakcyjnie. 
- Paginacja/filtry: dla MVP ograniczone (endpointy MVP nie paginują). 
- Real‑time (SignalR): opcjonalnie w backlogu (np. broadcast wydarzeń do panelu). 

Proponowane endpointy MVP:
- POST /api/v1/auth/login-scan  
  Body: { cardNumber } → 200 OK: { token, user: { id, name } } 
- POST /api/v1/pallets/{palletCode}/move  
  Body: { toLocation, deviceId, occurredAt } → 200 OK: { movementId, palletCode, fromLocation, toLocation, userId, occurredAt }
- GET /api/v1/locations/{locationCode}  
  Walidacja i metadane lokalizacji (np. aktywna/nieaktywna). 

## 10. Dane i baza danych
- Silnik: MySQL 8.0. 
- ORM: EF Core (provider Pomelo). 
- Migracje: EF Core migrations w repozytorium. 

Minimalny model (MVP): 
- User(id, name, cardNumber, isActive, createdAt, updatedAt)
- Session(id, userId, issuedAt, expiresAt, deviceId)
- Location(code, area, hall, rack, slot, level, isActive)
- Pallet(code, currentLocationCode, createdAt, updatedAt)
- Movement(id, palletCode, fromLocationCode, toLocationCode, userId, deviceId, occurredAt)

Uwagi: 
- W MVP paleta musi istnieć przed ruchem (patrz 6.2). 
- Lista lokalizacji jest wstępnie załadowana i utrzymywana przez backend. 

## 11. Bezpieczeństwo
- JWT access token: ważny 1h. 
- Idle timeout: wymuszenie ponownego skanu karty po 1h bezczynności aplikacji. 
- Brak refresh tokenów w MVP. 
- Wymagane: HTTPS w LAN przez Apache (reverse proxy do Kestrela); certyfikat z wewnętrznego CA lub self‑signed wdrożony na urządzeniach. 
- Kontrola dostępu oparta o aktywność użytkownika (brak ról na start). 

## 12. UI/UX (MVP)
- Interfejs PL/EN (rozszerzalny), duże kontrolki, nawigacja uproszczona (np. MAUI Shell z minimalnymi ekranami). 
- Flow sterowany skanami; minimalizacja dotyku ekranu. 
- Sygnał dźwiękowy po udanym skanie; czytelne komunikaty błędów. 
- Lokalizacja (i18n): zob. przewodnik: ../src/mobile/WmsApp/docs/LOCALIZATION_GUIDE.md

## 13. Infrastruktura i wdrożenie
- Serwer: Debian Linux. 
- Reverse proxy: Apache (HTTP → Kestrel), przygotowany pod WebSocket (SignalR) na przyszłość. 
- Środowiska: dev, test, prod (oddzielne bazy i konfiguracje). 
- Konteneryzacja: opcjonalnie Docker dla backendu i usług towarzyszących. 

## 14. Auto‑aktualizacja aplikacji (LAN)
Rekomendowany mechanizm bez MDM: 
- Backend/serwer WWW hostuje dwa pliki: 
  - app.json – manifest z metadanymi wersji, 
  - app.apk – pakiet instalacyjny. 
- Aplikacja mobilna przy starcie (i na żądanie w „O aplikacji”) pobiera app.json spod adresu:  
  https://***********/wms/app.json 
- Jeśli wersja w manifestcie > wersja zainstalowana: 
  1) wyświetla komunikat o dostępnej aktualizacji (jeśli „required” = true – blokujący),  
  2) pobiera APK,  
  3) weryfikuje sumę SHA‑256,  
  4) uruchamia instalację przez pakiet instalacyjny Android (wymaga zezwolenia na instalację z nieznanych źródeł). 
- Bezpieczeństwo: wymagany HTTPS (Apache) + weryfikacja sumy SHA‑256. 
- Alternatywa lepsza operacyjnie: MDM/EMM (Zebra StageNow, OEMConfig, Intune, SOTI) – umożliwia ciche/masowe aktualizacje i centralne profile DataWedge. 

Przykładowy manifest (app.json):

```json path=null start=null
{
  "appId": "com.example.wms",
  "version": "1.0.3",
  "versionCode": 1003,
  "minSdk": 29,
  "required": false,
  "apkUrl": "https://***********/wms/app.apk",
  "sha256": "<TU_WSTAW_SUMĘ_SHA256_APK>",
  "changelog": "Naprawa skanowania i poprawa stabilności.",
  "releasedAt": "2025-09-01T12:00:00Z"
}
```

Kryteria akceptacji (auto‑update): 
- Aplikacja wykrywa nową wersję na podstawie app.json i proponuje aktualizację. 
- Po pobraniu APK weryfikowana jest suma SHA‑256; błędna suma blokuje instalację. 
- Aktualizacja kończy się sukcesem i po ponownym uruchomieniu widoczna jest nowa wersja. 

## 15. Observability i jakość (wybrane rozwiązanie)
Wybrane rozwiązanie: 
- Logi aplikacyjne backendu: Serilog → konsola/pliki + Promtail → Grafana Loki (lekki i tani w utrzymaniu). 
- Metryki/trace: OpenTelemetry → Prometheus + Grafana (wizualizacja). 
- Raportowanie crashy mobile: Sentry (self‑host w Docker) lub alternatywnie proste raporty do backendu (mniej informacji). 
- Audyt operacji (kto, co, kiedy): zapisywany w DB (MySQL) w tabeli Movement i ewentualnie tabeli AuditLog (zdarzenia biznesowe). 

Alternatywa (jeśli koniecznie MySQL): 
- Serilog.Sinks.MySql do zapisywania logów tekstowych do MySQL z retencją i indeksami; zalecane krótkie retencje (np. 7–14 dni) i osobna tabela dla logów technicznych. 

Testy: 
- Jednostkowe (xUnit), integracyjne backendu, podstawowe testy UI MAUI. 
- Pokrycie min. 70% krytycznego kodu. 

## 16. Kryteria akceptacji (MVP)
Logowanie: 
- [ ] Skan karty „00000123453” loguje użytkownika aktywnego i wydaje JWT ważny 1h. 
- [ ] Po 1h bezczynności aplikacja wymusza ponowne zalogowanie skanem. 
- [ ] Nieznana/nieaktywna karta zwraca czytelny błąd; brak logowania. 

Zmiana lokalizacji: 
- [ ] Po skanie prawidłowego kodu palety oraz lokalizacji powstaje wpis Movement z pełnym audytem. 
- [ ] Dla nieistniejącej palety – błąd. 
- [ ] Dla nieistniejącej lub nieaktywnej lokalizacji – błąd. 
- [ ] Operacja wykonuje się transakcyjnie; w razie błędu brak częściowych zapisów. 

Auto‑aktualizacja: 
- [ ] Aplikacja wykrywa nową wersję na podstawie app.json i uruchamia pół‑automatyczną instalację. 
- [ ] Suma SHA‑256 jest weryfikowana przed instalacją. 

## 17. Ryzyka
- Brak MDM/EMM utrudnia cichą dystrybucję aktualizacji i spójne profile DataWedge. 
- Błędna konfiguracja DataWedge może powodować nieoczekiwane formaty danych. 
- Brak HTTPS w LAN naraża na modyfikację ruchu (zalecany TLS). 

## 18. Otwarte decyzje
- Lista i struktura lokalizacji – import inicjalny i proces utrzymania. 
- Wybór narzędzi CI/CD (GitHub Actions/GitLab CI) i proces wydawniczy APK. 

## Załączniki – wzorce (do walidacji kodów)
- SSCC (AI 00, 18 cyfr): 
  - uproszczony wzorzec: ^\d{18}$  
- Kod wewnętrzny palety: 
  - ^DS\d{8}$ 
- Lokalizacja „MP-<hala>-<regał>-<miejsce>-<poziom>”: 
  - ^MP-\d+-\d+-\d+-\d+$ 
- Numer karty pracownika: 
  - ^\d{5,20}$ (można zawęzić po poznaniu pełnej specyfikacji kart)

---

Notatki techniczne (skrót):
- .NET 9 + ASP.NET Core, EF Core (Pomelo MySQL). 
- MAUI (najnowsza stabilna zgodna z .NET 9), Android minSdk 29. 
- DataWedge: profil przypisany do aplikacji (Intent action/extra), odbiór danych w foreground. 
- Apache reverse proxy do Kestrela; przygotowanie pod WebSocket (SignalR) na przyszłość.

