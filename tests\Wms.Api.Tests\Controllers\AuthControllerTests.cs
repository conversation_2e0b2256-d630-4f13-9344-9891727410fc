using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Moq;
using System.Security.Claims;
using Wms.Api.Controllers;
using Wms.Application.DTOs.Auth;
using Wms.Application.Interfaces;

namespace Wms.Api.Tests.Controllers;

public class AuthControllerTests
{
    private readonly Mock<IAuthenticationService> _mockAuthService;
    private readonly AuthController _controller;

    public AuthControllerTests()
    {
        _mockAuthService = new Mock<IAuthenticationService>();
        _controller = new AuthController(_mockAuthService.Object);
        
        // Konfiguracja HttpContext dla testów
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-Forwarded-For"] = "***********";
        httpContext.Request.Headers["X-Device-Id"] = "device-123";
        httpContext.Connection.RemoteIpAddress = System.Net.IPAddress.Parse("127.0.0.1");
        
        _controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };
    }

    [Fact]
    public async Task LoginScan_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "device-123"
        };

        var expectedResponse = new LoginScanResponse
        {
            Token = "jwt-token",
            ExpiresAt = DateTime.UtcNow.AddHours(8),
            User = new UserInfo
            {
                Id = 1,
                FullName = "Jan Kowalski",
                Position = "Warehouse Worker",
                Email = "<EMAIL>",
                Department = "Warehouse"
            }
        };

        _mockAuthService
            .Setup(s => s.LoginWithCardAsync(It.IsAny<LoginScanRequest>(), It.IsAny<string>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.LoginScan(request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        
        var okResult = result.Result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(expectedResponse);
        
        _mockAuthService.Verify(s => s.LoginWithCardAsync(request, "***********"), Times.Once);
    }

    [Fact]
    public async Task LoginScan_WithInvalidCard_ReturnsUnauthorized()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "invalid-card",
            DeviceId = "device-123"
        };

        _mockAuthService
            .Setup(s => s.LoginWithCardAsync(It.IsAny<LoginScanRequest>(), It.IsAny<string>()))
            .ReturnsAsync((LoginScanResponse?)null);

        // Act
        var result = await _controller.LoginScan(request);

        // Assert
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<UnauthorizedObjectResult>();
        
        var unauthorizedResult = result.Result as UnauthorizedObjectResult;
        unauthorizedResult!.Value.Should().BeOfType<ProblemDetails>();
        
        var problemDetails = unauthorizedResult.Value as ProblemDetails;
        problemDetails!.Status.Should().Be(401);
        problemDetails.Title.Should().Be("Unauthorized");
        problemDetails.Detail.Should().Be("Invalid card number");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task LoginScan_WithInvalidCardNumber_ReturnsUnauthorized(string? cardNumber)
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber!,
            DeviceId = "device-123"
        };

        _mockAuthService
            .Setup(s => s.LoginWithCardAsync(It.IsAny<LoginScanRequest>(), It.IsAny<string>()))
            .ReturnsAsync((LoginScanResponse?)null);

        // Act
        var result = await _controller.LoginScan(request);

        // Assert
        result.Result.Should().BeOfType<UnauthorizedObjectResult>();
    }

    [Fact]
    public async Task LoginScan_ExtractsClientIpFromXForwardedFor()
    {
        // Arrange
        var request = new LoginScanRequest
        {
            CardNumber = "1234567890",
            DeviceId = "device-123"
        };

        var expectedResponse = new LoginScanResponse
        {
            Token = "jwt-token",
            ExpiresAt = DateTime.UtcNow.AddHours(8),
            User = new UserInfo
            {
                Id = 1,
                FullName = "Jan Kowalski",
                Position = "Worker",
                Email = "<EMAIL>"
            }
        };

        _mockAuthService
            .Setup(s => s.LoginWithCardAsync(It.IsAny<LoginScanRequest>(), "***********"))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.LoginScan(request);

        // Assert
        _mockAuthService.Verify(s => s.LoginWithCardAsync(request, "***********"), Times.Once);
    }

    [Fact]
    public async Task Logout_WithValidUser_ReturnsOkResult()
    {
        // Arrange
        var userId = 1;
        var deviceId = "device-123";

        var claims = new List<Claim>
        {
            new("userId", userId.ToString())
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var claimsPrincipal = new ClaimsPrincipal(identity);

        _controller.ControllerContext.HttpContext.User = claimsPrincipal;

        _mockAuthService
            .Setup(s => s.LogoutAsync(userId, deviceId))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.Logout();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        
        var okResult = result as OkObjectResult;
        var responseValue = okResult!.Value;
        responseValue.Should().BeEquivalentTo(new { Message = "Logged out successfully" });
        
        _mockAuthService.Verify(s => s.LogoutAsync(userId, deviceId), Times.Once);
    }

    [Fact]
    public async Task Logout_WithoutAuthentication_ReturnsUnauthorized()
    {
        // Arrange
        // User bez claims (nie uwierzytelniony)
        var identity = new ClaimsIdentity();
        var claimsPrincipal = new ClaimsPrincipal(identity);
        _controller.ControllerContext.HttpContext.User = claimsPrincipal;

        // Act
        var result = await _controller.Logout();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();
        
        _mockAuthService.Verify(s => s.LogoutAsync(It.IsAny<int>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task Logout_WithInvalidUserId_ThrowsFormatException()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new("userId", "invalid-user-id")
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var claimsPrincipal = new ClaimsPrincipal(identity);

        _controller.ControllerContext.HttpContext.User = claimsPrincipal;

        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() => _controller.Logout());
    }

    [Fact]
    public async Task Logout_WhenServiceThrowsUnauthorized_ReturnsUnauthorized()
    {
        // Arrange
        var userId = 1;
        var deviceId = "device-123";

        var claims = new List<Claim>
        {
            new("userId", userId.ToString())
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var claimsPrincipal = new ClaimsPrincipal(identity);

        _controller.ControllerContext.HttpContext.User = claimsPrincipal;

        _mockAuthService
            .Setup(s => s.LogoutAsync(userId, deviceId))
            .ThrowsAsync(new UnauthorizedAccessException());

        // Act
        var result = await _controller.Logout();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();
    }

    [Fact]
    public void GetClientIpAddress_WithXForwardedFor_ReturnsFirstIp()
    {
        // Arrange
        _controller.ControllerContext.HttpContext.Request.Headers["X-Forwarded-For"] = "***********, ********";

        // Act
        // Używamy refleksji aby przetestować protected metodę
        var method = typeof(AuthController).BaseType!.GetMethod("GetClientIpAddress", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var result = method!.Invoke(_controller, null) as string;

        // Assert
        result.Should().Be("***********");
    }

    [Fact]
    public void GetDeviceId_WithDeviceIdHeader_ReturnsDeviceId()
    {
        // Arrange
        _controller.ControllerContext.HttpContext.Request.Headers["X-Device-Id"] = "test-device-id";

        // Act
        var method = typeof(AuthController).BaseType!.GetMethod("GetDeviceId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var result = method!.Invoke(_controller, null) as string;

        // Assert
        result.Should().Be("test-device-id");
    }
}
