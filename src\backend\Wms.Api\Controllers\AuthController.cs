using Microsoft.AspNetCore.Mvc;
using Wms.Application.DTOs.Auth;
using Wms.Application.Interfaces;

namespace Wms.Api.Controllers;

[ApiVersion("1.0")]
public class AuthController : BaseApiController
{
    private readonly IAuthenticationService _authenticationService;

    public AuthController(IAuthenticationService authenticationService)
    {
        _authenticationService = authenticationService;
    }

    /// <summary>
    /// Logowanie poprzez skan karty pracownika
    /// </summary>
    /// <param name="request">Dane logowania</param>
    /// <returns>JWT token i informacje o użytkowniku</returns>
    [HttpPost("login-scan")]
    [ProducesResponseType(typeof(LoginScanResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 401)]
    public async Task<ActionResult<LoginScanResponse>> LoginScan([FromBody] LoginScanRequest request)
    {
        var ipAddress = GetClientIpAddress();
        var result = await _authenticationService.LoginWithCardAsync(request, ipAddress);

        if (result == null)
        {
            return Unauthorized(new ProblemDetails
            {
                Type = "https://tools.ietf.org/html/rfc7235#section-3.1",
                Title = "Unauthorized",
                Status = 401,
                Detail = "Invalid card number",
                Instance = Request.Path
            });
        }

        return Ok(result);
    }

    /// <summary>
    /// Wylogowanie użytkownika
    /// </summary>
    /// <returns>Status wylogowania</returns>
    [HttpPost("logout")]
    [ProducesResponseType(200)]
    [ProducesResponseType(typeof(ProblemDetails), 401)]
    public async Task<ActionResult> Logout()
    {
        try
        {
            var userId = GetCurrentUserId();
            var deviceId = GetDeviceId();
            
            await _authenticationService.LogoutAsync(int.Parse(userId), deviceId);
            return Ok(new { Message = "Logged out successfully" });
        }
        catch (UnauthorizedAccessException)
        {
            return Unauthorized();
        }
    }
}
