using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Domain.Entities.Receives;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class PalletRepository : IPalletRepository
{
    private readonly WmsDbContext _context;

    public PalletRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Pallet> CreatePalletAsync(int palletTypeId, CancellationToken cancellationToken = default)
    {
        var pallet = new Pallet
        {
            TypypaletId = palletTypeId,
            Ilosc = 0, // Początkowo pusta paleta
            TsUtworzenia = DateTime.Now
        };

        _context.Pallets.Add(pallet);
        await _context.SaveChangesAsync(cancellationToken);

        return pallet;
    }

    public async Task<ListControlPallet> CreateListControlPalletAsync(int listControlId, int paletaId, bool shouldPrint = true, CancellationToken cancellationToken = default)
    {
        // Sprawdź czy już nie istnieje taka kombinacja
        var existing = await _context.ListControlPallets
            .FirstOrDefaultAsync(lcp => lcp.ListcontrolId == listControlId && lcp.PaletaId == paletaId, cancellationToken);

        if (existing != null)
        {
            return existing;
        }

        var listControlPallet = new ListControlPallet
        {
            ListcontrolId = listControlId,
            PaletaId = paletaId,
            Wydruk = shouldPrint ? 1 : 0
        };

        _context.ListControlPallets.Add(listControlPallet);
        await _context.SaveChangesAsync(cancellationToken);

        return listControlPallet;
    }

    public async Task<IEnumerable<ListControlPallet>> GetCarriersForReceiveAsync(int listControlId, CancellationToken cancellationToken = default)
    {
        return await _context.ListControlPallets
            .AsNoTrackingWithIdentityResolution()
            .Where(lcp => lcp.ListcontrolId == listControlId)
            .Include(lcp => lcp.ListControl)
            .OrderBy(lcp => lcp.PaletaId)
            .ToListAsync(cancellationToken);
    }

    public async Task<ListControlPallet?> GetCarrierAsync(int listControlId, int paletaId, CancellationToken cancellationToken = default)
    {
        return await _context.ListControlPallets
            .AsNoTrackingWithIdentityResolution()
            .Include(lcp => lcp.ListControl)
            .FirstOrDefaultAsync(lcp => lcp.ListcontrolId == listControlId && lcp.PaletaId == paletaId, cancellationToken);
    }

    public async Task<IEnumerable<Label>> GetCarrierItemsAsync(int paletaId, CancellationToken cancellationToken = default)
    {
        return await _context.Labels
            .AsNoTrackingWithIdentityResolution()
            .Where(l => l.PaletaId == paletaId)
            .Include(l => l.Pallet)
            .Include(l => l.Location)
            .OrderBy(l => l.Id)
            .ThenBy(l => l.SystemId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TypyPalet>> GetTypyPaletAsync(CancellationToken cancellationToken = default)
    {
        return await _context.TypyPalets
            .AsNoTracking()
            .OrderBy(tp => tp.KolejnoscPal)
            .ThenBy(tp => tp.Opis)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateAsync(Pallet pallet, CancellationToken cancellationToken = default)
    {
        _context.Pallets.Update(pallet);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateListControlPalletAsync(ListControlPallet carrier, CancellationToken cancellationToken = default)
    {
        _context.ListControlPallets.Update(carrier);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<Pallet?> GetByIdAsync(int paletaId, CancellationToken cancellationToken = default)
    {
        return await _context.Pallets
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == paletaId, cancellationToken);
    }
}
