using FluentAssertions;
using Wms.Domain.Entities;
using Wms.Domain.Services;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.Services;

public class PalletGenerationServiceTests
{
    private readonly PalletGenerationService _service;

    public PalletGenerationServiceTests()
    {
        _service = new PalletGenerationService();
    }

    [Fact]
    public void GenerateDSCode_ShouldReturnValidCode()
    {
        // Act
        var result = _service.GenerateDSCode();

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().StartWith("DS");
        result.Value.Length.Should().Be(10); // DS + 8 digits
        var numericPart = result.Value.Substring(2);
        numericPart.Should().MatchRegex(@"^\d{8}$");
    }

    [Fact]
    public void GenerateDSCode_MultipleCalls_ShouldReturnUniqueValues()
    {
        // Arrange
        var codes = new HashSet<string>();
        const int iterations = 100;

        // Act
        for (int i = 0; i < iterations; i++)
        {
            var code = _service.GenerateDSCode();
            codes.Add(code.Value);
        }

        // Assert
        codes.Count.Should().Be(iterations, "wszystkie kody powinny być unikatowe");
    }

    [Fact]
    public void GenerateSSCCCode_ShouldReturnValidCode()
    {
        // Act
        var result = _service.GenerateSSCCCode();

        // Assert
        result.Should().NotBeNull();
        result.Value.Length.Should().Be(18);
        result.Value.Should().MatchRegex(@"^\d{18}$");
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void GenerateSSCCCode_MultipleCalls_ShouldReturnUniqueValues()
    {
        // Arrange
        var codes = new HashSet<string>();
        const int iterations = 100;

        // Act
        for (int i = 0; i < iterations; i++)
        {
            var code = _service.GenerateSSCCCode();
            codes.Add(code.Value);
        }

        // Assert
        codes.Count.Should().Be(iterations, "wszystkie kody SSCC powinny być unikatowe");
    }

    [Fact]
    public void CreatePallet_ValidParameters_ShouldSucceed()
    {
        // Arrange
        var dsCode = DSCode.Create("**********");
        var ssccCode = SSCCCode.Create("123456789012345678");
        var location = "A-01-01-01";
        var palletTypeId = 1;
        var userId = 10;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Pallet.Should().NotBeNull();
        result.Pallet!.Ds.Should().Be("**********");
        result.Pallet.Sscc.Should().Be("123456789012345678");
        result.Pallet.Miejsce.Should().Be(location);
        result.Pallet.TypPaletyId.Should().Be(palletTypeId);
        result.Pallet.UzytkownikId.Should().Be(userId);
        result.Pallet.DataWstawienia.Should().Be(createdAt);
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CreatePallet_NullDSCode_ShouldFail()
    {
        // Arrange
        DSCode dsCode = null!;
        var ssccCode = SSCCCode.Create("123456789012345678");
        var location = "A-01-01-01";
        var palletTypeId = 1;
        var userId = 10;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Pallet.Should().BeNull();
        result.ErrorMessage.Should().Be("Kod DS jest wymagany");
    }

    [Fact]
    public void CreatePallet_NullSSCCCode_ShouldFail()
    {
        // Arrange
        var dsCode = DSCode.Create("**********");
        SSCCCode ssccCode = null!;
        var location = "A-01-01-01";
        var palletTypeId = 1;
        var userId = 10;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Pallet.Should().BeNull();
        result.ErrorMessage.Should().Be("Kod SSCC jest wymagany");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void CreatePallet_InvalidLocation_ShouldFail(string location)
    {
        // Arrange
        var dsCode = DSCode.Create("**********");
        var ssccCode = SSCCCode.Create("123456789012345678");
        var palletTypeId = 1;
        var userId = 10;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Pallet.Should().BeNull();
        result.ErrorMessage.Should().Be("Lokalizacja jest wymagana");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void CreatePallet_InvalidPalletTypeId_ShouldFail(int palletTypeId)
    {
        // Arrange
        var dsCode = DSCode.Create("**********");
        var ssccCode = SSCCCode.Create("123456789012345678");
        var location = "A-01-01-01";
        var userId = 10;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Pallet.Should().BeNull();
        result.ErrorMessage.Should().Be("Typ palety musi być większy od 0");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void CreatePallet_InvalidUserId_ShouldFail(int userId)
    {
        // Arrange
        var dsCode = DSCode.Create("**********");
        var ssccCode = SSCCCode.Create("123456789012345678");
        var location = "A-01-01-01";
        var palletTypeId = 1;
        var createdAt = DateTime.UtcNow;

        // Act
        var result = _service.CreatePallet(dsCode, ssccCode, location, palletTypeId, userId, createdAt);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Pallet.Should().BeNull();
        result.ErrorMessage.Should().Be("ID użytkownika musi być większe od 0");
    }

    [Fact]
    public void CanGeneratePallet_ValidPalletType_ShouldReturnSuccess()
    {
        // Arrange
        var palletType = new TypyPalet
        {
            Id = 1,
            Opis = "Standard",
            UdzialSkladowania = 1.0m
        };

        // Act
        var result = _service.CanGeneratePallet(palletType);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Fact]
    public void CanGeneratePallet_NullPalletType_ShouldReturnFailure()
    {
        // Arrange
        TypyPalet palletType = null!;

        // Act
        var result = _service.CanGeneratePallet(palletType);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Typ palety nie został znaleziony");
    }

    [Fact]
    public void CanGeneratePallet_EmptyDescription_ShouldReturnFailure()
    {
        // Arrange
        var palletType = new TypyPalet
        {
            Id = 1,
            Opis = "",
            UdzialSkladowania = 1.0m
        };

        // Act
        var result = _service.CanGeneratePallet(palletType);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Typ palety '' nie może być używany do generowania DS");
    }

    [Theory]
    [InlineData(0.0)]
    [InlineData(-0.1)]
    public void CanGeneratePallet_InvalidStorageShare_ShouldReturnFailure(decimal storageShare)
    {
        // Arrange
        var palletType = new TypyPalet
        {
            Id = 1,
            Opis = "Invalid",
            UdzialSkladowania = storageShare
        };

        // Act
        var result = _service.CanGeneratePallet(palletType);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Typ palety 'Invalid' nie może być używany do generowania DS");
    }

    [Fact]
    public void IsValidPalletType_ValidType_ShouldReturnTrue()
    {
        // Arrange
        var palletType = new TypyPalet
        {
            Id = 1,
            Opis = "Standard",
            UdzialSkladowania = 1.0m
        };

        // Act
        var result = PalletGenerationService.IsValidPalletType(palletType);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsValidPalletType_NullType_ShouldReturnFalse()
    {
        // Arrange
        TypyPalet palletType = null!;

        // Act
        var result = PalletGenerationService.IsValidPalletType(palletType);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("", 1.0)]
    [InlineData("Valid", 0.0)]
    [InlineData("Valid", -0.1)]
    public void IsValidPalletType_InvalidParameters_ShouldReturnFalse(string description, decimal storageShare)
    {
        // Arrange
        var palletType = new TypyPalet
        {
            Id = 1,
            Opis = description,
            UdzialSkladowania = storageShare
        };

        // Act
        var result = PalletGenerationService.IsValidPalletType(palletType);

        // Assert
        result.Should().BeFalse();
    }
}
