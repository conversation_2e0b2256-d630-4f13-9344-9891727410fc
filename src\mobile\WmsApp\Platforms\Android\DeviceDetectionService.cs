using Android.Content;
using Android.OS;
using WmsApp.Services;

namespace WmsApp.Platforms.Android;

/// <summary>
/// Implementacja serwisu detekcji urz<PERSON>d<PERSON>ń dla platformy Android
/// </summary>
public class DeviceDetectionService : IDeviceDetectionService
{
    private static readonly Lazy<bool> _isEmulator = new(() => DetectEmulator());
    private static readonly Lazy<bool> _isZebraDevice = new(() => DetectZebraDevice());
    private static readonly Lazy<bool> _isDataWedgeAvailable = new(() => DetectDataWedge());

    public bool IsEmulator => _isEmulator.Value;
    
    public bool IsZebraDevice => _isZebraDevice.Value;
    
    public bool IsDataWedgeAvailable => _isDataWedgeAvailable.Value;
    
    public bool IsScanningAvailable => IsZebraDevice && IsDataWedgeAvailable;

    public string DeviceTypeInfo
    {
        get
        {
            if (IsEmulator)
            {
                return "🖥️ Emulator Android - Ograniczone funkcje skanowania";
            }
            
            if (IsZebraDevice && IsDataWedgeAvailable)
            {
                return "📱 Urządzenie Zebra z DataWedge - Pełne funkcje";
            }
            
            if (IsZebraDevice)
            {
                return "📱 Urządzenie Zebra - DataWedge niedostępny";
            }
            
            return "📱 Urządzenie Android standardowe - Ograniczone funkcje skanowania";
        }
    }

    /// <summary>
    /// Wykrywa czy aplikacja działa na emulatorze
    /// </summary>
    private static bool DetectEmulator()
    {
        try
        {
            // Sprawdzenie różnych właściwości które wskazują na emulator
            var brand = Build.Brand ?? string.Empty;
            var device = Build.Device ?? string.Empty;
            var model = Build.Model ?? string.Empty;
            var product = Build.Product ?? string.Empty;
            var hardware = Build.Hardware ?? string.Empty;
            var manufacturer = Build.Manufacturer ?? string.Empty;

            // Typowe oznaczenia emulatorów
            var emulatorIndicators = new[]
            {
                "generic", "unknown", "emulator", "simulator", "genymotion",
                "bluestacks", "nox", "memu", "ldplayer", "gameloop"
            };

            var deviceInfo = $"{brand} {device} {model} {product} {hardware} {manufacturer}".ToLowerInvariant();

            bool isEmulator = emulatorIndicators.Any(indicator => deviceInfo.Contains(indicator));

            // Dodatkowe sprawdzenia
            if (!isEmulator)
            {
                // Sprawdź czy to Android Studio Emulator
                isEmulator = device.StartsWith("generic") || 
                           product.Contains("sdk") ||
                           model.Contains("Emulator") ||
                           manufacturer.Equals("Google", StringComparison.OrdinalIgnoreCase);
            }

            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Device info: {deviceInfo}");
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Is Emulator: {isEmulator}");

            return isEmulator;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Error detecting emulator: {ex.Message}");
            return false; // Assume real device on error
        }
    }

    /// <summary>
    /// Wykrywa czy urządzenie to Zebra
    /// </summary>
    private static bool DetectZebraDevice()
    {
        try
        {
            var manufacturer = Build.Manufacturer ?? string.Empty;
            var brand = Build.Brand ?? string.Empty;
            var model = Build.Model ?? string.Empty;

            bool isZebra = manufacturer.Equals("Zebra Technologies", StringComparison.OrdinalIgnoreCase) ||
                          manufacturer.Equals("Symbol", StringComparison.OrdinalIgnoreCase) ||
                          brand.Equals("Zebra", StringComparison.OrdinalIgnoreCase) ||
                          model.StartsWith("TC", StringComparison.OrdinalIgnoreCase) ||
                          model.StartsWith("MC", StringComparison.OrdinalIgnoreCase);

            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Manufacturer: {manufacturer}");
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Brand: {brand}");
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Model: {model}");
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Is Zebra: {isZebra}");

            return isZebra;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Error detecting Zebra device: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Wykrywa czy DataWedge jest dostępny
    /// </summary>
    private static bool DetectDataWedge()
    {
        try
        {
            // Uproszona detekcja - załóżmy że DataWedge jest dostępny na urządzeniach Zebra
            // W prawdziwej implementacji można sprawdzić package manager
            bool hasDataWedge = DetectZebraDevice();
            
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] DataWedge available (assumed): {hasDataWedge}");
            return hasDataWedge;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[DeviceDetection] Error detecting DataWedge: {ex.Message}");
            return false;
        }
    }
}
