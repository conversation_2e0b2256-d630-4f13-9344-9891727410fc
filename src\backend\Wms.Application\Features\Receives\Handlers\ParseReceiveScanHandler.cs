using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;

namespace Wms.Application.Features.Receives.Handlers;

/// <summary>
/// Handler do parsowania skanów w kontekście rejestracji dostaw
/// </summary>
public class ParseReceiveScanHandler : IRequestHandler<ParseReceiveScanCommand, ParseReceiveScanResponse>
{
    private readonly ICodeValidationService _codeValidationService;
    private readonly IKodRepository _kodRepository;
    private readonly IReceiveRepository _receiveRepository;
    private readonly ILogger<ParseReceiveScanHandler> _logger;

    public ParseReceiveScanHandler(
        ICodeValidationService codeValidationService,
        IKodRepository kodRepository,
        IReceiveRepository receiveRepository,
        ILogger<ParseReceiveScanHandler> logger)
    {
        _codeValidationService = codeValidationService;
        _kodRepository = kodRepository;
        _receiveRepository = receiveRepository;
        _logger = logger;
    }

    public async Task<ParseReceiveScanResponse> Handle(ParseReceiveScanCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Parsowanie skanu dla ListControl: {ListControlId}, User: {UserId}, Data: '{ScanData}'", 
            request.ListControlId, request.UserId, request.ScanData);

        try
        {
            // Parsuj skan przez CodeValidationService
            var validationResult = _codeValidationService.ValidateAndParseScan(request.ScanData);

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Niepoprawny format skanu: {ErrorMessage}", validationResult.ErrorMessage);
                return ParseReceiveScanResponse.Failure(request.ScanData, validationResult.ScanType.ToString(), 
                    validationResult.ErrorMessage ?? "Niepoprawny format skanu");
            }

            // Sprawdź czy to skan GS1
            if (validationResult.ScanType == ScanType.GS1 && validationResult.GS1Data != null)
            {
                var gs1ScanData = GS1ScanData.FromDomain(validationResult.GS1Data);
                var formSuggestion = await BuildFormSuggestion(validationResult.GS1Data, request.ListControlId, cancellationToken);

                // Loguj szczegóły parsowania
                _logger.LogInformation("Sparsowano GS1: HasIzPrefix={HasIzPrefix}, ParsedFields={ParsedFields}, SSCC={SSCC}, GTIN={GTIN}", 
                    validationResult.HasIzPrefix, gs1ScanData.ParsedFieldsCount, gs1ScanData.SSCC, gs1ScanData.GTIN);

                return ParseReceiveScanResponse.Success(
                    rawData: request.ScanData,
                    scanType: "GS1",
                    hasIzPrefix: validationResult.HasIzPrefix,
                    parsedData: gs1ScanData,
                    formSuggestion: formSuggestion
                );
            }

            // Inne typy skanów (SSCC, DS, LK, Location)
            return ParseReceiveScanResponse.Success(
                rawData: request.ScanData,
                scanType: validationResult.ScanType.ToString()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania skanu: {ScanData}", request.ScanData);
            return ParseReceiveScanResponse.Failure(request.ScanData, "Error", 
                "Wystąpił błąd podczas parsowania skanu");
        }
    }

    /// <summary>
    /// Buduje sugestie wypełnienia formularza na podstawie sparsowanych danych GS1
    /// </summary>
    private async Task<ReceiveFormSuggestion?> BuildFormSuggestion(
        Domain.ValueObjects.GS1ParseResult gs1Data,
        int? receiveId,
        CancellationToken cancellationToken)
    {
        ReceiveFormSuggestion? suggestion = null;

        try
        {
            // Jeśli mamy GTIN, spróbuj znaleźć kod w bazie
            if (gs1Data.HasGtin)
            {
                var kod = await _kodRepository.GetByKodValueAsync(gs1Data.Gtin!.Value, cancellationToken: cancellationToken);
                if (kod != null)
                {
                    var suggestedQuantity = CalculateSuggestedQuantity(gs1Data.Quantity, kod);

                    // Sprawdź czy kod pochodzi z awizacji
                    bool isFromAdvice = false;
                    if (receiveId.HasValue)
                    {
                        isFromAdvice = await _receiveRepository.IsCodeFromAdviceAsync(receiveId.Value, gs1Data.Gtin.Value, cancellationToken);
                    }

                    suggestion = new ReceiveFormSuggestion
                    {
                        KodId = kod.Id,
                        KodValue = gs1Data.Gtin.Value,
                        KodNazwa = kod.KodNazwa,
                        Lot = gs1Data.Lot?.Value,
                        ExpiryDate = gs1Data.ExpiryDate?.Value.ToString("yyyy-MM-dd"),
                        SuggestedQuantity = suggestedQuantity,
                        PackagingUnit = kod.IloscWOpakowaniu,
                        IsFromAdvice = isFromAdvice,
                        WarningMessage = BuildWarningMessage(gs1Data)
                    };

                    _logger.LogInformation("Znaleziono kod w bazie: KodId={KodId}, Nazwa='{Nazwa}', PackagingUnit={PackagingUnit}", 
                        kod.Id, kod.KodNazwa, kod.IloscWOpakowaniu);
                }
                else
                {
                    _logger.LogWarning("Nie znaleziono kodu w bazie dla GTIN: {GTIN}", gs1Data.Gtin.Value);
                }
            }

            // Jeśli nie mamy pełnej sugestii, ale mamy inne dane (lot, data), utwórz częściową sugestię
            if (suggestion == null && (gs1Data.HasLot || gs1Data.HasExpiryDate))
            {
                suggestion = new ReceiveFormSuggestion
                {
                    Lot = gs1Data.Lot?.Value,
                    ExpiryDate = gs1Data.ExpiryDate?.Value.ToString("yyyy-MM-dd"),
                    WarningMessage = BuildWarningMessage(gs1Data)
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas budowania sugestii formularza dla GTIN: {GTIN}", gs1Data.Gtin?.Value);
        }

        return suggestion;
    }

    /// <summary>
    /// Przelicza sugerowaną ilość na podstawie jednostki opakowania
    /// </summary>
    private decimal? CalculateSuggestedQuantity(Domain.ValueObjects.Quantity? scannedQuantity, Kod kod)
    {
        if (scannedQuantity == null || kod.IloscWOpakowaniu <= 0)
            return scannedQuantity?.Value;

        // AI 37 to zwykle liczba opakowań, więc mnożymy przez ilość w opakowaniu
        return scannedQuantity.Value * kod.IloscWOpakowaniu;
    }

    /// <summary>
    /// Buduje komunikat ostrzeżenia na podstawie danych
    /// </summary>
    private string? BuildWarningMessage(Domain.ValueObjects.GS1ParseResult gs1Data)
    {
        var warnings = new List<string>();

        // Sprawdź datę ważności
        if (gs1Data.HasExpiryDate)
        {
            if (gs1Data.ExpiryDate!.IsExpired)
            {
                warnings.Add("Produkt przeterminowany");
            }
            else if (gs1Data.ExpiryDate.IsExpiringSoon)
            {
                warnings.Add($"Produkt wygasa za {gs1Data.ExpiryDate.DaysUntilExpiry} dni");
            }
        }

        // Sprawdź czy data jest w rozsądnym zakresie
        if (gs1Data.HasExpiryDate && !gs1Data.ExpiryDate!.IsReasonableExpiryDate())
        {
            warnings.Add("Nieprawdopodobna data ważności");
        }

        return warnings.Any() ? string.Join("; ", warnings) : null;
    }
}
