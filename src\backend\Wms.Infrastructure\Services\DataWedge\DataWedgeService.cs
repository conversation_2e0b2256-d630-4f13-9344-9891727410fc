using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Interfaces;
using MediatR;

namespace Wms.Infrastructure.Services.DataWedge;

/// <summary>
/// Implementacja serwisu DataWedge dla urządzeń Zebra
/// Obsługuje komunikację przez Intents na Androidzie
/// </summary>
public class DataWedgeService : IScanInputService, IDisposable
{
    private readonly ILogger<DataWedgeService> _logger;
    private readonly DataWedgeOptions _options;
    private readonly IMediator _mediator;
    private bool _isListening;
    private bool _disposed;
    
    public event EventHandler<ScanReceivedEventArgs>? ScanReceived;
    
    public bool IsListening => _isListening;

    public DataWedgeService(
        ILogger<DataWedgeService> logger,
        IOptions<DataWedgeOptions> options,
        IMediator mediator)
    {
        _logger = logger;
        _options = options.Value;
        _mediator = mediator;
    }

    /// <summary>
    /// Rozpoczyna nasłuchiwanie skanów z DataWedge
    /// </summary>
    public async Task StartListeningAsync()
    {
        if (_isListening)
        {
            _logger.LogWarning("DataWedgeService już nasłuchuje skanów");
            return;
        }

        try
        {
            _logger.LogInformation("Rozpoczynam nasłuchiwanie skanów DataWedge...");
            
            // Konfiguracja profilu DataWedge
            await ConfigureDataWedgeProfileAsync(_options.ProfileName, _options.PackageName);
            
            _isListening = true;
            _logger.LogInformation("DataWedgeService gotowy do odbierania skanów");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas uruchamiania DataWedgeService");
            throw;
        }
    }

    /// <summary>
    /// Zatrzymuje nasłuchiwanie skanów
    /// </summary>
    public async Task StopListeningAsync()
    {
        if (!_isListening)
            return;

        try
        {
            _logger.LogInformation("Zatrzymuję nasłuchiwanie skanów DataWedge...");
            
            _isListening = false;
            _logger.LogInformation("DataWedgeService zatrzymany");
            
            await Task.CompletedTask; // Dla zgodności z async pattern
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas zatrzymywania DataWedgeService");
            throw;
        }
    }

    /// <summary>
    /// Konfiguruje profil DataWedge dla aplikacji
    /// </summary>
    public async Task ConfigureDataWedgeProfileAsync(string profileName, string packageName)
    {
        try
        {
            _logger.LogInformation("Konfiguracja profilu DataWedge: {ProfileName} dla {PackageName}", 
                profileName, packageName);

            var profile = CreateDataWedgeProfile(profileName, packageName);
            
            // W rzeczywistej implementacji na Androidzie:
            // - Wysłać Intent do DataWedge z konfiguracją profilu
            // - Użyć DataWedge API do konfiguracji
            // Na razie logujemy konfigurację
            
            _logger.LogDebug("Profil DataWedge skonfigurowany: {Profile}", 
                JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true }));
                
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas konfiguracji profilu DataWedge");
            throw;
        }
    }

    /// <summary>
    /// Główna metoda wywoływana przez BroadcastReceiver na Androidzie
    /// </summary>
    public void OnScanReceived(string rawData, string? labelType = null, string? source = null)
    {
        if (string.IsNullOrEmpty(rawData))
        {
            _logger.LogWarning("Otrzymano pusty skan");
            return;
        }

        try
        {
            _logger.LogDebug("Otrzymano skan: Type={LabelType}, Source={Source}, Length={Length}", 
                labelType, source, rawData.Length);

            var deviceInfo = BuildDeviceInfo(labelType, source);
            var processedData = ProcessRawScan(rawData, deviceInfo);
            
            // Wywołaj event
            var eventArgs = new ScanReceivedEventArgs(rawData, deviceInfo, processedData);
            ScanReceived?.Invoke(this, eventArgs);
            
            _logger.LogInformation("Przetworzono skan: Clean='{CleanData}', HasControlChars={HasControlChars}", 
                processedData.CleanData, processedData.HasControlChars);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanu: {RawData}", rawData);
        }
    }

    /// <summary>
    /// Przetwarza surowy skan i normalizuje dane
    /// </summary>
    public ScanInputData ProcessRawScan(string rawData, string? deviceInfo = null)
    {
        var processedData = ScanInputData.Create(rawData, ScanSource.DataWedge, deviceInfo);
        
        _logger.LogDebug("Znormalizowano skan: Original='{Original}', Clean='{Clean}'", 
            rawData, processedData.CleanData);
            
        return processedData;
    }

    /// <summary>
    /// Wysyła skan do parsowania w kontekście dostaw
    /// </summary>
    public async Task<ParseReceiveScanResponse> ProcessScanForDeliveryAsync(
        string scanData, 
        int? listControlId = null, 
        string? deviceId = null)
    {
        try
        {
            var command = new ParseReceiveScanCommand
            {
                ScanData = scanData,
                ListControlId = listControlId,
                DeviceId = deviceId ?? "DataWedge",
                UserId = 0, // TODO: Pobierz z kontekstu
                Context = "delivery"
            };

            var result = await _mediator.Send(command);
            
            _logger.LogInformation("Przetworzono skan dla dostaw: Success={Success}, Type={Type}", 
                result.IsSuccess, result.ScanType);
                
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas przetwarzania skanu dla dostaw");
            return ParseReceiveScanResponse.Failure(scanData, "Error", 
                "Wystąpił błąd podczas przetwarzania skanu");
        }
    }

    /// <summary>
    /// Tworzy konfigurację profilu DataWedge
    /// </summary>
    private DataWedgeProfile CreateDataWedgeProfile(string profileName, string packageName)
    {
        return new DataWedgeProfile
        {
            ProfileName = profileName,
            PackageName = packageName,
            ActivityName = "*", // Wszystkie aktywności
            IntentAction = _options.IntentAction,
            IntentDelivery = IntentDeliveryMethod.Broadcast,
            IntentExtras = new Dictionary<string, bool>
            {
                ["com.symbol.datawedge.data_string"] = true,
                ["com.symbol.datawedge.label_type"] = true,
                ["com.symbol.datawedge.source"] = true,
                ["com.symbol.datawedge.timestamp"] = true
            },
            ScannerConfig = new ScannerConfig
            {
                Enabled = true,
                AutoSwitch = true,
                PreferredScanner = "auto",
                DecodeSessions = new[]
                {
                    "Code128", "Code39", "EAN8", "EAN13", "UPCA", "UPCE", 
                    "QRCode", "DataMatrix", "PDF417", "GS1-128"
                }
            }
        };
    }

    /// <summary>
    /// Buduje informacje o urządzeniu
    /// </summary>
    private string BuildDeviceInfo(string? labelType, string? source)
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(labelType))
            parts.Add($"Type:{labelType}");
            
        if (!string.IsNullOrEmpty(source))
            parts.Add($"Source:{source}");
            
        parts.Add("DataWedge");
        
        return string.Join("|", parts);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            StopListeningAsync().GetAwaiter().GetResult();
            _disposed = true;
        }
    }
}

/// <summary>
/// Opcje konfiguracyjne DataWedge
/// </summary>
public class DataWedgeOptions
{
    public const string SectionName = "DataWedge";
    
    public string ProfileName { get; set; } = "WmsReceives";
    public string PackageName { get; set; } = "com.wms.receives";
    public string IntentAction { get; set; } = "com.wms.receives.SCAN";
    public bool AutoConfigure { get; set; } = true;
    public int ScanTimeout { get; set; } = 5000; // ms
}

/// <summary>
/// Model konfiguracji profilu DataWedge
/// </summary>
internal record DataWedgeProfile
{
    public string ProfileName { get; init; } = string.Empty;
    public string PackageName { get; init; } = string.Empty;
    public string ActivityName { get; init; } = string.Empty;
    public string IntentAction { get; init; } = string.Empty;
    public IntentDeliveryMethod IntentDelivery { get; init; }
    public Dictionary<string, bool> IntentExtras { get; init; } = new();
    public ScannerConfig? ScannerConfig { get; init; }
}

/// <summary>
/// Konfiguracja skanera
/// </summary>
internal record ScannerConfig
{
    public bool Enabled { get; init; }
    public bool AutoSwitch { get; init; }
    public string PreferredScanner { get; init; } = "auto";
    public string[] DecodeSessions { get; init; } = Array.Empty<string>();
}

/// <summary>
/// Metoda dostarczania Intent
/// </summary>
internal enum IntentDeliveryMethod
{
    Broadcast,
    StartActivity,
    StartService
}
