using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Queries;

namespace Wms.Application.Features.Receives.Handlers;

public class GetPrintersHandler : IRequestHandler<GetPrintersQuery, List<PrinterDto>>
{
    private readonly ILogger<GetPrintersHandler> _logger;

    public GetPrintersHandler(ILogger<GetPrintersHandler> logger)
    {
        _logger = logger;
    }

    public Task<List<PrinterDto>> Handle(GetPrintersQuery request, CancellationToken cancellationToken)
    {
        // TODO: W przyszłości można pobrać drukarki z bazy danych lub zewnętrznego serwisu
        // Na razie zwraca przykładowe drukarki
        
        var printers = new List<PrinterDto>
        {
            new()
            {
                Id = 1,
                Nazwa = "Drukarka Dostaw 1",
                IpAddress = "*************",
                Lokalizacja = "Rampa 1",
                IsActive = true
            },
            new()
            {
                Id = 2,
                Nazwa = "Drukarka Dostaw 2", 
                IpAddress = "*************",
                Lokalizacja = "Rampa 2",
                IsActive = true
            },
            new()
            {
                Id = 3,
                Nazwa = "Drukarka Biurowa",
                IpAddress = "*************", 
                Lokalizacja = "Biuro",
                IsActive = false
            }
        };

        var result = request.OnlyActive 
            ? printers.Where(p => p.IsActive).ToList()
            : printers;

        _logger.LogDebug("Pobrano {Count} drukarek (tylko aktywne: {OnlyActive})", 
            result.Count(), request.OnlyActive);

        return Task.FromResult(result.ToList());
    }
}
