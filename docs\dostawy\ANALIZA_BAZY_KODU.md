# ANALIZA BAZY KODU – <PERSON><PERSON>ł "Dostawy"

**Etap 1 - Podsumowanie analizy dla implementacji modułu dostaw**

Data: 2025-09-09  
Autor: System WMS Development Team  
Cel: Identyfikacja punktów integracji, wzorców i wymaganych zmian dla modułu dostaw

## 1. Aktualny stan projektu WMS

### 1.1 Struktura Clean Architecture ✅
Projekt posiada już zaimplementowaną Clean Architecture z pełnym podziałem warstw:

```
src/backend/
├── Wms.Api/           # Kontrolery, middleware, konfiguracja
├── Wms.Application/   # Serwisy, DTO, interfejsy
├── Wms.Domain/        # Encje, logika domenowa
└── Wms.Infrastructure/ # EF Core, repozytoria
```

### 1.2 Istniejące encje Domain (mapowane do legacy DB) ✅
- **User** → `pracownicy` (zawiera `NumerKarty` dla skanowania)
- **Label** → `etykiety` (kluczowa dla dostaw - ma `ListcontrolId`, `Sscc`)  
- **Pallet** → `palety` (agregat etykiet)
- **Location** → `miejsca` (lokalizacje magazynowe)
- **Movement** → `zmianym` (historia ruchów)
- **Session** → `sessions` (nowa tabela dla JWT)

### 1.3 Wzorce implementacyjne ✅
- **Repository Pattern**: `ILabelRepository`, `IUserRepository`, etc.
- **Unit of Work**: `IUnitOfWork` z transakcjami
- **Code Validation**: `CodeValidationService` (SSCC, DS, Location)
- **JWT Authentication**: `AuthenticationService` z card-based login
- **Global Exception Handling**: ProblemDetails middleware
- **FluentValidation**: walidacja DTO w API

### 1.4 Aplikacja MAUI ✅
```
src/mobile/WmsApp/
├── Views/        # Strony XAML
├── ViewModels/   # MVVM z CommunityToolkit
├── Services/     # HTTP clients, serwisy biznesowe
├── Models/       # DTO, modele danych
└── Platforms/Android/  # DataWedge integration
```

## 2. Identyfikacja punktów integracji dla dostaw

### 2.1 Nowe encje Domain dla dostaw
**✅ DODANE encje dla modułu dostaw:**

| Encja | Tabela docelowa | Lokalizacja | Status |
|-------|-----------------|-------------|--------|
| `ListControl` | `list_control` | `Entities/Receives/` | ✅ **DODANE** - podstawa dostaw |
| `ListControlPallet` | `listcontrol_palety` | `Entities/Receives/` | ✅ **DODANE** - powiązanie DS/LK |
| `AwizacjaHead` | `awizacje_dostaw_head` | `Entities/Receives/` | ✅ **DODANE** - źródło awizacji |
| `AwizacjaDane` | `awizacje_dostaw_dane` | `Entities/Receives/` | ✅ **DODANE** - pozycje awizacji |
| `Kod` | `kody` | `Entities/` | ✅ **DODANE** - kartoteka produktów |
| `TypyPalet` | `typypalet` | `Entities/` | ✅ **DODANE** - typy palet |

### 2.2 Rozszerzenia istniejących encji

#### Label.cs - gotowe do użycia:
```csharp
// ✅ OK: etykiety.lot już istnieje - dla partii produktu
public string? Lot { get; set; } // Mapowane na etykiety.lot (VARCHAR(300))

// ✅ OK: etykiety.blloc już istnieje - używane jako "certyfikat" w kontekście dostaw  
public string? Blloc { get; set; } // Mapowane na etykiety.blloc (VARCHAR(45))
```

#### User.cs - gotowe do użycia:
```csharp
public string? NumerKarty { get; set; } // ✅ OK dla logowania kartą
```

### 2.3 Brakujące migracje DB
**Wymagane zmiany schematu:**

```sql
-- 1. Dodanie kolumny dla claim dostawy
ALTER TABLE list_control ADD COLUMN realizujacy_pracownik_id INT UNSIGNED NULL;
CREATE INDEX idx_realizujacy_pracownik ON list_control(realizujacy_pracownik_id);

-- 2. Ensure docnumber entry for 'nrpalety'
INSERT IGNORE INTO docnumber (name, last) VALUES ('nrpalety', 650000);

-- UWAGA: Pola dla dostaw już istnieją:
-- etykiety.lot (VARCHAR(300)) - dla partii produktu
-- etykiety.blloc (VARCHAR(45)) - dla certyfikatu  
-- awizacje_dostaw_dane.lot (VARCHAR(45)) - źródło partii z awizacji
-- awizacje_dostaw_dane.blloc (VARCHAR(45)) - źródło certyfikatu z awizacji
```

## 3. Wzorce do rozszerzenia

### 3.1 Code Validation Service ✅ READY
Istniejący `CodeValidationService` zawiera:
- ✅ `ValidateSSCC(string)` - dla SSCC (AI 00)
- ✅ `ValidateDS(string)` - dla nośników DS
- ✅ `ValidateLocationCode(string)` - dla lokalizacji

**Wymagane rozszerzenie:**
```csharp
// Dodać walidację LK (format: LK + 1-8 cyfr)
public bool ValidateLK(string code)
{
    return LKPattern.IsMatch(code); // ^LK\d{1,8}$
}
```

### 3.2 Repository Patterns ✅ READY
Istniejące repozytoria można rozszerzyć:
- `ILabelRepository` - rozszerzyć o queries dla dostaw
- `IUnitOfWork` - użyć dla transakcji DS generation

**Wymagane nowe repozytoria:**
```csharp
interface IReceiveRepository          // list_control
interface IReceivePalletRepository   // listcontrol_palety  
interface IAwizacjaRepository        // awizacje_dostaw_*
interface IKodyRepository            // kody (products)
```

### 3.3 Global Exception Handling ✅ READY
Istniejący `GlobalExceptionMiddleware` - dodać mappingi:
```csharp
// Nowe wyjątki domenowe dla dostaw
ReceiveNotFoundException -> 404
ReceiveAlreadyClaimedException -> 409
InvalidGS1FormatException -> 400
```

## 4. Struktura API dla dostaw

### 4.1 Kontrolery do dodania
```csharp
[Route("api/v{version:apiVersion}/receives")]
public class ReceivesController : BaseApiController
{
    // Lista dostaw, claim/release, GS1 parsing
}

[Route("api/v{version:apiVersion}/receives/{receiveId}/pallets")]  
public class DeliveryPalletsController : BaseApiController
{
    // Generowanie DS, druk etykiet
}

[Route("api/v{version:apiVersion}/printers")]
public class PrintersController : BaseApiController
{
    // Lista drukarek, ZPL printing
}
```

### 4.2 Wzorce DTO/Request/Response
Bazować na istniejących wzorcach z `MovePalletRequest/Response`.

## 5. Frontend MAUI - punkty integracji

### 5.1 Istniejące wzorce ✅ READY
- **MVVM**: `CommunityToolkit.Mvvm` z `[ObservableProperty]` i `[RelayCommand]`
- **DataWedge**: Platform-specific BroadcastReceiver w `Platforms/Android/`
- **HTTP Client**: Pattern dla API communication z retry policies
- **Navigation**: MAUI Shell routing

### 5.2 Wymagane nowe komponenty
```
Views/Receives/
├── ReceivesSelectionPage.xaml     # Widok 1: Lista dostaw + LK input
└── ReceivesRegistrationPage.xaml  # Widok 2: Skanowanie + rejestracja

ViewModels/Receives/
├── ReceivesSelectionViewModel.cs
└── ReceivesRegistrationViewModel.cs

Services/Receives/
├── IReceiveService.cs             # HTTP API client
├── IPrintingService.cs            # ZPL/TCP printing
└── IGS1ParsingService.cs          # GS1 barcode parsing
```

## 6. Nowe serwisy Application layer

### 6.1 GS1 Parsing Service (nowy)
```csharp
public interface IGS1ParsingService
{
    GS1ParseResult ParseScan(string rawScanData);
    // AI 00 (SSCC), 02 (GTIN), 10 (Lot), 17 (Expiry), 37 (Count)
}
```

### 6.2 Printing Service (nowy)
```csharp
public interface IPrintingService  
{
    Task PrintDSLabelAsync(int dsNumber, string printerIp);
    Task<IEnumerable<PrinterInfo>> GetAvailablePrintersAsync();
    // ZPL generation dla DS{numer} Code 128
}
```

### 6.3 Receive Management Service (nowy)
```csharp
public interface IReceiveManagementService
{
    Task<ReceiveDto> ClaimReceiveAsync(int receiveId, int userId);
    Task ReleaseReceiveAsync(int receiveId, int userId);
    Task<IEnumerable<int>> GeneratePalletsAsync(GeneratePalletsCommand cmd);
}
```

## 7. Diagram kontekstowy architektury

```
┌─ DOSTAWY MODULE ─┐
│                  │
│  Widok 1: LK     │──┐
│  Widok 2: GS1    │  │
│                  │  │
└──────────────────┘  │
                      │
┌─ API LAYER ──────┐  │
│ ReceivesController│◄─┘
│ PrintersController│
│                   │
└───────────────────┘
           │
┌─ APPLICATION ────┐  
│ ReceiveService   │◄─┘
│ GS1ParsingService│
│ PrintingService  │
└──────────────────┘
           │
┌─ DOMAIN ─────────┐
│ ListControl      │◄─┘
│ AwizacjaHead     │
│ GS1ParseResult   │  
└──────────────────┘
           │
┌─ INFRASTRUCTURE ┐
│ ReceiveRepository│◄─┘
│ EF Core Mapping  │
│ MySQL Tables     │
└──────────────────┘
```

## 8. Plan migracji i ryzyka

### 8.1 Wysokie ryzyko ⚠️
1. **Legacy Database Constraints**: Tabela `etykiety` ma constraint `chk_paleta_id` - może blokować INSERT-only pattern
2. **Concurrent Access**: `list_control.realizujacy_pracownik_id` - wymagana obsługa race conditions  
3. **SSCC Integration**: Powiązanie `awizacje_dostaw_dane.etykieta_klient` z `etykiety.etykieta_klient`
4. **Lot/Certyfikat Mapping**: 
   - `etykiety.lot` (VARCHAR(300)) - partia produktu
   - `etykiety.blloc` (VARCHAR(45)) - certyfikat
   - Wymagana walidacja długości certyfikatu (max 45 znaków)

### 8.2 Średnie ryzyko ⚠️ 
1. **DataWedge Profiles**: Konfiguracja skanera dla różnych typów kodów (GS1, DS, LK)
2. **TCP Printing**: Komunikacja z drukarkami przez raw TCP socket (ZPL)
3. **GS1 Parsing**: Complexity parsowania różnych AI (Application Identifiers)

### 8.3 Niskie ryzyko ✅
1. **Clean Architecture**: Istniejąca struktura jest kompatybilna
2. **Authentication**: JWT i card scanning już działają
3. **MAUI Patterns**: Wzorce MVVM i navigation gotowe do rozszerzenia

## 9. Kolejne kroki (Etap 2)

### 9.1 Priorytet 1 - Database Schema  
1. Utworzenie migracji EF Core dla `list_control.realizujacy_pracownik_id`
2. Mapowanie legacy tabel (`list_control`, `awizacje_dostaw_*`, `kody`)
3. Testowanie constraints i foreign keys
4. **Weryfikacja mapowania pól**: 
   - `etykiety.lot` → partia (VARCHAR(300))
   - `etykiety.blloc` → certyfikat (VARCHAR(45))

### 9.2 Priorytet 2 - Domain Layer
1. Nowe encje: `ListControl`, `AwizacjaHead`, `Kody`
2. Value Objects: `LKCode`, `GS1Barcode`, `DSCode` 
3. Domain Services dla claim/release logic

### 9.3 Priorytet 3 - Application Services
1. Commands/Queries dla 12 endpointów (zgodnie z PRD)
2. `GS1ParsingService` implementation
3. `ReceiveManagementService` z UnitOfWork

---

## 📋 Podsumowanie

**Status przygotowania do implementacji dostaw: 70% READY**

✅ **Gotowe komponenty:**
- Clean Architecture foundation
- Legacy DB mapping patterns  
- JWT Authentication + Code Validation
- MAUI MVVM + DataWedge integration
- Repository + UnitOfWork patterns

⚠️ **Wymagane rozszerzenia:**
- 6 nowych encji Domain (ListControl, AwizacjaHead, etc.)  
- 1 migracja DB (realizujacy_pracownik_id) - pole certyfikat już istnieje jako etykiety.lot
- 12 nowych endpointów API
- 3 nowe serwisy Application (GS1, Printing, Receive)
- 2 nowe widoki MAUI (Selection, Registration)

🚨 **Krytyczne punkty do adresowania:**
1. Race conditions przy claim dostawy
2. INSERT-only pattern dla tabeli etykiety
3. GS1 parsing complexity (AIs: 00, 02, 10, 17, 37)
4. TCP printing komunikacja z drukarkami

**KOREKTA - Mapowanie certyfikatu:**
- ✅ `etykiety.lot` (VARCHAR(300)) → partia produktu (już zmapowane w Label.cs)
- ✅ `etykiety.blloc` (VARCHAR(45)) → certyfikat (już zmapowane w Label.cs)
- ✅ `awizacje_dostaw_dane.lot` → źródło partii z awizacji
- ✅ `awizacje_dostaw_dane.blloc` → źródło certyfikatu z awizacji

**Wniosek:** Wszystkie wymagane pola dla partii i certyfikatu już istnieją w bazie i są zmapowane!

**Rekomendacja:** Rozpocząć implementację od **Etapu 2 - Database Schema** aby ustabilizować fundament przed rozwojem wyższych warstw.
