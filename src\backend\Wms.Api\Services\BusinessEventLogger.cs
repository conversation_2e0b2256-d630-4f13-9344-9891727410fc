using System.Text.Json;
using Wms.Api.Middleware;

namespace Wms.Api.Services;

public interface IBusinessEventLogger
{
    void LogUserAuthentication(string cardNumber, bool success, string? failureReason = null, TimeSpan? duration = null);
    void LogPalletMovement(string ssccCode, string fromLocation, string toLocation, bool success, 
        string? userId = null, string? failureReason = null, TimeSpan? duration = null);
    void LogLocationQuery(string locationCode, int palletCount, string? userId = null, TimeSpan? duration = null);
    void LogValidationError(string entityType, string fieldName, string errorMessage, object? inputValue = null);
    void LogBusinessRuleViolation(string ruleName, string description, object? context = null);
    void LogSystemEvent(string eventType, string description, object? data = null);
}

public class BusinessEventLogger : IBusinessEventLogger
{
    private readonly ILogger<BusinessEventLogger> _logger;
    private readonly ICorrelationIdService _correlationIdService;

    public BusinessEventLogger(ILogger<BusinessEventLogger> logger, ICorrelationIdService correlationIdService)
    {
        _logger = logger;
        _correlationIdService = correlationIdService;
    }

    public void LogUserAuthentication(string cardNumber, bool success, string? failureReason = null, TimeSpan? duration = null)
    {
        var eventData = new
        {
            EventType = "UserAuthentication",
            CardNumber = HashCardNumber(cardNumber), // Hash for privacy
            Success = success,
            FailureReason = failureReason,
            Duration = duration?.TotalMilliseconds,
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        if (success)
        {
            _logger.LogInformation("User authentication successful for card {CardNumberHash} in {Duration}ms",
                eventData.CardNumber, eventData.Duration ?? 0);
        }
        else
        {
            _logger.LogWarning("User authentication failed for card {CardNumberHash}: {FailureReason}",
                eventData.CardNumber, failureReason ?? "Unknown reason");
        }

        _logger.LogDebug("Authentication event: {@EventData}", eventData);
    }

    public void LogPalletMovement(string ssccCode, string fromLocation, string toLocation, bool success,
        string? userId = null, string? failureReason = null, TimeSpan? duration = null)
    {
        var eventData = new
        {
            EventType = "PalletMovement",
            SsccCode = ssccCode,
            FromLocation = fromLocation,
            ToLocation = toLocation,
            Success = success,
            UserId = userId,
            FailureReason = failureReason,
            Duration = duration?.TotalMilliseconds,
            FromLocationType = GetLocationTypeFromCode(fromLocation),
            ToLocationType = GetLocationTypeFromCode(toLocation),
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        if (success)
        {
            _logger.LogInformation("Pallet {SsccCode} moved from {FromLocation} to {ToLocation} by user {UserId} in {Duration}ms",
                ssccCode, fromLocation, toLocation, userId ?? "unknown", duration?.TotalMilliseconds ?? 0);
        }
        else
        {
            _logger.LogError("Pallet movement failed for {SsccCode} from {FromLocation} to {ToLocation}: {FailureReason}",
                ssccCode, fromLocation, toLocation, failureReason ?? "Unknown reason");
        }

        _logger.LogDebug("Pallet movement event: {@EventData}", eventData);
    }

    public void LogLocationQuery(string locationCode, int palletCount, string? userId = null, TimeSpan? duration = null)
    {
        var eventData = new
        {
            EventType = "LocationQuery",
            LocationCode = locationCode,
            PalletCount = palletCount,
            UserId = userId,
            Duration = duration?.TotalMilliseconds,
            LocationType = GetLocationTypeFromCode(locationCode),
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        _logger.LogInformation("Location {LocationCode} queried by user {UserId}: {PalletCount} pallets found in {Duration}ms",
            locationCode, userId ?? "unknown", palletCount, duration?.TotalMilliseconds ?? 0);

        _logger.LogDebug("Location query event: {@EventData}", eventData);
    }

    public void LogValidationError(string entityType, string fieldName, string errorMessage, object? inputValue = null)
    {
        var eventData = new
        {
            EventType = "ValidationError",
            EntityType = entityType,
            FieldName = fieldName,
            ErrorMessage = errorMessage,
            InputValue = inputValue?.ToString(), // Convert to string for logging
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        _logger.LogWarning("Validation error in {EntityType}.{FieldName}: {ErrorMessage}. Input: {InputValue}",
            entityType, fieldName, errorMessage, inputValue?.ToString() ?? "null");

        _logger.LogDebug("Validation error event: {@EventData}", eventData);
    }

    public void LogBusinessRuleViolation(string ruleName, string description, object? context = null)
    {
        var eventData = new
        {
            EventType = "BusinessRuleViolation",
            RuleName = ruleName,
            Description = description,
            Context = context,
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        _logger.LogWarning("Business rule violation: {RuleName} - {Description}",
            ruleName, description);

        if (context != null)
        {
            _logger.LogDebug("Business rule violation context: {@Context}", context);
        }

        _logger.LogDebug("Business rule violation event: {@EventData}", eventData);
    }

    public void LogSystemEvent(string eventType, string description, object? data = null)
    {
        var eventData = new
        {
            EventType = $"System.{eventType}",
            Description = description,
            Data = data,
            Timestamp = DateTimeOffset.UtcNow,
            CorrelationId = _correlationIdService.GetCorrelationId()
        };

        _logger.LogInformation("System event {EventType}: {Description}", eventType, description);

        if (data != null)
        {
            _logger.LogDebug("System event data: {@Data}", data);
        }

        _logger.LogDebug("System event: {@EventData}", eventData);
    }

    private static string GetLocationTypeFromCode(string locationCode)
    {
        if (locationCode.StartsWith("MP-", StringComparison.OrdinalIgnoreCase))
            return "Storage";
        if (locationCode.StartsWith("DOCK-", StringComparison.OrdinalIgnoreCase))
            return "Dock";
        if (locationCode.StartsWith("PICK-", StringComparison.OrdinalIgnoreCase))
            return "Picking";
        return "Unknown";
    }

    private static string HashCardNumber(string cardNumber)
    {
        if (string.IsNullOrEmpty(cardNumber))
            return "empty";

        // Simple hash for privacy - in production, use a proper hashing algorithm
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(cardNumber + "WMS_SALT"));
        return Convert.ToHexString(hashBytes)[..8]; // First 8 characters
    }
}

// Extension method for service registration
public static class BusinessEventLoggerExtensions
{
    public static IServiceCollection AddBusinessEventLogging(this IServiceCollection services)
    {
        services.AddScoped<IBusinessEventLogger, BusinessEventLogger>();
        return services;
    }
}
