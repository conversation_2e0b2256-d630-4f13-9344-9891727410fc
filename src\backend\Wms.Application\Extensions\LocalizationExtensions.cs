using System.Globalization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Wms.Application.Interfaces;
using Wms.Application.Services;

namespace Wms.Application.Extensions;

/// <summary>
/// Rozszerzenia do rejestracji serwisów lokalizacji
/// </summary>
public static class LocalizationExtensions
{
    /// <summary>
    /// Dodaje serwisy lokalizacji do DI container
    /// </summary>
    public static IServiceCollection AddLocalization(this IServiceCollection services)
    {
        // Konfiguracja domyślnej kultury na polski
        var polishCulture = new CultureInfo("pl-PL");
        CultureInfo.DefaultThreadCurrentCulture = polishCulture;
        CultureInfo.DefaultThreadCurrentUICulture = polishCulture;

        // Rejestracja serwisu lokalizacji
        services.AddSingleton<ILocalizationService, LocalizationService>();

        // Microsoft Localization Extensions (opcjonalnie dla przyszłości)
        services.AddLocalization(options => options.ResourcesPath = "Resources");

        return services;
    }
    
    /// <summary>
    /// Dodaje serwisy lokalizacji z niestandardową kulturą
    /// </summary>
    public static IServiceCollection AddLocalization(this IServiceCollection services, CultureInfo defaultCulture)
    {
        // Konfiguracja domyślnej kultury
        CultureInfo.DefaultThreadCurrentCulture = defaultCulture;
        CultureInfo.DefaultThreadCurrentUICulture = defaultCulture;

        // Rejestracja serwisu lokalizacji
        services.AddSingleton<ILocalizationService, LocalizationService>();

        // Microsoft Localization Extensions
        services.AddLocalization(options => options.ResourcesPath = "Resources");

        return services;
    }
}
