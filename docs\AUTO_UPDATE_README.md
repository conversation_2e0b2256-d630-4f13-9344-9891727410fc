# Auto-Update APK - Developer README

## 🚀 Quick Start

### Prerequisites
- .NET 9 MAUI
- Android API 29+
- HTTPS server with JSON manifest and APK files

### Basic Implementation
```csharp
// 1. Register UpdateService in DI
services.AddHttpClient<IUpdateService, UpdateService>();

// 2. Inject and use in your app
public AboutViewModel(IUpdateService updateService)
{
    _updateService = updateService;
}

// 3. Check for updates
var updateInfo = await _updateService.CheckForUpdatesAsync();
if (updateInfo.IsAvailable)
{
    // Handle update...
}
```

## 📁 File Structure

```
WmsApp/
├── Models/Update/
│   ├── AppManifest.cs           # JSON manifest model
│   ├── UpdateInfo.cs            # Update result model
│   └── DownloadProgress.cs      # Download progress tracking
├── Services/
│   ├── Contracts/
│   │   └── IUpdateService.cs    # Service interface
│   └── UpdateService.cs         # Main implementation
├── ViewModels/
│   └── AboutViewModel.cs        # UI logic for update page
├── Views/
│   ├── AboutPage.xaml           # Update UI page
│   └── AboutPage.xaml.cs        # Code-behind
└── Platforms/Android/
    ├── AndroidManifest.xml      # Permissions & FileProvider
    └── Resources/xml/
        └── file_provider_paths.xml  # FileProvider config
```

## 🔧 Configuration

### Android Permissions
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
                 android:maxSdkVersion="28" />
```

### FileProvider Setup
```xml
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_provider_paths" />
</provider>
```

### DI Registration
```csharp
// In MauiProgram.cs
services.AddHttpClient<IUpdateService, UpdateService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(60);
});
```

## 🌐 Server-side Setup

### Manifest Format (app.json)
```json
{
  "version": "1.0.5",
  "versionCode": 105,
  "downloadUrl": "https://your-server.com/wms_android_update/app.apk",
  "sha256": "file-hash-here",
  "fileSize": 15728640,
  "releaseNotes": "What's new in this version",
  "required": false,
  "minSdkVersion": 29,
  "releaseDate": "2025-01-09T10:00:00Z"
}
```

### Apache Virtual Host
```apache
<VirtualHost *:443>
    ServerName your-server.com
    DocumentRoot /var/www
    
    SSLEngine on
    SSLCertificateFile /path/to/cert.pem
    SSLCertificateKeyFile /path/to/key.pem
    
    Alias /wms_android_update /var/www/wms_android_update
    <Directory "/var/www/wms_android_update">
        Options Indexes FollowSymLinks
        AllowOverride None
        Require all granted
        
        AddType application/json .json
        AddType application/vnd.android.package-archive .apk
        
        <Files "*.apk">
            Header set Content-Disposition "attachment"
            Header set Cache-Control "no-cache"
        </Files>
    </Directory>
</VirtualHost>
```

## 💻 Code Examples

### Basic Update Check
```csharp
public async Task<bool> CheckAndInstallUpdatesAsync()
{
    try
    {
        // Check for updates
        var updateInfo = await _updateService.CheckForUpdatesAsync();
        
        if (!updateInfo.IsAvailable)
            return false;
            
        // Show dialog to user
        var userAccepted = await ShowUpdateDialog(updateInfo);
        if (!userAccepted && !updateInfo.IsRequired)
            return false;
            
        // Download APK with progress
        var progress = new Progress<DownloadProgress>(OnDownloadProgress);
        var apkPath = await _updateService.DownloadApkAsync(
            updateInfo.Manifest, progress);
        
        // Verify integrity
        var isValid = await _updateService.VerifyApkIntegrityAsync(
            apkPath, updateInfo.Manifest.Sha256);
            
        if (!isValid)
            throw new Exception("APK integrity check failed");
            
        // Install
        return await _updateService.InstallApkAsync(apkPath);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Update failed");
        return false;
    }
}

private void OnDownloadProgress(DownloadProgress progress)
{
    MainThread.BeginInvokeOnMainThread(() =>
    {
        ProgressText = $"Downloading: {progress.PercentageComplete}%";
        ProgressValue = progress.PercentageComplete / 100.0;
    });
}
```

### Custom Update Service Implementation
```csharp
public class CustomUpdateService : IUpdateService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CustomUpdateService> _logger;
    private const string ManifestUrl = "https://your-server.com/wms_android_update/app.json";

    public async Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync(ManifestUrl, cancellationToken);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync(cancellationToken);
            var manifest = JsonSerializer.Deserialize<AppManifest>(json);

            var currentVersionCode = GetCurrentVersionCode();
            var isUpdateAvailable = manifest.VersionCode > currentVersionCode;

            return new UpdateInfo
            {
                IsAvailable = isUpdateAvailable,
                CurrentVersion = GetCurrentVersion(),
                NewVersion = manifest.Version,
                IsRequired = manifest.Required,
                Manifest = manifest
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check for updates");
            return new UpdateInfo
            {
                IsAvailable = false,
                CurrentVersion = GetCurrentVersion(),
                ErrorMessage = ex.Message
            };
        }
    }

    // ... implement other interface methods
}
```

### XAML UI Example
```xml
<ContentPage x:Class="WmsApp.Views.AboutPage" Title="About">
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="20">
            
            <!-- App Info -->
            <Frame BackgroundColor="LightGray" Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding AppName}" FontSize="24" FontAttributes="Bold" />
                    <Label Text="{Binding CurrentVersion, StringFormat='Version: {0}'}" />
                    <Label Text="{Binding BuildNumber, StringFormat='Build: {0}'}" />
                </VerticalStackLayout>
            </Frame>

            <!-- Update Section -->
            <Frame BackgroundColor="LightGray" Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="Updates" FontSize="18" FontAttributes="Bold" />
                    
                    <Button Text="Check for Updates"
                            Command="{Binding CheckForUpdatesCommand}" />
                    
                    <Label Text="{Binding UpdateStatus}" FontSize="14" />
                    
                    <ActivityIndicator IsVisible="{Binding IsCheckingUpdate}"
                                       IsRunning="{Binding IsCheckingUpdate}" />
                    
                    <!-- Update Available Section -->
                    <StackLayout IsVisible="{Binding UpdateAvailable}" Spacing="10">
                        <Label Text="{Binding NewVersion, StringFormat='New version: {0}'}"
                               FontSize="16" FontAttributes="Bold" />
                        
                        <Label Text="{Binding ReleaseNotes, StringFormat='Changes: {0}'}"
                               FontSize="14" />
                        
                        <Button Text="Download and Install"
                                Command="{Binding DownloadAndInstallCommand}"
                                BackgroundColor="Green" TextColor="White" />
                        
                        <Label Text="{Binding DownloadProgress}" FontSize="14" />
                        
                        <ActivityIndicator IsVisible="{Binding IsDownloading}"
                                           IsRunning="{Binding IsDownloading}" />
                    </StackLayout>
                </VerticalStackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
```

## 🧪 Testing

### Unit Test Example
```csharp
[TestFixture]
public class UpdateServiceTests
{
    private UpdateService _updateService;
    private Mock<HttpClient> _mockHttpClient;
    private Mock<ILogger<UpdateService>> _mockLogger;

    [SetUp]
    public void Setup()
    {
        _mockHttpClient = new Mock<HttpClient>();
        _mockLogger = new Mock<ILogger<UpdateService>>();
        _updateService = new UpdateService(_mockHttpClient.Object, _mockLogger.Object);
    }

    [Test]
    public async Task CheckForUpdatesAsync_WhenNewVersionAvailable_ReturnsTrue()
    {
        // Arrange
        var manifestJson = @"{
            ""version"": ""1.0.5"",
            ""versionCode"": 105,
            ""downloadUrl"": ""https://example.com/app.apk"",
            ""sha256"": ""testhash"",
            ""fileSize"": 1024,
            ""releaseNotes"": ""Test release"",
            ""required"": false
        }";
        
        // Setup mock HTTP response
        // ... (setup mocking)

        // Act
        var result = await _updateService.CheckForUpdatesAsync();

        // Assert
        Assert.IsTrue(result.IsAvailable);
        Assert.AreEqual("1.0.5", result.NewVersion);
    }

    [Test]
    public async Task VerifyApkIntegrityAsync_WithCorrectHash_ReturnsTrue()
    {
        // Arrange
        var testFile = CreateTestFile("test content");
        var expectedHash = CalculateSHA256("test content");

        // Act
        var result = await _updateService.VerifyApkIntegrityAsync(testFile, expectedHash);

        // Assert
        Assert.IsTrue(result);
    }
}
```

### Integration Test
```csharp
[TestFixture]
public class UpdateIntegrationTests
{
    private TestServer _testServer;
    private UpdateService _updateService;

    [SetUp]
    public void Setup()
    {
        // Setup test server with manifest endpoint
        var builder = WebApplication.CreateBuilder();
        var app = builder.Build();
        
        app.MapGet("/wms/app.json", () => new AppManifest
        {
            Version = "1.0.5",
            VersionCode = 105,
            DownloadUrl = "https://test.com/app.apk",
            Sha256 = "testhash",
            // ... other properties
        });
        
        _testServer = new TestServer(app);
        
        var httpClient = _testServer.CreateClient();
        _updateService = new UpdateService(httpClient, Mock.Of<ILogger<UpdateService>>());
    }

    [Test]
    public async Task EndToEnd_UpdateCheck_Works()
    {
        // Act
        var result = await _updateService.CheckForUpdatesAsync();

        // Assert
        Assert.IsTrue(result.IsAvailable);
        Assert.AreEqual("1.0.5", result.NewVersion);
    }
}
```

## 🐛 Common Issues & Solutions

### Issue: InitializeComponent not found
**Solution**: Clean and rebuild the project. XAML compilation may have failed.
```bash
dotnet clean
dotnet build
```

### Issue: FileProvider not working
**Solution**: Check AndroidManifest.xml and file_provider_paths.xml configuration.
```xml
<!-- Ensure correct authority -->
android:authorities="${applicationId}.fileprovider"
```

### Issue: SHA-256 verification fails
**Solution**: Ensure hash is calculated correctly and matches manifest.
```bash
# Calculate hash correctly
sha256sum app.apk | cut -d' ' -f1
```

### Issue: Installation permission denied
**Solution**: Check if app has install permissions.
```csharp
if (!_updateService.CanInstallFromUnknownSources())
{
    await _updateService.RequestInstallPermissionAsync();
}
```

## 📊 Monitoring & Logging

### Log Levels Used
- **Information**: Normal operations (check updates, download progress)
- **Warning**: Non-critical errors (network issues, user cancelled)
- **Error**: Critical failures (download failed, hash mismatch)

### Key Metrics to Track
```csharp
// Track these metrics in production
public class UpdateMetrics
{
    public int ChecksPerformed { get; set; }
    public int UpdatesAvailable { get; set; }
    public int DownloadsStarted { get; set; }
    public int DownloadsCompleted { get; set; }
    public int InstallationsSuccessful { get; set; }
    public TimeSpan AverageDownloadTime { get; set; }
}
```

## 🔄 Deployment Process

### 1. Build & Package
```bash
# Build release APK
dotnet build -c Release -f net9.0-android

# Calculate SHA-256
sha256sum bin/Release/net9.0-android/com.example.wms-Signed.apk
```

### 2. Update Server
```bash
# Upload new APK
scp app.apk server:/var/www/wms/app.apk

# Update manifest
curl -X POST server/update-manifest \
  -H "Content-Type: application/json" \
  -d '{"version":"1.0.5","versionCode":105,"sha256":"..."}'
```

### 3. Verify Deployment
```bash
# Test manifest endpoint
curl https://your-server.com/wms_android_update/app.json

# Test APK download
curl -I https://your-server.com/wms_android_update/app.apk
```

---

**Last Updated**: 2025-01-09  
**Version**: 1.0  
**Contact**: WMS System Development Team
