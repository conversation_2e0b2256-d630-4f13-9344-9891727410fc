# Reguły dla Asystenta AI - WMS System

## Opis projektu
System WMS (Warehouse Management System) składający się z:
- **Backend**: ASP.NET Core .NET 9 z MySQL (Clean Architecture)
- **Frontend**: MAUI Android dla skanerów Zebra MC3300
- **Infrastruktura**: Apache reverse proxy, observability (Grafana/Loki/Prometheus)

**Status projektu**: Backend ukończony (100%), Frontend MVP ukoń<PERSON>ony (85%), wdrożenie w toku.

**Cel MVP**: logowanie skanem karty + zmiana lokalizacji palety (scan→scan, bez potwierdzenia).

## Struktura projektu
```
/
├── src/
│   ├── backend/           # ASP.NET Core (.NET 9)
│   │   ├── Wms.Api/      # Kontrolery, middleware 
│   │   ├── Wms.Application/ # Use cases, DTO, walidacje
│   │   ├── Wms.Domain/   # Encje, reguły biznesowe  
│   │   └── Wms.Infrastructure/ # EF Core, repozytoria
│   └── mobile/           # MAUI Android
│       └── WmsApp/       # Główna aplikacja mobilna
├── docs/                 # Dokumentacja techniczna
├── infra/               # Konfiguracja infrastruktury
├── tests/               # Testy automatyczne
└── scripts/             # Skrypty wdrożeniowe
```

## Kluczowe technologie
- **.NET 9**: ASP.NET Core, EF Core, MAUI
- **MySQL 8.0**: Baza danych z legacy schema
- **Zebra DataWedge**: Integracja skanerów przez Intent
- **JWT Authentication**: Card-based login (60min tokens)
- **Clean Architecture**: Rozdzielenie warstw, DI, Repository pattern
- **Observability**: Serilog, Prometheus, Grafana, Sentry

## Reguły kodowania (KRYTYCZNE)

### 1. Język komunikacji
- **Wszystkie odpowiedzi i wyjaśnienia: POLSKI**
- **Kod źródłowy**: angielski (nazwy klas, metod, zmiennych)
- **Komunikaty użytkownika**: polski (błędy, UI, API responses)
- **Dokumentacja**: polski

### 2. Architektura i wzorce
- **Zawsze przestrzegaj Clean Architecture** - rozdzielenie Domain/Application/Infrastructure/Api
- **Repository Pattern**: wszystkie operacje bazodanowe przez interfejsy
- **Unit of Work**: transakcyjność operacji biznesowych
- **CQRS-like**: oddzielne DTO dla request/response
- **Dependency Injection**: wszystkie zależności przez DI container

### 3. Backend (.NET 9) - Zasady obowiązkowe
```csharp
// OBOWIĄZKOWY format kontrolerów
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class ExampleController : ControllerBase
{
    private readonly IExampleService _service;
    
    public ExampleController(IExampleService service) => _service = service;
}

// OBOWIĄZKOWA obsługa błędów - ProblemDetails z polskimi komunikatami
public class ApiException : Exception
{
    public ApiException(string message) : base(message) { }
}
```

### 4. Frontend (MAUI) - Zasady obowiązkowe
```csharp
// OBOWIĄZKOWY MVVM pattern z CommunityToolkit.Mvvm
public partial class ExampleViewModel : ObservableObject
{
    [ObservableProperty]
    private string exampleProperty = string.Empty;

    [RelayCommand]
    private async Task ExampleCommand() { }
}
```

### 5. Baza danych - Migracje EF Core
- **NIGDY nie modyfikuj legacy tabel** - tylko mapowanie przez Fluent API
- **Nowe tabele**: zawsze przez migracje z prefiksem daty `YYYYMMDD_HHmm_`
- **Repository z AsNoTracking**: dla operacji read-only

## Walidacja kodów WMS (OBOWIĄZKOWA)
```csharp
// Wzorce walidacji - NIGDY nie zmieniaj
public static class WmsCodeValidators
{
    public static readonly Regex SsccPattern = new(@"^\d{18}$");           // 18 cyfr
    public static readonly Regex DsPattern = new(@"^DS\d{8}$");            // DS + 8 cyfr  
    public static readonly Regex LocationPattern = new(@"^MP-\d+-\d+-\d+-\d+$"); // MP-H-R-M-P
    public static readonly Regex CardPattern = new(@"^\d{5,20}$");         // 5-20 cyfr
}
```

## API Endpoints (v1 - UKOŃCZONE)
- **POST /api/v1/auth/login-scan** - logowanie kartą
- **POST /api/v1/auth/logout** - wylogowanie
- **POST /api/v1/pallets/{code}/move** - przeniesienie palety
- **GET /api/v1/locations/{code}** - info o lokalizacji
- **GET /api/v1/pallets/{code}** - info o palecie

## DataWedge Integration (Zebra)
```csharp
// OBOWIĄZKOWA konfiguracja BroadcastReceiver w Platforms/Android
[BroadcastReceiver(Enabled = true, Exported = false)]
[IntentFilter(new[] { "com.example.wms.SCAN" })]
public class DataWedgeReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        var scannedData = intent.GetStringExtra("com.symbol.datawedge.data_string");
        // Przekazanie do ViewModel przez Messenger
    }
}
```

## Dokumentacja (AKTUALIZUJ ZAWSZE)
Po każdej większej zmianie OBOWIĄZKOWA aktualizacja:
- **docs/TODO.md** - postęp zadań z procentami  
- **docs/ARCHITECTURE.md** - zmiany architektoniczne
- **docs/CHANGELOG_*.md** - opis zmian dla odpowiedniej części

## Workflow Development
1. **Przeczytaj dokumentację** w docs/ przed rozpoczęciem
2. **Sprawdź TODO.md** - aktualny postęp i następne zadania
3. **Zachowaj Clean Architecture** - nie mieszaj warstw
4. **Testuj integrację** - szczególnie DataWedge i API calls
5. **Aktualizuj dokumentację** - TODO.md, ARCHITECTURE.md

## Bezpieczeństwo (NIGDY nie naruszaj)
- **HTTPS only** - wszystkie połączenia
- **JWT tokens** - 60min expiry, session tracking w DB
- **Input validation** - wszystkie dane wejściowe przez FluentValidation
- **SQL Injection protection** - tylko EF Core parametryzowane zapytania

## Kompilacja i Build
- **ZAWSZE używaj konfiguracji Debug** dla wszystkich projektóws


## Debugowanie i Obsługa błędów
```csharp
// OBOWIĄZKOWE logowanie z polskimi komunikatami
_logger.LogInformation("Paleta {PalletCode} przeniesiona z {From} do {To}", code, from, to);
_logger.LogError(ex, "Błąd podczas przenoszenia palety {PalletCode}", code);

// OBOWIĄZKOWA obsługa wyjątków z komunikatami PL
throw new PalletNotFoundException($"Paleta {code} nie została znaleziona w systemie");
```

## Lokalizacja (i18n)
- **Nowe elementy UI**: ZAWSZE dodawaj do mechanizmu tłumaczeń
- **Przewodnik**: [docs/../src/mobile/WmsApp/docs/LOCALIZATION_GUIDE.md]
- **Formaty**: .resx files + TranslateExtension w XAML

## OSTRZEŻENIA KRYTYCZNE ⚠️
1. **NIE MODYFIKUJ legacy tabel MySQL** - tylko mapowanie EF Core
2. **NIE USUWAJ kodu bez pełnego zrozumienia** - system produkcyjny
3. **ZAWSZE testuj DataWedge integration** przed commitem  
4. **NIE ZMIENIAJ wzorców walidacji kodów** - są zgodne z hardware
5. **ZAWSZE aktualizuj TODO.md z procentami** po ukończeniu zadania

## Status i następne kroki
**Aktualny postęp**: MVP 85% (Backend 100%, Frontend 85%)

**Priorytet 1**: Testowanie na Zebra MC3300
**Priorytet 2**: Wdrożenie Apache + HTTPS  
**Priorytet 3**: Monitoring i observability

---
*Ostatnia aktualizacja: 2025-01-10*
