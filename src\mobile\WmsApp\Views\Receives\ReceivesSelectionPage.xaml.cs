using WmsApp.ViewModels.Receives;
using WmsApp.Models.Receives;

namespace WmsApp.Views.Receives;

public partial class ReceivesSelectionPage : ContentPage
{
    public ReceivesSelectionPage(ReceivesSelectionViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public ReceivesSelectionViewModel VM => (ReceivesSelectionViewModel)BindingContext;

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // Auto-load data when page appears
        if (BindingContext is ReceivesSelectionViewModel viewModel)
        {
            await viewModel.LoadDataCommand.ExecuteAsync(null);
        }

        // Focus LK entry
        LkEntry.Focus();
    }

    private async void OnRealizujClicked(object sender, EventArgs e)
    {
        if (sender is But<PERSON> button && button.BindingContext is ReceiveDto receive)
        {
            if (BindingContext is ReceivesSelectionViewModel viewModel)
            {
                await viewModel.ClaimReceiveCommand.ExecuteAsync(receive);
            }
        }
    }
}
