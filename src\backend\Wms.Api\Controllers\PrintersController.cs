using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Queries;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler zarządzania drukarkami
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
[Produces("application/json")]
public class PrintersController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<PrintersController> _logger;

    public PrintersController(IMediator mediator, ILogger<PrintersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera listę dostępnych drukarek
    /// </summary>
    /// <param name="onlyActive"><PERSON><PERSON> pokazy<PERSON> tylko aktywne drukarki (domy<PERSON><PERSON>ie true)</param>
    /// <returns>Lista drukarek</returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<PrinterDto>), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<List<PrinterDto>>> GetPrinters(
        [FromQuery] bool onlyActive = true)
    {
        try
        {
            var query = new GetPrintersQuery { OnlyActive = onlyActive };
            var result = await _mediator.Send(query);

            _logger.LogDebug("Pobrano {Count} drukarek (tylko aktywne: {OnlyActive})", 
                result.Count, onlyActive);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania listy drukarek");
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił nieoczekiwany błąd podczas pobierania drukarek",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Pobiera szczegóły konkretnej drukarki
    /// </summary>
    /// <param name="id">ID drukarki</param>
    /// <returns>Szczegóły drukarki</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(PrinterDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<PrinterDto>> GetPrinter([FromRoute] int id)
    {
        try
        {
            var query = new GetPrintersQuery { OnlyActive = false };
            var printers = await _mediator.Send(query);
            
            var printer = printers.FirstOrDefault(p => p.Id == id);
            if (printer == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Drukarka nie znaleziona",
                    Detail = $"Drukarka o ID {id} nie została znaleziona",
                    Status = 404
                });
            }

            return Ok(printer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania drukarki {PrinterId}", id);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił nieoczekiwany błąd podczas pobierania drukarki",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Testuje połączenie z drukarką
    /// </summary>
    /// <param name="id">ID drukarki</param>
    /// <returns>Wynik testu połączenia</returns>
    [HttpPost("{id:int}/test")]
    [ProducesResponseType(typeof(PrinterTestResult), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<PrinterTestResult>> TestPrinter([FromRoute] int id)
    {
        try
        {
            var query = new GetPrintersQuery { OnlyActive = false };
            var printers = await _mediator.Send(query);
            
            var printer = printers.FirstOrDefault(p => p.Id == id);
            if (printer == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Drukarka nie znaleziona",
                    Detail = $"Drukarka o ID {id} nie została znaleziona",
                    Status = 404
                });
            }

            // TODO: Implementacja rzeczywistego testu drukarki
            // Na razie zwracamy symulowany wynik
            var testResult = new PrinterTestResult
            {
                Success = printer.IsActive,
                Message = printer.IsActive 
                    ? "Drukarka jest dostępna" 
                    : "Drukarka jest nieaktywna",
                TestedAt = DateTime.UtcNow,
                PrinterId = id,
                PrinterIp = printer.IpAddress,
                ResponseTimeMs = printer.IsActive ? 150 : -1
            };

            _logger.LogInformation("Test drukarki {PrinterId} ({PrinterName}): {Success}", 
                id, printer.Nazwa, testResult.Success);

            return Ok(testResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas testowania drukarki {PrinterId}", id);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił nieoczekiwany błąd podczas testowania drukarki",
                Status = 500
            });
        }
    }
}

/// <summary>
/// Model wyników testu drukarki
/// </summary>
public class PrinterTestResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime TestedAt { get; set; }
    public int PrinterId { get; set; }
    public string PrinterIp { get; set; } = string.Empty;
    public int ResponseTimeMs { get; set; }
}
