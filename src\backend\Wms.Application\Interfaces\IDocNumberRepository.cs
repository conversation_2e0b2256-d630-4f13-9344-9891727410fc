using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

public interface IDocNumberRepository
{
    /// <summary>
    /// Pobiera wpis docnumber po nazwie
    /// </summary>
    Task<DocNumber?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Atomowo pobiera następny numer i aktualizuje wartość w bazie
    /// </summary>
    Task<int> GetNextNumberAsync(string name, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Aktualizu<PERSON> warto<PERSON> last dla danej nazwy
    /// </summary>
    Task UpdateLastNumberAsync(string name, int newValue, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Tworzy nowy wpis docnumber jeśli nie istnieje
    /// </summary>
    Task<DocNumber> CreateIfNotExistsAsync(string name, int initialValue = 0, CancellationToken cancellationToken = default);
}
