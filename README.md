# WMS-System

System zarządzania magazynem (WMS) składający się z aplikacji mobilnej MAUI dla urządzeń Zebra MC3300 (Android) oraz backendu ASP.NET Core (.NET 9) z bazą danych MySQL.

## 🎯 Cel projektu
Stworzenie MVP systemu WMS skupionego na dwóch kluczowych operacjach:
- **Logowanie użytkownika** przez skan karty (bez PIN)
- **Zmiana lokalizacji palety** w workflow "scan → scan" (bez dodatkowego potwierdzenia)

## 🏗️ Architektura

### Backend (.NET 9)
- **Wms.Api** - kontrolery REST API, middleware
- **Wms.Application** - use cases, DTO, walidacje
- **Wms.Domain** - encje domenowe, reguły biznesowe
- **Wms.Infrastructure** - EF Core, repozytoria, MySQL

### Frontend (MAUI Android)
- **WmsApp** - aplikacja mobilna dla Zebra MC3300
- Integracja z Zebra DataWedge (skanowanie)
- Komunikacja z backendem przez HTTPS/REST

### Infrastruktura
- **MySQL 8.0** - baza danych
- **Apache** - reverse proxy (HTTPS)
- **Observability** - Grafana/Loki/Prometheus

## 🚀 Uruchomienie

### Wymagania
- .NET 9 SDK
- MySQL 8.0
- Visual Studio 2022 lub VS Code z C# Dev Kit

### Backend
```bash
cd src/backend/Wms.Api
dotnet run
```

### Frontend (Android)
```bash
cd src/mobile/WmsApp
dotnet build -t:Run -f net9.0-android
```

### Cały solution
```bash
dotnet build
```

## 📚 Dokumentacja
Pełna dokumentacja znajduje się w folderze `docs/`:

- **[PRD.md](docs/PRD.md)** - wymagania produktowe
- **[ARCHITECTURE.md](docs/ARCHITECTURE.md)** - architektura systemu
- **[STYLE_GUIDE.md](docs/STYLE_GUIDE.md)** - zasady kodowania
- **[TODO.md](docs/TODO.md)** - plan zadań i postęp
- **[TODO_BACKEND.md](docs/TODO_BACKEND.md)** - szczegóły backendu
- **[TODO_FRONTEND.md](docs/TODO_FRONTEND.md)** - szczegóły frontendu
- **[LOCAL_TESTING.md](docs/LOCAL_TESTING.md)** - lokalne testy: backend (8081) + proxy (8080) + aplikacja MAUI
- **[GS1_PARSING.md](docs/GS1_PARSING.md)** - parsowanie GS1-128 (prefiks IZ, FNC1, AI 00/01/02/10/17/37), przykłady curl przez Proxy
- **[GS1_PROXY_CHEATSHEET.md](docs/GS1_PROXY_CHEATSHEET.md)** - skrócone komendy do szybkich testów przez Proxy

## ⚡ Quick start lokalny (backend + proxy)

1) Backend (nowe okno cmd):
```bat
start "WMS API 8081" cmd /k "set DISABLE_FLUENT_VALIDATION=1 && set ASPNETCORE_URLS=http://127.0.0.1:8081 && set ASPNETCORE_ENVIRONMENT=Development && dotnet run --project src\backend\Wms.Api\Wms.Api.csproj --no-launch-profile"
```

2) Proxy (nowe okno cmd):
```bat
start "API Proxy Local" python api-proxy-local.py
```

3) Test:
```bash
curl -v http://127.0.0.1:8080/api/v1/health
curl -v -H "Content-Type: application/json" -d "{\"cardNumber\":\"1234567\",\"deviceId\":\"curl-localhost\"}" http://127.0.0.1:8080/api/v1/auth/login-scan
```

### Wariant PowerShell (alternatywa dla cmd)

1) Backend (nowe okno PowerShell):
```powershell
$env:DISABLE_FLUENT_VALIDATION='1'
$env:ASPNETCORE_URLS='http://127.0.0.1:8081'
$env:ASPNETCORE_ENVIRONMENT='Development'
Start-Process -WindowStyle Normal -FilePath dotnet -ArgumentList "run --project 'src\backend\Wms.Api\Wms.Api.csproj' --no-launch-profile"
```

2) Proxy (nowe okno PowerShell):
```powershell
Start-Process -WindowStyle Normal -FilePath python -ArgumentList "api-proxy-local.py"
```

3) Test:
```powershell
curl -v http://127.0.0.1:8080/api/v1/health
curl -v -H "Content-Type: application/json" -d '{"cardNumber":"1234567","deviceId":"curl-localhost"}' http://127.0.0.1:8080/api/v1/auth/login-scan
```

### Użycie na emulatorze Android (********)

- W aplikacji MAUI wybierz środowisko: "Android Emulator (********:8080)"
- ******** to alias hosta z perspektywy emulatora – żądania trafią na lokalny port 8080 hosta
- Upewnij się, że:
  - Backend działa na 127.0.0.1:8081 (patrz sekcja Quick start)
  - Proxy (api-proxy-local.py) działa na 127.0.0.1:8080
- Na Windows (bez emulatora) możesz użyć środowiska: "Localhost (127.0.0.1:8080)"
- W razie błędu WinError 10061 (connection refused) sprawdź czy backend na 8081 jest uruchomiony i endpoint /api/v1/health zwraca 200

### Common issues and solutions

- Proxy error: WinError 10061 (connection refused)
  - Przyczyna: backend na 127.0.0.1:8081 nie działa lub port jest zablokowany.
  - Rozwiązanie:
    - Uruchom backend (patrz Quick start) i sprawdź zdrowie:
      - netstat -ano | findstr LISTENING | findstr ":8081"
      - curl -v http://127.0.0.1:8081/api/v1/health (oczekiwane 200)

- 503 Service Unavailable (zdalny backend)
  - Przyczyna: serwer zdalny w konserwacji/awarii.
  - Rozwiązanie: użyj lokalnego scenariusza backend+proxy (127.0.0.1:8081 + proxy 8080) i ustaw w aplikacji środowisko Localhost lub Android Emulator (********:8080).

- 500 TypeLoadException (FluentValidation.DependencyInjectionExtensions)
  - Przyczyna: konflikt wersji FluentValidation między projektami.
  - Rozwiązanie:
    - Do szybkich testów ustaw DISABLE_FLUENT_VALIDATION=1 przy starcie API.
    - Ujednolić wersje FV (gałąź 11.x) w projektach Wms.Api i Wms.Application, następnie dotnet restore i dotnet build.

- Build/port lock: "file is being used by another process" lub port 8080/8081 zajęty
  - Rozwiązanie:
    - Zamknij okno uruchomionego API lub zabij proces:
      - taskkill /IM Wms.Api.exe /F
    - Sprawdź nasłuchujące porty i PID:
      - netstat -ano | findstr LISTENING | findstr ":8081"
      - netstat -ano | findstr LISTENING | findstr ":8080"

- Emulator nie łączy się z hostem
  - Rozwiązanie:
    - Użyj ********:8080 (alias hosta dla emulatora Android).
    - Upewnij się, że proxy działa na 8080 i nie blokuje go firewall.
    - Zweryfikuj curl z hosta: curl -v http://127.0.0.1:8080/api/v1/health

- Problemy z łącznością MySQL
  - Rozwiązanie:
    - PowerShell: Test-NetConnection -ComputerName ************ -Port 3306
    - Sprawdź poświadczenia i uprawnienia użytkownika MySQL oraz istnienie bazy wmsggtest.

- /api/v1/health nie zwraca 200
  - Rozwiązanie:
    - Zrestartuj API (Development), sprawdź logi: src/backend/Wms.Api/logs/wms-api-YYYYMMDD.log

## 📋 Status projektu
**Postęp MVP: 22%**

- ✅ Struktura solution i projektów
- ✅ Dokumentacja projektu
- 🔄 Implementacja encji domenowych
- ⏳ Konfiguracja bazy danych
- ⏳ API endpoints
- ⏳ Aplikacja mobilna

## 🛠️ Stack technologiczny

### Backend
- ASP.NET Core .NET 9
- Entity Framework Core
- MySQL (Pomelo provider)
- JWT Authentication
- Serilog + OpenTelemetry

### Frontend
- .NET MAUI (Android)
- Zebra DataWedge
- HttpClient dla API
- CommunityToolkit.Mvvm

### DevOps
- Apache HTTP Server
- Docker (planowane)
- Grafana + Loki + Prometheus

## 📱 Urządzenia docelowe
- **Zebra MC3300** (Android 10+)
- Skanery 1D/2D z DataWedge
- Komunikacja w sieci lokalnej (LAN)

## 🔧 Development

### Build całego solution
```bash
dotnet build
```

### Uruchomienie testów
```bash
dotnet test
```

### Formatowanie kodu
Projekt używa EditorConfig - upewnij się, że Twoje IDE wspiera go.

---

**Autor**: ldomansk  
**Wersja**: 0.1  
**Data**: 2025-09-01
