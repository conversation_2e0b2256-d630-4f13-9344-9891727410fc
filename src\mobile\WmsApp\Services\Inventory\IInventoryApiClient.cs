using Refit;
using WmsApp.Models.Inventory;

namespace WmsApp.Services.Inventory;

/// <summary>
/// API client interface dla modułu inwentaryzacji
/// Definiuje wszystkie endpointy komunikacji z backendem
/// </summary>
public interface IInventoryApiClient
{
    // ===== ZARZĄDZANIE INWENTARYZACJAMI =====

    /// <summary>
    /// Pobiera listę aktywnych inwentaryzacji
    /// </summary>
[Get("/api/v1/inventory/sessions")]
Task<IApiResponse<ApiListResponse<InventoryDto>>> GetActiveInventoriesAsync([Query] InventoryType? type = null);

    /// <summary>
    /// Pobiera szczegóły inwentaryzacji
    /// </summary>
    [Get("/api/v1/inventory/{inventoryId}")]
Task<IApiResponse<ApiDataResponse<InventoryDetailsDto>>> GetInventoryDetailsAsync(int inventoryId);

    /// <summary>
    /// Pobiera postęp inwentaryzacji
    /// </summary>
    [Get("/api/v1/inventory/{inventoryId}/progress")]
Task<IApiResponse<ApiDataResponse<InventoryProgressDto>>> GetInventoryProgressAsync(int inventoryId);

    // ===== SESJE INWENTARYZACJI =====

    /// <summary>
    /// Rozpoczyna sesję inwentaryzacji
    /// </summary>
    [Post("/api/v1/inventory/sessions/start")]
Task<IApiResponse<ApiDataResponse<InventorySessionDto>>> StartInventorySessionAsync(
        [Body] StartInventoryRequest request);

    /// <summary>
    /// Kończy sesję inwentaryzacji
    /// </summary>
    [Post("/api/v1/inventory/sessions/{sessionId}/end")]
    Task<IApiResponse<bool>> EndInventorySessionAsync(int sessionId);

    // ===== SKANOWANIE I POZYCJE =====

    /// <summary>
    /// Przetwarza skan w sesji inwentaryzacji (SSCC, DS, kody produktów)
    /// </summary>
    [Post("/api/v1/inventory/scan")]
Task<IApiResponse<ApiDataResponse<InventoryScanResponse>>> ProcessScanAsync([Body] InventoryScanRequest request);

    /// <summary>
    /// Wyszukuje etykietę w inwentaryzacji
    /// </summary>
    [Get("/api/v1/inventory/{inventoryId}/search")]
Task<IApiResponse<ApiDataResponse<InventoryItemDto>>> SearchInventoryLabelAsync(
        int inventoryId,
        [Query] string? etykietaId = null,
        [Query] string? nrSap = null,
        [Query] string? paletaId = null);

    /// <summary>
    /// Pobiera pozycje inwentaryzacji
    /// </summary>
    [Get("/api/v1/inventory/{inventoryId}/items")]
Task<IApiResponse<ApiListResponse<InventoryItemDto>>> GetInventoryItemsAsync(
        int inventoryId,
        [Query] int? pracownikId = null,
        [Query] bool onlyCompleted = false);

    // ===== OPERACJE NA POZYCJACH =====

    /// <summary>
    /// Tworzy nową pozycję inwentaryzacji
    /// </summary>
    [Post("/api/v1/inventory/{inventoryId}/items")]
Task<IApiResponse<ApiDataResponse<InventoryItemDto>>> CreateInventoryItemAsync(
        int inventoryId,
        [Body] InventoryItemRequest request);

    /// <summary>
    /// Aktualizuje pozycję inwentaryzacji
    /// </summary>
    [Put("/api/v1/inventory/items/{itemId}")]
Task<IApiResponse<ApiDataResponse<InventoryItemDto>>> UpdateInventoryItemAsync(
        int itemId,
        [Body] InventoryItemRequest request);

    // ===== WYSZUKIWANIE KODÓW PRODUKTÓW =====

    /// <summary>
    /// Wyszukuje kod produktu w systemie
    /// </summary>
    [Get("/api/v1/inventory/product-codes/search")]
    Task<IApiResponse<ProductCodeDto>> SearchProductCodeAsync(
        [Query] string code,
        [Query] int systemId);

    // ===== OPCJE INWENTARYZACJI =====

    /// <summary>
    /// Pobiera dostępne opcje inwentaryzacji dla menu
    /// </summary>
    [Get("/api/v1/inventory/options")]
    Task<IApiResponse<InventoryOptionDto[]>> GetInventoryOptionsAsync();
}