---
description: 
auto_execution_mode: 3
---

## WMS System Development Guidelines

### Compilation Configuration
- Always use **Debug** configuration when building the project
- Use `dotnet build` with Debug configuration for both backend and mobile projects

### Backend Application Startup
When running the WMS backend application, use the following command with specific environment variables:

```bash
DISABLE_FLUENT_VALIDATION=1 ASPNETCORE_URLS=http://127.0.0.1:8081 ASPNETCORE_ENVIRONMENT=Development dotnet run --project src/backend/Wms.Api/Wms.Api.csproj --no-launch-profile
```

**Environment Variables Explained:**
- `DISABLE_FLUENT_VALIDATION=1` - Disables FluentValidation for testing purposes
- `ASPNETCORE_URLS=http://127.0.0.1:8081` - Sets the backend to listen on port 8081
- `ASPNETCORE_ENVIRONMENT=Development` - Sets development environment mode

### API Testing Configuration
When testing API endpoints:

1. **Test Card Number:** Use `1234567` as the test card number
2. **Proxy Server:** Use the API proxy server running on port 8080
3. **Login Endpoint:** `http://127.0.0.1:8080/api/v1/auth/login-scan`

**Test Login Request:**
```json
{
  "cardNumber": "1234567",
  "deviceId": "Emulator"
}
```

### Testing Environment Requirements
- **DO NOT** use mock services for testing - always test against the real backend through the proxy
- **DO** use the API proxy server for all endpoint testing
- **Proxy Startup:** Run the proxy server using: `python api-proxy-local.py`

### Architecture Notes
- Backend runs on port 8081 (direct access)
- API proxy runs on port 8080 (for mobile app testing)
- Mobile app should connect through proxy (port 8080) not directly to backend (port 8081)