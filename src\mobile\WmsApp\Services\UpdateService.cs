using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text.Json;
using WmsApp.Models.Update;
using WmsApp.Services.Contracts;

namespace WmsApp.Services;

/// <summary>
/// Implementacja serwisu auto-aktualizacji aplikacji
/// </summary>
public class UpdateService : IUpdateService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<UpdateService> _logger;
    private const string ManifestUrl = "https://172.6.1.249/wms_android_update/app.json";
    
    public UpdateService(HttpClient httpClient, ILogger<UpdateService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sprawdzanie aktualizacji z {ManifestUrl}", ManifestUrl);

            // Pobranie manifestu z serwera
            var response = await _httpClient.GetAsync(ManifestUrl, cancellationToken);
            response.EnsureSuccessStatusCode();

            var jsonContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var manifest = JsonSerializer.Deserialize<AppManifest>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (manifest == null)
            {
                _logger.LogWarning("Nie udało się sparsować manifestu aplikacji");
                return new UpdateInfo
                {
                    IsAvailable = false,
                    CurrentVersion = GetCurrentVersion(),
                    ErrorMessage = "Nie udało się sparsować manifestu aplikacji"
                };
            }

            var currentVersionCode = GetCurrentVersionCode();
            var isUpdateAvailable = manifest.VersionCode > currentVersionCode;

            _logger.LogInformation(
                "Aktualna wersja: {CurrentVersion} (kod: {CurrentVersionCode}), " +
                "dostępna wersja: {NewVersion} (kod: {NewVersionCode}), " +
                "aktualizacja dostępna: {IsAvailable}",
                GetCurrentVersion(), currentVersionCode,
                manifest.Version, manifest.VersionCode,
                isUpdateAvailable);

            return new UpdateInfo
            {
                IsAvailable = isUpdateAvailable,
                CurrentVersion = GetCurrentVersion(),
                NewVersion = manifest.Version,
                IsRequired = manifest.Required,
                Manifest = manifest
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas sprawdzania aktualizacji");
            return new UpdateInfo
            {
                IsAvailable = false,
                CurrentVersion = GetCurrentVersion(),
                ErrorMessage = $"Błąd podczas sprawdzania aktualizacji: {ex.Message}"
            };
        }
    }

    public async Task<string> DownloadApkAsync(AppManifest manifest, IProgress<DownloadProgress>? progress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Rozpoczynam pobieranie APK z {DownloadUrl}", manifest.DownloadUrl);

            // Utworzenie katalogu downloads jeśli nie istnieje
            var downloadsPath = Path.Combine(FileSystem.Current.CacheDirectory, "downloads");
            Directory.CreateDirectory(downloadsPath);

            var fileName = $"wms-app-{manifest.Version}.apk";
            var filePath = Path.Combine(downloadsPath, fileName);

            // Usuń poprzednią wersję jeśli istnieje
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            progress?.Report(new DownloadProgress
            {
                BytesDownloaded = 0,
                TotalBytes = manifest.FileSize,
                Status = DownloadStatus.InProgress
            });

            using var response = await _httpClient.GetAsync(manifest.DownloadUrl, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            var totalBytes = response.Content.Headers.ContentLength ?? manifest.FileSize;
            var downloadedBytes = 0L;

            using var contentStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize: 8192, useAsync: true);

            var buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                downloadedBytes += bytesRead;

                progress?.Report(new DownloadProgress
                {
                    BytesDownloaded = downloadedBytes,
                    TotalBytes = totalBytes,
                    Status = DownloadStatus.InProgress
                });
            }

            progress?.Report(new DownloadProgress
            {
                BytesDownloaded = downloadedBytes,
                TotalBytes = totalBytes,
                Status = DownloadStatus.Completed
            });

            _logger.LogInformation("APK pobrany pomyślnie do {FilePath} ({Size} bajtów)", filePath, downloadedBytes);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania APK");
            progress?.Report(new DownloadProgress
            {
                Status = DownloadStatus.Failed,
                ErrorMessage = ex.Message
            });
            throw;
        }
    }

    public async Task<bool> VerifyApkIntegrityAsync(string filePath, string expectedSha256)
    {
        try
        {
            _logger.LogInformation("Weryfikacja integralności pliku {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Plik APK nie istnieje: {FilePath}", filePath);
                return false;
            }

            using var sha256 = SHA256.Create();
            using var fileStream = File.OpenRead(filePath);
            var hashBytes = await sha256.ComputeHashAsync(fileStream);
            var actualSha256 = Convert.ToHexString(hashBytes).ToLowerInvariant();

            var isValid = string.Equals(actualSha256, expectedSha256, StringComparison.OrdinalIgnoreCase);

            _logger.LogInformation(
                "Weryfikacja SHA-256: oczekiwany={Expected}, rzeczywisty={Actual}, poprawny={IsValid}",
                expectedSha256, actualSha256, isValid);

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas weryfikacji integralności pliku");
            return false;
        }
    }

    public async Task<bool> InstallApkAsync(string apkFilePath)
    {
        try
        {
            _logger.LogInformation("Uruchamianie instalacji APK: {ApkFilePath}", apkFilePath);

            if (!File.Exists(apkFilePath))
            {
                _logger.LogError("Plik APK nie istnieje: {ApkFilePath}", apkFilePath);
                return false;
            }

            // Sprawdź uprawnienia
            if (!CanInstallFromUnknownSources())
            {
                var permissionGranted = await RequestInstallPermissionAsync();
                if (!permissionGranted)
                {
                    _logger.LogWarning("Brak uprawnień do instalacji z nieznanych źródeł");
                    return false;
                }
            }

#if ANDROID
            return await InstallApkOnAndroidAsync(apkFilePath);
#else
            _logger.LogWarning("Instalacja APK jest obsługiwana tylko na Androidzie");
            return false;
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas uruchamiania instalacji APK");
            return false;
        }
    }

#if ANDROID
private async Task<bool> InstallApkOnAndroidAsync(string apkFilePath)
    {
        // Upewnij się, że metoda jest asynchroniczna (CS1998)
        await Task.Yield();
        try
        {
            var context = Platform.CurrentActivity ?? Android.App.Application.Context;
            var file = new Java.IO.File(apkFilePath);

            // Tworzenie URI dla pliku APK (z FileProvider dla Android 7+)
Android.Net.Uri? apkUri;
            if (Android.OS.Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.N)
            {
                apkUri = AndroidX.Core.Content.FileProvider.GetUriForFile(
                    context,
                    $"{context.PackageName}.fileprovider",
                    file);
            }
            else
            {
                apkUri = Android.Net.Uri.FromFile(file);
            }

// Tworzenie Intent do instalacji
            #pragma warning disable CA1422 // Validate platform compatibility
            var installIntent = new Android.Content.Intent(Android.Content.Intent.ActionInstallPackage);
            #pragma warning restore CA1422
            if (apkUri == null)
            {
                _logger.LogError("Nie udało się utworzyć URI do APK");
                return false;
            }
            installIntent.SetData(apkUri);
            installIntent.SetFlags(Android.Content.ActivityFlags.GrantReadUriPermission | Android.Content.ActivityFlags.NewTask);

            // Uruchomienie instalatora
            context.StartActivity(installIntent);

            _logger.LogInformation("Intent instalacji APK został uruchomiony");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas uruchamiania instalatora APK na Androidzie");
            return false;
        }
    }
#endif

    public string GetCurrentVersion()
    {
        return AppInfo.Current.VersionString;
    }

    public int GetCurrentVersionCode()
    {
        return int.Parse(AppInfo.Current.BuildString);
    }

    public bool CanInstallFromUnknownSources()
    {
#if ANDROID
        try
        {
            var context = Platform.CurrentActivity ?? Android.App.Application.Context;
            if (Android.OS.Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.O)
            {
                var packageManager = context.PackageManager;
                return packageManager?.CanRequestPackageInstalls() ?? false;
            }
            else
            {
                // Dla starszych wersji Androida sprawdź ustawienie systemowe
                var contentResolver = context.ContentResolver;
#pragma warning disable CA1422 // Validate platform compatibility
                return Android.Provider.Settings.Secure.GetInt(contentResolver, Android.Provider.Settings.Secure.InstallNonMarketApps, 0) == 1;
                #pragma warning restore CA1422
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas sprawdzania uprawnień do instalacji z nieznanych źródeł");
            return false;
        }
#else
        return false;
#endif
    }

    public async Task<bool> RequestInstallPermissionAsync()
    {
#if ANDROID
        try
        {
            if (Android.OS.Build.VERSION.SdkInt >= Android.OS.BuildVersionCodes.O)
            {
                var context = Platform.CurrentActivity ?? Android.App.Application.Context;
                var packageManager = context.PackageManager;

                if (packageManager?.CanRequestPackageInstalls() == false)
                {
                    // Przekieruj użytkownika do ustawień
                    var intent = new Android.Content.Intent(Android.Provider.Settings.ActionManageUnknownAppSources);
                    intent.SetData(Android.Net.Uri.Parse($"package:{context.PackageName}"));
                    intent.SetFlags(Android.Content.ActivityFlags.NewTask);
                    context.StartActivity(intent);

                    // Czekaj na powrót użytkownika (uproszczona implementacja)
                    await Task.Delay(2000);
                    return packageManager?.CanRequestPackageInstalls() ?? false;
                }
                return true;
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas żądania uprawnień do instalacji");
            return false;
        }
#else
        await Task.CompletedTask;
        return false;
#endif
    }
}
