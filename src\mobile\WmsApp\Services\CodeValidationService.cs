using System.Text.RegularExpressions;
using WmsApp.Models;

namespace WmsApp.Services;

public interface ICodeValidationService
{
    ScanResult ValidateCode(string code);
    bool IsValidSSCC(string code);
    bool IsValidDS(string code);
    bool IsValidLocationCode(string code);
    bool IsValidEmployeeCard(string code);
}

public class CodeValidationService : ICodeValidationService
{
    private readonly Regex _ssccPattern = new(@"^\d{18}$", RegexOptions.Compiled);
    private readonly Regex _dsPattern = new(@"^DS\d{4,9}$", RegexOptions.Compiled);
    private readonly Regex _locationPattern = new(@"^MP-\d+-[A-Z0-9]+-\d+-[A-Z0-9]+$", RegexOptions.Compiled);
    private readonly Regex _employeeCardPattern = new(@"^\d{10,}$", RegexOptions.Compiled);

    public ScanResult ValidateCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            return new ScanResult
            {
                Code = code ?? string.Empty,
                Type = ScanCodeType.Unknown,
                IsValid = false,
                ErrorMessage = "Kod nie może być pusty"
            };
        }

        code = code.Trim().ToUpperInvariant();

        // SSCC - 18 cyfr
        if (IsValidSSCC(code))
        {
            return new ScanResult
            {
                Code = code,
                Type = ScanCodeType.SSCC,
                IsValid = true
            };
        }

        // DS kod - DS + 4-9 cyfr
        if (IsValidDS(code))
        {
            return new ScanResult
            {
                Code = code,
                Type = ScanCodeType.DS,
                IsValid = true
            };
        }

        // Lokalizacja - MP-hala-regal-miejsce-poziom (regal i poziom mogą być alfanumeryczne)
        if (IsValidLocationCode(code))
        {
            return new ScanResult
            {
                Code = code,
                Type = ScanCodeType.Location,
                IsValid = true
            };
        }

        // Karta pracownika - 10+ cyfr
        if (IsValidEmployeeCard(code))
        {
            return new ScanResult
            {
                Code = code,
                Type = ScanCodeType.EmployeeCard,
                IsValid = true
            };
        }

        return new ScanResult
        {
            Code = code,
            Type = ScanCodeType.Unknown,
            IsValid = false,
            ErrorMessage = "Nierozpoznany format kodu"
        };
    }

    public bool IsValidSSCC(string code)
    {
        return !string.IsNullOrEmpty(code) && _ssccPattern.IsMatch(code);
    }

    public bool IsValidDS(string code)
    {
        return !string.IsNullOrEmpty(code) && _dsPattern.IsMatch(code.ToUpperInvariant());
    }

    public bool IsValidLocationCode(string code)
    {
        return !string.IsNullOrEmpty(code) && _locationPattern.IsMatch(code.ToUpperInvariant());
    }

    public bool IsValidEmployeeCard(string code)
    {
        return !string.IsNullOrEmpty(code) && _employeeCardPattern.IsMatch(code);
    }
}
