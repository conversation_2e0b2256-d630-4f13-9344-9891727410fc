using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WmsApp.Models.Auth;
using WmsApp.Services;

namespace WmsApp.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IWmsApiService _apiService;

    [ObservableProperty]
    private UserInfo? currentUser;

    [ObservableProperty]
    private string welcomeMessage = "Welcome to WMS!";

    public MainViewModel(IWmsApiService apiService)
    {
        _apiService = apiService;
        UpdateWelcomeMessage();
    }

    [RelayCommand]
    private async Task NavigateToLocationsAsync()
    {
        await Shell.Current.GoToAsync("///locations");
    }

    [RelayCommand]
    private async Task NavigateToPalletsAsync()
    {
        await Shell.Current.GoToAsync("///pallets");
    }

    [RelayCommand]
    private async Task NavigateToOptionsAsync()
    {
        await Shell.Current.GoToAsync("///options");
    }

    [RelayCommand]
    private async Task NavigateToAcceptanceAsync()
    {
        // Navigate to receives selection page (Widok 1: Wybór dostawy)
        await Shell.Current.GoToAsync("//receivesselection");
    }

    [RelayCommand]
    private async Task NavigateToIssuesAsync()
    {
        // Placeholder for issues functionality
        await Shell.Current.DisplayAlert("Informacja", "Funkcja Wydania będzie dostępna wkrótce", "OK");
    }

    [RelayCommand]
    private async Task NavigateToOperationsAsync()
    {
        // Redirect to existing pallets page for now
        await Shell.Current.GoToAsync("///pallets");
    }

    [RelayCommand]
    private async Task NavigateToTasksAsync()
    {
        // Placeholder for tasks functionality
        await Shell.Current.DisplayAlert("Informacja", "Funkcja Zadania będzie dostępna wkrótce", "OK");
    }

    [RelayCommand]
    private async Task NavigateToInventoryAsync()
    {
        // Navigate to inventory selection page
        await Shell.Current.GoToAsync("//inventoryselection");
    }

    [RelayCommand]
    private async Task NavigateToReportsAsync()
    {
        // Placeholder for reports functionality
        await Shell.Current.DisplayAlert("Informacja", "Funkcja Raporty będzie dostępna wkrótce", "OK");
    }

    [RelayCommand]
    private async Task NavigateToMovementsAsync()
    {
        // Redirect to existing locations page for now (movement operations)
        await Shell.Current.GoToAsync("///locations");
    }

    [RelayCommand]
    private async Task NavigateToSettingsAsync()
    {
        await Shell.Current.GoToAsync("///options");
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            await _apiService.LogoutAsync();
            await Shell.Current.GoToAsync("//login");
        }
        catch (Exception ex)
        {
            // Handle error
            await Shell.Current.DisplayAlert("Error", $"Logout failed: {ex.Message}", "OK");
        }
    }

    private void UpdateWelcomeMessage()
    {
        if (CurrentUser != null)
        {
            var timeOfDay = DateTime.Now.Hour switch
            {
                < 12 => "Good morning",
                < 18 => "Good afternoon",
                _ => "Good evening"
            };
            
            WelcomeMessage = $"{timeOfDay}, {CurrentUser.FullName}!";
        }
        else
        {
            WelcomeMessage = "Welcome!";
        }
    }
}
