using System.Text.RegularExpressions;
using Wms.Application.DTOs;
using Wms.Domain.Services.GS1;
using Wms.Domain.ValueObjects;

namespace Wms.Application.Services;

public interface ICodeValidationService
{
    bool ValidateSSCC(string code);
    bool ValidateDS(string code);
    bool ValidateLocationCode(string code);
    bool ValidateLK(string code);
    (int hala, string regal, int miejsce, string? poziom) ParseLocationCode(string code);
    int ParseLKId(string lkCode);
    
    // GS1 parsing methods
    ScanValidationResult ValidateAndParseScan(string rawData);
    GS1ParseResult ParseGS1(string rawData);
    ScanType DetectScanType(string rawData);
}

public class CodeValidationService : ICodeValidationService
{
    private readonly GS1Parser _gs1Parser;
    
    // SSCC: 18 cyfr (zgodnie z GS1 standard)
    private static readonly Regex SSCCPattern = new(@"^\d{18}$", RegexOptions.Compiled);
    
    // DS: DS + 4-9 cyfr (DS1234 do DS123456789)
    private static readonly Regex DSPattern = new(@"^DS\d{4,9}$", RegexOptions.Compiled);
    
    // Lokalizacja: MP-H-R-M-P gdzie:
    // H = hala (1-3 cyfry)
    // R = regal (alfanumeryczny, np. RMP, 1, 149)
    // M = miejsce (1-3 cyfry, może być 010, 099, 123)  
    // P = poziom (1 cyfra/znak, np. 1, 6, 4)
    private static readonly Regex LocationPattern = new(@"^MP-(\d{1,3})-([A-Z0-9]+)-(\d{1,3})-([A-Z0-9]{1})$", RegexOptions.Compiled);
    
    // LK: LK + 1-8 cyfr (np. LK1, LK123, LK12345678)
    private static readonly Regex LKPattern = new(@"^LK\d{1,8}$", RegexOptions.Compiled);
    
    // GS1 detection patterns
    private static readonly Regex GS1Pattern = new(@"^(IZ)?(\]C1)?\d{2,4}", RegexOptions.Compiled);
    private static readonly Regex IZPrefixPattern = new(@"^IZ", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    
    public CodeValidationService()
    {
        _gs1Parser = new GS1Parser();
    }

    public bool ValidateSSCC(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        if (!SSCCPattern.IsMatch(code))
            return false;

        // Walidacja check digit według GS1 standard
        return ValidateSSCCCheckDigit(code);
    }

    public bool ValidateDS(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        return DSPattern.IsMatch(code);
    }

    public bool ValidateLocationCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        return LocationPattern.IsMatch(code);
    }

    public (int hala, string regal, int miejsce, string? poziom) ParseLocationCode(string code)
    {
        if (!ValidateLocationCode(code))
            throw new ArgumentException($"Invalid location code format: {code}. Expected: MP-H-R-M-P (e.g., MP-1-RMP-010-1)");

        var match = LocationPattern.Match(code);
        
        return (
            hala: int.Parse(match.Groups[1].Value),
            regal: match.Groups[2].Value,
            miejsce: int.Parse(match.Groups[3].Value),
            poziom: match.Groups[4].Value
        );
    }

    public bool ValidateLK(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        return LKPattern.IsMatch(code);
    }

    public int ParseLKId(string lkCode)
    {
        if (!ValidateLK(lkCode))
            throw new ArgumentException($"Invalid LK code format: {lkCode}. Expected format: LK + 1-8 digits");

        var numberPart = lkCode[2..]; // Remove "LK" prefix
        if (!int.TryParse(numberPart, out var id))
            throw new ArgumentException($"Cannot parse ID from LK code: {lkCode}");

        return id;
    }

    private bool ValidateSSCCCheckDigit(string sscc)
    {
        // GS1 check digit calculation (modulo 10)
        var sum = 0;
        var multiplier = 3;

        // Przetwarzanie od prawej do lewej, pomijając ostatnią cyfrę (check digit)
        for (int i = sscc.Length - 2; i >= 0; i--)
        {
            var digit = int.Parse(sscc[i].ToString());
            sum += digit * multiplier;
            multiplier = multiplier == 3 ? 1 : 3;
        }

        var checkDigit = (10 - (sum % 10)) % 10;
        var providedCheckDigit = int.Parse(sscc[^1].ToString());

        return checkDigit == providedCheckDigit;
    }
    
    /// <summary>
    /// Główna metoda walidacji i parsowania skanów
    /// </summary>
    public ScanValidationResult ValidateAndParseScan(string rawData)
    {
        if (string.IsNullOrWhiteSpace(rawData))
            return ScanValidationResult.Failure(rawData ?? "", ScanType.Unknown, "Dane skanu nie mogą być puste");

        var scanType = DetectScanType(rawData);
        
        switch (scanType)
        {
            case ScanType.GS1:
                var gs1Result = ParseGS1(rawData);
                return gs1Result.IsSuccess 
                    ? ScanValidationResult.Success(rawData, ScanType.GS1, gs1Result)
                    : ScanValidationResult.Failure(rawData, ScanType.GS1, gs1Result.ErrorMessage ?? "Błąd parsowania GS1");
                    
            case ScanType.SSCC:
                return ValidateSSCC(rawData) 
                    ? ScanValidationResult.Success(rawData, ScanType.SSCC)
                    : ScanValidationResult.Failure(rawData, ScanType.SSCC, "Nieprawidłowy kod SSCC");
                    
            case ScanType.DS:
                return ValidateDS(rawData) 
                    ? ScanValidationResult.Success(rawData, ScanType.DS)
                    : ScanValidationResult.Failure(rawData, ScanType.DS, "Nieprawidłowy kod DS");
                    
            case ScanType.LK:
                return ValidateLK(rawData) 
                    ? ScanValidationResult.Success(rawData, ScanType.LK)
                    : ScanValidationResult.Failure(rawData, ScanType.LK, "Nieprawidłowy kod LK");
                    
            case ScanType.Location:
                return ValidateLocationCode(rawData) 
                    ? ScanValidationResult.Success(rawData, ScanType.Location)
                    : ScanValidationResult.Failure(rawData, ScanType.Location, "Nieprawidłowy kod lokalizacji");
                    
            default:
                return ScanValidationResult.Failure(rawData, ScanType.Unknown, "Nierozpoznany typ kodu");
        }
    }
    
    /// <summary>
    /// Parsuje kod GS1-128
    /// </summary>
    public GS1ParseResult ParseGS1(string rawData)
    {
        return _gs1Parser.Parse(rawData);
    }
    
    /// <summary>
    /// Wykrywa typ zeskanowanego kodu
    /// </summary>
    public ScanType DetectScanType(string rawData)
    {
        if (string.IsNullOrWhiteSpace(rawData))
            return ScanType.Unknown;
            
        var trimmedData = rawData.Trim();
        
        // Sprawdzenie GS1 (z lub bez prefiksu IZ)
        if (GS1Pattern.IsMatch(trimmedData))
            return ScanType.GS1;
            
        // Sprawdzenie konkretnych typów kodów
        if (LKPattern.IsMatch(trimmedData))
            return ScanType.LK;
            
        if (DSPattern.IsMatch(trimmedData))
            return ScanType.DS;
            
        if (LocationPattern.IsMatch(trimmedData))
            return ScanType.Location;
            
        // SSCC bez GS1 wrapper
        if (SSCCPattern.IsMatch(trimmedData))
            return ScanType.SSCC;
            
        return ScanType.Unknown;
    }
}
