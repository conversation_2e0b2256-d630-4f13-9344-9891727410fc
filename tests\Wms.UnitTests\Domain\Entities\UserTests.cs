using FluentAssertions;
using Wms.Domain.Entities;

namespace Wms.UnitTests.Domain.Entities;

public class UserTests
{
    [Fact]
    public void User_Creation_WithValidData_ShouldCreateUserSuccessfully()
    {
        // Arrange
        var login = "<PERSON><PERSON><PERSON><PERSON>";
        var imie<PERSON>az<PERSON><PERSON> = "<PERSON>";
        var haslo = "password123";
        var stanowisko = "Operator";
        var numerKarty = "1234567890123456";
        var pin = 1234;
        var isActive = true;

        // Act
        var user = new User
        {
            Login = login,
            ImieNazwisko = imieNazwisko,
            Haslo = haslo,
            Stanowisko = stanowisko,
            NumerKarty = numerKarty,
            Pin = pin,
            IsActive = isActive,
            CreatedAt = DateTime.UtcNow
        };

        // Assert
        user.Should().NotBeNull();
        user.Login.Should().Be(login);
        user.ImieNazwisko.Should().Be(imieNazwisko);
        user.Haslo.Should().Be(haslo);
        user.Stanowisko.Should().Be(stanowisko);
        user.NumerKarty.Should().Be(numerKarty);
        user.Pin.Should().Be(pin);
        user.IsActive.Should().Be(isActive);
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void User_Creation_WithInvalidLogin_ShouldHaveInvalidState(string invalidLogin)
    {
        // Arrange & Act
        var user = new User
        {
            Login = invalidLogin,
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Assert
        user.Login.Should().Be(invalidLogin);
        // Note: In a real domain model, we would have validation that prevents invalid states
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void User_Creation_WithInvalidImieNazwisko_ShouldHaveInvalidState(string invalidImieNazwisko)
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = invalidImieNazwisko,
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Assert
        user.ImieNazwisko.Should().Be(invalidImieNazwisko);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void User_Creation_WithInvalidHaslo_ShouldHaveInvalidState(string invalidHaslo)
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = invalidHaslo,
            Stanowisko = "Operator",
            IsActive = true
        };

        // Assert
        user.Haslo.Should().Be(invalidHaslo);
    }

    [Fact]
    public void User_IsActive_WhenSetToFalse_ShouldBeInactive()
    {
        // Arrange
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Act
        user.IsActive = false;

        // Assert
        user.IsActive.Should().BeFalse();
    }

    [Theory]
    [InlineData("Operator")]
    [InlineData("Supervisor")]
    [InlineData("Admin")]
    public void User_Stanowisko_WhenSetToValidStanowisko_ShouldHaveCorrectStanowisko(string stanowisko)
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = stanowisko,
            IsActive = true
        };

        // Assert
        user.Stanowisko.Should().Be(stanowisko);
    }

    [Theory]
    [InlineData(1234)]
    [InlineData(5678)]
    [InlineData(9999)]
    public void User_Pin_WhenSetToValidPin_ShouldHaveCorrectPin(int pin)
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            Pin = pin,
            IsActive = true
        };

        // Assert
        user.Pin.Should().Be(pin);
    }

    [Fact]
    public void User_NumerKarty_WhenSet_ShouldBeAccessible()
    {
        // Arrange
        var numerKarty = "1234567890123456";
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            NumerKarty = numerKarty,
            IsActive = true
        };

        // Assert
        user.NumerKarty.Should().Be(numerKarty);
    }

    [Fact]
    public void User_OptionalFields_CanBeNull()
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            TelefonKom = null,
            Email = null,
            Telefon = null,
            NumerKarty = null,
            NumerRfid = null,
            IsActive = true
        };

        // Assert
        user.TelefonKom.Should().BeNull();
        user.Email.Should().BeNull();
        user.Telefon.Should().BeNull();
        user.NumerKarty.Should().BeNull();
        user.NumerRfid.Should().BeNull();
    }

    [Fact]
    public void User_OptionalFields_CanBeSet()
    {
        // Arrange
        var telefonKom = "123456789";
        var email = "<EMAIL>";
        var telefon = "987654321";
        var numerRfid = "RFID123456";

        // Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            TelefonKom = telefonKom,
            Email = email,
            Telefon = telefon,
            NumerRfid = numerRfid,
            IsActive = true
        };

        // Assert
        user.TelefonKom.Should().Be(telefonKom);
        user.Email.Should().Be(email);
        user.Telefon.Should().Be(telefon);
        user.NumerRfid.Should().Be(numerRfid);
    }

    [Fact]
    public void User_JednostkaId_HasDefaultValue()
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Assert
        user.JednostkaId.Should().Be(2); // Default value
    }

    [Fact]
    public void User_UpdatedAt_WhenModified_ShouldReflectChange()
    {
        // Arrange
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true,
            CreatedAt = DateTime.UtcNow.AddDays(-1)
        };
        var updateTime = DateTime.UtcNow;

        // Act
        user.UpdatedAt = updateTime;

        // Assert
        user.UpdatedAt.Should().Be(updateTime);
        user.UpdatedAt.Should().BeAfter(user.CreatedAt);
    }

    [Fact]
    public void User_Equality_WithSameId_ShouldBeEqual()
    {
        // Arrange
        var userId = 1;
        var user1 = new User
        {
            Id = userId,
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        var user2 = new User
        {
            Id = userId,
            Login = "anowak",
            ImieNazwisko = "Anna Nowak",
            Haslo = "password456",
            Stanowisko = "Admin",
            IsActive = false
        };

        // Act & Assert
        user1.Id.Should().Be(user2.Id);
    }

    [Fact]
    public void User_Equality_WithDifferentId_ShouldNotBeEqual()
    {
        // Arrange
        var user1 = new User
        {
            Id = 1,
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        var user2 = new User
        {
            Id = 2,
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Act & Assert
        user1.Id.Should().NotBe(user2.Id);
    }

    [Fact]
    public void User_NavigationProperties_ShouldBeInitialized()
    {
        // Arrange & Act
        var user = new User
        {
            Login = "jkowalski",
            ImieNazwisko = "Jan Kowalski",
            Haslo = "password123",
            Stanowisko = "Operator",
            IsActive = true
        };

        // Assert
        user.Sessions.Should().NotBeNull();
        user.Sessions.Should().BeEmpty();
        user.Movements.Should().NotBeNull();
        user.Movements.Should().BeEmpty();
    }
}
