# Uruchamianie Aplikacji WMS na Emulatorach

## 🎯 Problem z Emulatorami - Rozwiązanie

Aplikacja WMS była pierwotnie zaprojektowana do uruchamiania tylko na urządzeniach Zebra z DataWedge. Dodaliśmy teraz pełną obsługę emulatorów Android z funkcją Mock Scanner.

## ✅ **Co zostało naprawione:**

### 1. **Obniżone wymagania API Level**
- Zmieniono z `android-29.0` na `android-26.0`
- Lepsza kompatybilność z emulatorami

### 2. **Graceful Degradation**
- Aplikacja wykrywa automatycznie typ urządzenia
- Nie crashuje na emulatorach bez DataWedge
- Przełącza się na Mock Scanner mode

### 3. **Mock Scanner dla Emulatorów**
- Pełnofunkcyjny symulator skanowania 
- Generacja kodów testowych (GS1, SSCC, DS, LK)
- Historia skanów i debugowanie

## 🚀 **Instrukcje Uruchamiania**

> Wskazówka: jeśli potrzebujesz uruchomić backend lokalnie i testować aplikację przez proxy (8080 → 8081), skorzystaj z przewodnika: [LOCAL_TESTING.md](../../../docs/LOCAL_TESTING.md)

### Na Emulatorze Android Studio

1. **Utwórz Emulator:**
   ```bash
   # W Android Studio AVD Manager
   - API Level: 26 lub wyższy
   - Google APIs (zalecane)
   - RAM: 4GB+
   ```

2. **Uruchom aplikację:**
   ```bash
   dotnet build -f net9.0-android
   dotnet run -f net9.0-android
   ```

3. **Dostęp do Mock Scanner:**
   - Uruchom aplikację
   - Przejdź do "🖥️ Mock Scanner" w menu
   - Testuj funkcje skanowania

### Na Urządzeniu Zebra

```bash
# Normalny deployment na urządzenie
dotnet build -f net9.0-android -c Release
# Deploy through Visual Studio lub ADB
```

## 🛠️ **Funkcje Mock Scanner**

### Typy kodów do testowania:
- **GS1 Code**: Symuluje kody towarów z GTIN, LOT, EXP
- **SSCC Code**: 20-cyfrowe kody kontenerów/palet
- **DS Code**: Kody dokumentów spedycyjnych
- **LK Code**: Lista księgowa
- **Printer IP**: IP drukarek etykiet

### Przykładowe kody testowe:
```
GS1: 0112345678901234101LOT45617251201
SSCC: 00123456789012345678
DS: DS20241201001
LK: LK123
IP: *************
```

## 🔍 **Detekcja Urządzenia**

Aplikacja automatycznie wykrywa:

### Emulator Android:
- ✅ Mock Scanner dostępny
- ✅ Generacja kodów testowych
- ✅ Pełna funkcjonalność modułu dostaw
- ❌ Prawdziwe skanowanie fizyczne

### Urządzenie Zebra z DataWedge:
- ✅ Prawdziwe skanowanie
- ✅ Integracja DataWedge
- ✅ Pełna funkcjonalność przemysłowa
- ❌ Mock Scanner ukryty

### Urządzenie Android standardowe:
- 🟡 Mock Scanner dostępny
- 🟡 Ograniczona funkcjonalność skanowania
- ✅ Podstawowe funkcje WMS

## 🐛 **Debugowanie**

### Logi Device Detection:
```csharp
// W Output oknie Visual Studio lub logcat
[DeviceDetection] Device info: google generic sdk_gphone64_x86_64
[DeviceDetection] Is Emulator: true
[DeviceDetection] Is Zebra: false
[DeviceDetection] DataWedge available: false
```

### Mock Scanner Logs:
```csharp
[MockScanner] Simulating GS1 scan: 0112345678901234...
[MockScanner] Generated testcode: DS20241201001
```

## 📋 **Testowanie Przepływów**

### 1. Test Wyboru Dostawy:
```
1. Uruchom aplikację na emulatorze
2. Przejdź do "Przyjęcia" -> "Wybór dostawy"
3. Kliknij "Dodaj LK" (lub użyj Mock Scanner)
4. Wygeneruj/wpisz LK123
5. Sprawdź listę dostaw
```

### 2. Test Rejestracji Pozycji:
```
1. Wybierz dostawę i przejdź do rejestracji
2. Kliknij przycisk "Mock Scanner" 
3. Wygeneruj GS1 code
4. Sprawdź automatyczne wypełnienie pól
5. Zarejestruj pozycję
```

### 3. Test Generowania DS:
```
1. W widoku dostaw kliknij "Generuj DS"
2. Wybierz typ palety i ilość
3. Sprawdź wygenerowane kody
4. (Opcjonalnie) test drukowania
```

## ⚡ **Performance Tips**

### Optymalne ustawienia emulatora:
- **RAM**: 4-8 GB
- **VM Heap**: 512 MB
- **Hardware acceleration**: Włączone
- **GPU**: Hardware - GLES 2.0

### Build optimizations:
```xml
<!-- W WmsApp.csproj dla debug -->
<PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <AndroidLinkMode>None</AndroidLinkMode>
    <AndroidUseSharedRuntime>true</AndroidUseSharedRuntime>
</PropertyGroup>
```

## 🔧 **Konfiguracja dla Zespołu**

### Visual Studio Code:
```json
// .vscode/launch.json
{
    "name": ".NET MAUI Android (Emulator)",
    "type": "dotnet-maui",
    "request": "launch",
    "projectPath": "${workspaceFolder}/WmsApp.csproj",
    "targetFramework": "net9.0-android"
}
```

### Visual Studio 2022:
1. Set Startup Project: WmsApp
2. Platform: Android
3. Device: Emulator (API 26+)
4. Configuration: Debug

---

## 📞 **Support i Troubleshooting**

### Częste problemy:

**Problem**: Aplikacja nie uruchamia się na emulatorze
**Rozwiązanie**: 
```bash
# Sprawdź API level
adb shell getprop ro.build.version.sdk
# Powinien być >= 26

# Wyczyść cache
dotnet clean
dotnet restore
```

**Problem**: Mock Scanner nie działa
**Rozwiązanie**:
- Sprawdź czy jesteś na widoku "🖥️ Mock Scanner"
- Sprawdź logi device detection
- Restartuj aplikację

**Problem**: Brak menu "Mock Scanner" 
**Rozwiazanie**:
- Sprawdź czy jesteś na emulatorze/standardowym Android
- Menu jest ukryte na urządzeniach Zebra

---

**Data ostatniej aktualizacji:** ${new Date().toLocaleDateString('pl-PL')}  
**Wersja:** 1.0 - Emulator Support Added
