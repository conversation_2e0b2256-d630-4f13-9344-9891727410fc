using Refit;
using WmsApp.Models.Auth;
using WmsApp.Models.Pallets;

namespace WmsApp.Services;

/// <summary>
/// Mock implementacja WmsApiService do testowania bez rzeczywistego backendu
/// </summary>
public class MockWmsApiService : IWmsApiService
{
    private readonly List<LocationInfo> _mockLocations;
    private readonly List<MockPalletInfo> _mockPallets;

    public MockWmsApiService()
    {
        _mockLocations = new List<LocationInfo>
        {
            new LocationInfo { Id = 1, Code = "MP-1-145-002-1", Hala = 1, Regal = "145", Miejsce = 2, Poziom = "1", IsVisible = true, IsPickingLocation = true, MaxCapacity = 5, CurrentPalletCount = 2 },
            new LocationInfo { Id = 2, Code = "MP-1-146-003-2", Hala = 1, Regal = "146", Miejsce = 3, Poziom = "2", IsVisible = true, IsPickingLocation = false, MaxCapacity = 10, CurrentPalletCount = 0 },
            new LocationInfo { Id = 3, Code = "MP-2-200-001-1", Hala = 2, Regal = "200", Miej<PERSON><PERSON> = 1, Poziom = "1", IsVisible = true, IsPickingLocation = true, MaxCapacity = 8, CurrentPalletCount = 3 }
        };

        _mockPallets = new List<MockPalletInfo>
        {
            new MockPalletInfo
            {
                Id = 1,
                MainSSCC = "123456789012345678",
                CurrentLocation = _mockLocations[0], // MP-1-145-002-1
                LastMovementAt = DateTime.Now.AddHours(-2),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo { Id = 1, SSCC = "123456789012345678", ClientLabel = "TEST-001", Quantity = 100, ExpiryDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)), Batch = "BATCH001", IsActive = true }
                }
            },
            new MockPalletInfo
            {
                Id = 2,
                MainSSCC = "DS12345678",
                CurrentLocation = _mockLocations[1], // MP-1-146-003-2
                LastMovementAt = DateTime.Now.AddHours(-1),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo { Id = 2, SSCC = "DS12345678", ClientLabel = "TEST-002", Quantity = 50, ExpiryDate = DateOnly.FromDateTime(DateTime.Today.AddDays(15)), Batch = "BATCH002", IsActive = true }
                }
            },
            new MockPalletInfo
            {
                Id = 3,
                MainSSCC = "DS3345469",
                CurrentLocation = _mockLocations[2], // MP-2-200-001-1
                LastMovementAt = DateTime.Now.AddHours(-3),
                Labels = new List<LabelInfo>
                {
                    new LabelInfo { Id = 3, SSCC = "DS3345469", ClientLabel = "TEST-003", Quantity = 75, ExpiryDate = DateOnly.FromDateTime(DateTime.Today.AddDays(45)), Batch = "BATCH003", IsActive = true }
                }
            }
        };
    }

    #region IWmsApiService Implementation

    public async Task<IApiResponse<LoginScanResponse>> LoginScanAsync(LoginScanRequest request)
    {
        // Symuluj opóźnienie sieci
        await Task.Delay(500);

        // Mock logowanie - przyjmij dowolną kartę zaczynającą się od "00000"
        if (request.CardNumber.StartsWith("00000"))
        {
            var mockResponse = new LoginScanResponse
            {
                Token = "mock-jwt-token-12345",
                ExpiresAt = DateTime.UtcNow.AddHours(1),
                User = new UserInfo
                {
                    Id = 1,
                    FullName = "Testowy Użytkownik",
                    Position = "Operator",
                    IsActive = true
                }
            };

            return new MockApiResponse<LoginScanResponse>(mockResponse, true, System.Net.HttpStatusCode.OK);
        }

        return new MockApiResponse<LoginScanResponse>(null, false, System.Net.HttpStatusCode.Unauthorized);
    }

    public async Task<IApiResponse<object>> LogoutAsync()
    {
        await Task.Delay(200);
        return new MockApiResponse<object>(new { }, true, System.Net.HttpStatusCode.OK);
    }

    public async Task<IApiResponse<MovePalletResponse>> MovePalletAsync(string palletCode, MovePalletRequest request)
    {
        await Task.Delay(1000); // Symuluj dłuższą operację

        var pallet = _mockPallets.FirstOrDefault(p => 
            p.MainSSCC == palletCode || 
            p.MainSSCC == request.PalletCode);

        if (pallet == null)
        {
            return new MockApiResponse<MovePalletResponse>(null, false, System.Net.HttpStatusCode.NotFound);
        }

        var targetLocation = _mockLocations.FirstOrDefault(l => l.Code == request.ToLocationCode);
        if (targetLocation == null)
        {
            return new MockApiResponse<MovePalletResponse>(null, false, System.Net.HttpStatusCode.BadRequest);
        }

        // Sprawdź czy paleta nie jest już w tej lokalizacji
        if (pallet.CurrentLocation?.Code == request.ToLocationCode)
        {
            return new MockApiResponse<MovePalletResponse>(null, false, System.Net.HttpStatusCode.Conflict);
        }

        var fromLocation = pallet.CurrentLocation?.Code ?? "UNKNOWN";
        
        // Aktualizuj lokalizację palety w mock danych
        pallet.CurrentLocation = targetLocation;
        pallet.LastMovementAt = DateTime.Now;

        var response = new MovePalletResponse
        {
            PalletCode = palletCode,
            FromLocationCode = fromLocation,
            ToLocationCode = request.ToLocationCode,
            MovementTime = DateTime.Now,
            MovedBy = "Testowy Użytkownik",
            MovementId = Random.Shared.Next(1000, 9999)
        };

        return new MockApiResponse<MovePalletResponse>(response, true, System.Net.HttpStatusCode.OK);
    }

    public async Task<IApiResponse<PalletInfo>> GetPalletAsync(string palletCode)
    {
        await Task.Delay(300);

        var pallet = _mockPallets.FirstOrDefault(p => p.MainSSCC == palletCode);
        
        if (pallet == null)
        {
            return new MockApiResponse<PalletInfo>(null, false, System.Net.HttpStatusCode.NotFound);
        }

        return new MockApiResponse<PalletInfo>(pallet.ToPalletInfo(), true, System.Net.HttpStatusCode.OK);
    }

    public async Task<IApiResponse<LocationInfo>> GetLocationAsync(string locationCode)
    {
        await Task.Delay(200);

        var location = _mockLocations.FirstOrDefault(l => l.Code == locationCode);
        
        if (location == null)
        {
            return new MockApiResponse<LocationInfo>(null, false, System.Net.HttpStatusCode.NotFound);
        }

        return new MockApiResponse<LocationInfo>(location, true, System.Net.HttpStatusCode.OK);
    }

    #endregion
}

/// <summary>
/// Mock implementacja IApiResponse do testowania
/// </summary>
public class MockApiResponse<T> : IApiResponse<T>
{
    private readonly HttpResponseMessage _httpResponse;

    public MockApiResponse(T? content, bool isSuccess, System.Net.HttpStatusCode statusCode)
    {
        Content = content;
        IsSuccessStatusCode = isSuccess;
        StatusCode = statusCode;
        ReasonPhrase = statusCode.ToString();
        Error = null;
        
        _httpResponse = new HttpResponseMessage(statusCode)
        {
            ReasonPhrase = ReasonPhrase,
            Version = new Version(1, 1)
        };
        
        HttpResponseMessage = _httpResponse;
    }

    public T? Content { get; }
    public HttpResponseMessage? HttpResponseMessage { get; }
    public bool IsSuccessStatusCode { get; }
    public string? ReasonPhrase { get; }
    public HttpRequestMessage? RequestMessage { get; } = null;
    public System.Net.HttpStatusCode StatusCode { get; }
    public Version Version => _httpResponse.Version;
    public System.Net.Http.Headers.HttpResponseHeaders Headers => _httpResponse.Headers;
    public System.Net.Http.Headers.HttpContentHeaders ContentHeaders => _httpResponse.Content?.Headers ?? new StringContent("").Headers;
    public ApiException? Error { get; }

    public void Dispose() 
    {
        _httpResponse?.Dispose();
    }

    public Task<T?> GetContentAsync() => Task.FromResult(Content);
}

/// <summary>
/// Mock klasa dla PalletInfo z modyfikowalnymi właściwościami
/// </summary>
public class MockPalletInfo
{
    public int Id { get; set; }
    public string? MainSSCC { get; set; }
    public LocationInfo? CurrentLocation { get; set; }
    public DateTime? LastMovementAt { get; set; }
    public List<LabelInfo> Labels { get; set; } = new();

    public PalletInfo ToPalletInfo()
    {
        return new PalletInfo
        {
            Id = Id,
            MainSSCC = MainSSCC,
            CurrentLocation = CurrentLocation,
            LastMovementAt = LastMovementAt,
            Labels = Labels
        };
    }
}
