-- Skrypt dodający wpis dla numeracji etykiet w tabeli docnumber
-- Wykonuje się tylko jeśli wpis nie istnieje

-- ==========================================
-- 1. D<PERSON><PERSON><PERSON> WPISU DLA NUMERACJI ETYKIET
-- ==========================================

-- Insert tylko gdy nie istnieje
INSERT IGNORE INTO docnumber (name, last) 
VALUES ('nretykiety', 1);

-- ==========================================
-- 2. WERYFIKACJA POPRAWNOŚCI
-- ==========================================

-- Sprawdzenie czy wpisy dla numeracji istnieją
SELECT 
    'DOCNUMBER VERIFICATION' as step,
    name,
    last,
    CASE 
        WHEN name = 'nrpalety' THEN 'Numeracja palet'
        WHEN name = 'nretykiety' THEN 'Numeracja etykiet'
        ELSE 'Inne'
    END as description
FROM docnumber 
WHERE name IN ('nrpalety', 'nretykiety')
ORDER BY name;

-- Sprawdzenie czy oba wpisy istnieją
SELECT 
    CASE 
        WHEN COUNT(*) = 2 THEN 'SUCCESS: Oba wpisy numeracji istnieją'
        ELSE CONCAT('ERROR: Brakuje wpisów numeracji. Znaleziono: ', COUNT(*), ' z 2')
    END as result
FROM docnumber 
WHERE name IN ('nrpalety', 'nretykiety');
