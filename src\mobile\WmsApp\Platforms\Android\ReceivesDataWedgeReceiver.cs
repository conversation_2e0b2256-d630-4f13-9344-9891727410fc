using Android.Content;
using CommunityToolkit.Mvvm.Messaging;
using WmsApp.Services;
using WmsApp.Services.Receives;
using WmsApp.Messages;

namespace WmsApp.Platforms.Android;

/// <summary>
/// BroadcastReceiver dla skanów DataWedge w module dostaw
/// Obsługuje LK, GS1, SSCC, DS, IP drukarek
/// </summary>
[BroadcastReceiver(Enabled = true, Exported = false)]
public class ReceivesDataWedgeReceiver : BroadcastReceiver
{
    // DataWedge extras
    private const string DATA_STRING_TAG = "com.symbol.datawedge.data_string";
    private const string LABEL_TYPE_TAG = "com.symbol.datawedge.label_type";
    private const string SOURCE_TAG = "com.symbol.datawedge.source";

    public override void OnReceive(Context? context, Intent? intent)
    {
        if (context == null || intent == null)
        {
            System.Diagnostics.Debug.WriteLine("[ReceivesDataWedge] Null context or intent");
            return;
        }
        
        // Sprawdź czy jesteśmy na urządzeniu które obsługuje DataWedge
        var deviceDetection = new DeviceDetectionService();
        if (!deviceDetection.IsDataWedgeAvailable)
        {
            System.Diagnostics.Debug.WriteLine("[ReceivesDataWedge] DataWedge not available, ignoring scan");
            return;
        }

        try
        {
            var action = intent.Action;
            System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Received action: {action}");

            if (action != "com.wms.receives.SCAN")
            {
                System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Ignoring action: {action}");
                return;
            }

            // Pobierz dane z intent
            var scanData = intent.GetStringExtra(DATA_STRING_TAG);
            var labelType = intent.GetStringExtra(LABEL_TYPE_TAG);
            var source = intent.GetStringExtra(SOURCE_TAG);

            System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Scan Data: '{scanData}', Label Type: '{labelType}', Source: '{source}'");

            if (string.IsNullOrEmpty(scanData))
            {
                System.Diagnostics.Debug.WriteLine("[ReceivesDataWedge] Empty scan data");
                return;
            }

            // Waliduj i kategoryzuj kod
            var validationService = new ReceiveCodeValidationService();
            var scanResult = validationService.ProcessScan(scanData);

            System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Processed scan - Type: {scanResult.Type}, Valid: {scanResult.IsValid}");

            if (!scanResult.IsValid)
            {
                System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Invalid scan: {scanResult.ErrorMessage}");
                // Można wysłać komunikat o błędzie
WeakReferenceMessenger.Default.Send(new ScanErrorMessage(scanResult.ErrorMessage ?? "Nieprawidłowy kod"));
                return;
            }

            // Wyślij odpowiedni komunikat na podstawie typu kodu
            switch (scanResult.Type)
            {
                case Models.Receives.ReceiveScanCodeType.LK:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Sending LK scan: {scanData}");
WeakReferenceMessenger.Default.Send(new LkScannedMessage(scanData));
                    break;

                case Models.Receives.ReceiveScanCodeType.GS1:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Sending GS1 scan: {scanData.Substring(0, Math.Min(20, scanData.Length))}...");
WeakReferenceMessenger.Default.Send(new Gs1ScannedMessage(scanData));
                    break;

                case Models.Receives.ReceiveScanCodeType.SSCC:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Sending SSCC scan: {scanData}");
WeakReferenceMessenger.Default.Send(new SsccScannedMessage(scanData));
                    break;

                case Models.Receives.ReceiveScanCodeType.DS:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Sending DS scan: {scanData}");
WeakReferenceMessenger.Default.Send(new DsScannedMessage(scanData));
                    break;

                case Models.Receives.ReceiveScanCodeType.PrinterIP:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Sending PrinterIP scan: {scanData}");
WeakReferenceMessenger.Default.Send(new PrinterIpScannedMessage(scanData));
                    break;

                default:
                    System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Unknown scan type: {scanResult.Type}");
WeakReferenceMessenger.Default.Send(new ScanErrorMessage($"Nierozpoznany typ kodu: {scanResult.Type}"));
                    break;
            }

            // Wyślij też ogólny komunikat z pełnymi danymi skanu dla zaawansowanego przetwarzania
WeakReferenceMessenger.Default.Send(new ScanProcessedMessage(scanResult));
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Error processing scan: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"[ReceivesDataWedge] Stack trace: {ex.StackTrace}");
WeakReferenceMessenger.Default.Send(new ScanErrorMessage($"Błąd przetwarzania skanu: {ex.Message}"));
        }
    }
}

/// <summary>
/// Helper class dla konfiguracji DataWedge profile dla modułu dostaw
/// </summary>
public static class ReceivesDataWedgeConfig
{
    // Nazwa profilu DataWedge dla modułu dostaw
    public const string PROFILE_NAME = "WMS_Receives_Profile";
    
    // Package aplikacji
    public const string PACKAGE_NAME = "com.example.wms";
    
    // Intent action dla skanów dostaw
    public const string SCAN_ACTION = "com.wms.receives.SCAN";

    /// <summary>
    /// Przykładowa konfiguracja profilu DataWedge dla modułu dostaw
    /// Do użycia przez StageNow lub OEMConfig
    /// </summary>
    public static string GetDataWedgeProfileXml()
    {
        return $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<ProfileConfig>
    <PROFILE_NAME>{PROFILE_NAME}</PROFILE_NAME>
    <PROFILE_ENABLED>true</PROFILE_ENABLED>
    <APPLICATION>
        <PACKAGE_NAME>{PACKAGE_NAME}</PACKAGE_NAME>
        <ACTIVITY_NAME>*</ACTIVITY_NAME>
    </APPLICATION>
    <PLUGIN_CONFIG>
        <PLUGIN_NAME>BARCODE</PLUGIN_NAME>
        <ENABLED>true</ENABLED>
        <CONFIG>
            <TRIGGER>
                <TRIGGER_MODE>hard</TRIGGER_MODE>
            </TRIGGER>
            <DECODER_PARAMS>
                <code128>true</code128>
                <ean13>true</ean13>
                <ean8>true</ean8>
                <upca>true</upca>
                <upce>true</upce>
                <gs1_128>true</gs1_128>
                <datamatrix>true</datamatrix>
                <qrcode>true</qrcode>
            </DECODER_PARAMS>
        </CONFIG>
    </PLUGIN_CONFIG>
    <PLUGIN_CONFIG>
        <PLUGIN_NAME>KEYSTROKE</PLUGIN_NAME>
        <ENABLED>false</ENABLED>
    </PLUGIN_CONFIG>
    <PLUGIN_CONFIG>
        <PLUGIN_NAME>INTENT</PLUGIN_NAME>
        <ENABLED>true</ENABLED>
        <CONFIG>
            <ACTION>{SCAN_ACTION}</ACTION>
            <DELIVERY>2</DELIVERY>
            <CATEGORY>android.intent.category.DEFAULT</CATEGORY>
        </CONFIG>
    </PLUGIN_CONFIG>
</ProfileConfig>";
    }

    /// <summary>
    /// Instrukcje wdrożenia DataWedge dla administratorów
    /// </summary>
    public static string GetDeploymentInstructions()
    {
        return $@"
=== INSTRUKCJE WDROŻENIA DATAWEDGE DLA MODUŁU DOSTAW ===

1. ZEBRA STAGENOW / OEMCONFIG:
   - Utwórz nowy profil DataWedge o nazwie: {PROFILE_NAME}
   - Przypisz do aplikacji: {PACKAGE_NAME}
   - Ustaw Intent Action: {SCAN_ACTION}
   - Włącz dekodery: GS1-128, Code128, EAN13, EAN8, DataMatrix, QR Code

2. RĘCZNA KONFIGURACJA NA URZĄDZENIU:
   - Otwórz aplikację DataWedge
   - Dodaj nowy profil: {PROFILE_NAME}
   - W sekcji 'Applications' dodaj {PACKAGE_NAME}
   - W sekcji 'Barcode Input' włącz potrzebne dekodery
   - W sekcji 'Intent Output':
     * Włącz Intent Output
     * Intent Action: {SCAN_ACTION}
     * Intent Category: android.intent.category.DEFAULT
     * Intent delivery: Broadcast Intent

3. TESTOWANIE:
   - Uruchom aplikację WMS
   - Przejdź do modułu Dostaw
   - Zeskanuj kod - powinien być automatycznie przetworzony

4. ROZWIĄZYWANIE PROBLEMÓW:
   - Sprawdź logi aplikacji: filtr '[ReceivesDataWedge]'
   - Upewnij się, że profil jest aktywny w DataWedge
   - Sprawdź czy Intent Action się zgadza
   - Dla testów użyj kodu testowego: LK123, **********, IP172.7.1.44

UWAGA: Każdy typ kodu (LK, GS1, SSCC, DS, IP) jest automatycznie rozpoznawany
       i przesyłany do odpowiedniej części aplikacji.
";
    }
}
