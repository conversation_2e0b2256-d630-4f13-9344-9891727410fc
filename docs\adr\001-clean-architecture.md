# ADR-001: Adoption of Clean Architecture

## Status
Accepted

## Date
2025-09-01

## Context
The WMS system needs to be maintainable, testable, and scalable. The backend needs to support multiple interfaces (mobile app, potential web UI, API integrations) while maintaining business logic integrity.

## Decision
We adopt Clean Architecture pattern for the backend, organized into distinct layers:

- **Wms.Domain** - Enterprise business rules and entities
- **Wms.Application** - Application business rules, use cases, and interfaces
- **Wms.Infrastructure** - External concerns (database, external services)
- **Wms.Api** - Presentation layer (REST API controllers)

## Rationale

### Benefits:
1. **Dependency Inversion** - Business logic doesn't depend on frameworks or external systems
2. **Testability** - Core business logic can be tested in isolation
3. **Maintainability** - Clear separation of concerns and responsibilities
4. **Framework Independence** - Business logic is not tied to ASP.NET Core or Entity Framework
5. **Database Independence** - Can switch ORMs or databases without affecting business logic

### Implementation Details:
- Dependencies flow inward (API → Application → Domain)
- Infrastructure implements Application interfaces
- No direct EF Core dependencies in Application layer
- Repository pattern with Unit of Work for data access

## Consequences

### Positive:
- High testability and maintainability
- Clear boundaries between layers
- Easy to add new features without affecting existing code
- Better support for multiple presentation layers

### Negative:
- More complex project structure for simple operations
- Additional abstraction layers
- Steeper learning curve for new developers

## Implementation Status
- ✅ Project structure created
- ✅ Dependency injection configured
- ✅ Repository pattern implemented
- ✅ Unit of Work pattern implemented
- ✅ Application services follow Clean Architecture principles
