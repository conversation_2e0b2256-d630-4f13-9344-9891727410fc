# ADR-004: JWT Authentication Strategy

## Status
Accepted

## Date
2025-09-01

## Context
The WMS mobile application requires secure authentication for warehouse workers using RFID/barcode card scanners. The system needs to:
- Support card-based login without passwords
- Maintain session state for security and audit purposes
- Work efficiently with mobile devices (offline tolerance)
- Provide secure API access with reasonable token lifetimes

## Decision
Implement JWT (JSON Web Token) authentication with card-based login and session tracking:

### Authentication Flow:
1. **Login**: POST `/api/v1/auth/login-scan` with card number
2. **Token Generation**: 60-minute JWT token with user claims
3. **Session Tracking**: Database session record for security/audit
4. **Authorization**: Bearer token required for all protected endpoints
5. **Logout**: Explicit logout invalidates database session

### JWT Configuration:
```json
{
  "JwtSettings": {
    "Issuer": "WMS-API",
    "Audience": "WMS-Mobile", 
    "SecretKey": "your-256-bit-secret-key-here-minimum-32-characters",
    "ExpiryMinutes": 60
  }
}
```

## Rationale

### Why JWT over Session Cookies:
1. **Mobile-First**: JWT works better with mobile HTTP clients
2. **Stateless API**: Reduces server memory usage and complexity
3. **Cross-Domain**: Easier to handle different client applications
4. **Offline Tolerance**: Mobile app can validate token structure offline

### Why Card-Based Login:
1. **Warehouse Environment**: RFID/barcode scanners are standard equipment
2. **Speed**: Faster than typing credentials with industrial devices
3. **Security**: Physical card possession required
4. **Worker Friendly**: No password management for warehouse staff

### Session Tracking Benefits:
1. **Security**: Ability to revoke sessions immediately
2. **Audit Trail**: Complete login/logout history
3. **Device Tracking**: Monitor which devices are used
4. **Analytics**: Session duration and usage patterns

## Implementation Details

### Database Schema:
```sql
-- New sessions table
CREATE TABLE sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_id VARCHAR(100),
    ip_address VARCHAR(45),
    login_time DATETIME NOT NULL,
    logout_time DATETIME NULL,
    is_active TINYINT(1) DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES pracownicy(id)
);

-- Enhanced user table
ALTER TABLE pracownicy ADD COLUMN is_active TINYINT(1) DEFAULT 1;
```

### JWT Claims:
```json
{
  "sub": "123",           // User ID
  "userId": "123",        // Custom claim for easy access
  "name": "Jan Kowalski", // User full name
  "iat": 1640995200,      // Issued at
  "exp": 1640998800,      // Expires at
  "iss": "WMS-API",       // Issuer
  "aud": "WMS-Mobile"     // Audience
}
```

### Security Features:
- **Token Expiry**: 60-minute lifetime balances security vs usability
- **Secure Headers**: JWT includes issuer, audience validation
- **Session Validation**: Database session must be active for token to be valid
- **IP Tracking**: Login IP address logged for security audit
- **Device Tracking**: Optional device ID for multi-device management

## API Endpoints

### Authentication:
```http
POST /api/v1/auth/login-scan
Content-Type: application/json

{
  "cardNumber": "ABC123456", 
  "deviceId": "mobile-scanner-01" 
}

Response 200:
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expiresAt": "2025-09-01T17:00:00Z",
  "user": {
    "id": 123,
    "fullName": "Jan Kowalski",
    "position": "Warehouse Worker",
    "department": "Logistics"
  }
}
```

### Protected Endpoints:
```http
GET /api/v1/pallets/123456789012345678
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Error Handling

### Authentication Errors:
- **401 Unauthorized**: Invalid card number or inactive user
- **401 Unauthorized**: Expired or invalid JWT token  
- **401 Unauthorized**: Valid token but inactive database session

### Validation Errors:
- **400 Bad Request**: Invalid card number format
- **400 Bad Request**: Missing or invalid device ID

## Security Considerations

### Strengths:
- **Card Possession**: Physical security factor
- **Token Expiry**: Limited blast radius if compromised
- **Session Revocation**: Immediate logout capability
- **Audit Trail**: Complete authentication history
- **HTTPS Only**: Tokens transmitted securely

### Limitations:
- **Card Loss**: Lost card provides access until reported
- **No 2FA**: Single authentication factor (mitigated by physical card requirement)
- **Token Replay**: Valid token can be reused (mitigated by short expiry)

## Consequences

### Positive:
- Fast, warehouse-worker-friendly authentication
- Secure API access with proper authorization
- Complete audit trail of all access
- Mobile-optimized authentication flow
- Easy to implement additional security layers

### Negative:
- Dependency on card management processes
- No built-in password fallback
- JWT token size overhead in requests
- Additional complexity of session management

## Implementation Status
- ✅ JWT configuration and middleware setup
- ✅ Card-based authentication service
- ✅ Session database tracking
- ✅ API endpoints implemented
- ✅ Error handling and validation
- ✅ Security headers and HTTPS enforcement
