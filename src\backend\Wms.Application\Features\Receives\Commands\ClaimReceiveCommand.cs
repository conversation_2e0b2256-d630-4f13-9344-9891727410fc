using MediatR;
using Wms.Application.DTOs.Receives;

namespace Wms.Application.Features.Receives.Commands;

public record ClaimReceiveCommand : IRequest<ClaimReceiveResponse>
{
    public int ReceiveId { get; init; }
    public int PracownikId { get; init; }
}

public record ReleaseReceiveCommand : IRequest<ClaimReceiveResponse>
{
    public int ReceiveId { get; init; }
    public int PracownikId { get; init; }
}
