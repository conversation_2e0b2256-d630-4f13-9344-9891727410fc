using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;
using Wms.Infrastructure.Repositories;

namespace Wms.UnitTests.Infrastructure.Repositories;

public class DocNumberRepositoryTests : IDisposable
{
    private readonly WmsDbContext _context;
    private readonly DocNumberRepository _repository;

    public DocNumberRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<WmsDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new WmsDbContext(options);
        _repository = new DocNumberRepository(_context);
    }

    [Fact]
    public async Task GetByNameAsync_WithExistingName_ShouldReturnDocNumber()
    {
        // Arrange
        var docNumber = new DocNumber
        {
            Id = 1,
            Name = "nrpalety",
            Last = 650000
        };
        _context.DocNumbers.Add(docNumber);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByNameAsync("nrpalety");

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be("nrpalety");
        result.Last.Should().Be(650000);
    }

    [Fact]
    public async Task GetByNameAsync_WithNonExistingName_ShouldReturnNull()
    {
        // Act
        var result = await _repository.GetByNameAsync("nonexistent");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetNextNumberAsync_ShouldIncrementAndReturnNextNumber()
    {
        // Arrange
        var docNumber = new DocNumber
        {
            Id = 1,
            Name = "nrpalety",
            Last = 650000
        };
        _context.DocNumbers.Add(docNumber);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetNextNumberAsync("nrpalety");

        // Assert
        result.Should().Be(650001);

        // Verify the value was incremented in database
        // Note: W rzeczywistości SaveChangesAsync jest wywoływane przez UnitOfWork
        // ale w teście jednostkowym już zostało wywołane w metodzie
        var updated = await _context.DocNumbers.FindAsync(1);
        updated!.Last.Should().Be(650001);
    }

    [Fact]
    public async Task GetNextNumberAsync_WithNonExistingName_ShouldThrowException()
    {
        // Act & Assert
        var act = async () => await _repository.GetNextNumberAsync("nonexistent");
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("DocNumber entry for 'nonexistent' not found");
    }

    [Fact]
    public async Task CreateIfNotExistsAsync_WithNewName_ShouldCreateNewEntry()
    {
        // Act
        var result = await _repository.CreateIfNotExistsAsync("nretykiety", 1);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be("nretykiety");
        result.Last.Should().Be(1);

        // Verify it was saved to database
        var saved = await _context.DocNumbers.FirstOrDefaultAsync(d => d.Name == "nretykiety");
        saved.Should().NotBeNull();
        saved!.Last.Should().Be(1);
    }

    [Fact]
    public async Task CreateIfNotExistsAsync_WithExistingName_ShouldReturnExisting()
    {
        // Arrange
        var existing = new DocNumber
        {
            Id = 1,
            Name = "nrpalety",
            Last = 650000
        };
        _context.DocNumbers.Add(existing);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.CreateIfNotExistsAsync("nrpalety", 999999);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be("nrpalety");
        result.Last.Should().Be(650000); // Should return existing value, not new one
        result.Id.Should().Be(1);
    }

    [Fact]
    public async Task UpdateLastNumberAsync_ShouldUpdateValue()
    {
        // Arrange
        var docNumber = new DocNumber
        {
            Id = 1,
            Name = "nrpalety",
            Last = 650000
        };
        _context.DocNumbers.Add(docNumber);
        await _context.SaveChangesAsync();

        // Act
        await _repository.UpdateLastNumberAsync("nrpalety", 700000);

        // Assert
        var updated = await _context.DocNumbers.FindAsync(1);
        updated!.Last.Should().Be(700000);
    }

    [Fact]
    public async Task UpdateLastNumberAsync_WithNonExistingName_ShouldThrowException()
    {
        // Act & Assert
        var act = async () => await _repository.UpdateLastNumberAsync("nonexistent", 123);
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("DocNumber entry for 'nonexistent' not found");
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
