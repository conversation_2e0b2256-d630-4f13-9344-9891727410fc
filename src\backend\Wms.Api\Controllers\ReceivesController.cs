using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using Wms.Application.DTOs;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.Features.Receives.Queries;
using Wms.Domain.Exceptions;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler zarządzania dostawami
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
[Produces("application/json")]
public class ReceivesController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<ReceivesController> _logger;

    public ReceivesController(IMediator mediator, ILogger<ReceivesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera listę dostępnych dostaw
    /// </summary>
    /// <param name="magazynId">ID magazynu (opcjonalne)</param>
    /// <param name="limit">Maksymalna liczba dostaw (domyślnie 100)</param>
    /// <param name="includeAssigned">Czy uwzględnić przypisane dostawy</param>
    /// <returns>Lista dostępnych dostaw</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ReceiveListResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ReceiveListResponse>> GetAvailableReceives(
        [FromQuery] int? magazynId = null,
        [FromQuery] int limit = 100,
        [FromQuery] bool includeAssigned = false)
    {
        try
        {
            var currentUserId = int.TryParse(GetCurrentUserId(), out var userId) ? userId : (int?)null;

            var query = new GetAvailableReceivesQuery
            {
                MagazynId = magazynId,
                Limit = limit,
                IncludeAssigned = includeAssigned,
                CurrentUserId = currentUserId
            };

            var result = await _mediator.Send(query);

            _logger.LogDebug("Pobrano {Count} dostaw (magazyn: {MagazynId}, includeAssigned: {IncludeAssigned})", 
                result.TotalCount, magazynId, includeAssigned);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Nieprawidłowe parametry zapytania o dostawy: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowe parametry",
                Detail = ex.Message,
                Status = 400
            });
        }
    }

    /// <summary>
    /// Pobiera szczegóły dostawy
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Szczegółowe informacje o dostawie</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ReceiveDetailsDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ReceiveDetailsDto>> GetReceiveDetails([FromRoute] int id)
    {
        try
        {
            var query = new GetReceiveDetailsQuery { ReceiveId = id };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Dostawa nie znaleziona",
                    Detail = $"Dostawa LK{id} nie została znaleziona",
                    Status = 404
                });
            }

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            _logger.LogWarning("Dostawa nie znaleziona: {Message}", ex.Message);
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Przypisuje dostawę do pracownika (claim)
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Wynik operacji claim</returns>
    [HttpPost("{id:int}/claim")]
    [ProducesResponseType(typeof(ClaimReceiveResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ClaimReceiveResponse>> ClaimReceive([FromRoute] int id)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new ClaimReceiveCommand
            {
                ReceiveId = id,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return Conflict(new ProblemDetails
                {
                    Title = "Nie można przypisać dostawy",
                    Detail = result.Message,
                    Status = 409
                });
            }

            _logger.LogInformation("Dostawa LK{ReceiveId} została przypisana do pracownika {UserId}", 
                id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Brak autoryzacji",
                Detail = ex.Message,
                Status = 400
            });
        }
    }

    /// <summary>
    /// Zwalnia dostawę (release)
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Wynik operacji release</returns>
    [HttpPost("{id:int}/release")]
    [ProducesResponseType(typeof(ClaimReceiveResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 409)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ClaimReceiveResponse>> ReleaseReceive([FromRoute] int id)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new ReleaseReceiveCommand
            {
                ReceiveId = id,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return Conflict(new ProblemDetails
                {
                    Title = "Nie można zwolnić dostawy",
                    Detail = result.Message,
                    Status = 409
                });
            }

            _logger.LogInformation("Dostawa LK{ReceiveId} została zwolniona przez pracownika {UserId}", 
                id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Pobiera oczekiwane pozycje z awizacji dla dostawy
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Lista oczekiwanych pozycji</returns>
    [HttpGet("{id:int}/awizacja/pozycje")]
    [ProducesResponseType(typeof(List<ExpectedItemDto>), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<List<ExpectedItemDto>>> GetExpectedItems([FromRoute] int id)
    {
        try
        {
            var query = new GetReceiveExpectedItemsQuery { ListControlId = id };
            var result = await _mediator.Send(query);

            _logger.LogDebug("Pobrano {Count} oczekiwanych pozycji dla dostawy LK{ReceiveId}", 
                result.Count, id);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Pobiera nośniki (palety) przypisane do dostawy
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Lista nośników</returns>
    [HttpGet("{id:int}/nosniki")]
    [ProducesResponseType(typeof(List<CarrierDto>), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<List<CarrierDto>>> GetCarriers([FromRoute] int id)
    {
        try
        {
            var query = new GetReceiveCarriersQuery { ListControlId = id };
            var result = await _mediator.Send(query);

            _logger.LogDebug("Pobrano {Count} nośników dla dostawy LK{ReceiveId}", 
                result.Count, id);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Pobiera pozycje na konkretnym nośniku
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <param name="paletaId">ID palety</param>
    /// <returns>Lista pozycji na nośniku</returns>
    [HttpGet("{id:int}/nosniki/{paletaId:int}/pozycje")]
    [ProducesResponseType(typeof(List<ReceiveItemDto>), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<List<ReceiveItemDto>>> GetCarrierItems(
        [FromRoute] int id, 
        [FromRoute] int paletaId)
    {
        try
        {
            var query = new GetCarrierItemsQuery 
            { 
                ListControlId = id, 
                PaletaId = paletaId 
            };
            var result = await _mediator.Send(query);

            _logger.LogDebug("Pobrano {Count} pozycji z nośnika DS{PalletId:D8} dla dostawy LK{ReceiveId}", 
                result.Count, paletaId, id);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Kończy sesję pracy z dostawą
    /// </summary>
    /// <param name="id">ID dostawy</param>
    /// <returns>Wynik zakończenia sesji</returns>
    [HttpPost("{id:int}/koniec")]
    [ProducesResponseType(typeof(ClaimReceiveResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ClaimReceiveResponse>> EndSession([FromRoute] int id)
    {
        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new EndReceiveSessionCommand
            {
                ListControlId = id,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Nie można zakończyć sesji",
                    Detail = result.Message,
                    Status = 400
                });
            }

            _logger.LogInformation("Zakończono sesję dla dostawy LK{ReceiveId} przez pracownika {UserId}", 
                id, userId);

            return Ok(result);
        }
        catch (ReceiveNotFoundException ex)
        {
            return NotFound(new ProblemDetails
            {
                Title = "Dostawa nie znaleziona",
                Detail = ex.Message,
                Status = 404
            });
        }
    }

    /// <summary>
    /// Zwraca pozycje awizacji dla dostawy wskazanej przez kod LK
    /// Zgodny endpoint dla klienta mobilnego: GET /api/v1/receives/{lk}/awizacja-positions
    /// </summary>
    [HttpGet("/api/v{version:apiVersion}/receives/{lk}/awizacja-positions")]
    [ProducesResponseType(typeof(List<AwizacjaPositionDto>), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    public async Task<ActionResult<List<AwizacjaPositionDto>>> GetAwizacjaPositionsByLk([FromRoute] string lk)
    {
        if (string.IsNullOrWhiteSpace(lk))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy LK",
                Detail = "Kod LK jest wymagany",
                Status = 400
            });
        }

        var id = ParseLkToId(lk);
        if (!id.HasValue)
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy LK",
                Detail = $"Nie można sparsować kodu LK: {lk}",
                Status = 400
            });
        }

        var expectedItems = await _mediator.Send(new GetReceiveExpectedItemsQuery { ListControlId = id.Value });
        var result = expectedItems.Select(e => new AwizacjaPositionDto
        {
            Id = e.Id,
            SystemId = e.SystemId,
            TowarKod = e.Kod ?? string.Empty,
            TowarNazwa = e.KodNazwa,
            IloscAwizowana = e.IloscAwizowana,
            IloscPrzyjeta = e.IloscPrzyjeta,
            EtykietaKlient = e.EtykietaKlient,
            Lot = e.Lot,
            Blloc = e.Blloc,
            SSCC = e.EtykietaKlient
        }).ToList();

        return Ok(result);
    }

    /// <summary>
    /// Pobiera dane etykiety z awizacji po kodzie SSCC dla automatycznego wypełnienia formularza
    /// </summary>
    /// <param name="sscc">Kod SSCC etykiety</param>
    /// <returns>Dane etykiety z awizacji</returns>
    [HttpGet("/api/v{version:apiVersion}/awizacja/labels/{sscc}")]
    [ProducesResponseType(typeof(AwizacjaLabelDataDto), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 404)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    public async Task<ActionResult<AwizacjaLabelDataDto>> GetAwizacjaLabelData([FromRoute] string sscc)
    {
        if (string.IsNullOrWhiteSpace(sscc))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Nieprawidłowy SSCC",
                Detail = "Kod SSCC jest wymagany",
                Status = 400
            });
        }

        try
        {
            var query = new GetAwizacjaLabelDataQuery { SSCC = sscc };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Etykieta nie znaleziona",
                    Detail = $"Nie znaleziono etykiety z kodem SSCC: {sscc} w awizacjach dostaw",
                    Status = 404
                });
            }

            _logger.LogInformation("Pobrano dane etykiety z awizacji dla SSCC: {SSCC}", sscc);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania danych etykiety z awizacji dla SSCC: {SSCC}", sscc);
            return StatusCode(500, new ProblemDetails
            {
                Title = "Błąd serwera",
                Detail = "Wystąpił błąd podczas pobierania danych etykiety",
                Status = 500
            });
        }
    }

    /// <summary>
    /// Parsuje skan w kontekście rejestracji dostaw
    /// </summary>
    /// <param name="request">Dane skanu</param>
    /// <returns>Wyniki parsowania z sugestiami wypełnienia formularza</returns>
    [HttpPost("scan")]
    [ProducesResponseType(typeof(ParseReceiveScanResponse), 200)]
    [ProducesResponseType(typeof(ProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public async Task<ActionResult<ParseReceiveScanResponse>> ParseScan([FromBody] ParseScanRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.ScanData))
        {
            return BadRequest(new ProblemDetails
            {
                Title = "Błędne dane",
                Detail = "Dane skanu nie mogą być puste",
                Status = 400
            });
        }

        try
        {
            var userId = int.Parse(GetCurrentUserId());
            var command = new ParseReceiveScanCommand
            {
                ScanData = request.ScanData,
                ListControlId = request.ListControlId,
                UserId = userId,
                DeviceId = request.DeviceId ?? "unknown",
                Context = request.Context ?? "delivery"
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("Przetworzono skan: Success={IsSuccess}, Type={ScanType}, HasIzPrefix={HasIzPrefix}", 
                result.IsSuccess, result.ScanType, result.HasIzPrefix);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas parsowania skanu: {ScanData}", request.ScanData);
            return BadRequest(new ProblemDetails
            {
                Title = "Błąd parsowania",
                Detail = "Wystąpił błąd podczas przetwarzania skanu",
                Status = 500
            });
        }
    }

    private static int? ParseLkToId(string lk)
    {
        if (string.IsNullOrWhiteSpace(lk)) return null;
        var s = lk.Trim().ToUpperInvariant();
        if (s.StartsWith("LK")) s = s.Substring(2);
        return int.TryParse(s, out var id) ? id : null;
    }
}

/// <summary>
/// Request DTO dla parsowania skanu
/// </summary>
public record ParseScanRequest
{
    /// <summary>
    /// Surowe dane ze skanera
    /// </summary>
    /// <example>]C1001234567890123456102987654321012345610ABC123171250908370001</example>
    public string ScanData { get; init; } = string.Empty;
    
    /// <summary>
    /// ID dostawy (opcjonalne)
    /// </summary>
    /// <example>12345</example>
    public int? ListControlId { get; init; }
    
    /// <summary>
    /// ID urządzenia skanującego (opcjonalne)
    /// </summary>
    /// <example>MC3300-001</example>
    public string? DeviceId { get; init; }
    
    /// <summary>
    /// Kontekst skanu (opcjonalne)
    /// </summary>
    /// <example>delivery</example>
    public string? Context { get; init; }
}
