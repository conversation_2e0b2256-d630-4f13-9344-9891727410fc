using FluentValidation;
using Wms.Application.DTOs.Auth;

namespace Wms.Application.Validators;

public class LoginScanRequestValidator : AbstractValidator<LoginScanRequest>
{
    public LoginScanRequestValidator()
    {
        RuleFor(x => x.CardNumber)
            .NotEmpty()
            .WithMessage("Card number is required")
            .Length(6, 50) // Zakres typowych numerów kart pracowniczych
            .WithMessage("Card number must be between 6 and 50 characters")
            .Matches(@"^[A-Za-z0-9]+$")
            .WithMessage("Card number can contain only letters and numbers");

        RuleFor(x => x.DeviceId)
            .MaximumLength(100)
            .WithMessage("Device ID cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.DeviceId));
    }
}
