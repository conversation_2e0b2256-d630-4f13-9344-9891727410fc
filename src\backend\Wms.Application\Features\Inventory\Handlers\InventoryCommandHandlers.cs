using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Inventory;
using Wms.Application.Features.Inventory.Commands;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.Inventory.Handlers;

/// <summary>
/// Handler rozpoczynania sesji inwentaryzacji
/// </summary>
public class StartInventorySessionHandler : IRequestHandler<StartInventoryCommand, InventorySessionDto>
{
    private readonly Wms.Application.Interfaces.IInventoryRepository _inventoryRepository;
    private readonly ILogger<StartInventorySessionHandler> _logger;

    public StartInventorySessionHandler(Wms.Application.Interfaces.IInventoryRepository inventoryRepository, ILogger<StartInventorySessionHandler> logger)
    {
        _inventoryRepository = inventoryRepository;
        _logger = logger;
    }

    public async Task<InventorySessionDto> Handle(StartInventoryCommand request, CancellationToken cancellationToken)
    {
        if (request.InventoryId <= 0)
            throw new ArgumentException("InventoryId must be greater than 0");
        if (string.IsNullOrWhiteSpace(request.DeviceId))
            throw new ArgumentException("DeviceId is required");

        _logger.LogInformation("Starting inventory session for InventoryId={InventoryId}, User={UserId}, Device={DeviceId}",
            request.InventoryId, request.PracownikId, request.DeviceId);

        // Utwórz sesję przez repozytorium
        var sessionEntity = await _inventoryRepository.CreateSessionAsync(
            request.InventoryId,
            request.PracownikId,
            request.DeviceId,
            cancellationToken);

        return new InventorySessionDto
        {
            Id = sessionEntity.Id,
            InventoryId = sessionEntity.InventoryId,
            PracownikId = sessionEntity.PracownikId,
            DeviceId = sessionEntity.DeviceId,
            StartedAt = sessionEntity.StartedAt,
            EndedAt = sessionEntity.EndedAt,
            IsActive = sessionEntity.IsActive,
            Type = InventoryType.Product // Domyślnie produktowa (jak w kontrolerze)
        };
    }
}