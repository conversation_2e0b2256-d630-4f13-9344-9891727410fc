<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.OptionsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:OptionsViewModel"
             Title="{loc:Translate Key=Options_Title}"
             BackgroundColor="{DynamicResource PageBackgroundColor}">


    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="20">
            
            <!-- Header -->
<Label Text="{loc:Translate Key=Options_Header}"
                   FontSize="24"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   Margin="0,0,0,30" />

            <!-- <PERSON>miana miejsca -->
            <Frame BackgroundColor="{DynamicResource PrimaryColor}"
                   CornerRadius="12"
                   Padding="0"
                   HasShadow="True">
<Button Text="{loc:Translate Key=Options_MovePallet_Button}"
                        FontSize="18"
                        FontAttributes="Bold"
                        TextColor="White"
                        BackgroundColor="Transparent"
                        HeightRequest="80"
                        Command="{Binding NavigateToMovePalletCommand}" />
            </Frame>

            <!-- Przyszłe opcje (placeholder) -->
            <Frame BackgroundColor="{DynamicResource SecondaryColor}"
                   CornerRadius="12"
                   Padding="0"
                   HasShadow="True"
                   Opacity="0.6">
                <Button Text="📋 Przyjęcie towaru"
                        FontSize="18"
                        FontAttributes="Bold"
                        TextColor="White"
                        BackgroundColor="Transparent"
                        HeightRequest="80"
                        IsEnabled="False" />
            </Frame>

            <Frame BackgroundColor="{DynamicResource SecondaryColor}"
                   CornerRadius="12"
                   Padding="0"
                   HasShadow="True"
                   Opacity="0.6">
                <Button Text="📤 Wydanie towaru"
                        FontSize="18"
                        FontAttributes="Bold"
                        TextColor="White"
                        BackgroundColor="Transparent"
                        HeightRequest="80"
                        IsEnabled="False" />
            </Frame>

            <Frame BackgroundColor="{DynamicResource SecondaryColor}"
                   CornerRadius="12"
                   Padding="0"
                   HasShadow="True"
                   Opacity="0.6">
                <Button Text="📊 Inwentaryzacja"
                        FontSize="18"
                        FontAttributes="Bold"
                        TextColor="White"
                        BackgroundColor="Transparent"
                        HeightRequest="80"
                        IsEnabled="False" />
            </Frame>

            <!-- Opcje systemowe -->
<Label Text="{loc:Translate Key=Options_SystemOptions_Header}"
                   FontSize="18"
                   FontAttributes="Bold"
                   Margin="0,30,0,10" />

            <!-- O aplikacji -->
            <Frame BackgroundColor="{DynamicResource TertiaryColor}"
                   CornerRadius="12"
                   Padding="0"
                   HasShadow="True">
<Button Text="{loc:Translate Key=Options_About_Button}"
                        FontSize="16"
                        FontAttributes="Bold"
                        TextColor="White"
                        BackgroundColor="Transparent"
                        HeightRequest="60"
                        Command="{Binding NavigateToAboutCommand}" />
            </Frame>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
