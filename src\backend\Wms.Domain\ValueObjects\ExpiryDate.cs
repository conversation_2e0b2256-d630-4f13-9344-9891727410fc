using System.Globalization;
using System.Text.RegularExpressions;
using Wms.Domain.Exceptions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący datę ważności/przydatności
/// Format: YYMMDD zgodnie ze standardem GS1 (AI 17)
/// Obsługuje również AI 15 (minimum durability date) i AI 13 (packing date)
/// </summary>
public record ExpiryDate : IComparable<ExpiryDate>
{
    // GS1 date format: YYMMDD (6 digits)
    private static readonly Regex DatePattern = new(@"^\d{6}$", RegexOptions.Compiled);
    
    public DateOnly Value { get; private init; }
    public string GS1Format => Value.ToString("yyMMdd");
    public string FormattedValue => $"(17) {GS1Format}";
    public string DisplayFormat => Value.ToString("dd.MM.yyyy");
    
    // Business logic properties
    public bool IsExpired => Value < DateOnly.FromDateTime(DateTime.Today);
    public bool IsExpiringSoon => Value <= DateOnly.FromDateTime(DateTime.Today.AddDays(7)) && !IsExpired;
    public int DaysUntilExpiry => Value.DayNumber - DateOnly.FromDateTime(DateTime.Today).DayNumber;
    
    private ExpiryDate(DateOnly value)
    {
        Value = value;
    }

    /// <summary>
    /// Tworzy ExpiryDate z DateOnly
    /// </summary>
    public static ExpiryDate Create(DateOnly date)
    {
        return new ExpiryDate(date);
    }

    /// <summary>
    /// Tworzy ExpiryDate z DateTime
    /// </summary>
    public static ExpiryDate Create(DateTime dateTime)
    {
        return new ExpiryDate(DateOnly.FromDateTime(dateTime));
    }

    /// <summary>
    /// Tworzy ExpiryDate z formatu GS1 YYMMDD
    /// </summary>
    public static ExpiryDate CreateFromGS1(string gs1Date)
    {
        if (string.IsNullOrWhiteSpace(gs1Date))
            throw new InvalidGS1FormatException(gs1Date ?? "null", "Data ważności nie może być pusta");

        // Remove any non-digits
        var digitsOnly = Regex.Replace(gs1Date.Trim(), @"[^\d]", "");
        
        if (!DatePattern.IsMatch(digitsOnly))
            throw new InvalidGS1FormatException(gs1Date, $"Nieprawidłowy format daty GS1. Oczekiwano YYMMDD (6 cyfr), otrzymano: '{digitsOnly}'");

        var dateOnly = ParseGS1Date(digitsOnly);
        return new ExpiryDate(dateOnly);
    }

    /// <summary>
    /// Próbuje utworzyć ExpiryDate z formatu GS1
    /// </summary>
    public static bool TryCreateFromGS1(string gs1Date, out ExpiryDate? expiryDate)
    {
        expiryDate = null;
        
        if (string.IsNullOrWhiteSpace(gs1Date))
            return false;

        try
        {
            var digitsOnly = Regex.Replace(gs1Date.Trim(), @"[^\d]", "");
            
            if (!DatePattern.IsMatch(digitsOnly))
                return false;

            var dateOnly = ParseGS1Date(digitsOnly);
            expiryDate = new ExpiryDate(dateOnly);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Waliduje czy string może być datą GS1
    /// </summary>
    public static bool IsValidGS1Format(string? gs1Date)
    {
        if (string.IsNullOrWhiteSpace(gs1Date))
            return false;

        var digitsOnly = Regex.Replace(gs1Date.Trim(), @"[^\d]", "");
        
        if (!DatePattern.IsMatch(digitsOnly))
            return false;

        try
        {
            ParseGS1Date(digitsOnly);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Parsuje datę w formacie GS1 YYMMDD
    /// Obsługuje specjalne przypadki zgodnie ze specyfikacją GS1:
    /// - Dzień 00 oznacza ostatni dzień miesiąca
    /// </summary>
    private static DateOnly ParseGS1Date(string yymmdd)
    {
        if (yymmdd.Length != 6)
            throw new InvalidGS1FormatException(yymmdd, "Data GS1 musi mieć dokładnie 6 cyfr");

        var yy = int.Parse(yymmdd[0..2]);
        var mm = int.Parse(yymmdd[2..4]);
        var dd = int.Parse(yymmdd[4..6]);

        // Y2K handling: assume 00-50 means 2000-2050, 51-99 means 1951-1999
        var year = yy + (yy <= 50 ? 2000 : 1900);

        // Validate month
        if (mm < 1 || mm > 12)
            throw new InvalidGS1FormatException(yymmdd, $"Nieprawidłowy miesiąc: {mm:D2}. Dozwolone wartości: 01-12");

        // Handle special case: day 00 means last day of month (GS1 specification)
        if (dd == 0)
        {
            dd = DateTime.DaysInMonth(year, mm);
        }
        else if (dd < 1 || dd > DateTime.DaysInMonth(year, mm))
        {
            throw new InvalidGS1FormatException(yymmdd, $"Nieprawidłowy dzień: {dd:D2} dla miesiąca {mm:D2}/{year}");
        }

        return new DateOnly(year, mm, dd);
    }

    /// <summary>
    /// Sprawdza czy data jest w rozsądnym zakresie dla produktów
    /// </summary>
    public bool IsReasonableExpiryDate()
    {
        var today = DateOnly.FromDateTime(DateTime.Today);
        var maxDate = today.AddYears(10); // Maksymalnie 10 lat w przyszłość
        var minDate = today.AddDays(-30);   // Maksymalnie 30 dni wstecz

        return Value >= minDate && Value <= maxDate;
    }

    /// <summary>
    /// Porównuje daty chronologicznie
    /// </summary>
    public int CompareTo(ExpiryDate? other)
    {
        if (other is null) return 1;
        return Value.CompareTo(other.Value);
    }

    public static bool operator <(ExpiryDate left, ExpiryDate right) => left.CompareTo(right) < 0;
    public static bool operator >(ExpiryDate left, ExpiryDate right) => left.CompareTo(right) > 0;
    public static bool operator <=(ExpiryDate left, ExpiryDate right) => left.CompareTo(right) <= 0;
    public static bool operator >=(ExpiryDate left, ExpiryDate right) => left.CompareTo(right) >= 0;

    public override string ToString() => DisplayFormat;
    
    public static implicit operator DateOnly(ExpiryDate expiryDate) => expiryDate.Value;
    public static explicit operator ExpiryDate(DateOnly date) => Create(date);
    public static explicit operator ExpiryDate(DateTime dateTime) => Create(dateTime);
}
