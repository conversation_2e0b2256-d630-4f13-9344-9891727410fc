using FluentAssertions;
using Wms.Domain.Exceptions;
using Wms.Domain.ValueObjects;
using Xunit;

namespace Wms.UnitTests.Domain.ValueObjects;

public class DSCodeTests
{
    [Theory]
    [InlineData("DS1234")] // 4 cyfry - minimum
    [InlineData("DS12345")] // 5 cyfr
    [InlineData("DS123456")] // 6 cyfr
    [InlineData("DS1234567")] // 7 cyfr
    [InlineData("DS12345678")] // 8 cyfr
    [InlineData("DS123456789")] // 9 cyfr - maximum
    [InlineData("DS3535620")] // 7 cyfr - przykład z problemu
    [InlineData("DS0001")] // 4 cyfry z zerami wiodącymi
    public void Create_ValidCode_ShouldSucceed(string validCode)
    {
        // Act
        var result = DSCode.Create(validCode);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(validCode);
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("DS")]
    [InlineData("DS1")] // za krótkie - tylko 1 cyfra
    [InlineData("DS12")] // za krótkie - tylko 2 cyfry
    [InlineData("DS123")] // za krótkie - tylko 3 cyfry
    [InlineData("DS1234567890")] // za długie - 10 cyfr
    [InlineData("12345678")] // brak prefiksu DS
    [InlineData("ds12345678")] // małe litery
    [InlineData("DX12345678")] // zły prefiks
    [InlineData("DS1234567A")] // litera w numerze
    [InlineData("DSABCDEFGH")] // same litery
    public void Create_InvalidCode_ShouldThrowException(string invalidCode)
    {
        // Act & Assert
        var exception = Assert.Throws<InvalidDSCodeException>(() => DSCode.Create(invalidCode));
        exception.Message.Should().Be($"Nieprawidłowy kod DS: '{invalidCode}'. Wymagany format: DS + 4-9 cyfr");
    }

    [Fact]
    public void TryCreate_ValidCode_ShouldReturnTrueAndCode()
    {
        // Arrange
        var validCode = "DS3535620"; // Przykład z problemu

        // Act
        var result = DSCode.TryCreate(validCode, out var dsCode);

        // Assert
        result.Should().BeTrue();
        dsCode.Should().NotBeNull();
        dsCode!.Value.Should().Be(validCode);
    }

    [Theory]
    [InlineData("")]
    [InlineData("DS123")] // za krótkie - tylko 3 cyfry
    [InlineData("DS1234567890")] // za długie - 10 cyfr
    [InlineData("DSABCDEFGH")]
    public void TryCreate_InvalidCode_ShouldReturnFalseAndNull(string invalidCode)
    {
        // Act
        var result = DSCode.TryCreate(invalidCode, out var dsCode);

        // Assert
        result.Should().BeFalse();
        dsCode.Should().BeNull();
    }

    [Theory]
    [InlineData("DS1234", true)] // 4 cyfry - minimum
    [InlineData("DS12345", true)] // 5 cyfr
    [InlineData("DS123456", true)] // 6 cyfr
    [InlineData("DS1234567", true)] // 7 cyfr
    [InlineData("DS12345678", true)] // 8 cyfr
    [InlineData("DS123456789", true)] // 9 cyfr - maximum
    [InlineData("DS3535620", true)] // 7 cyfr - przykład z problemu
    [InlineData("", false)]
    [InlineData("DS", false)]
    [InlineData("DS123", false)] // za krótkie - tylko 3 cyfry
    [InlineData("DS1234567890", false)] // za długie - 10 cyfr
    [InlineData("12345678", false)] // brak prefiksu
    [InlineData("ds12345678", false)] // małe litery
    [InlineData("DSABCDEFGH", false)] // same litery
    [InlineData(null, false)]
    public void IsValid_ShouldReturnExpectedResult(string code, bool expected)
    {
        // Act
        var result = DSCode.IsValid(code);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Equals_SameCodes_ShouldReturnTrue()
    {
        // Arrange
        var code1 = DSCode.Create("DS3535620");
        var code2 = DSCode.Create("DS3535620");

        // Act & Assert
        code1.Equals(code2).Should().BeTrue();
        (code1 == code2).Should().BeTrue();
        (code1 != code2).Should().BeFalse();
    }

    [Fact]
    public void Equals_DifferentCodes_ShouldReturnFalse()
    {
        // Arrange
        var code1 = DSCode.Create("DS1234567");
        var code2 = DSCode.Create("DS9876543");

        // Act & Assert
        code1.Equals(code2).Should().BeFalse();
        (code1 == code2).Should().BeFalse();
        (code1 != code2).Should().BeTrue();
    }

    [Fact]
    public void Equals_NullComparison_ShouldReturnFalse()
    {
        // Arrange
        var code = DSCode.Create("DS3535620");

        // Act & Assert
        code.Equals(null).Should().BeFalse();
        (code == null).Should().BeFalse();
        (code != null).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_SameCodes_ShouldReturnSameHashCode()
    {
        // Arrange
        var code1 = DSCode.Create("DS3535620");
        var code2 = DSCode.Create("DS3535620");

        // Act & Assert
        code1.GetHashCode().Should().Be(code2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnValue()
    {
        // Arrange
        var code = DSCode.Create("DS3535620");

        // Act
        var result = code.ToString();

        // Assert
        result.Should().Be("DS3535620");
    }

    [Fact]
    public void ImplicitConversion_FromDSCodeToString_ShouldWork()
    {
        // Arrange
        var code = DSCode.Create("DS3535620");

        // Act
        string result = code;

        // Assert
        result.Should().Be("DS3535620");
    }

    [Fact]
    public void ExplicitConversion_FromStringToDSCode_ShouldWork()
    {
        // Arrange
        var codeString = "DS3535620";

        // Act
        var result = (DSCode)codeString;

        // Assert
        result.Value.Should().Be(codeString);
    }

    [Fact]
    public void ExplicitConversion_FromInvalidStringToDSCode_ShouldThrowException()
    {
        // Arrange
        var invalidCode = "INVALID";

        // Act & Assert
        Assert.Throws<InvalidDSCodeException>(() => (DSCode)invalidCode);
    }

    [Fact]
    public void ParsedValue_ShouldReturnCorrectNumericPart()
    {
        // Arrange
        var code = DSCode.Create("DS3535620");

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(3535620);
    }

    [Fact]
    public void ParsedValue_WithLeadingZeros_ShouldReturnCorrectValue()
    {
        // Arrange
        var code = DSCode.Create("DS0000123"); // 7 cyfr z zerami wiodącymi

        // Act
        var parsedValue = code.ParsedValue;

        // Assert
        parsedValue.Should().Be(123);
    }

    [Theory]
    [InlineData("DS3535620", "DS3535620", 0)] // równe
    [InlineData("DS1234", "DS1235", -1)] // pierwszy mniejszy
    [InlineData("DS1235", "DS1234", 1)] // pierwszy większy
    [InlineData("DS999999999", "DS1234", 1)] // dłuższy większy
    public void CompareTo_ShouldReturnExpectedResult(string code1, string code2, int expected)
    {
        // Arrange
        var dsCode1 = DSCode.Create(code1);
        var dsCode2 = DSCode.Create(code2);

        // Act
        var result = dsCode1.CompareTo(dsCode2);

        // Assert
        if (expected == 0)
            result.Should().Be(0);
        else if (expected > 0)
            result.Should().BePositive();
        else
            result.Should().BeNegative();
    }

    [Fact]
    public void CompareTo_WithNull_ShouldReturn1()
    {
        // Arrange
        var code = DSCode.Create("DS3535620");

        // Act
        var result = code.CompareTo(null);

        // Assert
        result.Should().Be(1);
    }

    [Theory]
    [InlineData("DS1234", "DS1235")]
    [InlineData("DS3535620", "DS9999999")]
    public void ComparisonOperators_ShouldWorkCorrectly(string smaller, string larger)
    {
        // Arrange
        var smallerCode = DSCode.Create(smaller);
        var largerCode = DSCode.Create(larger);

        // Act & Assert
        (smallerCode < largerCode).Should().BeTrue();
        (largerCode > smallerCode).Should().BeTrue();
        (smallerCode <= largerCode).Should().BeTrue();
        (largerCode >= smallerCode).Should().BeTrue();
        (smallerCode >= largerCode).Should().BeFalse();
        (largerCode <= smallerCode).Should().BeFalse();
    }
}
