using Microsoft.Extensions.Logging;
using WmsApp.Services.Contracts;

namespace WmsApp;

public partial class AppShell : Shell
{
    private readonly IUpdateService _updateService;
    private readonly ILogger<AppShell> _logger;

    public AppShell(IUpdateService updateService, ILogger<AppShell> logger)
    {
        _updateService = updateService;
        _logger = logger;
        InitializeComponent();
        
        // Sprawdź aktualizacje przy starcie (z opóźnieniem)
        _ = Task.Run(async () =>
        {
            await Task.Delay(3000); // Opóźnienie 3 sekundy po starcie
            await CheckForUpdatesOnStartupAsync();
        });
    }

    private async Task CheckForUpdatesOnStartupAsync()
    {
        try
        {
            _logger.LogInformation("Sprawdzanie aktualizacji przy starcie aplikacji");
            
            var updateInfo = await _updateService.CheckForUpdatesAsync();
            
            if (updateInfo.IsAvailable)
            {
                _logger.LogInformation("Dostępna aktualizacja: v{NewVersion} (wymagana: {Required})", 
                    updateInfo.NewVersion, updateInfo.IsRequired);
                
                // Nawiguj do MainThread dla UI
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await ShowUpdateDialogAsync(updateInfo);
                });
            }
            else
            {
                _logger.LogInformation("Aplikacja jest aktualna");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Błąd podczas automatycznego sprawdzania aktualizacji");
        }
    }

    private async Task ShowUpdateDialogAsync(Models.Update.UpdateInfo updateInfo)
    {
        try
        {
            var title = updateInfo.IsRequired ? "Wymagana aktualizacja" : "Dostępna aktualizacja";
            var message = $"Dostępna jest nowa wersja {updateInfo.NewVersion}.\n\n" +
                         $"{updateInfo.Manifest?.ReleaseNotes ?? "Sprawdź szczegóły w sekcji O aplikacji."}";
            
            var action = updateInfo.IsRequired ? "Aktualizuj teraz" : "Aktualizuj";
            var cancel = updateInfo.IsRequired ? null : "Później";
            
            var result = await DisplayAlert(title, message, action, cancel);
            
            if (result || updateInfo.IsRequired)
            {
                // Przejdź do strony About gdzie użytkownik może pobrać aktualizację
                await GoToAsync("///aboutpage");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas wyświetlania dialogu aktualizacji");
        }
    }
}
