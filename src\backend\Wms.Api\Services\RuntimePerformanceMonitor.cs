using System.Diagnostics;
using Wms.Api.Metrics;

namespace Wms.Api.Services;

public interface IRuntimePerformanceMonitor
{
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    RuntimePerformanceMetrics GetCurrentMetrics();
}

public class RuntimePerformanceMonitor : IRuntimePerformanceMonitor, IDisposable
{
    private readonly ILogger<RuntimePerformanceMonitor> _logger;
    private readonly WmsMetrics _metrics;
    private readonly Timer _monitoringTimer;
    private readonly PerformanceCounterService _performanceCounters;
    private readonly TimeSpan _monitoringInterval = TimeSpan.FromSeconds(30);

    private RuntimePerformanceMetrics _lastMetrics = new();

    public RuntimePerformanceMonitor(ILogger<RuntimePerformanceMonitor> logger, WmsMetrics metrics)
    {
        _logger = logger;
        _metrics = metrics;
        _performanceCounters = new PerformanceCounterService(logger);
        
        _monitoringTimer = new Timer(CollectMetrics, null, Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
    }

    public Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting runtime performance monitoring with {Interval}s interval", 
            _monitoringInterval.TotalSeconds);

        _monitoringTimer.Change(TimeSpan.Zero, _monitoringInterval);
        
        return Task.CompletedTask;
    }

    public RuntimePerformanceMetrics GetCurrentMetrics()
    {
        return _lastMetrics;
    }

    private void CollectMetrics(object? state)
    {
        try
        {
            var metrics = new RuntimePerformanceMetrics
            {
                Timestamp = DateTimeOffset.UtcNow,
                
                // Memory metrics
                WorkingSet = GC.GetTotalMemory(false),
                Gen0Collections = GC.CollectionCount(0),
                Gen1Collections = GC.CollectionCount(1),
                Gen2Collections = GC.CollectionCount(2),
                
                // Process metrics
                ProcessorTime = _performanceCounters.GetProcessorTime(),
                ThreadCount = _performanceCounters.GetThreadCount(),
                HandleCount = _performanceCounters.GetHandleCount(),
                
                // Thread pool metrics
                WorkerThreadsAvailable = _performanceCounters.GetAvailableWorkerThreads(),
                CompletionPortThreadsAvailable = _performanceCounters.GetAvailableCompletionPortThreads(),
                
                // Additional metrics
                TotalAllocatedBytes = GC.GetTotalAllocatedBytes(),
                HeapSize = GC.GetTotalMemory(false)
            };

            LogPerformanceMetrics(metrics);
            UpdateMetricsCounters(metrics);
            
            _lastMetrics = metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting runtime performance metrics");
        }
    }

    private void LogPerformanceMetrics(RuntimePerformanceMetrics metrics)
    {
        // Log basic metrics at info level
        _logger.LogInformation(
            "Runtime Performance - Memory: {MemoryMB}MB, CPU Time: {CpuTimeMs}ms, Threads: {ThreadCount}, GC: {Gen0}/{Gen1}/{Gen2}",
            metrics.WorkingSet / 1024 / 1024,
            metrics.ProcessorTime?.TotalMilliseconds ?? 0,
            metrics.ThreadCount,
            metrics.Gen0Collections,
            metrics.Gen1Collections,
            metrics.Gen2Collections);

        // Log detailed metrics at debug level
        _logger.LogDebug("Detailed performance metrics: {@Metrics}", metrics);

        // Log warnings for concerning metrics
        if (metrics.WorkingSet > 500 * 1024 * 1024) // 500MB
        {
            _logger.LogWarning("High memory usage detected: {MemoryMB}MB", metrics.WorkingSet / 1024 / 1024);
        }

        if (metrics.ThreadCount > 100)
        {
            _logger.LogWarning("High thread count detected: {ThreadCount}", metrics.ThreadCount);
        }

        if (metrics.WorkerThreadsAvailable < 10)
        {
            _logger.LogWarning("Low worker threads available: {Available}", metrics.WorkerThreadsAvailable);
        }
    }

    private void UpdateMetricsCounters(RuntimePerformanceMetrics metrics)
    {
        // Update gauges for current state
        _metrics.ActiveSessions.Add(metrics.ThreadCount - (_lastMetrics.ThreadCount), 
            new System.Diagnostics.TagList { { "metric_type", "thread_count" } });

        // Record memory usage
        var memoryTags = new System.Diagnostics.TagList 
        { 
            { "memory_type", "working_set" } 
        };
        // Note: We'd need to add memory gauge to WmsMetrics for this to work properly
        
        // Log GC pressure if significant
        var gen2Delta = metrics.Gen2Collections - _lastMetrics.Gen2Collections;
        if (gen2Delta > 0)
        {
            _logger.LogWarning("Gen2 garbage collection occurred {Count} times in the last interval", gen2Delta);
        }
    }

    public void Dispose()
    {
        _monitoringTimer?.Dispose();
        _performanceCounters?.Dispose();
    }
}

public class RuntimePerformanceMetrics
{
    public DateTimeOffset Timestamp { get; set; }
    
    // Memory metrics
    public long WorkingSet { get; set; }
    public long TotalAllocatedBytes { get; set; }
    public long HeapSize { get; set; }
    
    // Garbage collection metrics
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    
    // Process metrics  
    public TimeSpan? ProcessorTime { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    
    // Thread pool metrics
    public int WorkerThreadsAvailable { get; set; }
    public int CompletionPortThreadsAvailable { get; set; }
}

public class PerformanceCounterService : IDisposable
{
    private readonly ILogger _logger;
    private readonly Process _currentProcess;

    public PerformanceCounterService(ILogger logger)
    {
        _logger = logger;
        _currentProcess = Process.GetCurrentProcess();
    }

    public TimeSpan? GetProcessorTime()
    {
        try
        {
            return _currentProcess.TotalProcessorTime;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Could not get processor time: {Error}", ex.Message);
            return null;
        }
    }

    public int GetThreadCount()
    {
        try
        {
            return _currentProcess.Threads.Count;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Could not get thread count: {Error}", ex.Message);
            return 0;
        }
    }

    public int GetHandleCount()
    {
        try
        {
            return _currentProcess.HandleCount;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Could not get handle count: {Error}", ex.Message);
            return 0;
        }
    }

    public int GetAvailableWorkerThreads()
    {
        ThreadPool.GetAvailableThreads(out int workerThreads, out _);
        return workerThreads;
    }

    public int GetAvailableCompletionPortThreads()
    {
        ThreadPool.GetAvailableThreads(out _, out int completionPortThreads);
        return completionPortThreads;
    }

    public void Dispose()
    {
        _currentProcess?.Dispose();
    }
}

// Extension method for service registration
public static class RuntimePerformanceMonitorExtensions
{
    public static IServiceCollection AddRuntimePerformanceMonitoring(this IServiceCollection services)
    {
        services.AddSingleton<IRuntimePerformanceMonitor, RuntimePerformanceMonitor>();
        return services;
    }
}
