# PRD Frontend – <PERSON><PERSON><PERSON>wy” (MAUI)

## 1. Cel i zakres
Zaprojektowanie dwóch widoków w aplikacji MAUI dla rejestracji dostaw:
- Widok 1: „Dostawa wybór” – wprowadzenie/zeskanowanie LK, lista dostaw, generowanie DS (palet), przejście do rejestracji.
- Widok 2: „Dostawa rejestracja” – skanowanie GS1/DS, rejestracja pozycji na nośnik, podgląd, „Nośnik kompletny”, „Koniec”.

Komunikacja wyłącznie JSON z nowym API .NET.

## 2. Widok 1 – Dostawa wybór
Elementy:
- Pole tekstowe „LK” z walidacją: akceptuje „LK” + liczba (bez zer wiodących). Możliwość skanowania.
- Przycisk „Lista dostaw” – wyświetla listę z API (list_control z docin_id_man IS NULL; opcjonalnie odfiltrowane zajęte). Prezentacja: "numer_zamowienia | system_nazwa | miejsce_dostawy | data".
- Przycisk „Generuj DS” – otwiera modal/dodatkowy ekran:
  - Typ palety (lista z typypalet),
  - Ilość palet (1..100),
  - Skan drukarki z prefiksem „IP” (np. „IP172.7.1.44”); aplikacja usuwa prefiks „IP” i waliduje IPv4; + checkbox „Czy drukować?”
  - Przycisk „Generuj DS” – wywołuje API generujące DS (i druk, jeśli zaznaczono).
- Po wyborze/dokonaniu wyboru dostawy: aplikacja wywołuje claim; w razie 409 informuje, że dostawa jest zajęta.
- Po udanym wyborze LK: przejście do Widoku 2.

UX/Focus:
- Po powrocie z listy „Wybierz” – wartość LK wstawia się do pola i jest gotowa do potwierdzenia.
- Walidacja: nie pozwól przejść dalej bez poprawnego LK/claim.

## 3. Architektura MAUI (zgodnie z ARCHITECTURE.md)

### 3.1 Integracja z MAUI Shell
```xml path=null start=null
<!-- AppShell.xaml - sekcja „Przyjęcia” z podmenu „Opcje przyjęć” -->
<FlyoutItem Title="Przyjęcia" Route="receives">
  <ShellSection Title="Opcje przyjęć">
    <ShellContent Title="Rejestracja przyjęcia"
                  Route="receives/registration"
                  ContentTemplate="{DataTemplate views:ReceivesRegistrationPage}" />
  </ShellSection>
</FlyoutItem>
```

### 3.2 Struktura ViewModels (MVVM + CommunityToolkit)
```csharp path=null start=null
// ReceivesSelectionViewModel.cs
public partial class ReceivesSelectionViewModel : BaseViewModel
{
    [ObservableProperty]
    private string lkInput = string.Empty;
    
    [ObservableProperty] 
private ObservableCollection<ReceiveDto> availableReceives = [];
    
    [RelayCommand]
private async Task LoadReceives() { /* implementacja */ }
    
    [RelayCommand] 
private async Task ClaimReceive(int receiveId) { /* implementacja */ }
}
```

## 4. Widok 2 – Dostawa rejestracja

### 4.1 Elementy UI (zgodnie z STYLE_GUIDE.md)
Elementy główne z dużymi kontrolkami (min. 44px touch target):
- **Pole "Skanuj"** (autofocus, integracja DataWedge): akceptuje GS1 i DS
- **Nr nośnika**: bieżący DS/SSCC lub "(Auto)"
- **Sekcja towaru**: kod z przyciskiem "Szukaj", walidacja EAN
- **Daty**: auto-data produkcji, edytowalna data ważności
- **Partia/Lot**: pole z walidacją wymagalności
- **Certyfikat**: pole tekstowe (etykiety.blloc) 
- **Stan jakości i Typ palety**: wybory z list
- **Ilości**: przeliczanie sztuk/opakowań wg `kody.ilosc_w_opakowaniu`
- **Akcje**: "Koniec", "Podgląd", "Nośnik kompletny"

Zachowanie:
- Po skanie GS1:
  - Parsujemy przez API: 00 (SSCC), 02 (lookup kody.ean/kody.id → kodId), 10 (lot), 17 (data_waznosci), 37 (ilość = ilosc_w_opakowaniu × AI37).
  - Jeśli 02 zwraca wiele kandydatów – pokaż selektor (id, kod) i umożliw wybór.
  - Jeśli znajdziemy SSCC w awizacji – prefiluj pola; jeśli nie – dopuszczamy przyjęcie bez awizacji.
- Rejestracja pozycji:
  - Gdy bieżący nośnik = „(Auto)” → aplikacja tworzy nowy DS przez endpoint „/nosniki” (typ palety z UI), następnie wykonuje insert pozycji.
  - Gdy bieżący nośnik wskazany (SSCC lub DS) → insert pozycji na ten nośnik.
  - Walidacje z API: brak wymaganej partii/daty → błąd; niezgodność ilości z awizacją → confirm (pokaż modal potwierdzenia).
  - W polu "Ilość" klawisz Enter = szybkie zatwierdzenie i pozostanie na tym samym nośniku (DS/SSCC).
- Ręczny wybór towaru: z pozycji awizacji (preferowane) lub z kartoteki „kody” (przełącznik: „Awizacja” | „Kartoteka”).
- „Nośnik kompletny”: zamyka bieżący DS w kontekście sesji; kolejny insert w trybie „(Auto)” utworzy nowy DS.
- „Podgląd”: pokazuje pozycje dla bieżącego DS (GET /nosniki/{paletaId}/pozycje), sumy, ewentualne ostrzeżenia.
- „Koniec”: kończy sesję rejestracji (POST /koniec), zwalnia claim; jeśli spełnione warunki zamknięcia – pokaż informację.

UX/Focus:
- Po każdym skanie focus wraca do pola „Skanuj”.
- Komunikaty z API (success/error/confirm) w formie toast/modal; confirm z wyraźnymi przyciskami „Kontynuuj”/„Anuluj”.

## 5. Integracja z DataWedge (zgodnie z ARCHITECTURE.md)

### 5.1 Konfiguracja skanera
```csharp path=null start=null
// Platforms/Android/DataWedgeReceiver.cs
[BroadcastReceiver(Enabled = true, Exported = false)]
[IntentFilter(new[] { "com.wms.receives.SCAN" })]
public class ReceivesDataWedgeReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        var scanData = intent.GetStringExtra("com.symbol.datawedge.data_string");
        var labelType = intent.GetStringExtra("com.symbol.datawedge.label_type");
        
        MessagingCenter.Send<string>(scanData, "ScanReceived");
    }
}
```

### 5.2 Obsługa skanów w ViewModels
```csharp path=null start=null
public partial class ReceivesRegistrationViewModel : BaseViewModel
{
public ReceivesRegistrationViewModel()
    {
        MessagingCenter.Subscribe<string>(this, "ScanReceived", OnScanReceived);
    }
    
    private async void OnScanReceived(string scanData)
    {
        await ProcessScanAsync(scanData);
    }
    
    [RelayCommand]
    private async Task ProcessScan(string scanData)
    {
        // Walidacja kodów (LK, SSCC, DS, GS1)
if (ReceiveCodeValidators.IsValidLK(scanData))
        {
            await ProcessLKScan(scanData);
        }
else if (ReceiveCodeValidators.IsValidSSCC(scanData))
        {
            await ProcessSSCCScan(scanData);
        }
        // itd.
    }
}
```

## 6. Integracje i dane
- **typypalet**: lista typów palet z API
- **kody**: walidacja i przeliczniki przez API
- **list_control**: kontekst dostawy (LK, miejsce_id)
- **awizacje_dostaw_head/awizacje_dostaw_dane**: źródło pozycji awizacji do ręcznego wyboru (dopuszczalne przyjęcie bez dopasowania SSCC)
- **etykiety**: rejestracja pozycji przez API
- **drukarki**: walidacja IP drukarek

## 5. Walidacje frontend
- Format LK – bez zer wiodących, prefiks „LK”.
- Ilość palet 1..100.
- IP drukarki – akceptuj skan z prefiksem „IP”; usuń prefiks i waliduj IPv4; dodatkowo weryfikuj IP z listą pobraną z API.
- Certyfikat – walidacja wymagalności wg kontrahenta/kodu (na podstawie informacji z API).
- Blokada akcji, gdy claim odrzucony (zajęte przez innego pracownika).

## 6. Błędy i confirm
- error – blokuje; confirm – wymaga potwierdzenia użytkownika (np. niezgodność ilości vs awizacja).

## 7. Kryteria akceptacyjne (wybór)
- Z listy dostaw wybieram LK, claim przechodzi; przy próbie drugiego operatora – informacja „zajęte”.
- Generuję 5 DS – aplikacja pokazuje listę wygenerowanych numerów i (opcjonalnie) drukuje.
- Skan GS1 (00/02/10/17/37) wypełnia pola zgodnie z mapowaniem; przy wielu kodach – selektor „id, kod”.
- W polu "Ilość" klawisz Enter podczas rejestracji zatwierdza pozycję i pozostaje na bieżącym nośniku (DS/SSCC).
- (Zmienione) Sekcja skanowania: przycisk „Wybierz” znajduje się w tej samej linii co pole skanowania.
- (Zmienione) Usunięto nagłówek „Dane towaru” oraz zakładki "Awizacja" i "Kartoteka"; przepływ wyboru towaru odbywa się przez skan lub dedykowany picker.
- (Zmienione) Usunięto pole wyszukiwania kodu pod sekcją towaru.
- Skan IP drukarki z prefiksem „IP” jest poprawnie interpretowany i walidowany.
- „Certyfikat” może zostać wprowadzony i przekazany do API.
- „Nośnik kompletny” powoduje, że kolejna pozycja z „(Auto)” utworzy nowy DS.
- „Koniec” zwalnia claim i zamyka sesję; jeśli awizacja kompletna – pojawia się informacja o gotowości zamknięcia dostawy.

