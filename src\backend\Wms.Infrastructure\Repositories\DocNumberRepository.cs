using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class DocNumberRepository : IDocNumberRepository
{
    private readonly WmsDbContext _context;

    public DocNumberRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<DocNumber?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.DocNumbers
            .AsNoTracking()
            .FirstOrDefaultAsync(d => d.Name == name, cancellationToken);
    }

    public async Task<int> GetNextNumberAsync(string name, CancellationToken cancellationToken = default)
    {
        // <PERSON><PERSON>ywamy blokady FOR UPDATE w ramach istniejącej transakcji zarządzanej przez UnitOfWork
        // Pobierz z blokadą FOR UPDATE (w MySQL)
        var docNumber = await _context.DocNumbers
            .FromSqlRaw("SELECT * FROM docnumber WHERE name = {0} FOR UPDATE", name)
            .FirstOrDefaultAsync(cancellationToken);

        if (docNumber == null)
        {
            throw new InvalidOperationException($"DocNumber entry for '{name}' not found");
        }

        var nextNumber = docNumber.GetNextNumber();
        docNumber.IncrementNumber();

        // Zapisz zmiany w ramach istniejącej transakcji
        // UnitOfWork zarządza transakcją i wywoła SaveChangesAsync() na końcu
        await _context.SaveChangesAsync(cancellationToken);

        return nextNumber;
    }

    public async Task UpdateLastNumberAsync(string name, int newValue, CancellationToken cancellationToken = default)
    {
        var docNumber = await _context.DocNumbers
            .FirstOrDefaultAsync(d => d.Name == name, cancellationToken);

        if (docNumber == null)
        {
            throw new InvalidOperationException($"DocNumber entry for '{name}' not found");
        }

        docNumber.Last = newValue;
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<DocNumber> CreateIfNotExistsAsync(string name, int initialValue = 0, CancellationToken cancellationToken = default)
    {
        var existing = await GetByNameAsync(name, cancellationToken);
        if (existing != null)
        {
            return existing;
        }

        var docNumber = new DocNumber
        {
            Name = name,
            Last = initialValue
        };

        _context.DocNumbers.Add(docNumber);
        await _context.SaveChangesAsync(cancellationToken);

        return docNumber;
    }
}
