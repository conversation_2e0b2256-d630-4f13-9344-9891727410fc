using Microsoft.EntityFrameworkCore;
using Wms.Application.Interfaces;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

public class UserRepository : IUserRepository
{
    private readonly WmsDbContext _context;

    public UserRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<User?> GetByCardNumberAsync(string cardNumber)
    {
        return await _context.Users
            .AsNoTracking()
            .FirstOrDefaultAsync(u => u.NumerKarty == cardNumber);
    }

    public async Task<User?> GetByIdAsync(int id)
    {
        return await _context.Users
            .AsNoTracking()
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<IEnumerable<User>> GetActiveUsersAsync()
    {
        // Jeśli kolumna is_active nie istnieje j<PERSON>, zwraca wszystkich
        return await _context.Users
            .AsNoTracking()
            .ToListAsync();
    }
}
