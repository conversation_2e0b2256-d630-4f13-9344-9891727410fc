# Przepływy Procesów Inwentaryzacji - WMS

## Przegląd

Dokument opisuje szczegółowe przepływy procesów dla różnych typów inwentaryzacji w systemie WMS, adaptowane do architektury Clean Architecture z wzorcem CQRS/MediatR.

## Główne Typy Inwentaryzacji

### 1. Inwentaryzacja Produktowa - ZAIMPLEMENTOWANA ✅

**Cel:** Inwentaryzacja konkretnych produktów i etykiet
**Status:** Pełna implementacja w module WMS
**Wzorzec architektoniczny:** CQRS z MediatR

#### Przepływ Procesu

```mermaid
graph TD
    A[Start Aplikacji] --> B[Logowanie JWT]
    B --> C[Wybór: Inwentaryzacja Produktowa]
    C --> D[Wyszukanie Aktywnych Sesji]
    D --> E{Czy istnieją aktywne sesje?}
    E -->|Tak| F[Wybór z Listy Sesji]
    E -->|Nie| G[Tworzenie Nowej Sesji]
    F --> H[StartInventorySessionCommand]
    G --> H
    H --> I[Inicjalizacja FormularZa]
    I --> J[Skanowanie DataWedge]
    J --> K[ProcessInventoryScanCommand]
    K --> L{Typ Skanu?}
    L -->|SSCC| M[Wyszukanie Etykiety]
    L -->|DS Code| N[Wyszukanie Palety]
    L -->|Kod Produktu| O[Wyszukanie w Kartotece]
    M --> P[CreateInventoryItemCommand]
    N --> P
    O --> P
    P --> Q[Walidacja i Zapis]
    Q --> R[UpdateInventoryStatusCommand]
    R --> S[Komunikat Sukcesu]
    S --> T[Reset Formularza]
    T --> J
```

#### Szczegółowe Komendy CQRS

##### 1. StartInventorySessionCommand
```csharp
public record StartInventorySessionCommand(
    int UserId,
    string Description,
    DateTime StartDate,
    InventoryType Type
) : IRequest<StartInventorySessionResponse>;
```

##### 2. ProcessInventoryScanCommand
```csharp
public record ProcessInventoryScanCommand(
    int SessionId,
    string ScannedCode,
    int UserId,
    string DeviceId
) : IRequest<ProcessScanResponse>;
```

##### 3. CreateInventoryItemCommand
```csharp
public record CreateInventoryItemCommand(
    int SessionId,
    string ProductCode,
    decimal CountedQuantity,
    string Location,
    int UserId,
    string EtykietaId = null,
    int? PaletaId = null
) : IRequest<CreateInventoryItemResponse>;
```

#### Walidacje Biznesowe

1. **Sprawdzenie Uprawnień**
   ```sql
   -- Weryfikacja czy użytkownik może przeprowadzać inwentaryzację
   SELECT id FROM users WHERE id = ? AND is_active = 1 AND can_inventory = 1;
   ```

2. **Walidacja Kodu**
   ```csharp
   // CodeValidationService - już zaimplementowany
   var validationResult = _codeValidationService.ValidateCode(scannedCode);
   ```

3. **Sprawdzenie Duplikatów**
   ```sql
   -- Czy pozycja już została spisana w tej sesji
   SELECT COUNT(*) FROM inwentaryzacja
   WHERE inwentaryzacja_id = ? AND etykieta_id = ? AND active = 1;
   ```

### 2. Inwentaryzacja Ogólna - PLANOWANA 📋

**Cel:** Kompleksowa inwentaryzacja z pełną kontrolą
**Operacja:** `CurrentOperacja = "19"`
**Specyfika:** Obsługuje pola `nr_wspolny` i `proba`

#### Przepływ Procesu

```mermaid
graph TD
    A[Start Sesji Ogólnej] --> B[Wybór Parametrów]
    B --> C{Nr Wspólny?}
    C -->|Tak| D[Ustawienie nr_wspolny]
    C -->|Nie| E[Generowanie ID Sesji]
    D --> F[Sprawdzenie Próby]
    E --> F
    F --> G[Inicjalizacja Formularza]
    G --> H[Skanowanie Uniwersalne]
    H --> I{Typ Kodu?}
    I -->|Etykieta| J[Wyszukanie w Inwentaryzacji]
    I -->|Paleta DS/SSCC| K[Wyszukanie Palety]
    I -->|Kod Produktu| L[Wyszukanie w Kartotece]
    I -->|Lokalizacja| M[Skanowanie Miejsca]
    J --> N[Wypełnienie Wszystkich Pól]
    K --> N
    L --> N
    M --> N
    N --> O[Wprowadzenie Ilości]
    O --> P[Weryfikacja Zmian]
    P --> Q{Zmiana Lokalizacji?}
    Q -->|Tak| R[Rejestracja w zmianym]
    Q -->|Nie| S[Standardowy Zapis]
    R --> T[Aktualizacja etykiety.miejscep]
    T --> S
    S --> U[Rejestracja w operacje]
    U --> V[Aktualizacja Stanu]
    V --> W[Komunikat i Reset]
    W --> H
```

#### Zapytania SQL - Inwentaryzacja Ogólna

```sql
-- Wyszukiwanie aktywnych sesji z nr_wspolny
SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data, opis, inwentaryzacja_id,
       nr_wspolny, proba
FROM inwentaryzacja i
WHERE active = 1 AND system_id = ?
GROUP BY i.data, opis, inwentaryzacja_id
ORDER BY inwentaryzacja_id DESC
LIMIT 40;

-- Wyszukiwanie etykiety w inwentaryzacji ogólnej
SELECT i.kod, k.kod_nazwa, i.podkod,
       CAST(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM i.ilosc)) AS CHAR) as ilosc,
       m.hala, m.regal, m.miejsce, m.poziom,
       i.hala as hala_i, i.regal as regal_i,
       i.miejsce as miejsce_i, i.poziom as poziom_i,
       i.etykieta_id, i.paleta_id, i.nrsap,
       i.nr_wspolny, i.proba
FROM inwentaryzacja i
LEFT JOIN etykiety e ON e.id = i.etykieta_id
LEFT JOIN miejsca m ON m.id = e.miejscep
LEFT JOIN kody k ON k.kod = i.kod AND i.system_id = k.system_id
WHERE (i.etykieta_id = ? OR i.nrsap = ?)
  AND i.inwentaryzacja_id = ?
  AND i.nr_wspolny = ?
  AND i.proba = ?
  AND i.active = 1
ORDER BY i.ts ASC
LIMIT 1;
```

### 3. Inwentaryzacja Miejsc - PLANOWANA 📋

**Cel:** Inwentaryzacja lokalizacji magazynowych
**Workflow:** Lokalizacja → Skanowanie zawartości → Zapis

#### Przepływ Procesu

```mermaid
graph TD
    A[Start Inwentaryzacji Miejsc] --> B[Wybór/Tworzenie Sesji]
    B --> C[Skanowanie Lokalizacji MP-H-R-M-P]
    C --> D[Walidacja Lokalizacji]
    D --> E{Lokalizacja Poprawna?}
    E -->|Nie| F[Komunikat Błędu]
    E -->|Tak| G[Wczytanie Teoretycznej Zawartości]
    G --> H[Skanowanie Zawartości Miejsca]
    H --> I{Kod Etykiety/Produktu?}
    I -->|Etykieta| J[Identyfikacja Etykiety]
    I -->|Kod Produktu| K[Wyszukanie Produktu]
    J --> L[Wprowadzenie Ilości]
    K --> L
    L --> M[Dodanie do Listy Spisanej]
    M --> N{Więcej Pozycji?}
    N -->|Tak| H
    N -->|Nie| O[Podsumowanie Miejsca]
    O --> P[Zapis Wszystkich Pozycji]
    P --> Q[Oznaczenie Miejsca Jako Spisane]
    Q --> R[Przejście do Następnego Miejsca]
    R --> C
```

#### Zapytania SQL - Inwentaryzacja Miejsc

```sql
-- Wyszukiwanie teoretycznej zawartości miejsca
SELECT e.id as etykieta_id, e.etykieta_klient, e.kod_id,
       k.kod, k.kod_nazwa, e.ilosc, e.lot, e.data_waznosci,
       m.hala, m.regal, m.miejsce, m.poziom
FROM etykiety e
LEFT JOIN kody k ON k.id = e.kod_id
LEFT JOIN miejsca m ON m.id = e.miejscep
WHERE e.miejscep = ? AND e.active IS NOT NULL AND e.system_id = ?
ORDER BY k.kod;

-- Dodanie nowej pozycji inwentaryzacji miejsc
INSERT INTO inwentaryzacja(
    data, ilosc_spisana, kod, inwentaryzacja_id,
    hala, regal, miejsce, poziom, pracownik, ts, jm,
    system_id, etykieta_id, stat
) VALUES (
    CURDATE(), ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, 'MIEJSCE'
);
```

### 4. Inwentaryzacja GG - PLANOWANA 📋

**Cel:** Specjalny typ inwentaryzacji z dedykowaną logiką
**Status:** Wymagane szczegółowe wymagania biznesowe

## Obsługa Błędów i Wyjątków

### Standardowe Komunikaty

#### 1. Błędy Skanowania
```csharp
public class InvalidScanException : DomainException
{
    public InvalidScanException(string scannedCode)
        : base($"Nierozpoznany kod: {scannedCode}") { }
}

public class ItemNotInInventoryException : DomainException
{
    public ItemNotInInventoryException(string code, int sessionId)
        : base($"Brak w tej inwentaryzacji etykiety: {code}") { }
}
```

#### 2. Błędy Połączenia
```csharp
public class DatabaseConnectionException : InfrastructureException
{
    public DatabaseConnectionException()
        : base("Brak połączenia z bazą danych") { }
}
```

#### 3. Błędy Walidacji
```csharp
public class LocationNotActiveException : DomainException
{
    public LocationNotActiveException(string locationCode)
        : base($"Lokalizacja {locationCode} nie jest aktywna") { }
}
```

### Mechanizmy Odzyskiwania

#### 1. Retry Pattern dla Operacji Bazy Danych
```csharp
public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
{
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            return await operation();
        }
        catch (MySqlException ex) when (i < maxRetries - 1)
        {
            await Task.Delay(1000 * (i + 1)); // Exponential backoff
        }
    }
    throw new DatabaseConnectionException();
}
```

#### 2. Offline Mode (Tylko Inwentaryzacja Produktowa)
```csharp
// Checkbox w UI pozwala na pracę offline
if (_isOfflineMode)
{
    await _localStorageService.SaveInventoryItemAsync(item);
    await _syncService.ScheduleSyncWhenOnline();
}
```

## Monitorowanie Postępu

### Obliczanie Stanu Inwentaryzacji

```sql
-- Query zaimplementowany w GetInventoryStatusQuery
SELECT
    SUM(IF(ilosc_spisana IS NULL, 0, 1)) as ilosc_zliczona,
    COUNT(1) as stan,
    ROUND((SUM(IF(ilosc_spisana IS NULL, 0, 1)) / COUNT(1)) * 100, 1) as procent
FROM inwentaryzacja i
WHERE i.inwentaryzacja_id = ?
  AND i.kod != '10101'  -- Wykluczenie kodów technicznych
  AND i.active = 1
  AND i.system_id = ?;
```

### Dashboard Progress
```csharp
public record InventoryStatusResponse(
    int TotalItems,
    int CountedItems,
    decimal ProgressPercentage,
    TimeSpan ElapsedTime,
    TimeSpan EstimatedTimeRemaining
);
```

## Integracja z DataWedge (Zebra MC3300)

### Konfiguracja Profilu DataWedge
```xml
<!-- Intent Output -->
<intent-action>com.wms.INVENTORY_SCAN</intent-action>
<intent-category>android.intent.category.DEFAULT</intent-category>
<intent-delivery>2</intent-delivery> <!-- Broadcast Intent -->

<!-- Data Formatting -->
<data-prefix></data-prefix>
<data-suffix></data-suffix>
```

### Handler Skanów w MAUI
```csharp
[BroadcastReceiver(Enabled = true)]
[IntentFilter(new[] { "com.wms.INVENTORY_SCAN" })]
public class InventoryDataWedgeReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        var scannedData = intent.GetStringExtra("com.symbol.datawedge.data_string");
        var labelType = intent.GetStringExtra("com.symbol.datawedge.label_type");

        MessagingCenter.Send<string>(scannedData, "InventoryScanReceived");
    }
}
```

## Rejestracja Operacji w Systemie

### Tabela `operacje` - Audyt Trail
```sql
INSERT INTO operacje(
    etykieta_id, doc_type, doc_nr, imie_nazwisko,
    typ_operacji, system_id, wozek, operac_id, ilosc, ts_ins
) VALUES (
    ?, 'INW', ?, ?, 'INVENTORY_COUNT', ?, ?, ?, ?, NOW()
);
```

### Handler dla Operacji
```csharp
public class RegisterInventoryOperationHandler : INotificationHandler<InventoryItemCreated>
{
    public async Task Handle(InventoryItemCreated notification, CancellationToken cancellationToken)
    {
        var operation = new Operation
        {
            EtykietaId = notification.EtykietaId,
            DocType = "INW",
            DocNr = notification.SessionId.ToString(),
            ImieNazwisko = notification.UserName,
            TypOperacji = "INVENTORY_COUNT",
            SystemId = notification.SystemId,
            Ilosc = (int)notification.CountedQuantity,
            TsIns = DateTime.Now
        };

        await _operationRepository.AddAsync(operation);
    }
}
```

---

*Dokument zaktualizowany: 2025-01-14 - Przepływy procesów zdefiniowane zgodnie z Clean Architecture* ✅