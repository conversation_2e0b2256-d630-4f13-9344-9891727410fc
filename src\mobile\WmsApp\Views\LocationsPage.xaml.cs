using WmsApp.ViewModels;

namespace WmsApp.Views;

public partial class LocationsPage : ContentPage
{
    public LocationsPage(LocationsViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public LocationsViewModel VM => (LocationsViewModel)BindingContext;

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is LocationsViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}
