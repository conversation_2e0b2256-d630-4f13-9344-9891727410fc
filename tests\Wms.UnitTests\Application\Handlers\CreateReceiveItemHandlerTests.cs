using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Handlers;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Entities;
using Wms.Domain.Exceptions;

namespace Wms.UnitTests.Application.Handlers;

public class CreateReceiveItemHandlerTests
{
    private readonly Mock<IReceiveRepository> _mockReceiveRepository;
    private readonly Mock<IPalletRepository> _mockPalletRepository;
    private readonly Mock<ILabelRepository> _mockLabelRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<CreateReceiveItemHandler>> _mockLogger;
    private readonly Mock<NumberGenerationService> _mockNumberGenerationService;
    private readonly CreateReceiveItemHandler _handler;

    public CreateReceiveItemHandlerTests()
    {
        _mockReceiveRepository = new Mock<IReceiveRepository>();
        _mockPalletRepository = new Mock<IPalletRepository>();
        _mockLabelRepository = new Mock<ILabelRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<CreateReceiveItemHandler>>();
        _mockNumberGenerationService = new Mock<NumberGenerationService>();

        _handler = new CreateReceiveItemHandler(
            _mockReceiveRepository.Object,
            _mockPalletRepository.Object,
            _mockLabelRepository.Object,
            _mockUnitOfWork.Object,
            _mockNumberGenerationService.Object,
            _mockMapper.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateReceiveItemSuccessfully()
    {
        // Arrange
        var command = new CreateReceiveItemCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            PaletaId = 12345,
            KodId = 1001,
            Sscc = "123456789012345678",
            Lot = "LOT123",
            DataWaznosci = new DateOnly(2024, 12, 31),
            Ilosc = 50
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        var carrier = new NosnikDostaw
        {
            PaletaId = 12345,
            ListControlId = 1
        };

        var receiveItemDto = new ReceiveItemDto
        {
            Id = 1,
            PaletaId = 12345,
            KodId = 1001,
            Sscc = "123456789012345678",
            Lot = "LOT123",
            DataWaznosci = new DateOnly(2024, 12, 31),
            Ilosc = 50
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockPalletRepository
            .Setup(x => x.GetCarrierAsync(command.ListControlId, command.PaletaId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(carrier);

        _mockNumberGenerationService
            .Setup(x => x.GetNextLabelNumberAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(42);

        _mockUnitOfWork
            .Setup(x => x.ExecuteInTransactionAsync(It.IsAny<Func<Task>>()))
            .Returns(Task.CompletedTask);

        _mockMapper
            .Setup(x => x.Map<ReceiveItemDto>(It.IsAny<Label>()))
            .Returns(receiveItemDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(receiveItemDto);

        // Verify repository calls
        _mockReceiveRepository.Verify(
            x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()),
            Times.Once);

        _mockPalletRepository.Verify(
            x => x.GetCarrierAsync(command.ListControlId, command.PaletaId, It.IsAny<CancellationToken>()),
            Times.Once);

        _mockUnitOfWork.Verify(
            x => x.ExecuteInTransactionAsync(It.IsAny<Func<Task>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithReceiveNotFound_ShouldThrowReceiveNotFoundException()
    {
        // Arrange
        var command = new CreateReceiveItemCommand
        {
            ListControlId = 999,
            PracownikId = 100,
            PaletaId = 12345,
            KodId = 1001,
            Ilosc = 50
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ListControl)null!);

        // Act & Assert
        await Assert.ThrowsAsync<ReceiveNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));
    }
}
