namespace Wms.Application.DTOs.Inventory;

/// <summary>
/// Typy inwentaryzacji zgodnie z dokumentacją
/// </summary>
public enum InventoryType
{
    /// <summary>
    /// Inwentaryzacja Ogólna - kompleksowa inwentaryzacja z pełną kontrolą
    /// </summary>
    General = 1,
    
    /// <summary>
    /// Inwentaryzacja Produktowa - inwentaryzacja konkretnych produktów
    /// </summary>
    Product = 2,
    
    /// <summary>
    /// Inwentaryzacja GG - specjalny typ inwentaryzacji
    /// </summary>
    GG = 3,
    
    /// <summary>
    /// Inwentaryzacja Miejsc - inwentaryzacja lokalizacji
    /// </summary>
    Location = 4
}

/// <summary>
/// DTO dla listy inwentaryzacji
/// </summary>
public record InventoryDto
{
    public int Id { get; init; }
    public DateTime Data { get; init; }
    public string Opis { get; init; } = string.Empty;
    public int InwentaryzacjaId { get; init; }
    public string? NrWspolny { get; init; }
    public string? Proba { get; init; }
    public bool Active { get; init; }
    public InventoryType Type { get; init; }
}

/// <summary>
/// DTO dla szczegółów inwentaryzacji
/// </summary>
public record InventoryDetailsDto
{
    public int Id { get; init; }
    public DateTime Data { get; init; }
    public string Opis { get; init; } = string.Empty;
    public int InwentaryzacjaId { get; init; }
    public string? NrWspolny { get; init; }
    public string? Proba { get; init; }
    public bool Active { get; init; }
    public InventoryType Type { get; init; }
    public int TotalItems { get; init; }
    public int CompletedItems { get; init; }
    public decimal CompletionPercentage { get; init; }
}

/// <summary>
/// DTO dla sesji inwentaryzacji
/// </summary>
public record InventorySessionDto
{
    public int Id { get; init; }
    public int InventoryId { get; init; }
    public int PracownikId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
    public DateTime StartedAt { get; init; }
    public DateTime? EndedAt { get; init; }
    public bool IsActive { get; init; }
    public InventoryType Type { get; init; }
}

/// <summary>
/// DTO dla pozycji inwentaryzacji
/// </summary>
public record InventoryItemDto
{
    public int Id { get; init; }
    public int InwentaryzacjaId { get; init; }
    public DateTime Data { get; init; }
    public string? Opis { get; init; }
    public decimal Ilosc { get; init; }
    public decimal? IloscSpisana { get; init; }
    public string? Kod { get; init; }
    public string? KodNazwa { get; init; }
    public int Hala { get; init; }
    public int Regal { get; init; }
    public int Miejsce { get; init; }
    public int Poziom { get; init; }
    public int? Pracownik { get; init; }
    public DateTime Ts { get; init; }
    public string? Podkod { get; init; }
    public string? Skan { get; init; }
    public int Stat { get; init; }
    public string? EtykietaId { get; init; }
    public string? PaletaId { get; init; }
    public string? NrSap { get; init; }
    public int SystemId { get; init; }
    public string? Jm { get; init; }
    public bool IsCompleted => IloscSpisana.HasValue;
}

/// <summary>
/// Response dla skanowania w inwentaryzacji
/// </summary>
public record InventoryScanResponse
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; } = string.Empty;
    public ScanType ScanType { get; init; }
    public InventoryItemDto? FoundItem { get; init; }
    public ProductCodeDto? ProductInfo { get; init; }
    public bool RequiresNewEntry { get; init; }
}

/// <summary>
/// Typ skanu w inwentaryzacji
/// </summary>
public enum ScanType
{
    Unknown,
    Label,      // Etykieta
    Pallet,     // Paleta (DS lub SSCC)
    ProductCode // Kod produktu
}

/// <summary>
/// DTO dla postępu inwentaryzacji
/// </summary>
public record InventoryProgressDto
{
    public int InwentaryzacjaId { get; init; }
    public int TotalItems { get; init; }
    public int CompletedItems { get; init; }
    public decimal CompletionPercentage { get; init; }
    public string ProgressText => $"{CompletedItems}/{TotalItems}";
}

/// <summary>
/// DTO dla informacji o kodzie produktu
/// </summary>
public record ProductCodeDto
{
    public int Id { get; init; }
    public string Kod { get; init; } = string.Empty;
    public string? Kod2 { get; init; }
    public string KodNazwa { get; init; } = string.Empty;
    public int SystemId { get; init; }
    public string? Jm { get; init; }
    public string EanJednostki { get; init; } = string.Empty;
    public string? Ean { get; init; }
    public bool IsActive { get; init; }
}

/// <summary>
/// Request dla rozpoczęcia inwentaryzacji
/// </summary>
public record StartInventoryRequest
{
    public int InventoryId { get; init; }
    public string DeviceId { get; init; } = string.Empty;
}

/// <summary>
/// Request dla skanowania w inwentaryzacji
/// </summary>
public record InventoryScanRequest
{
    public int InventorySessionId { get; init; }
    public string ScanData { get; init; } = string.Empty;
    public string DeviceId { get; init; } = string.Empty;
}

/// <summary>
/// Request dla dodania/aktualizacji pozycji inwentaryzacji
/// </summary>
public record InventoryItemRequest
{
    public string? EtykietaId { get; init; }
    public string? PaletaId { get; init; }
    public string? Kod { get; init; }
    public decimal IloscSpisana { get; init; }
    public int Hala { get; init; }
    public int Regal { get; init; }
    public int Miejsce { get; init; }
    public int Poziom { get; init; }
    public string? Podkod { get; init; }
    public string? Skan { get; init; }
    public string? NrSap { get; init; }
}
