using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using WmsApp.Models.Receives;
using WmsApp.Services;

namespace WmsApp.ViewModels;

public partial class EmulatorScannerViewModel : ObservableObject
{
    private readonly IMockScannerService _mockScanner;
    private readonly IDeviceDetectionService _deviceDetection;
    private readonly ILogger<EmulatorScannerViewModel> _logger;

    [ObservableProperty]
    private string scanInput = string.Empty;

    [ObservableProperty]
    private string deviceInfo = string.Empty;

    [ObservableProperty]
    private DateTime lastScanTime = DateTime.Now;

    public ObservableCollection<string> RecentScans { get; } = new();

    public EmulatorScannerViewModel(
        IMockScannerService mockScanner,
        IDeviceDetectionService deviceDetection,
        ILogger<EmulatorScannerViewModel> logger)
    {
        _mockScanner = mockScanner;
        _deviceDetection = deviceDetection;
        _logger = logger;

        DeviceInfo = _deviceDetection.DeviceTypeInfo;
        
        _logger.LogInformation("EmulatorScannerViewModel initialized - Device: {DeviceInfo}", DeviceInfo);
    }

    [RelayCommand]
    private void SimulateScan()
    {
        if (string.IsNullOrWhiteSpace(ScanInput))
        {
            _logger.LogWarning("Attempted to scan empty input");
            return;
        }

        try
        {
            _logger.LogInformation("Simulating scan: {ScanInput}", ScanInput);
            
            _mockScanner.SimulateScan(ScanInput);
            
            // Dodaj do historii
            RecentScans.Insert(0, ScanInput);
            LastScanTime = DateTime.Now;
            
            // Ogranicz historię do 20 ostatnich skanów
            while (RecentScans.Count > 20)
            {
                RecentScans.RemoveAt(RecentScans.Count - 1);
            }

            // Wyczyść input po skanie
            ScanInput = string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error simulating scan: {ScanInput}", ScanInput);
        }
    }

    [RelayCommand]
    private void GenerateTestCode(string codeTypeString)
    {
        try
        {
            if (!Enum.TryParse<ReceiveScanCodeType>(codeTypeString, out var codeType))
            {
                _logger.LogWarning("Invalid code type: {CodeType}", codeTypeString);
                return;
            }

            var testCode = _mockScanner.GenerateTestCode(codeType);
            ScanInput = testCode;
            
            _logger.LogInformation("Generated {CodeType} test code: {TestCode}", codeType, testCode);
            
            // Automatycznie zaskanuj wygenerowany kod
            _mockScanner.SimulateScan(testCode, codeType);
            
            // Dodaj do historii
            RecentScans.Insert(0, $"{codeType}: {testCode}");
            LastScanTime = DateTime.Now;
            
            while (RecentScans.Count > 20)
            {
                RecentScans.RemoveAt(RecentScans.Count - 1);
            }

            // Wyczyść input
            ScanInput = string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating test code: {CodeType}", codeTypeString);
        }
    }

    [RelayCommand]
    private void Clear()
    {
        ScanInput = string.Empty;
        _logger.LogDebug("Cleared scan input");
    }

    [RelayCommand]
    private void ClearHistory()
    {
        RecentScans.Clear();
        _logger.LogDebug("Cleared scan history");
    }
}
