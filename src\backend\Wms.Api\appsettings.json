{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=************;Database=wmsggtest;User=test;Password=test123;Port=3306;"}, "JwtSettings": {"Issuer": "WMS-API", "Audience": "WMS-Mobile", "SecretKey": "your-256-bit-secret-key-here-minimum-32-characters", "ExpiryMinutes": 60}, "ApiSettings": {"Version": "1.0", "Title": "WMS API", "Description": "Warehouse Management System API for mobile applications"}}