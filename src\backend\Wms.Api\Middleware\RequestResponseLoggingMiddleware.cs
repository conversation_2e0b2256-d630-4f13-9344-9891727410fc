using System.Diagnostics;
using System.Text;
using System.Text.Json;

namespace Wms.Api.Middleware;

public class RequestResponseLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestResponseLoggingMiddleware> _logger;
    private readonly RequestResponseLoggingOptions _options;

    public RequestResponseLoggingMiddleware(
        RequestDelegate next, 
        ILogger<RequestResponseLoggingMiddleware> logger,
        RequestResponseLoggingOptions? options = null)
    {
        _next = next;
        _logger = logger;
        _options = options ?? new RequestResponseLoggingOptions();
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (!ShouldLog(context))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var correlationId = context.Items["CorrelationId"]?.ToString() ?? "unknown";
        
        // Log request
        var requestInfo = await LogRequestAsync(context, correlationId);
        
        // Capture original response stream
        var originalBodyStream = context.Response.Body;
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            
            // Log response
            await LogResponseAsync(context, requestInfo, responseBody, stopwatch.Elapsed);
            
            // Copy response back to original stream
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
    }

    private async Task<RequestInfo> LogRequestAsync(HttpContext context, string correlationId)
    {
        var request = context.Request;
        var requestInfo = new RequestInfo
        {
            Method = request.Method,
            Path = request.Path.Value ?? "",
            QueryString = request.QueryString.Value ?? "",
            Headers = GetSafeHeaders(request.Headers),
            UserAgent = request.Headers.UserAgent.FirstOrDefault() ?? "",
            RemoteIpAddress = context.Connection.RemoteIpAddress?.ToString() ?? "",
            ContentType = request.ContentType ?? "",
            ContentLength = request.ContentLength ?? 0,
            Timestamp = DateTimeOffset.UtcNow
        };

        // Read request body if it's reasonable size
        if (_options.LogRequestBody && 
            request.ContentLength.HasValue && 
            request.ContentLength > 0 && 
            request.ContentLength < _options.MaxBodySizeToLog &&
            IsLoggableContentType(request.ContentType))
        {
            request.EnableBuffering();
            using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
            requestInfo.Body = await reader.ReadToEndAsync();
            request.Body.Position = 0;
        }

        _logger.LogInformation("HTTP Request {Method} {Path}{QueryString} from {RemoteIp} - {UserAgent}",
            requestInfo.Method,
            requestInfo.Path,
            requestInfo.QueryString,
            requestInfo.RemoteIpAddress,
            requestInfo.UserAgent);

        if (_options.LogRequestHeaders)
        {
            _logger.LogDebug("Request Headers: {@Headers}", requestInfo.Headers);
        }

        if (!string.IsNullOrEmpty(requestInfo.Body))
        {
            _logger.LogDebug("Request Body: {Body}", requestInfo.Body);
        }

        return requestInfo;
    }

    private async Task LogResponseAsync(HttpContext context, RequestInfo requestInfo, 
        MemoryStream responseBody, TimeSpan elapsed)
    {
        var response = context.Response;
        responseBody.Seek(0, SeekOrigin.Begin);
        
        var responseInfo = new ResponseInfo
        {
            StatusCode = response.StatusCode,
            ContentType = response.ContentType ?? "",
            ContentLength = responseBody.Length,
            Headers = GetSafeHeaders(response.Headers),
            ElapsedMilliseconds = elapsed.TotalMilliseconds
        };

        // Read response body if it's reasonable size
        if (_options.LogResponseBody && 
            responseBody.Length > 0 && 
            responseBody.Length < _options.MaxBodySizeToLog &&
            IsLoggableContentType(response.ContentType))
        {
            using var reader = new StreamReader(responseBody, Encoding.UTF8, leaveOpen: true);
            responseInfo.Body = await reader.ReadToEndAsync();
            responseBody.Seek(0, SeekOrigin.Begin);
        }

        var logLevel = GetLogLevel(response.StatusCode, elapsed);
        
        _logger.Log(logLevel, 
            "HTTP Response {Method} {Path} responded {StatusCode} in {ElapsedMs}ms - {ContentLength} bytes",
            requestInfo.Method,
            requestInfo.Path,
            response.StatusCode,
            elapsed.TotalMilliseconds,
            responseBody.Length);

        if (_options.LogResponseHeaders)
        {
            _logger.LogDebug("Response Headers: {@Headers}", responseInfo.Headers);
        }

        if (!string.IsNullOrEmpty(responseInfo.Body))
        {
            _logger.LogDebug("Response Body: {Body}", responseInfo.Body);
        }

        // Log performance warning for slow requests
        if (elapsed.TotalMilliseconds > _options.SlowRequestThresholdMs)
        {
            _logger.LogWarning("Slow request detected: {Method} {Path} took {ElapsedMs}ms", 
                requestInfo.Method, requestInfo.Path, elapsed.TotalMilliseconds);
        }
    }

    private bool ShouldLog(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
        
        // Skip health check endpoints and metrics
        if (_options.SkipPaths.Any(skipPath => path.StartsWith(skipPath, StringComparison.OrdinalIgnoreCase)))
        {
            return false;
        }

        return true;
    }

    private static Dictionary<string, string> GetSafeHeaders(IHeaderDictionary headers)
    {
        var sensitiveHeaders = new[] { "authorization", "cookie", "x-api-key", "x-auth-token" };
        
        return headers
            .Where(h => !sensitiveHeaders.Contains(h.Key.ToLowerInvariant()))
            .ToDictionary(h => h.Key, h => string.Join(", ", h.Value.AsEnumerable()));
    }

    private static bool IsLoggableContentType(string? contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        var loggableTypes = new[]
        {
            "application/json",
            "application/xml",
            "text/plain",
            "text/xml",
            "application/x-www-form-urlencoded"
        };

        return loggableTypes.Any(type => contentType.StartsWith(type, StringComparison.OrdinalIgnoreCase));
    }

    private static LogLevel GetLogLevel(int statusCode, TimeSpan elapsed)
    {
        // Error responses
        if (statusCode >= 500)
            return LogLevel.Error;
        
        if (statusCode >= 400)
            return LogLevel.Warning;
        
        // Slow requests
        if (elapsed.TotalMilliseconds > 1000)
            return LogLevel.Warning;
        
        return LogLevel.Information;
    }
}

public class RequestResponseLoggingOptions
{
    public bool LogRequestBody { get; set; } = true;
    public bool LogResponseBody { get; set; } = true;
    public bool LogRequestHeaders { get; set; } = true;
    public bool LogResponseHeaders { get; set; } = false;
    public long MaxBodySizeToLog { get; set; } = 4096; // 4KB
    public double SlowRequestThresholdMs { get; set; } = 1000; // 1 second
    public List<string> SkipPaths { get; set; } = new() { "/health", "/metrics" };
}

public class RequestInfo
{
    public string Method { get; set; } = "";
    public string Path { get; set; } = "";
    public string QueryString { get; set; } = "";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string UserAgent { get; set; } = "";
    public string RemoteIpAddress { get; set; } = "";
    public string ContentType { get; set; } = "";
    public long ContentLength { get; set; }
    public string? Body { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}

public class ResponseInfo
{
    public int StatusCode { get; set; }
    public string ContentType { get; set; } = "";
    public long ContentLength { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public string? Body { get; set; }
    public double ElapsedMilliseconds { get; set; }
}

// Extension method for easy registration
public static class RequestResponseLoggingMiddlewareExtensions
{
    public static IApplicationBuilder UseRequestResponseLogging(
        this IApplicationBuilder builder, 
        RequestResponseLoggingOptions? options = null)
    {
        return builder.UseMiddleware<RequestResponseLoggingMiddleware>(options ?? new RequestResponseLoggingOptions());
    }
}
