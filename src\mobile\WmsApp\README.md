# WMS Mobile App

Aplikacja mobilna dla systemu zarządzania magazynem (WMS) zbudowana w technologii .NET MAUI.

## Funkcje

✅ **Zaimplementowane:**
- Logowanie przez skanowanie kodu lub wprowadzanie numeru karty
- Dashboard z głównym menu i nawigacją
- Zarządzanie lokalizacjami - lista, wyszukiwanie, statusy
- Zarządzanie paletami - lista, wyszukiwanie, operacje
- Integracja z API WMS (Refit)
- Pull-to-refresh i loading indicators
- Responsywny design z Material Design
- Obsługa wielu platform (Android, iOS, Windows, macOS)

🚧 **W planach:**
- Rzeczywiste skanowanie kodów QR/EAN
- Szczegółowe widoki lokalizacji i palet
- Operacje CRUD (tworzenie, edycja, usuwanie)
- Offline support i synchronizacja
- Powiadomienia push

## Architektura

Aplikacja wykorzystuje wzorzec MVVM (Model-View-ViewModel) z następującymi komponentami:

### Models
- `LoginScanRequest` - model żądania logowania
- `LoginScanResponse` - model odpowiedzi logowania 
- `UserInfo` - informacje o użytkowniku
- `MovePalletRequest/Response` - modele do operacji na paletach
- `LocationInfo` - informacje o lokalizacji
- `PalletInfo` - informacje o palecie
- `LabelInfo` - informacje o etykiecie

### Services
- `IWmsApiService` - interfejs API komunikujący się z backendem (używa Refit)

### ViewModels
- `LoginViewModel` - obsługa logiki logowania
- `MainViewModel` - obsługa głównego dashboard  
- `LocationsViewModel` - zarządzanie lokalizacjami
- `PalletsViewModel` - zarządzanie paletami

### Views
- `LoginPage` - strona logowania z symulacją skanowania
- `MainPage` - główna strona dashboard z menu
- `LocationsPage` - lista lokalizacji z wyszukiwaniem
- `PalletsPage` - lista palet z funkcjami zarządzania

## Technologie

- **.NET MAUI** - framework wieloplatformowy
- **CommunityToolkit.Mvvm** - MVVM helpers i source generators
- **Refit** - REST client dla API
- **CommunityToolkit.Maui** - dodatkowe kontrolki MAUI

## Konfiguracja

### Wymagania
- .NET 9.0 lub wyższy
- Visual Studio 2022 z pakietem .NET MAUI workload
- Android SDK (dla Androida)
- Xcode (dla iOS/macOS)

### Instalacja i uruchomienie

1. Przywróć pakiety NuGet:
   ```bash
   dotnet restore
   ```

2. Zbuduj aplikację:
   ```bash
   dotnet build
   ```

3. Uruchom na wybranej platformie:
   ```bash
   # Android
   dotnet run -f net9.0-android
   
   # iOS (tylko na macOS)
   dotnet run -f net9.0-ios
   
   # Windows
   dotnet run -f net9.0-windows10.0.19041.0
   ```

### Konfiguracja API

Adres API jest skonfigurowany w `MauiProgram.cs`:

```csharp
client.BaseAddress = new Uri("https://localhost:7000/");
```

Zmień ten adres na URL twojego API.

## Struktura projektu

```
WmsApp/
├── Models/
│   ├── Auth/
│   │   ├── LoginScanRequest.cs    # Żądania/odpowiedzi logowania
│   │   └── LoginResponse.cs       # UserInfo i tokeny
│   └── Pallets/
│       └── MovePalletRequest.cs   # Modele palet i lokalizacji
├── Services/
│   └── IWmsApiService.cs          # Interfejs API (Refit)
├── ViewModels/
│   ├── LoginViewModel.cs          # Logowanie i autentykacja
│   ├── MainViewModel.cs           # Dashboard główny
│   ├── LocationsViewModel.cs      # Zarządzanie lokalizacjami
│   └── PalletsViewModel.cs        # Zarządzanie paletami
├── Views/
│   ├── LoginPage.xaml             # Strona logowania
│   ├── MainPage.xaml              # Dashboard z menu
│   ├── LocationsPage.xaml         # Lista lokalizacji
│   └── PalletsPage.xaml           # Lista palet (planowane)
├── Converters/
│   └── ValueConverters.cs         # Konwertery UI (kolory, statusy)
└── Resources/
    └── Styles/                    # Style, kolory i motywy
```

## Funkcjonalności do zaimplementowania

- [ ] Skanowanie kodów QR/EAN (biblioteka ZXing)
- [ ] Zarządzanie sesjami użytkownika
- [ ] Obsługa offline
- [ ] Powiadomienia push
- [ ] Szczegółowe widoki palet i lokalizacji
- [ ] Operacje przenoszenia palet
- [ ] Historia operacji
- [ ] Raportowanie

## API Endpoints

Aplikacja komunikuje się z następującymi endpointami:

- `POST /api/v1.0/auth/login-scan` - logowanie przez skan karty
- `POST /api/v1.0/auth/logout` - wylogowanie
- `GET /api/v1.0/pallets/{palletCode}` - pobranie informacji o palecie
- `GET /api/v1.0/locations/{locationCode}` - pobranie informacji o lokalizacji
- `POST /api/v1.0/pallets/{palletCode}/move` - przeniesienie palety

## Optymalizacje XAML Bindingów ⚡

**Projekt wykorzystuje w pełni zoptymalizowane bindingi XAML dla maksymalnej wydajności!**

### ✅ Zaimplementowane optymalizacje:
- `MauiEnableXamlCBindingWithSourceCompilation=true` - kompilacja bindingów z Source
- `x:DataType` na wszystkich stronach i DataTemplates
- Structured binding dla wszystkich Picker kontrolek
- **0 ostrzeżeń XC0022 i XC0025** w całym projekcie

### 📚 Dokumentacja:
- **[Kompletny przewodnik](docs/XAML_BINDING_OPTIMIZATION.md)** - szczegółowa dokumentacja
- **[Ściągawka dla deweloperów](docs/XAML_BINDING_CHEATSHEET.md)** - szybka pomoc
- **[STYLE_GUIDE.md](docs/STYLE_GUIDE.md)** - zaktualizowany o sekcję XAML

### 🚨 WAŻNE dla deweloperów:
Przy dodawaniu nowego kodu XAML **ZAWSZE** dodawaj `x:DataType` - bez tego projekt nie przejdzie weryfikacji!

---

## Rozwój

### Dodawanie nowych funkcji

1. Dodaj modele do odpowiedniego folderu w `Models/`
2. Rozszerz `IWmsApiService` o nowe endpointy
3. Stwórz nowy ViewModel dziedziczący po `ObservableObject`
4. Stwórz odpowiedni widok XAML
5. Zarejestruj ViewModel i View w `MauiProgram.cs`
6. Dodaj routing w `AppShell.xaml` jeśli potrzebne

### Style i motywy

Style są zdefiniowane w:
- `Resources/Styles/Colors.xaml` - kolory
- `Resources/Styles/Styles.xaml` - style kontrolek
- `App.xaml` - globalne zasoby i konwertery

### Debugowanie

Aplikacja zawiera obsługę debugowania w trybie DEBUG. Logi są dostępne przez `Microsoft.Extensions.Logging.Debug`.

## Licencja

[Określ licencję projektu]
