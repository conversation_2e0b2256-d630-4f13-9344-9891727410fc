using Wms.Domain.ValueObjects;

namespace Wms.Application.DTOs;

/// <summary>
/// Wynik walidacji i parsowania skanu
/// </summary>
public record ScanValidationResult
{
    public bool IsValid { get; init; }
    public string RawData { get; init; } = string.Empty;
    public ScanType ScanType { get; init; }
    public string? ErrorMessage { get; init; }
    
    // GS1 specific fields
    public GS1ParseResult? GS1Data { get; init; }
    public bool HasIzPrefix { get; init; }
    
    // Quick access to common values
    public string? SSCC => GS1Data?.SSCC?.Value;
    public string? GTIN => GS1Data?.Gtin?.Value;
    public string? Lot => GS1Data?.Lot?.Value;
    public DateOnly? ExpiryDate => GS1Data?.ExpiryDate?.Value;
    public decimal? Quantity => GS1Data?.Quantity?.Value;
    
    // Context information
    public bool IsDeliveryContext => HasIzPrefix;
    public int ParsedFieldsCount => GS1Data?.ParsedFieldsCount ?? 0;
    
    /// <summary>
    /// Tworzy pomyślny wynik walidacji
    /// </summary>
    public static ScanValidationResult Success(string rawData, ScanType scanType, GS1ParseResult? gs1Data = null)
    {
        return new ScanValidationResult
        {
            IsValid = true,
            RawData = rawData,
            ScanType = scanType,
            GS1Data = gs1Data,
            HasIzPrefix = gs1Data?.HasIzPrefix ?? false
        };
    }
    
    /// <summary>
    /// Tworzy niepomyślny wynik walidacji
    /// </summary>
    public static ScanValidationResult Failure(string rawData, ScanType scanType, string errorMessage)
    {
        return new ScanValidationResult
        {
            IsValid = false,
            RawData = rawData,
            ScanType = scanType,
            ErrorMessage = errorMessage
        };
    }
    
    public override string ToString()
    {
        if (!IsValid)
            return $"ScanValidationResult {{ IsValid = False, Type = {ScanType}, Error = '{ErrorMessage}' }}";
            
        var info = $"IsValid = True, Type = {ScanType}";
        
        if (HasIzPrefix)
            info += ", HasIzPrefix = True";
            
        if (GS1Data != null)
            info += $", ParsedFields = {ParsedFieldsCount}";
            
        return $"ScanValidationResult {{ {info} }}";
    }
}

/// <summary>
/// Typ zeskanowanego kodu
/// </summary>
public enum ScanType
{
    Unknown = 0,
    GS1 = 1,
    SSCC = 2,
    DS = 3,
    LK = 4,
    Location = 5,
    Manual = 6
}
