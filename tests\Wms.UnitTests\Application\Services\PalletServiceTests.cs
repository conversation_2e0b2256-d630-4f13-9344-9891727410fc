using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;

namespace Wms.UnitTests.Application.Services;

public class PalletServiceTests
{
    private readonly Mock<ICodeValidationService> _mockCodeValidationService;
    private readonly Mock<ILocationRepository> _mockLocationRepository;
    private readonly Mock<ILabelRepository> _mockLabelRepository;
    private readonly Mock<IMovementRepository> _mockMovementRepository;
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<ILogger<PalletService>> _mockLogger;
    private readonly Mock<ITransaction> _mockTransaction;
    private readonly PalletService _palletService;

    public PalletServiceTests()
    {
        _mockCodeValidationService = new Mock<ICodeValidationService>();
        _mockLocationRepository = new Mock<ILocationRepository>();
        _mockLabelRepository = new Mock<ILabelRepository>();
        _mockMovementRepository = new Mock<IMovementRepository>();
        _mockUserRepository = new Mock<IUserRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockLogger = new Mock<ILogger<PalletService>>();
        _mockTransaction = new Mock<ITransaction>();

        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync())
            .ReturnsAsync(_mockTransaction.Object);

        // Zapewnienie, że Commit/Rollback zwracają poprawne Taski we wszystkich testach
        _mockTransaction.Setup(x => x.CommitAsync()).Returns(Task.CompletedTask);
        _mockTransaction.Setup(x => x.RollbackAsync()).Returns(Task.CompletedTask);

        // Globalna emulacja ExecuteInTransactionAsync dla wszystkich testów
        _mockUnitOfWork
            .Setup(x => x.ExecuteInTransactionAsync(It.IsAny<System.Func<Task>>()))
            .Returns<System.Func<Task>>(async operation =>
            {
                var tx = await _mockUnitOfWork.Object.BeginTransactionAsync();
                try
                {
                    await operation();
                    await tx.CommitAsync();
                }
                catch
                {
                    await tx.RollbackAsync();
                    throw;
                }
            });

        _palletService = new PalletService(
            _mockCodeValidationService.Object,
            _mockLocationRepository.Object,
            _mockLabelRepository.Object,
            _mockMovementRepository.Object,
            _mockUserRepository.Object,
            _mockUnitOfWork.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task MovePalletAsync_WithValidSSCCRequest_ShouldReturnMovePalletResponse()
    {
        // Arrange
        var userId = 1;
        var palletCode = "123456789012345678"; // SSCC
        var toLocationCode = "MP-1-A-1-1";
        
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode,
            Notes = "Test move"
        };

        var currentLocation = CreateTestLocation(1, "MP-1-B-2-1", "Current Location");
        var targetLocation = CreateTestLocation(2, toLocationCode, "Target Location");
        var pallet = CreateTestPallet(1, palletCode);
        var label = CreateTestLabel(1, palletCode, pallet, currentLocation);
        var user = CreateTestUser(userId, "Jan Kowalski");

        SetupValidMockCalls(palletCode, toLocationCode, label, targetLocation, user);

        // Act
        var result = await _palletService.MovePalletAsync(request, userId, "device-1", "192.168.1.1");

        // Assert
        result.Should().NotBeNull();
        result.PalletCode.Should().Be(palletCode);
        result.FromLocationCode.Should().Be(currentLocation.Code);
        result.ToLocationCode.Should().Be(targetLocation.Code);
        result.MovedBy.Should().Be(user.ImieNazwisko);

        // Verify all repositories were called correctly
        _mockMovementRepository.Verify(x => x.CreateAsync(It.Is<Movement>(m =>
            m.Typ == "ZM" &&
            m.PracownikId == userId &&
            m.Etykieta == label.Id &&
            m.StareM == currentLocation.Id &&
            m.NoweM == targetLocation.Id &&
            m.Uwagi == request.Notes
        )), Times.Once);

        _mockLabelRepository.Verify(x => x.UpdateLocationAsync(label.Id, label.SystemId, targetLocation.Id), Times.Once);
        _mockTransaction.Verify(x => x.CommitAsync(), Times.Once);
    }

    [Fact]
    public async Task MovePalletAsync_WithValidDSRequest_FoundByPalletId_ShouldReturnMovePalletResponse()
    {
        // Arrange
        var userId = 1;
        var palletCode = "DS123456"; // DS code
        var extractedPalletId = 123456;
        var toLocationCode = "MP-2-RMP-010-4";
        
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode
        };

        var currentLocation = CreateTestLocation(1, "MP-1-A-1-1", "Current Location");
        var targetLocation = CreateTestLocation(2, toLocationCode, "Target Location");
        var pallet = CreateTestPallet(extractedPalletId, "123456789012345678");
        var label = CreateTestLabel(1, "123456789012345678", pallet, currentLocation);
        label.PaletaId = extractedPalletId; // Ensure pallet ID matches extracted value
        var user = CreateTestUser(userId, "Anna Nowak");

        // Setup mocks for DS code validation
        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);

        // Setup new DS logic: first try by pallet ID, should succeed
        _mockLabelRepository.Setup(x => x.GetByPalletIdDirectAsync(extractedPalletId)).ReturnsAsync(label);
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync(targetLocation);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(extractedPalletId))
            .ReturnsAsync(new List<Label> { label });
        
        SetupCommonMockCalls(targetLocation, user);

        // Act
        var result = await _palletService.MovePalletAsync(request, userId);

        // Assert
        result.Should().NotBeNull();
        result.PalletCode.Should().Be(palletCode);
        result.FromLocationCode.Should().Be(currentLocation.Code);
        result.ToLocationCode.Should().Be(targetLocation.Code);
        result.MovedBy.Should().Be(user.ImieNazwisko);
        
        // Verify that pallet ID search was used (new logic)
        _mockLabelRepository.Verify(x => x.GetByPalletIdDirectAsync(extractedPalletId), Times.Once);
        _mockLabelRepository.Verify(x => x.GetByClientCodeAsNoTrackingAsync(palletCode), Times.Never); // Should not fallback
    }

    [Fact]
    public async Task MovePalletAsync_WithValidDSRequest_FallbackToClientCode_ShouldReturnMovePalletResponse()
    {
        // Arrange
        var userId = 1;
        var palletCode = "DS654321"; // DS code
        var extractedPalletId = 654321;
        var toLocationCode = "MP-2-RMP-010-4";
        
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode
        };

        var currentLocation = CreateTestLocation(1, "MP-1-A-1-1", "Current Location");
        var targetLocation = CreateTestLocation(2, toLocationCode, "Target Location");
        var pallet = CreateTestPallet(1, "123456789012345678");
        var label = CreateTestLabel(1, "123456789012345678", pallet, currentLocation);
        label.EtykietaKlient = palletCode; // Set as client code
        var user = CreateTestUser(userId, "Anna Nowak");

        // Setup mocks for DS code validation
        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);

        // Setup new DS logic: pallet ID search fails, fallback to client code succeeds
        _mockLabelRepository.Setup(x => x.GetByPalletIdDirectAsync(extractedPalletId)).ReturnsAsync((Label?)null);
        _mockLabelRepository.Setup(x => x.GetByClientCodeAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync(targetLocation);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(label.PaletaId!.Value))
            .ReturnsAsync(new List<Label> { label });
        
        SetupCommonMockCalls(targetLocation, user);

        // Act
        var result = await _palletService.MovePalletAsync(request, userId);

        // Assert
        result.Should().NotBeNull();
        result.PalletCode.Should().Be(palletCode);
        result.FromLocationCode.Should().Be(currentLocation.Code);
        result.ToLocationCode.Should().Be(targetLocation.Code);
        result.MovedBy.Should().Be(user.ImieNazwisko);
        
        // Verify that both search methods were called in correct order
        _mockLabelRepository.Verify(x => x.GetByPalletIdDirectAsync(extractedPalletId), Times.Once);
        _mockLabelRepository.Verify(x => x.GetByClientCodeAsNoTrackingAsync(palletCode), Times.Once);
    }

    [Theory]
    [InlineData("invalid-sscc")]
    [InlineData("12345")] // Too short for SSCC
    [InlineData("DSX123")] // Invalid DS format
    public async Task MovePalletAsync_WithInvalidPalletCode_ShouldThrowArgumentException(string invalidPalletCode)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = invalidPalletCode,
            ToLocationCode = "MP-1-A-1-1"
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(invalidPalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(invalidPalletCode)).Returns(false);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Theory]
    [InlineData("INVALID")]
    [InlineData("MP-1-A-1")]  // Missing level
    [InlineData("1-A-1-1")]   // Missing MP prefix
    public async Task MovePalletAsync_WithInvalidLocationCode_ShouldThrowArgumentException(string invalidLocationCode)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = "123456789012345678",
            ToLocationCode = invalidLocationCode
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(invalidLocationCode)).Returns(false);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithNonExistentPallet_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = "MP-1-A-1-1"
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync((Label?)null);

        // Act & Assert
        await Assert.ThrowsAsync<KeyNotFoundException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithLabelWithoutPallet_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = "MP-1-A-1-1"
        };

        var labelWithoutPallet = new Label
        {
            Id = 1,
            SystemId = 123,
            Sscc = palletCode,
            Pallet = null, // No associated pallet
            Location = CreateTestLocation(1, "MP-1-B-2-1", "Current Location")
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(labelWithoutPallet);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithLabelWithoutLocation_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = "MP-1-A-1-1"
        };

        var pallet = CreateTestPallet(1, palletCode);
        var labelWithoutLocation = new Label
        {
            Id = 1,
            SystemId = 123,
            Sscc = palletCode,
            Pallet = pallet,
            Location = null // No current location
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(labelWithoutLocation);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithNonExistentTargetLocation_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var toLocationCode = "MP-1-A-1-1";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode
        };

        var currentLocation = CreateTestLocation(1, "MP-1-B-2-1", "Current Location");
        var pallet = CreateTestPallet(1, palletCode);
        var label = CreateTestLabel(1, palletCode, pallet, currentLocation);

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(pallet.Id)).ReturnsAsync(new List<Label> { label });
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync((Location?)null);

        // Act & Assert
        await Assert.ThrowsAsync<KeyNotFoundException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithInvisibleTargetLocation_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var toLocationCode = "MP-1-A-1-1";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode
        };

        var currentLocation = CreateTestLocation(1, "MP-1-B-2-1", "Current Location");
        var targetLocation = CreateTestLocation(2, toLocationCode, "Target Location");
        targetLocation.Widoczne = 0; // Location is not visible
        
        var pallet = CreateTestPallet(1, palletCode);
        var label = CreateTestLabel(1, palletCode, pallet, currentLocation);

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(pallet.Id)).ReturnsAsync(new List<Label> { label });
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync(targetLocation);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WithFullTargetLocation_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var toLocationCode = "MP-1-A-1-1";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = toLocationCode
        };

        var currentLocation = CreateTestLocation(1, "MP-1-B-2-1", "Current Location");
        var targetLocation = CreateTestLocation(2, toLocationCode, "Target Location");
        targetLocation.MaxPojemnosc = 2; // Max capacity is 2
        
        var pallet = CreateTestPallet(1, palletCode);
        var label = CreateTestLabel(1, palletCode, pallet, currentLocation);

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(pallet.Id)).ReturnsAsync(new List<Label> { label });
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync(targetLocation);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(targetLocation.Id)).ReturnsAsync(2); // Already at max capacity

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _palletService.MovePalletAsync(request, 1));
    }

    [Fact]
    public async Task MovePalletAsync_WhenExceptionOccurs_ShouldRollbackTransaction()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var request = new MovePalletRequest
        {
            PalletCode = palletCode,
            ToLocationCode = "MP-1-A-1-1"
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);
        
        // Setup to throw exception during label retrieval
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode))
            .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => 
            _palletService.MovePalletAsync(request, 1));

        _mockTransaction.Verify(x => x.RollbackAsync(), Times.Once);
    }

    [Fact]
    public async Task GetPalletInfoAsync_WithValidSSCC_ShouldReturnPalletInfo()
    {
        // Arrange
        var palletCode = "123456789012345678";
        var location = CreateTestLocation(1, "MP-1-A-1-1", "Test Location");
        var pallet = CreateTestPallet(1, palletCode);
        var label = CreateTestLabel(1, palletCode, pallet, location);
        
        var allLabels = new List<Label> { label };
        var lastMovement = new Movement
        {
            Id = 1,
            Tszm = DateTime.UtcNow.AddHours(-1)
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(pallet.Id)).ReturnsAsync(allLabels);
        _mockMovementRepository.Setup(x => x.GetLastMovementForLabelAsync(label.Id)).ReturnsAsync(lastMovement);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(location.Id)).ReturnsAsync(3);

        // Act
        var result = await _palletService.GetPalletInfoAsync(palletCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(pallet.Id);
        result.MainSSCC.Should().Be(pallet.GetMainSSCC());
        result.CurrentLocation.Should().NotBeNull();
        result.CurrentLocation!.Code.Should().Be(location.Code);
        result.CurrentLocation.CurrentPalletCount.Should().Be(3);
        result.LastMovementAt.Should().Be(lastMovement.Tszm);
        result.Labels.Should().HaveCount(1);
        result.Labels.First().SSCC.Should().Be(label.Sscc);
    }

    [Fact]
    public async Task GetPalletInfoAsync_WithNonExistentPallet_ShouldReturnNull()
    {
        // Arrange
        var palletCode = "123456789012345678";

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync((Label?)null);

        // Act
        var result = await _palletService.GetPalletInfoAsync(palletCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetLocationInfoAsync_WithValidLocationCode_ShouldReturnLocationInfo()
    {
        // Arrange
        var locationCode = "MP-001-A-001-1";
        var location = CreateTestLocation(1, locationCode, "Test Location");

        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(locationCode)).Returns(true);
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(locationCode)).ReturnsAsync(location);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(location.Id)).ReturnsAsync(5);

        // Act
        var result = await _palletService.GetLocationInfoAsync(locationCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(location.Id);
        result.Code.Should().Be(location.Code);
        result.Hala.Should().Be(location.Hala);
        result.Regal.Should().Be(location.Regal);
        result.Miejsce.Should().Be(location.Miejsce);
        result.Poziom.Should().Be(location.Poziom);
        result.IsVisible.Should().Be(location.IsVisible);
        result.IsPickingLocation.Should().Be(location.IsPickingLocation);
        result.MaxCapacity.Should().Be((int)location.MaxPojemnosc);
        result.CurrentPalletCount.Should().Be(5);
    }

    [Fact]
    public async Task GetLocationInfoAsync_WithNonExistentLocation_ShouldReturnNull()
    {
        // Arrange
        var locationCode = "MP-001-A-001-1";

        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(locationCode)).Returns(true);
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(locationCode)).ReturnsAsync((Location?)null);

        // Act
        var result = await _palletService.GetLocationInfoAsync(locationCode);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetPalletInfoAsync_WithValidDSCode_FoundByPalletId_ShouldReturnPalletInfo()
    {
        // Arrange
        var dsCode = "DS3345469";
        var extractedPalletId = 3345469;
        var location = CreateTestLocation(1, "MP-1-A-1-1", "Test Location");
        var pallet = CreateTestPallet(extractedPalletId, "123456789012345678");
        var label = CreateTestLabel(1, "123456789012345678", pallet, location);
        label.PaletaId = extractedPalletId; // Ensure pallet ID matches
        
        var allLabels = new List<Label> { label };
        var lastMovement = new Movement
        {
            Id = 1,
            Tszm = DateTime.UtcNow.AddHours(-1)
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(dsCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(dsCode)).Returns(true);
        
        // DS code should be found by pallet ID first
        _mockLabelRepository.Setup(x => x.GetByPalletIdDirectAsync(extractedPalletId)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(extractedPalletId)).ReturnsAsync(allLabels);
        _mockMovementRepository.Setup(x => x.GetLastMovementForLabelAsync(label.Id)).ReturnsAsync(lastMovement);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(location.Id)).ReturnsAsync(3);

        // Act
        var result = await _palletService.GetPalletInfoAsync(dsCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(extractedPalletId);
        result.MainSSCC.Should().Be(pallet.GetMainSSCC());
        result.CurrentLocation.Should().NotBeNull();
        result.CurrentLocation!.Code.Should().Be(location.Code);
        result.LastMovementAt.Should().Be(lastMovement.Tszm);
        result.Labels.Should().HaveCount(1);
        
        // Verify that pallet ID search was attempted first
        _mockLabelRepository.Verify(x => x.GetByPalletIdDirectAsync(extractedPalletId), Times.Once);
        _mockLabelRepository.Verify(x => x.GetByClientCodeAsNoTrackingAsync(dsCode), Times.Never); // Should not fallback
    }

    [Fact]
    public async Task GetPalletInfoAsync_WithValidDSCode_NotFoundByPalletId_ShouldFallbackToClientCode()
    {
        // Arrange
        var dsCode = "DS1234567";
        var extractedPalletId = 1234567;
        var location = CreateTestLocation(1, "MP-1-A-1-1", "Test Location");
        var pallet = CreateTestPallet(1, "123456789012345678");
        var label = CreateTestLabel(1, "123456789012345678", pallet, location);
        label.EtykietaKlient = dsCode; // Set as client code
        
        var allLabels = new List<Label> { label };
        var lastMovement = new Movement
        {
            Id = 1,
            Tszm = DateTime.UtcNow.AddHours(-1)
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(dsCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(dsCode)).Returns(true);
        
        // First attempt by pallet ID should return null
        _mockLabelRepository.Setup(x => x.GetByPalletIdDirectAsync(extractedPalletId)).ReturnsAsync((Label?)null);
        // Fallback to client code should succeed
        _mockLabelRepository.Setup(x => x.GetByClientCodeAsNoTrackingAsync(dsCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(label.PaletaId!.Value)).ReturnsAsync(allLabels);
        _mockMovementRepository.Setup(x => x.GetLastMovementForLabelAsync(label.Id)).ReturnsAsync(lastMovement);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(location.Id)).ReturnsAsync(3);

        // Act
        var result = await _palletService.GetPalletInfoAsync(dsCode);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(pallet.Id);
        result.MainSSCC.Should().Be(pallet.GetMainSSCC());
        
        // Verify that both search methods were called in correct order
        _mockLabelRepository.Verify(x => x.GetByPalletIdDirectAsync(extractedPalletId), Times.Once);
        _mockLabelRepository.Verify(x => x.GetByClientCodeAsNoTrackingAsync(dsCode), Times.Once);
    }

    [Fact]
    public async Task GetPalletInfoAsync_WithInvalidDSCode_ShouldReturnNull()
    {
        // Arrange
        var invalidDsCode = "DSABC123"; // Non-numeric part

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(invalidDsCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(invalidDsCode)).Returns(true); // Validation passes but parsing should fail
        
        // Should not find by client code either
        _mockLabelRepository.Setup(x => x.GetByClientCodeAsNoTrackingAsync(invalidDsCode)).ReturnsAsync((Label?)null);

        // Act
        var result = await _palletService.GetPalletInfoAsync(invalidDsCode);

        // Assert
        result.Should().BeNull();
        
        // Verify that no pallet ID search was attempted (due to parsing failure)
        _mockLabelRepository.Verify(x => x.GetByPalletIdDirectAsync(It.IsAny<int>()), Times.Never);
        _mockLabelRepository.Verify(x => x.GetByClientCodeAsNoTrackingAsync(invalidDsCode), Times.Once);
    }

    // Helper methods for creating test objects
    private static Location CreateTestLocation(int id, string code, string description)
    {
        // Parse the location code to extract components (MP-H-R-M-P)
        var parts = code.Split('-');
        return new Location
        {
            Id = id,
            Hala = parts.Length > 1 && int.TryParse(parts[1], out var hala) ? hala : 1,
            Regal = parts.Length > 2 ? parts[2] : "A",
            Miejsce = parts.Length > 3 && int.TryParse(parts[3], out var miejsce) ? miejsce : 1,
            Poziom = parts.Length > 4 ? parts[4] : "1",
            Widoczne = 1, // Visible
            Zbiorka = 0, // Not picking location
            MaxPojemnosc = 10
        };
    }

    private static Pallet CreateTestPallet(int id, string mainSSCC)
    {
        return new Pallet
        {
            Id = id,
            TsUtworzenia = DateTime.UtcNow
        };
    }

    private static Label CreateTestLabel(int id, string sscc, Pallet pallet, Location location)
    {
        return new Label
        {
            Id = id,
            SystemId = 100 + id,
            Sscc = sscc,
            EtykietaKlient = $"CLIENT-{id}",
            Ilosc = 100.0m,
            DataWaznosci = DateOnly.FromDateTime(DateTime.Now.AddDays(30)),
            Lot = $"LOT{id}",
            Active = 1, // Active
            PaletaId = pallet.Id,
            Miejscep = location.Id,
            Pallet = pallet,
            Location = location,
            Ts = DateTime.UtcNow
        };
    }

    private static User CreateTestUser(int id, string fullName)
    {
        return new User
        {
            Id = id,
            ImieNazwisko = fullName,
            Login = $"user{id}",
            Stanowisko = "Operator",
            Email = $"user{id}@example.com",
            JednostkaId = 1,
            IsActive = true
        };
    }

    private void SetupValidMockCalls(string palletCode, string toLocationCode, Label label, Location targetLocation, User user)
    {
        _mockCodeValidationService.Setup(x => x.ValidateSSCC(palletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(toLocationCode)).Returns(true);
        
        _mockLabelRepository.Setup(x => x.GetBySSCCAsNoTrackingAsync(palletCode)).ReturnsAsync(label);
        _mockLabelRepository.Setup(x => x.GetActiveByPalletIdAsNoTrackingAsync(label.Pallet!.Id)).ReturnsAsync(new List<Label> { label });
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(toLocationCode)).ReturnsAsync(targetLocation);
        
        SetupCommonMockCalls(targetLocation, user);
    }

    private void SetupCommonMockCalls(Location targetLocation, User user)
    {
        _mockLocationRepository.Setup(x => x.GetByCodeAsync(targetLocation.Code)).ReturnsAsync(targetLocation);
        _mockLocationRepository.Setup(x => x.GetCurrentPalletCountAsync(targetLocation.Id)).ReturnsAsync(0);
        _mockUserRepository.Setup(x => x.GetByIdAsync(user.Id)).ReturnsAsync(user);
        
        _mockMovementRepository.Setup(x => x.CreateAsync(It.IsAny<Movement>())).ReturnsAsync(new Movement { Id = 1 });
        _mockLabelRepository.Setup(x => x.UpdateLocationAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>())).Returns(Task.CompletedTask);
        
        _mockTransaction.Setup(x => x.CommitAsync()).Returns(Task.CompletedTask);
        _mockTransaction.Setup(x => x.RollbackAsync()).Returns(Task.CompletedTask);
    }
}
