﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:WmsApp"
             xmlns:converters="clr-namespace:WmsApp.Converters"
             x:Class="WmsApp.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Converters -->
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:BoolToStringConverter x:Key="BoolToStringConverter" />
            <converters:IsNotNullConverter x:Key="IsNotNullConverter" />
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters:InverseBoolToColorConverter x:Key="InverseBoolToColorConverter" />
            <converters:BoolToStatusConverter x:Key="BoolToStatusConverter" />
            <converters:CapacityToProgressConverter x:Key="CapacityToProgressConverter" />
            <converters:CapacityToColorConverter x:Key="CapacityToColorConverter" />
            <converters:CountToBoolConverter x:Key="CountToBoolConverter" />
            <converters:HasExpiringLabelsConverter x:Key="HasExpiringLabelsConverter" />
            <converters:ApiEnvironmentDisplayConverter x:Key="ApiEnvironmentDisplayConverter" />
            
            <!-- Receives Module Converters -->
            <converters:BoolToOccupiedConverter x:Key="BoolToOccupiedConverter" />
            <converters:BoolToOccupiedColorConverter x:Key="BoolToOccupiedColorConverter" />
            <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
            <converters:ReceiveBoolToColorConverter x:Key="ReceiveBoolToColorConverter" />
            <converters:DecimalToBoolConverter x:Key="DecimalToBoolConverter" />
            <converters:StringEqualsConverter x:Key="StringEqualsConverter" />
            <converters:CollectionToBoolConverter x:Key="CollectionToBoolConverter" />
            <converters:IdToTypPaletyConverter x:Key="IdToTypPaletyConverter" />
            <converters:NullableDateTimeConverter x:Key="NullableDateTimeConverter" />
            <converters:NumberWithUnitConverter x:Key="NumberWithUnitConverter" />
            <converters:RegistrationStatusToColorConverter x:Key="RegistrationStatusToColorConverter" />
            <converters:SsccDisplayConverter x:Key="SsccDisplayConverter" />
            <converters:IntToBoolConverter x:Key="IntToBoolConverter" />
            <converters:TabIndexToColorConverter x:Key="TabIndexToColorConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
