# Moduł Inwentaryzacji - System WMS

## Przegląd

Moduł inwentaryzacji jest kluczowym składnikiem systemu WMS umożliwiającym przeprowadzanie różnych typów inwentaryzacji w magazynie. System obsługuje skanery Zebra MC3300 i integruje się z istniejącą bazą danych MySQL oraz architekturą Clean Architecture.

## Typy Inwentaryzacji

### 1. Inwentaryzacja Produktowa (`InventoryType.Product`)
- **Status implementacji**: ✅ **ZAIMPLEMENTOWANA**
- **Cel**: Inwentaryzacja konkretnych produktów i etykiet
- **Operacja**: Skanowanie kodów produktów, SSCC, DS
- **Workflow**: Kod produktu → Ilość → Lokalizacja → Zapis

### 2. Inwentaryzacja Ogólna (`InventoryType.General`)
- **Status implementacji**: 🔄 **PLANOWANA**
- **Cel**: Kompleksowa inwentaryzacja z pełną kontrolą
- **Operacja**: `CurrentOperacja = "19"`
- **Specyfika**: Obsługuje pola `nr_wspolny` i `proba`, może obsługiwać palety

### 3. Inwentaryzacja Miejsc (`InventoryType.Location`)
- **Status implementacji**: 🔄 **PLANOWANA**
- **Cel**: Inwentaryzacja lokalizacji magazynowych
- **Workflow**: Lokalizacja → Skanowanie zawartości → Zapis

### 4. Inwentaryzacja GG (`InventoryType.GG`)
- **Status implementacji**: 🔄 **PLANOWANA**
- **Cel**: Specjalny typ inwentaryzacji

## Architektura Modułu

### Backend (Clean Architecture)
```
src/backend/Wms.Application/Features/Inventory/
├── Commands/
│   └── InventoryCommands.cs          # Komendy CQRS dla inwentaryzacji
├── Queries/
│   └── InventoryQueries.cs           # Zapytania CQRS dla inwentaryzacji
└── Handlers/
    └── InventoryQueryHandlers.cs     # Handlery zapytań

src/backend/Wms.Application/DTOs/Inventory/
└── InventoryDtos.cs                  # DTOs dla inwentaryzacji

src/backend/Wms.Domain/Entities/Inventory/
└── InventoryEntity.cs                # Encje domenowe inwentaryzacji

src/backend/Wms.Application/Interfaces/
└── IInventoryRepository.cs           # Interfejs repozytorium
```

### Frontend (MAUI Android)
```
src/mobile/WmsApp/Models/Inventory/
└── InventoryModels.cs                # Modele i DTOs dla aplikacji mobilnej

src/mobile/WmsApp/Services/Inventory/
└── IInventoryService.cs              # Serwis biznesowy inwentaryzacji

src/mobile/WmsApp/ViewModels/Inventory/
├── InventorySelectionViewModel.cs    # ViewModel wyboru typu inwentaryzacji
└── InventoryProductViewModel.cs      # ViewModel inwentaryzacji produktowej

src/mobile/WmsApp/Views/Inventory/
├── InventorySelectionPage.xaml       # Strona wyboru typu inwentaryzacji
├── InventorySelectionPage.xaml.cs
├── InventoryProductPage.xaml         # Strona inwentaryzacji produktowej
└── InventoryProductPage.xaml.cs
```

## Nawigacja i Routing

- **Routing**: `//inventoryselection` - strona wyboru typu inwentaryzacji
- **Routing**: `//inventoryproduct` - strona inwentaryzacji produktowej
- **Nawigacja**: `MainViewModel.NavigateToInventoryAsync()` → wybór inwentaryzacji

## Integracja z Systemem

### Serwisy w MauiProgram.cs
```csharp
// Rejestracja serwisów inwentaryzacji
builder.Services.AddTransient<IInventoryService, InventoryService>();

// ViewModels
builder.Services.AddTransient<InventorySelectionViewModel>();
builder.Services.AddTransient<InventoryProductViewModel>();

// Views
builder.Services.AddTransient<InventorySelectionPage>();
builder.Services.AddTransient<InventoryProductPage>();
```

### Lokalizacja
- **Zasoby**: `AppResources.resx` (PL) i `AppResources.en.resx` (EN)
- **Klucze tłumaczeń**: `Inventory_*` dla wszystkich elementów UI
- **Użycie**: `{loc:Translate Key=Inventory_Title}`

## Kody Obsługiwane przez System

### 1. SSCC (Serial Shipping Container Code)
- **Format**: 18 cyfr
- **Wzorzec**: `^\d{18}$`
- **Przykład**: `123456789012345678`

### 2. Kod DS (Paleta wewnętrzna)
- **Format**: DS + 4-9 cyfr
- **Wzorzec**: `^DS\d{4,9}$`
- **Przykład**: `DS12345`, `DS123456789`

### 3. Lokalizacja
- **Format**: MP-H-R-M-P (Hala-Regał-Miejsce-Poziom)
- **Wzorzec**: `^MP-\d+-\d+-\d+-\d+$`
- **Przykład**: `MP-1-145-002-1`

### 4. Kod produktu (EAN)
- **Format**: Różne długości EAN
- **Walidacja**: Sprawdzanie w tabeli `kody`

## Przepływ Procesów

### Faza 1: Inicjalizacja
1. **Logowanie pracownika** - weryfikacja uprawnień
2. **Wybór typu inwentaryzacji** - 4 opcje do wyboru
3. **Wyszukanie aktywnych inwentaryzacji** - sprawdzenie w bazie danych
4. **Inicjalizacja formularza** - przygotowanie UI do pracy

### Faza 2: Skanowanie i Wprowadzanie Danych
1. **Skanowanie kodu** - DataWedge integration
2. **Dekodowanie i walidacja** - identyfikacja typu kodu
3. **Wyszukanie w systemie** - sprawdzenie w odpowiednich tabelach
4. **Wypełnienie formularza** - automatyczne uzupełnienie pól
5. **Wprowadzenie ilości** - input od użytkownika
6. **Wybór/weryfikacja lokalizacji** - potwierdzenie miejsca

### Faza 3: Walidacja i Zapis
1. **Walidacja danych** - sprawdzenie wymaganych pól
2. **Zapis do bazy** - transakcyjna operacja
3. **Rejestracja operacji** - audyt w tabeli `operacje`
4. **Aktualizacja stanu** - odświeżenie liczników
5. **Komunikat sukcesu** - potwierdzenie dla użytkownika

## Struktura Bazy Danych

### Główna Tabela: `inwentaryzacja`
- **Klucz główny**: `id` (auto-increment)
- **Sesja**: `inwentaryzacja_id` - grupowanie operacji
- **Produkt**: `kod`, `podkod` - identyfikacja produktu
- **Ilości**: `ilosc` (teoretyczna), `ilosc_spisana` (rzeczywista)
- **Lokalizacja**: `hala`, `regal`, `miejsce`, `poziom`
- **Audyt**: `pracownik`, `ts`, `system_id`

### Powiązania
- `etykieta_id` → `etykiety.id`
- `miejscep` → `miejsca.id`
- `kod` → `kody.kod` (+ system_id)
- `paleta_id` → `palety.id`

## API Endpoints

### Planowane Endpointy
- `GET /api/v1/inventory/sessions` - Lista aktywnych sesji inwentaryzacji
- `POST /api/v1/inventory/sessions/{id}/items` - Dodanie pozycji do inwentaryzacji
- `PUT /api/v1/inventory/items/{id}` - Aktualizacja pozycji
- `GET /api/v1/inventory/sessions/{id}/status` - Status postępu inwentaryzacji
- `POST /api/v1/inventory/scan` - Przetwarzanie skanów

## Stan Implementacji

### ✅ Ukończone
- Struktura modułu (backend + frontend)
- Inwentaryzacja produktowa (podstawowa funkcjonalność)
- Integracja z głównym menu aplikacji
- Lokalizacja (PL/EN)
- ViewModels z MVVM pattern
- Routing i nawigacja

### 🔄 W Toku
- API endpoints dla backendu
- Repository pattern dla bazy danych
- Pełna implementacja sesji inwentaryzacji

### 📋 Planowane
- Inwentaryzacja ogólna, miejsc, GG
- Zaawansowane funkcje (różnice, raporty)
- Integracja z drukarkami etykiet
- Eksport danych inwentaryzacji

## Dokumentacja Techniczna

- **[WORKFLOW.md](WORKFLOW.md)** - Szczegółowe przepływy procesów
- **[API.md](API.md)** - Specyfikacja endpointów API
- **[DATABASE.md](DATABASE.md)** - Schemat bazy danych
- **[MOBILE_GUIDE.md](MOBILE_GUIDE.md)** - Instrukcja użytkowania aplikacji mobilnej
- **[INTEGRATION.md](INTEGRATION.md)** - Integracja z DataWedge i skanerami

---

*Ostatnia aktualizacja: 2025-01-14 - Moduł inwentaryzacji zaimplementowany!* 🎉