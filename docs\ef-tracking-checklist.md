# EF Core Tracking - Checklist i Best Practices

Wersja: 1.0  
Data: 2025-09-10  
Zakres: Zasady używania AsNoTracking, AsTracking i AsNoTrackingWithIdentityResolution w WMS

## 1. Podstawowe zasady

### Ki<PERSON><PERSON>ć AsNoTracking
- **Zapytania tylko do odczytu** - gdy nie planujesz modyfikować encji
- **Projekcje do DTO** - gdy mapuje<PERSON> dane na obiekty transferu danych
- **Zapytania raportowe** - listy, statystyki, dashboardy
- **Walidacje** - sprawdzanie istnienia encji bez modyfikacji

```csharp
// ✅ DOBRZE - odczyt bez modyfikacji
public async Task<User?> GetByIdAsync(int id)
{
    return await _context.Users
        .AsNoTracking()
        .FirstOrDefaultAsync(u => u.Id == id);
}
```

### Kiedy u<PERSON> AsTracking (domyślne)
- **Modyfikacje encji** - gdy planu<PERSON> zmieniać właściwości
- **Operacje Create/Update/Delete** - gdy zapisujesz zmiany
- **Transakcje biznesowe** - gdy potrzebujesz śledzenia zmian

```csharp
// ✅ DOBRZE - pobranie do modyfikacji
public async Task<ListControl?> GetByIdForUpdateAsync(int id)
{
    return await _context.ListControls
        .FirstOrDefaultAsync(lc => lc.Id == id); // Domyślnie tracked
}
```

### Kiedy używać AsNoTrackingWithIdentityResolution
- **Zapytania z Include** - gdy ładujesz powiązane encje tylko do odczytu
- **Złożone grafy obiektów** - gdy potrzebujesz spójności referencji
- **Listy z nawigacjami** - gdy wyświetlasz dane z relacjami

```csharp
// ✅ DOBRZE - Include z AsNoTrackingWithIdentityResolution
public async Task<IEnumerable<ListControl>> GetAvailableReceivesAsync()
{
    return await _context.ListControls
        .AsNoTrackingWithIdentityResolution()
        .Include(lc => lc.RealizujacyPracownik)
        .Include(lc => lc.MiejsceDostawy)
        .ToListAsync();
}
```

## 2. Wzorce implementacyjne

### Repository Pattern
```csharp
public interface IReceiveRepository
{
    // Read-only queries (AsNoTracking/AsNoTrackingWithIdentityResolution)
    Task<ListControl?> GetByIdAsync(int id);
    Task<IEnumerable<ListControl>> GetAvailableReceivesAsync();
    
    // Tracked queries for updates
    Task<ListControl?> GetByIdForUpdateAsync(int id);
    
    // Modification operations
    Task<bool> ClaimReceiveAsync(int receiveId, int userId);
}
```

### Application Layer Handlers
```csharp
public class ClaimReceiveHandler : IRequestHandler<ClaimReceiveCommand, ClaimReceiveResponse>
{
    public async Task<ClaimReceiveResponse> Handle(ClaimReceiveCommand request)
    {
        // Walidacja - AsNoTracking
        var receive = await _receiveRepository.GetByIdAsync(request.ReceiveId);
        if (receive == null)
            throw new ReceiveNotFoundException(request.ReceiveId);

        // Modyfikacja - używa tracked entity w repository
        var claimed = await _receiveRepository.ClaimReceiveAsync(request.ReceiveId, request.UserId);
        
        await _unitOfWork.SaveChangesAsync();
        return new ClaimReceiveResponse { Success = claimed };
    }
}
```

## 3. Antywzorce (czego unikać)

### ❌ Globalny NoTracking w interceptorach
```csharp
// ❌ ŹLE - nie ustawiaj globalnie
public override async ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(...)
{
    // NIE ROB TEGO:
    // eventData.Context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
    return await base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
}
```

### ❌ Update(entity) na całym grafie
```csharp
// ❌ ŹLE - Update na encji z AsNoTracking
var entity = await _context.Entities.AsNoTracking().FirstAsync();
entity.Property = newValue;
_context.Update(entity); // Może nadpisać wszystkie właściwości!
```

### ❌ Mieszanie tracked i untracked
```csharp
// ❌ ŹLE - konflikt śledzenia
var entity1 = await _context.Entities.AsNoTracking().FirstAsync(e => e.Id == 1);
var entity2 = await _context.Entities.FirstAsync(e => e.Id == 1); // Błąd!
```

## 4. Checklisty

### Checklist dla Infrastructure (Repository)
- [ ] Metody GET/Search używają AsNoTracking lub AsNoTrackingWithIdentityResolution
- [ ] Metody z Include używają AsNoTrackingWithIdentityResolution
- [ ] Metody ForUpdate używają domyślnego tracking
- [ ] Metody modyfikujące (Claim/Release) używają tracked entities
- [ ] Brak globalnych ustawień QueryTrackingBehavior

### Checklist dla Application (Handlers)
- [ ] Walidacje używają metod AsNoTracking z repository
- [ ] Modyfikacje używają metod tracked lub ForUpdate
- [ ] Brak bezpośredniego Update() na encjach AsNoTracking
- [ ] SaveChanges wywoływane przez UnitOfWork
- [ ] Transakcje używają ExecuteInTransactionAsync

### Checklist dla testów
- [ ] Testy Claim/Release sprawdzają zapis w DB
- [ ] Testy GET sprawdzają brak tracking (EntityState.Detached)
- [ ] Testy modyfikacji używają tracked entities
- [ ] Mock repository zwraca odpowiednie typy (tracked/untracked)

## 5. Przykłady kodu

### Przykład 1: Repository z tracking patterns
```csharp
public class ReceiveRepository : IReceiveRepository
{
    public async Task<ListControl?> GetByIdAsync(int id)
    {
        return await _context.ListControls
            .AsNoTracking()
            .FirstOrDefaultAsync(lc => lc.Id == id);
    }

    public async Task<ListControl?> GetByIdForUpdateAsync(int id)
    {
        return await _context.ListControls
            .FirstOrDefaultAsync(lc => lc.Id == id);
    }

    public async Task<bool> ClaimReceiveAsync(int receiveId, int userId)
    {
        var receive = await GetByIdForUpdateAsync(receiveId);
        if (receive?.RealizujacyPracownikId != null) return false;
        
        receive.RealizujacyPracownikId = userId;
        return true;
    }
}
```

### Przykład 2: Handler z właściwym użyciem tracking
```csharp
public class CreateReceiveItemHandler : IRequestHandler<CreateReceiveItemCommand, ReceiveItemDto>
{
    public async Task<ReceiveItemDto> Handle(CreateReceiveItemCommand request)
    {
        // Walidacja - AsNoTracking
        var receive = await _receiveRepository.GetByIdAsync(request.ListControlId);
        if (receive == null) throw new ReceiveNotFoundException(request.ListControlId);

        await _unitOfWork.ExecuteInTransactionAsync(async () =>
        {
            // Tworzenie nowej encji - automatycznie tracked
            var label = new Label
            {
                ListcontrolId = request.ListControlId,
                PaletaId = request.PaletaId,
                // ... inne właściwości
            };
            
            _context.Labels.Add(label); // Tracked
        });

        return _mapper.Map<ReceiveItemDto>(labelResult);
    }
}
```

## 6. Diagnostyka i debugging

### Sprawdzanie stanu tracking
```csharp
// W testach lub debugging
var entity = await repository.GetByIdAsync(1);
var state = _context.Entry(entity).State; // Powinno być Detached dla AsNoTracking

var trackedEntity = await repository.GetByIdForUpdateAsync(1);
var trackedState = _context.Entry(trackedEntity).State; // Powinno być Unchanged
```

### Logowanie zapytań EF
```csharp
// W appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  }
}
```

## 7. Performance tips

- AsNoTracking może być 10-30% szybsze dla dużych zapytań
- AsNoTrackingWithIdentityResolution ma minimalny overhead vs AsNoTracking
- Unikaj ładowania dużych grafów obiektów z tracking
- Używaj projekcji (Select) zamiast Include gdy to możliwe

---

**Pamiętaj**: Decyzja o tracking powinna być podejmowana per-zapytanie w repozytoriach, nie globalnie!
