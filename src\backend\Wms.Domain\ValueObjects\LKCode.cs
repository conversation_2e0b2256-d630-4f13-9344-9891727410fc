using System.Text.RegularExpressions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący kod LK (List Control identifier)
/// Format: LK + 1-8 cyfr (np. LK1, LK123, LK12345678)
/// </summary>
public record LKCode
{
    private static readonly Regex LKPattern = new(@"^LK\d{1,8}$", RegexOptions.Compiled);
    
    public string Value { get; }
    public int Id { get; }

    private LKCode(string value, int id)
    {
        Value = value;
        Id = id;
    }

    /// <summary>
    /// Tworzy LKCode z stringa (np. "LK123")
    /// </summary>
    public static LKCode FromString(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("LK code nie może być pusty", nameof(value));

        if (!LKPattern.IsMatch(value))
            throw new ArgumentException($"Nieprawidłowy format LK: {value}. Oczekiwany format: LK + 1-8 cyfr", nameof(value));

        var numberPart = value[2..]; // Remove "LK" prefix
        if (!int.TryParse(numberPart, out var id))
            throw new ArgumentException($"Nie można sparsować ID z LK: {value}", nameof(value));

        return new LKCode(value, id);
    }

    /// <summary>
    /// Tworzy LKCode z ID (np. 123 -> "LK123")
    /// </summary>
    public static LKCode FromId(int id)
    {
        if (id <= 0)
            throw new ArgumentException("LK ID musi być większe od 0", nameof(id));

        if (id > 99999999) // max 8 cyfr
            throw new ArgumentException("LK ID nie może być większe niż 99999999", nameof(id));

        return new LKCode($"LK{id}", id);
    }

    /// <summary>
    /// Waliduje czy string może być LK kodem
    /// </summary>
    public static bool IsValid(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        return LKPattern.IsMatch(value);
    }

    public override string ToString() => Value;
    
    public static implicit operator string(LKCode lkCode) => lkCode.Value;
    public static explicit operator LKCode(string value) => FromString(value);
}
