using Serilog.Context;

namespace Wms.Api.Middleware;

public class CorrelationIdMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CorrelationIdMiddleware> _logger;
    
    private const string CorrelationIdHeaderName = "X-Correlation-ID";
    private const string CorrelationIdPropertyName = "CorrelationId";

    public CorrelationIdMiddleware(RequestDelegate next, ILogger<CorrelationIdMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var correlationId = GetOrGenerateCorrelationId(context);
        
        // Add correlation ID to HTTP headers
        if (!context.Response.HasStarted)
        {
            context.Response.Headers[CorrelationIdHeaderName] = correlationId;
        }
        
        // Add correlation ID to HttpContext for easy access
        context.Items[CorrelationIdPropertyName] = correlationId;
        
        // Push correlation ID to Serilog LogContext
        using (LogContext.PushProperty(CorrelationIdPropertyName, correlationId))
        {
            _logger.LogDebug("Processing request {Method} {Path} with correlation ID {CorrelationId}", 
                context.Request.Method, context.Request.Path, correlationId);
            
            await _next(context);
            
            _logger.LogDebug("Completed request {Method} {Path} with status {StatusCode}", 
                context.Request.Method, context.Request.Path, context.Response.StatusCode);
        }
    }

    private static string GetOrGenerateCorrelationId(HttpContext context)
    {
        // Try to get correlation ID from incoming request header
        if (context.Request.Headers.TryGetValue(CorrelationIdHeaderName, out var correlationIdFromHeader))
        {
            var existingCorrelationId = correlationIdFromHeader.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(existingCorrelationId))
            {
                return existingCorrelationId!;
            }
        }
        
        // Generate new correlation ID
        return Guid.NewGuid().ToString("D");
    }
}

// Extension method for easy registration
public static class CorrelationIdMiddlewareExtensions
{
    public static IApplicationBuilder UseCorrelationId(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<CorrelationIdMiddleware>();
    }
}

// Helper service for accessing correlation ID in application code
public interface ICorrelationIdService
{
    string GetCorrelationId();
}

public class CorrelationIdService : ICorrelationIdService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CorrelationIdService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string GetCorrelationId()
    {
        var context = _httpContextAccessor.HttpContext;
        if (context?.Items.ContainsKey("CorrelationId") == true)
        {
            return context.Items["CorrelationId"]?.ToString() ?? Guid.NewGuid().ToString("D");
        }
        
        return Guid.NewGuid().ToString("D");
    }
}
