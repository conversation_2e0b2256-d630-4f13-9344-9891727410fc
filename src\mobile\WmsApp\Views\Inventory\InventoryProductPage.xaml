<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.Inventory.InventoryProductPage"
             x:Name="InventoryProductPageRoot"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels.Inventory"
             xmlns:models="clr-namespace:WmsApp.Models.Inventory"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:InventoryProductViewModel"
             Title="{loc:Translate Key=InventoryProduct_Title}">



    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Header -->
            <Frame BackgroundColor="{StaticResource InventoryColor}" 
                   CornerRadius="10" 
                   Padding="15">
                <StackLayout>
                    <Label Text="{loc:Translate Key=InventoryProduct_Header}" 
                           FontSize="18" 
                           FontAttributes="Bold" 
                           TextColor="White" 
                           HorizontalOptions="Center" />
                    <Label Text="{loc:Translate Key=InventoryProduct_Subtitle}" 
                           FontSize="14" 
                           TextColor="White" 
                           HorizontalOptions="Center" 
                           Margin="0,5,0,0" />
                </StackLayout>
            </Frame>

            <!-- Loading Indicator -->
            <ActivityIndicator IsVisible="{Binding IsLoading}" 
                               IsRunning="{Binding IsLoading}" 
                               Color="{StaticResource Primary}" 
                               HeightRequest="50" />

            <!-- Error Message -->
            <Frame IsVisible="{Binding ErrorMessage, Converter={StaticResource StringToBoolConverter}}" 
                   BackgroundColor="LightCoral" 
                   CornerRadius="8" 
                   Padding="15">
                <Label Text="{Binding ErrorMessage}" 
                       TextColor="DarkRed" 
                       FontSize="14" 
                       HorizontalOptions="Center" />
            </Frame>

            <!-- Session Status -->
            <Frame IsVisible="{Binding IsSessionActive}" 
                   BackgroundColor="LightGreen" 
                   CornerRadius="8" 
                   Padding="15">
                <StackLayout>
                    <Label Text="{loc:Translate Key=InventoryProduct_SessionActive}" 
                           FontSize="14" 
                           FontAttributes="Bold" 
                           TextColor="DarkGreen" 
                           HorizontalOptions="Center" />
                    <Label Text="{Binding SelectedInventory.Opis}" 
                           FontSize="12" 
                           TextColor="DarkGreen" 
                           HorizontalOptions="Center" />
                </StackLayout>
            </Frame>

            <!-- Inventory Selection -->
            <StackLayout IsVisible="{Binding IsSessionActive, Converter={StaticResource InverseBoolConverter}}" 
                         Spacing="15">
                
                <Label Text="{loc:Translate Key=InventoryProduct_SelectInventory}" 
                       FontSize="16" 
                       FontAttributes="Bold" 
                       Margin="0,10,0,5" />

                <CollectionView ItemsSource="{Binding ActiveInventories}" 
                                SelectionMode="Single"
                                SelectedItem="{Binding SelectedInventory}"
                                IsVisible="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="models:InventoryDto">
                            <Frame BackgroundColor="White" 
                                   CornerRadius="8" 
                                   HasShadow="True" 
                                   Padding="15" 
                                   Margin="0,5">
                                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                                    <StackLayout Grid.Column="0" Spacing="3">
                                        <Label Text="{Binding Opis}" 
                                               FontSize="14" 
                                               FontAttributes="Bold" />
                                        <Label Text="{Binding FormattedDate}" 
                                               FontSize="12" 
                                               TextColor="Gray" />
                                        <Label Text="{Binding TypeDisplayName}" 
                                               FontSize="11" 
                                               TextColor="DarkBlue" />
                                    </StackLayout>
                                    
                                    <Label Grid.Column="1" 
                                           Text="▶" 
                                           FontSize="20" 
                                           TextColor="Green" 
                                           VerticalOptions="Center" />
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- No Inventories Message -->
                <Frame IsVisible="{Binding ActiveInventories.Count, Converter={StaticResource IntToBoolConverter}, ConverterParameter=0}" 
                       BackgroundColor="LightYellow" 
                       CornerRadius="8" 
                       Padding="15">
                    <Label Text="{loc:Translate Key=InventoryProduct_NoActiveInventories}" 
                           FontSize="14" 
                           TextColor="DarkOrange" 
                           HorizontalOptions="Center" />
                </Frame>
            </StackLayout>

            <!-- Scanning Section -->
            <StackLayout IsVisible="{Binding IsSessionActive}" 
                         Spacing="15">
                
                <Label Text="{loc:Translate Key=InventoryProduct_ScanSection}" 
                       FontSize="16" 
                       FontAttributes="Bold" 
                       Margin="0,10,0,5" />

                <Frame BackgroundColor="White" 
                       CornerRadius="8" 
                       HasShadow="True" 
                       Padding="15">
                    <StackLayout Spacing="10">
                        <Label Text="{loc:Translate Key=InventoryProduct_ScanPrompt}" 
                               FontSize="14" />
                        
                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                            <Entry Grid.Column="0" 
                                   Text="{Binding ScanInput}" 
                                   Placeholder="{loc:Translate Key=InventoryProduct_ScanPlaceholder}" 
                                   FontSize="16" 
                                   HeightRequest="50" 
                                   BackgroundColor="LightGray" />
                            
                            <Button Grid.Column="1" 
                                    Text="{loc:Translate Key=Common_Process}" 
                                    Command="{Binding ProcessScanCommand}" 
                                    BackgroundColor="{StaticResource Primary}" 
                                    TextColor="White" 
                                    FontSize="14" 
                                    WidthRequest="100" 
                                    HeightRequest="50" />
                        </Grid>
                    </StackLayout>
                </Frame>
            </StackLayout>

            <!-- Action Buttons -->
            <StackLayout Orientation="Horizontal" 
                         HorizontalOptions="FillAndExpand" 
                         Spacing="10" 
                         Margin="0,20,0,0">
                
                <!-- Session Active Buttons -->
                <StackLayout IsVisible="{Binding IsSessionActive}" 
                             Orientation="Horizontal" 
                             HorizontalOptions="FillAndExpand" 
                             Spacing="10">
                    <Button Text="{loc:Translate Key=InventoryProduct_EndSession}" 
                            Command="{Binding EndSessionCommand}" 
                            BackgroundColor="Red" 
                            TextColor="White" 
                            FontSize="14" 
                            HorizontalOptions="FillAndExpand" />
                </StackLayout>

                <!-- Session Inactive Buttons -->
                <StackLayout IsVisible="{Binding IsSessionActive, Converter={StaticResource InverseBoolConverter}}" 
                             Orientation="Horizontal" 
                             HorizontalOptions="FillAndExpand" 
                             Spacing="10">
                    <Button Text="{loc:Translate Key=Common_Refresh}" 
                            Command="{Binding RefreshCommand}" 
                            BackgroundColor="{StaticResource Secondary}" 
                            TextColor="White" 
                            FontSize="14" 
                            HorizontalOptions="FillAndExpand" />
                    
                    <Button Text="{loc:Translate Key=Common_Back}" 
                            Command="{Binding GoBackCommand}" 
                            BackgroundColor="{StaticResource Tertiary}" 
                            TextColor="White" 
                            FontSize="14" 
                            HorizontalOptions="FillAndExpand" />
                </StackLayout>
            </StackLayout>

        </StackLayout>
    </ScrollView>
</ContentPage>
