#!/bin/bash

# Quick API test script for DS3345469 pallet
# Usage: ./test-ds-api.sh [card_number]

API_BASE="http://***********/api/v1"
CARD_NUMBER=${1:-"1234567"}
PALLET_CODE="DS3345469"

echo "🔍 WMS API Diagnostic Test"
echo "=========================="
echo "API Base: $API_BASE"
echo "Card Number: $CARD_NUMBER"
echo "Pallet Code: $PALLET_CODE"
echo ""

# Step 1: Test API health/connectivity
echo "1️⃣ Testing API connectivity..."
curl -s -H "Accept: application/json" "$API_BASE/../health" > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ API server is reachable"
else
    echo "❌ Cannot reach API server at $API_BASE"
    echo "   Check network connection or server status"
    exit 1
fi

# Step 2: Authenticate and get token
echo ""
echo "2️⃣ Authenticating with card $CARD_NUMBER..."
AUTH_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -X POST "$API_BASE/auth/login-scan" \
    -d "{\"cardNumber\": \"$CARD_NUMBER\", \"deviceId\": \"test-device\", \"ipAddress\": \"127.0.0.1\"}")

if [ $? -ne 0 ]; then
    echo "❌ Authentication request failed"
    exit 1
fi

# Extract token from response
TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ Authentication failed - no token received"
    echo "Response: $AUTH_RESPONSE"
    exit 1
else
    echo "✅ Authentication successful"
    echo "Token: ${TOKEN:0:20}..."
fi

# Step 3: Test DS3345469 pallet lookup
echo ""
echo "3️⃣ Testing pallet lookup for $PALLET_CODE..."
PALLET_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -X GET "$API_BASE/pallets/$PALLET_CODE")

if [ $? -ne 0 ]; then
    echo "❌ Pallet lookup request failed"
    exit 1
fi

# Check if pallet was found
PALLET_ID=$(echo "$PALLET_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$PALLET_ID" ]; then
    echo "❌ Pallet $PALLET_CODE not found"
    echo "Response: $PALLET_RESPONSE"
    exit 1
else
    # Extract location info
    LOCATION_CODE=$(echo "$PALLET_RESPONSE" | grep -o '"code":"[^"]*"' | cut -d'"' -f4)
    LABELS_COUNT=$(echo "$PALLET_RESPONSE" | grep -o '"labels":\[[^]]*\]' | grep -o '{"id"' | wc -l)
    
    echo "✅ Pallet $PALLET_CODE found!"
    echo "   ID: $PALLET_ID"
    echo "   Location: $LOCATION_CODE"
    echo "   Labels: $LABELS_COUNT"
fi

echo ""
echo "🎯 Test Results:"
echo "================"
echo "✅ API Server: Reachable"
echo "✅ Authentication: Working"
echo "✅ Pallet DS3345469: Found (ID: $PALLET_ID)"
echo "✅ Location: $LOCATION_CODE"
echo ""
echo "🔧 Diagnosis: API is working correctly"
echo "📱 If mobile app shows 'Paleta nie została znaleziona':"
echo "   → Check API environment setting in mobile app"
echo "   → Should be set to 'Serwer produkcyjny (***********)'"
echo "   → Not 'Mock (Dane testowe)'"
