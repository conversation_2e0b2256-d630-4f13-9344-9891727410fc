namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący wyniki parsowania kodu GS1-128
/// Obsługuje Application Identifiers: 00 (SSCC), 02 (GTIN), 10 (Lot), 17 (ExpiryDate), 37 (Quantity)
/// Obsługuje również prefiks IZ dla rejestracji dostaw
/// </summary>
public record GS1ParseResult
{
    public bool IsSuccess { get; private init; }
    public SSCCCode? SSCC { get; private init; } // AI 00
    public GtinCode? Gtin { get; private init; } // AI 02  
    public LotNumber? Lot { get; private init; } // AI 10
    public ExpiryDate? ExpiryDate { get; private init; } // AI 17
    public Quantity? Quantity { get; private init; } // AI 37
    public bool HasIzPrefix { get; private init; } // Prefiks IZ
    public string RawData { get; private init; } = string.Empty; // Oryginalne dane
    public string? ErrorMessage { get; private init; }

    private GS1ParseResult() { }

    /// <summary>
    /// Tworzy pomyślny wynik parsowania
    /// </summary>
    public static GS1ParseResult Success(
        string rawData,
        SSCCCode? sscc = null, 
        GtinCode? gtin = null,
        LotNumber? lot = null, 
        ExpiryDate? expiryDate = null,
        Quantity? quantity = null,
        bool hasIzPrefix = false)
    {
        if (string.IsNullOrWhiteSpace(rawData))
            throw new ArgumentException("Surowe dane nie mogą być puste", nameof(rawData));

        return new GS1ParseResult
        {
            IsSuccess = true,
            RawData = rawData,
            SSCC = sscc,
            Gtin = gtin,
            Lot = lot,
            ExpiryDate = expiryDate,
            Quantity = quantity,
            HasIzPrefix = hasIzPrefix
        };
    }
    
    /// <summary>
    /// Tworzy niepomyślny wynik parsowania z błędem
    /// </summary>
    public static GS1ParseResult Failure(string rawData, string errorMessage)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new ArgumentException("Komunikat błędu nie może być pusty", nameof(errorMessage));

        return new GS1ParseResult
        {
            IsSuccess = false,
            RawData = rawData ?? string.Empty,
            ErrorMessage = errorMessage
        };
    }

    // Właściwości sprawdzające obecność danych
    public bool HasSSCC => SSCC is not null;
    public bool HasGtin => Gtin is not null;
    public bool HasLot => Lot is not null;
    public bool HasExpiryDate => ExpiryDate is not null;
    public bool HasQuantity => Quantity is not null;
    public bool IsDeliveryContext => HasIzPrefix;
    
    // Podsumowanie sparsowanych danych
    public int ParsedFieldsCount => 
        (HasSSCC ? 1 : 0) + 
        (HasGtin ? 1 : 0) + 
        (HasLot ? 1 : 0) + 
        (HasExpiryDate ? 1 : 0) + 
        (HasQuantity ? 1 : 0);

    public override string ToString()
    {
        if (!IsSuccess)
            return $"GS1ParseResult {{ IsSuccess = False, RawData = '{RawData}', ErrorMessage = '{ErrorMessage}' }}";

        var parts = new List<string> { "IsSuccess = True" };
        
        if (HasIzPrefix) parts.Add("HasIzPrefix = True");
        if (HasSSCC) parts.Add($"SSCC = {SSCC}");
        if (HasGtin) parts.Add($"GTIN = {Gtin}");
        if (HasLot) parts.Add($"Lot = {Lot}");
        if (HasExpiryDate) parts.Add($"ExpiryDate = {ExpiryDate?.DisplayFormat}");
        if (HasQuantity) parts.Add($"Quantity = {Quantity?.Value}");
        
        parts.Add($"ParsedFields = {ParsedFieldsCount}");
        
        return $"GS1ParseResult {{ {string.Join(", ", parts)} }}";
    }
}
