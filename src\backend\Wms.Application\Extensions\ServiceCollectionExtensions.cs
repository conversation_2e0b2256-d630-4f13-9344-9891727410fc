using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using System.Reflection;
using Wms.Application.Configuration;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using FluentValidation;
using Wms.Domain.Services;

namespace Wms.Application.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration
        services.Configure<JwtSettings>(configuration.GetSection(JwtSettings.SectionName));
        
        // AutoMapper profiles z Application layer
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        
        // MediatR dla CQRS pattern
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        
        // FluentValidation
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        
        // Domain Services
        services.AddScoped<ReceiveDomainService>();
        services.AddScoped<PalletGenerationService>();
        
        // Application services
        services.AddScoped<IAuthenticationService, AuthenticationService>();
        services.AddScoped<IPalletService, PalletService>();
        services.AddScoped<ICodeValidationService, CodeValidationService>();
        services.AddScoped<NumberGenerationService>();
        
        return services;
    }
}
