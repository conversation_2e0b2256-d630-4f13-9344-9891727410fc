using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;
using System.IdentityModel.Tokens.Jwt;
using Wms.Application.Configuration;
using Wms.Application.DTOs.Auth;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;

namespace Wms.UnitTests.Application.Services;

public class AuthenticationServiceTests
{
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<ISessionRepository> _mockSessionRepository;
    private readonly Mock<IOptions<JwtSettings>> _mockJwtOptions;
    private readonly JwtSettings _jwtSettings;
    private readonly AuthenticationService _authenticationService;

    public AuthenticationServiceTests()
    {
        _mockUserRepository = new Mock<IUserRepository>();
        _mockSessionRepository = new Mock<ISessionRepository>();
        _mockJwtOptions = new Mock<IOptions<JwtSettings>>();

        _jwtSettings = new JwtSettings
        {
            SecretKey = "ThisIsAVeryLongSecretKeyForTestingPurposesWithAtLeast256Bits",
            Issuer = "WmsTestIssuer",
            Audience = "WmsTestAudience",
            ExpiryMinutes = 60
        };

        _mockJwtOptions.Setup(x => x.Value).Returns(_jwtSettings);

        _authenticationService = new AuthenticationService(
            _mockUserRepository.Object,
            _mockSessionRepository.Object,
            _mockJwtOptions.Object);
    }

    [Fact]
    public async Task LoginWithCardAsync_WithValidCard_ShouldReturnLoginResponse()
    {
        // Arrange
        var cardNumber = "1234567890123456";
        var deviceId = "test-device";
        var ipAddress = "***********";

        var user = new User
        {
            Id = 1,
            NumerKarty = cardNumber,
            ImieNazwisko = "Jan Kowalski",
            Stanowisko = "Operator",
            Email = "<EMAIL>",
            JednostkaId = 2,
            IsActive = true
        };

        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = deviceId
        };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        // Act
        var result = await _authenticationService.LoginWithCardAsync(request, ipAddress);

        // Assert
        result.Should().NotBeNull();
        result.Token.Should().NotBeNullOrEmpty();
        result.ExpiresAt.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(60), TimeSpan.FromSeconds(5));
        result.User.Should().NotBeNull();
        result.User.Id.Should().Be(user.Id);
        result.User.FullName.Should().Be(user.ImieNazwisko);
        result.User.Position.Should().Be(user.Stanowisko);
        result.User.Email.Should().Be(user.Email);
        result.User.Department.Should().Be(user.JednostkaId.ToString());

        // Verify repositories were called
        _mockUserRepository.Verify(x => x.GetByCardNumberAsync(cardNumber), Times.Once);
        _mockSessionRepository.Verify(x => x.CreateAsync(It.Is<Session>(s =>
            s.UserId == user.Id &&
            s.DeviceId == deviceId &&
            s.IpAddress == ipAddress &&
            s.IsActive == true &&
            !string.IsNullOrEmpty(s.JwtTokenId)
        )), Times.Once);
    }

    [Fact]
    public async Task LoginWithCardAsync_WithInvalidCard_ShouldReturnNull()
    {
        // Arrange
        var cardNumber = "invalid-card";
        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = "test-device"
        };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _authenticationService.LoginWithCardAsync(request);

        // Assert
        result.Should().BeNull();
        _mockUserRepository.Verify(x => x.GetByCardNumberAsync(cardNumber), Times.Once);
        _mockSessionRepository.Verify(x => x.CreateAsync(It.IsAny<Session>()), Times.Never);
    }

    [Fact]
    public async Task LoginWithCardAsync_WithoutDeviceId_ShouldUseUnknownDeviceId()
    {
        // Arrange
        var cardNumber = "1234567890123456";
        var user = CreateTestUser(cardNumber);

        var request = new LoginScanRequest
        {
            CardNumber = cardNumber,
            DeviceId = null
        };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        // Act
        var result = await _authenticationService.LoginWithCardAsync(request);

        // Assert
        result.Should().NotBeNull();
        _mockSessionRepository.Verify(x => x.CreateAsync(It.Is<Session>(s =>
            s.DeviceId == "unknown"
        )), Times.Once);
    }

    [Fact]
    public async Task ValidateTokenAsync_WithValidToken_ShouldReturnTrue()
    {
        // Arrange
        var cardNumber = "1234567890123456";
        var user = CreateTestUser(cardNumber);

        // First login to get a token
        var request = new LoginScanRequest { CardNumber = cardNumber };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        var loginResult = await _authenticationService.LoginWithCardAsync(request);

        // Extract token ID from the generated token
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(loginResult!.Token);
        var tokenId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

        var session = new Session
        {
            Id = 1,
            UserId = user.Id,
            JwtTokenId = tokenId!,
            IsActive = true
        };

        _mockSessionRepository
            .Setup(x => x.GetByTokenIdAsync(tokenId!))
            .ReturnsAsync(session);

        // Act
        var isValid = await _authenticationService.ValidateTokenAsync(loginResult.Token);

        // Assert
        isValid.Should().BeTrue();
        _mockSessionRepository.Verify(x => x.GetByTokenIdAsync(tokenId!), Times.Once);
    }

    [Fact]
    public async Task ValidateTokenAsync_WithInvalidToken_ShouldReturnFalse()
    {
        // Arrange
        var invalidToken = "invalid.jwt.token";

        // Act
        var isValid = await _authenticationService.ValidateTokenAsync(invalidToken);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateTokenAsync_WithValidTokenButNoSession_ShouldReturnFalse()
    {
        // Arrange
        var cardNumber = "1234567890123456";
        var user = CreateTestUser(cardNumber);

        var request = new LoginScanRequest { CardNumber = cardNumber };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        var loginResult = await _authenticationService.LoginWithCardAsync(request);

        // Extract token ID
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(loginResult!.Token);
        var tokenId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

        // Setup session repository to return null (session not found)
        _mockSessionRepository
            .Setup(x => x.GetByTokenIdAsync(tokenId!))
            .ReturnsAsync((Session?)null);

        // Act
        var isValid = await _authenticationService.ValidateTokenAsync(loginResult.Token);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public async Task LogoutAsync_ShouldDeactivateSessions()
    {
        // Arrange
        var userId = 1;
        var deviceId = "test-device";

        _mockSessionRepository
            .Setup(x => x.DeactivateSessionsAsync(userId, deviceId))
            .Returns(Task.CompletedTask);

        // Act
        await _authenticationService.LogoutAsync(userId, deviceId);

        // Assert
        _mockSessionRepository.Verify(x => x.DeactivateSessionsAsync(userId, deviceId), Times.Once);
    }

    [Fact]
    public async Task LogoutAsync_WithoutDeviceId_ShouldDeactivateAllUserSessions()
    {
        // Arrange
        var userId = 1;

        _mockSessionRepository
            .Setup(x => x.DeactivateSessionsAsync(userId, null))
            .Returns(Task.CompletedTask);

        // Act
        await _authenticationService.LogoutAsync(userId);

        // Assert
        _mockSessionRepository.Verify(x => x.DeactivateSessionsAsync(userId, null), Times.Once);
    }

    [Fact]
    public async Task GenerateJwtToken_ShouldCreateTokenWithCorrectClaims()
    {
        // This tests the JWT token generation indirectly through LoginWithCardAsync
        // Arrange
        var cardNumber = "1234567890123456";
        var user = CreateTestUser(cardNumber);

        var request = new LoginScanRequest { CardNumber = cardNumber };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        // Act
        var result = await _authenticationService.LoginWithCardAsync(request);

        // Assert
        result.Should().NotBeNull();
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(result!.Token);

        // Verify token claims
        jwtToken.Claims.Should().Contain(c => c.Type == JwtRegisteredClaimNames.Sub && c.Value == user.Id.ToString());
        jwtToken.Claims.Should().Contain(c => c.Type == "userId" && c.Value == user.Id.ToString());
        jwtToken.Claims.Should().Contain(c => c.Type == "cardNumber" && c.Value == user.NumerKarty);
        jwtToken.Claims.Should().Contain(c => c.Type == "fullName" && c.Value == user.ImieNazwisko);
        jwtToken.Claims.Should().Contain(c => c.Type == "position" && c.Value == user.Stanowisko);

        // Verify token properties
        jwtToken.Issuer.Should().Be(_jwtSettings.Issuer);
        jwtToken.Audiences.Should().Contain(_jwtSettings.Audience);
        jwtToken.ValidTo.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryMinutes), TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task LoginWithCardAsync_WithUserWithoutCardNumber_ShouldHandleGracefully()
    {
        // Arrange
        var cardNumber = "1234567890123456";
        var user = new User
        {
            Id = 1,
            NumerKarty = null, // User without card number
            ImieNazwisko = "Jan Kowalski",
            Stanowisko = "Operator",
            JednostkaId = 2,
            IsActive = true
        };

        var request = new LoginScanRequest { CardNumber = cardNumber };

        _mockUserRepository
            .Setup(x => x.GetByCardNumberAsync(cardNumber))
            .ReturnsAsync(user);

        _mockSessionRepository
            .Setup(x => x.CreateAsync(It.IsAny<Session>()))
            .ReturnsAsync(new Session());

        // Act
        var result = await _authenticationService.LoginWithCardAsync(request);

        // Assert
        result.Should().NotBeNull();
        
        // Verify the token contains empty card number
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(result!.Token);
        jwtToken.Claims.Should().Contain(c => c.Type == "cardNumber" && c.Value == string.Empty);
    }

    private static User CreateTestUser(string cardNumber)
    {
        return new User
        {
            Id = 1,
            NumerKarty = cardNumber,
            ImieNazwisko = "Jan Kowalski",
            Stanowisko = "Operator",
            Email = "<EMAIL>",
            JednostkaId = 2,
            IsActive = true
        };
    }
}
