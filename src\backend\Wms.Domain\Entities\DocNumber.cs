using Wms.Domain.Common;

namespace Wms.Domain.Entities;

/// <summary>
/// Encja reprezentująca numerację dokumentów w systemie
/// Mapowana na tabelę docnumber
/// </summary>
public class DocNumber : BaseEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Last { get; set; } = 0;
    
    // Business logic helpers
    public bool IsForPallets => Name == "nrpalety";
    public bool IsForLabels => Name == "nretykiety";
    
    /// <summary>
    /// Pobiera następny numer w sekwencji
    /// </summary>
    public int GetNextNumber()
    {
        return Last + 1;
    }
    
    /// <summary>
    /// Aktualizuje numer na następny w sekwencji
    /// </summary>
    public void IncrementNumber()
    {
        Last++;
    }
}
