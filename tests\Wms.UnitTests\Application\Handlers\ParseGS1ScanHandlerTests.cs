using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.Features.Receives.Commands;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Handlers;
using Wms.Application.Interfaces;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Exceptions;

namespace Wms.UnitTests.Application.Handlers;

public class ParseGS1ScanHandlerTests
{
    private readonly Mock<IReceiveRepository> _mockReceiveRepository;
    private readonly Mock<ILogger<ParseGS1ScanHandler>> _mockLogger;
    private readonly ParseGS1ScanHandler _handler;

    public ParseGS1ScanHandlerTests()
    {
        _mockReceiveRepository = new Mock<IReceiveRepository>();
        _mockLogger = new Mock<ILogger<ParseGS1ScanHandler>>();
        
        _handler = new ParseGS1ScanHandler(
            _mockReceiveRepository.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Handle_WithValidGS1Data_ShouldReturnParsedData()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = "(00)123456789012345678(02)12345678901234(10)LOT123(17)241231(37)100"
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        var expectedItems = new List<AwizacjaDane>
        {
            new()
            {
                Id = 1,
                EtykietaKlient = "123456789012345678",
                Kod = "12345678901234",
                Ilosc = 100
            }
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedItems);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Sscc.Should().Be("123456789012345678");
        result.Ean.Should().Be("12345678901234");
        result.Lot.Should().Be("LOT123");
        result.DataWaznosci.Should().Be(new DateOnly(2024, 12, 31));
        result.Ilosc.Should().Be(100);
        result.IsExpected.Should().BeTrue();
        result.IsPrefilled.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithReceiveNotFound_ShouldThrowReceiveNotFoundException()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 999,
            PracownikId = 100,
            Scan = "(00)123456789012345678"
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ListControl)null!);

        // Act & Assert
        await Assert.ThrowsAsync<ReceiveNotFoundException>(
            () => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithReceiveNotAssignedToUser_ShouldReturnFailure()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = "(00)123456789012345678"
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 200, // Assigned to different user
            IsAssigned = true
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Message.Should().Contain("musi być przypisana do tego pracownika");
    }

    [Fact]
    public async Task Handle_WithEmptyScan_ShouldReturnFailure()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = ""
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Message.Should().Contain("Puste dane skanu");
    }

    [Theory]
    [InlineData("(00)123456789012345678")] // Only SSCC
    [InlineData("(02)12345678901234")] // Only GTIN
    [InlineData("(10)LOT123")] // Only Lot
    [InlineData("(17)241231")] // Only Date
    [InlineData("(37)100")] // Only Count
    public async Task Handle_WithPartialGS1Data_ShouldParseAvailableFields(string scanData)
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = scanData
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AwizacjaDane>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.IsExpected.Should().BeFalse();
        result.IsPrefilled.Should().BeFalse();

        // Verify specific fields based on scan data
        if (scanData.Contains("(00)"))
            result.Sscc.Should().Be("123456789012345678");
        
        if (scanData.Contains("(02)"))
            result.Ean.Should().Be("12345678901234");
        
        if (scanData.Contains("(10)"))
            result.Lot.Should().Be("LOT123");
        
        if (scanData.Contains("(17)"))
            result.DataWaznosci.Should().Be(new DateOnly(2024, 12, 31));
        
        if (scanData.Contains("(37)"))
            result.Ilosc.Should().Be(100);
    }

    [Fact]
    public async Task Handle_WithItemFoundInAwizacja_ShouldMarkAsExpected()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = "(00)123456789012345678(02)12345678901234"
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        var expectedItems = new List<AwizacjaDane>
        {
            new()
            {
                Id = 1,
                EtykietaKlient = "123456789012345678", // Matching SSCC
                Kod = "DIFFERENT_CODE",
                Ilosc = 50
            },
            new()
            {
                Id = 2,
                EtykietaKlient = "DIFFERENT_SSCC",
                Kod = "12345678901234", // Matching EAN
                Ilosc = 75
            }
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedItems);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.IsExpected.Should().BeTrue();
        result.IsPrefilled.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithInvalidDateFormat_ShouldLogWarningAndContinue()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = "(00)123456789012345678(17)999999" // Invalid date
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AwizacjaDane>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Message.Should().Contain("Błąd parsowania");
    }

    [Fact]
    public async Task Handle_ShouldLogInformationForSuccessfulParsing()
    {
        // Arrange
        var command = new ParseGS1ScanCommand
        {
            ListControlId = 1,
            PracownikId = 100,
            Scan = "(00)123456789012345678(02)12345678901234(10)LOT123"
        };

        var receive = new ListControl
        {
            Id = 1,
            RealizujacyPracownikId = 100,
            IsAssigned = true
        };

        _mockReceiveRepository
            .Setup(x => x.GetByIdAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(command.ListControlId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AwizacjaDane>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();

        // Verify logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Sparsowano dane GS1")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
