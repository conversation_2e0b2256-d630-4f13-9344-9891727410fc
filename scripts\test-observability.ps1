# Test script for WMS API Observability endpoints
# This script tests if all observability endpoints are working correctly

param(
    [string]$BaseUrl = "https://localhost:5001"
)

Write-Host "Testing WMS API Observability endpoints..." -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow

# Function to test endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Name
    )
    
    try {
        Write-Host "`nTesting $Name endpoint: $Url" -ForegroundColor Cyan
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ $Name endpoint is working (Status: $($response.StatusCode))" -ForegroundColor Green
            
            # Show content preview for some endpoints
            if ($Name -eq "Health Check" -or $Name -eq "Ready Check") {
                Write-Host "  Response: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))" -ForegroundColor Gray
            }
            elseif ($Name -eq "Prometheus Metrics") {
                # Count metrics lines
                $metricsCount = ($response.Content -split "`n" | Where-Object { $_ -match "^wms_" }).Count
                Write-Host "  Found $metricsCount WMS metrics" -ForegroundColor Gray
            }
            
            return $true
        }
        else {
            Write-Host "✗ $Name endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ $Name endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test endpoints
$endpoints = @(
    @{ Url = "$BaseUrl/health"; Name = "Health Check" },
    @{ Url = "$BaseUrl/health/ready"; Name = "Ready Check" },
    @{ Url = "$BaseUrl/health/live"; Name = "Live Check" },
    @{ Url = "$BaseUrl/metrics"; Name = "Prometheus Metrics" }
)

$successCount = 0
$totalCount = $endpoints.Count

foreach ($endpoint in $endpoints) {
    if (Test-Endpoint -Url $endpoint.Url -Name $endpoint.Name) {
        $successCount++
    }
}

Write-Host "`n" + "="*60 -ForegroundColor Yellow
Write-Host "Test Results: $successCount/$totalCount endpoints working" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })

if ($successCount -eq $totalCount) {
    Write-Host "✓ All observability endpoints are working correctly!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠ Some endpoints are not working. Check the API status." -ForegroundColor Yellow
    exit 1
}
