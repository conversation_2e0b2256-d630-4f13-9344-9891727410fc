# ADR-003: Code Validation Strategy

## Status
Accepted

## Date
2025-09-01

## Context
The WMS system needs to validate various types of codes used in warehouse operations:
- **SSCC codes** - 18-digit international pallet identifiers
- **DS codes** - Internal client codes with format DS + 4-9 digits  
- **Location codes** - Warehouse location identifiers in format MP-H-R-M-P

These codes are critical for system integrity and need robust validation before processing.

## Decision
Implement a centralized `CodeValidationService` with regex-based validation for all code types, with extensible patterns to accommodate variations found in production data.

### Validation Rules:

#### SSCC Codes:
- **Format**: Exactly 18 digits
- **Pattern**: `^\d{18}$`
- **Usage**: Primary pallet identification

#### DS Codes:
- **Format**: "DS" followed by 4-9 digits
- **Pattern**: `^DS\d{4,9}$` 
- **Examples**: DS1234, DS12345, DS123456789
- **Usage**: Client-specific pallet labeling

#### Location Codes:
- **Base Format**: MP-H-R-M-P (MP-Hall-Rack-Position-Level)
- **Extended Patterns**: Support for various real-world formats
- **Examples**: 
  - `MP-1-A-1-1` (standard)
  - `MP-01-A-01-1` (zero-padded)
  - `MP-1-AA-1-A` (multi-character sections)

## Rationale

### Benefits:
1. **Centralized Validation** - Single point of truth for all code validation logic
2. **Early Error Detection** - Invalid codes caught at API boundary
3. **Extensible Design** - Easy to add new code types or modify existing patterns
4. **Performance** - Compiled regex patterns for fast validation
5. **Testable** - Isolated service easy to unit test

### Implementation Details:

```csharp
public interface ICodeValidationService
{
    bool ValidateSSCC(string code);
    bool ValidateDS(string code);
    bool ValidateLocationCode(string code);
}

public class CodeValidationService : ICodeValidationService
{
    private static readonly Regex SSCCRegex = new(@"^\d{18}$", RegexOptions.Compiled);
    private static readonly Regex DSRegex = new(@"^DS\d{4,9}$", RegexOptions.Compiled);
    private static readonly Regex LocationRegex = new(@"^MP-\d{1,2}-[A-Z]{1,2}-\d{1,2}-[A-Z0-9]{1,2}$", RegexOptions.Compiled);
    
    // Implementation with multiple fallback patterns for location codes
}
```

## Validation Integration

### Application Layer:
- Used in `PalletService` for business logic validation
- Injected via dependency injection
- FluentValidation rules delegate to CodeValidationService

### API Layer:
- FluentValidation validators use CodeValidationService
- Automatic validation via ASP.NET Core model validation
- Consistent error messages across all endpoints

## Real-World Adaptations

Based on analysis of production data, we discovered various location code formats:
- Standard: `MP-1-A-1-1`
- Zero-padded: `MP-01-A-01-1` 
- Multi-character: `MP-1-AA-1-A`
- Mixed: `MP-12-B-03-C`

The validation service uses multiple regex patterns with fallback logic to handle all observed variations while maintaining strict validation.

## Error Handling

Invalid codes result in:
1. **API Level**: 400 Bad Request with descriptive error message
2. **Service Level**: `ArgumentException` with specific validation failure reason
3. **Validation Level**: FluentValidation error with field-specific message

## Consequences

### Positive:
- Consistent code validation across entire application
- Easy to maintain and extend validation rules
- Clear separation between validation logic and business logic
- Comprehensive error reporting

### Negative:
- Additional service layer for what could be simple validation
- Regex maintenance required for complex patterns
- Performance overhead for regex compilation (mitigated by compiled patterns)

## Implementation Status
- ✅ `CodeValidationService` implemented with all three code types
- ✅ Integrated with `PalletService` business logic
- ✅ FluentValidation rules implemented
- ✅ API error handling configured
- ✅ Production data patterns analyzed and accommodated
