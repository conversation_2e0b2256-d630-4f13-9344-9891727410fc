# Lokalizacja (i18n) – J<PERSON> budo<PERSON> nowe strony w WmsApp

Ten dokument opisuje, jak tworzy<PERSON> i rozwijać strony w aplikacji WmsApp z pełną obsługą wielu języków (PL/EN i kolejne). Opisuje istniejącą infrastrukturę lokalizacji, konwenc<PERSON>, dobre praktyki oraz gotowe wzorce XAML i C#.

Spis treści
- TL;DR – skrócony przepis
- Architektura i narzędzia lokalizacji w projekcie
- Konwencje nazewnicze kluczy (resx)
- Szablon tworzenia nowej strony (krok po kroku)
- Użycie w kodzie C# (VM/Code-Behind), teksty formatowane i daty/liczby
- Dodawanie nowego języka (np. DE)
- DataTemplates i uwagi dot. kompilatora XAML
- Checklist przy tworzeniu strony
- Przykład end‑to‑end

---

TL;DR – skrócony przepis
1) Dodaj klucze tekstów do plików zasobów:
   - Resources/Strings/AppResources.resx (polski – domyślny/neutralny)
   - Resources/Strings/AppResources.en.resx (angielski)
2) W XAML używaj TranslateExtension:
   - Dodaj xmlns:loc="clr-namespace:WmsApp.Localization"
   - Ustaw tytuł i etykiety: Title="{loc:Translate Key=MyPage_Title}", Label/Button/Entry.Placeholder – wszystko przez {loc:Translate}
3) W VM lub code-behind, gdy potrzebujesz tekstu w C# (np. DisplayAlert):
   - Pobierz tekst: LocalizationResourceManager.Current["Klucz"]
   - Dla formatowania: string.Format(LocalizationResourceManager.Current["KluczZFormatem"], arg1, arg2)
4) Zmiana języka w runtime (np. w LoginPage) działa natychmiast – używaj ILocalizationService.SetCulture("en")/("pl")…

---

Architektura i narzędzia lokalizacji w projekcie
- Pliki .resx z kluczami:
  - AppResources.resx – polski (neutralny), zawiera pełny zestaw kluczy
  - AppResources.en.resx – angielski, te same klucze, przetłumaczone wartości
  - Możesz dodawać kolejne, np.: AppResources.de.resx (niemiecki)
- TranslateExtension (WmsApp.Localization.TranslateExtension)
  - Umożliwia użycie w XAML: {loc:Translate Key=...}
  - Bazuje na LocalizationResourceManager
- LocalizationResourceManager (WmsApp.Localization.LocalizationResourceManager)
  - Trzyma aktualną kulturę (CultureInfo)
  - Notyfikuje UI o zmianach (INotifyPropertyChanged) – dzięki temu UI odświeża się w locie
- ILocalizationService (WmsApp.Services.LocalizationService)
  - Zmienia kulturę w runtime, zapisuje wybór w Preferences ("AppLanguage")
  - MauiProgram inicjuje kulturę przy starcie aplikacji zgodnie z zapisanym wyborem

---

Konwencje nazewnicze kluczy (resx)
- Struktura: Sekcja_Lokalizacja_Opis
  - Dla stron: MyPage_Title, MyPage_Header, MyPage_Filter_Placeholder, MyPage_Save_Button
  - Dla współdzielonych: Common_Close, Common_Save, Common_Error_Network, Common_Confirm
- Zasady:
  - Używaj Title dla tytułów stron
  - Sufiksy: _Label, _Placeholder, _Button, _Header, _Subtitle, _DialogTitle, _DialogMessage, _DialogConfirm, _DialogCancel
  - Korzystaj z Common_ dla elementów globalnych (unikaj duplikacji)
  - Dla tekstów z parametrami używaj {0}, {1}… (np. "Usunięto {0} pozycji")

---

Szablon tworzenia nowej strony (krok po kroku)
1) Zaplanuj klucze
- Wypisz wszystkie teksty na stronie (tytuł, etykiety, placeholdery, przyciski, toasty, dialogi)
- Nazwij je zgodnie z konwencją, np. PalletDetails_Title, PalletDetails_ItemCode_Label, PalletDetails_Save_Button

2) Dodaj klucze do .resx
- Dodaj je do AppResources.resx (PL) i AppResources.en.resx (EN)
- Jeśli planujesz inny język – dodaj odpowiedni plik .resx i wartości

3) Użyj tłumaczeń w XAML
- Dodaj namespace i stosuj TranslateExtension dla wszystkich tekstów:

```xml path=null start=null
<ContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:loc="clr-namespace:WmsApp.Localization"
    x:Class="WmsApp.Views.PalletDetailsPage"
    Title="{loc:Translate Key=PalletDetails_Title}">

    <VerticalStackLayout Padding="16" Spacing="12">
        <Label Text="{loc:Translate Key=PalletDetails_Header}" FontSize="20"/>

        <Label Text="{loc:Translate Key=PalletDetails_ItemCode_Label}"/>
        <Entry Placeholder="{loc:Translate Key=PalletDetails_ItemCode_Placeholder}"/>

        <Button Text="{loc:Translate Key=PalletDetails_Save_Button}"/>
    </VerticalStackLayout>
</ContentPage>
```

4) Użycie w DataTemplate
- TranslateExtension działa także w DataTemplate, bo używa jawnego Source – nie zależy od BindingContext elementu
- Kompilator XAML może zgłaszać ostrzeżenia dot. rozpoznawania BindingContext w DataTemplate – to nie wpływa na {loc:Translate}

5) Test – przełącz język
- Na LoginPage wybierz język (PL/EN). UI strony powinien zmieniać się w locie, bez restartu aplikacji

---

Użycie w kodzie C# (VM/Code-Behind), teksty formatowane i daty/liczby
1) Pobieranie tekstu w C#

```csharp path=null start=null
using WmsApp.Localization;

string message = LocalizationResourceManager.Current["Common_Error_Network"]; // np. "Błąd sieci"
await Application.Current.MainPage.DisplayAlert(
    LocalizationResourceManager.Current["Common_Error"],
    message,
    LocalizationResourceManager.Current["Common_Close"]);
```

2) Teksty formatowane z parametrami
- W .resx: Common_DeletedCount = "Usunięto {0} pozycji"
- W C#:

```csharp path=null start=null
string template = LocalizationResourceManager.Current["Common_DeletedCount"];
string text = string.Format(template, deletedCount);
```

3) Daty i liczby – używaj bieżącej kultury

```csharp path=null start=null
var now = DateTime.Now; // Domyślnie kultura jest aktualna (ustawiona przez ILocalizationService)
string dateText = now.ToString("d"); // Krótka data wg kultury

decimal amount = 12345.67m;
string amountText = string.Format("{0:N2}", amount); // Format liczbowy wg kultury
```

---

Dodawanie nowego języka (np. DE)
1) Dodaj plik zasobów: Resources/Strings/AppResources.de.resx
- Skupluj te same klucze co w AppResources.resx
- Uzupełnij tłumaczenia
2) Udostępnij język w UI (LoginPage)
- W LoginViewModel dopisz do AvailableLanguages np. new("de", "Deutsch")
3) Test – wybierz DE w LoginPage i sprawdź wszystkie strony

Uwagi:
- Możesz dodawać warianty regionalne: AppResources.de-DE.resx, AppResources.de-AT.resx itd.
- Jeśli tłumaczenia brak w danym języku, ResourceManager użyje wartości z kultury neutralnej (PL)

---

DataTemplates i uwagi dot. kompilatora XAML
- W tym projekcie włączone są skompilowane wiązania XAML
- Ostrzeżenia kompilatora o nierozpoznanym BindingContext w DataTemplate mogą występować (zwłaszcza przy skomplikowanych szablonach)
- TranslateExtension, ponieważ wiąże się przez Source=LocalizationResourceManager.Current, działa poprawnie także w DataTemplate (runtime OK)

---

Checklist przy tworzeniu strony
- [ ] Wypisane wszystkie teksty (tytuł, etykiety, placeholdery, przyciski, komunikaty)
- [ ] Dodane klucze do AppResources.resx i AppResources.en.resx (oraz innych plików językowych, jeśli trzeba)
- [ ] Zastąpione wszystkie twardo‑kodowane napisy w XAML na {loc:Translate}
- [ ] Dialogi/toasty/napisy w C# pobierane z LocalizationResourceManager
- [ ] Teksty formatowane używają {0}/{1} + string.Format
- [ ] Test przełączenia języka (LoginPage) – UI zmienia się w locie

---

Przykład end‑to‑end (PalletDetailsPage)
1) Klucze (PL) – AppResources.resx
- PalletDetails_Title = "Szczegóły palety"
- PalletDetails_Header = "Wprowadź dane palety"
- PalletDetails_ItemCode_Label = "Kod towaru"
- PalletDetails_ItemCode_Placeholder = "Wpisz lub zeskanuj kod"
- PalletDetails_Save_Button = "Zapisz"

2) Klucze (EN) – AppResources.en.resx
- PalletDetails_Title = "Pallet details"
- PalletDetails_Header = "Enter pallet data"
- PalletDetails_ItemCode_Label = "Item code"
- PalletDetails_ItemCode_Placeholder = "Type or scan the code"
- PalletDetails_Save_Button = "Save"

3) XAML
```xml path=null start=null
<ContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:loc="clr-namespace:WmsApp.Localization"
    x:Class="WmsApp.Views.PalletDetailsPage"
    Title="{loc:Translate Key=PalletDetails_Title}">

    <VerticalStackLayout Padding="16" Spacing="12">
        <Label Text="{loc:Translate Key=PalletDetails_Header}" FontSize="20"/>
        <Label Text="{loc:Translate Key=PalletDetails_ItemCode_Label}"/>
        <Entry Placeholder="{loc:Translate Key=PalletDetails_ItemCode_Placeholder}"/>
        <Button Text="{loc:Translate Key=PalletDetails_Save_Button}"/>
    </VerticalStackLayout>
</ContentPage>
```

4) C# (np. po zapisie)
```csharp path=null start=null
using WmsApp.Localization;

var title = LocalizationResourceManager.Current["Common_Confirmation"];
var message = LocalizationResourceManager.Current["Common_SavedSuccessfully"]; // np. "Zapisano pomyślnie"
var ok = LocalizationResourceManager.Current["Common_Close"];
await Application.Current.MainPage.DisplayAlert(title, message, ok);
```

---

Dobre praktyki (skrót)
- Nie twardo‑koduj żadnych tekstów w XAML/C# – zawsze używaj kluczy
- Grupuj klucze przez moduł/stronę, współdzielone dawaj pod Common_
- Używaj czytelnych, jednoznacznych nazw kluczy (bez skrótów)
- Teksty z danymi dynamicznymi – zawsze przez szablony z {0}/{1}
- Formatowanie liczb/dat przez kulturę (nie sklejać ręcznie kropek/przecinków)
- Zmiana języka przez ILocalizationService.SetCulture – unikaj własnych, równoległych mechanizmów

Jeśli masz pytania lub chcesz dodać przykłady do tego przewodnika – dopisz kolejne sekcje poniżej.

