# STYLE GUIDE – WMS Project

Wersja: 0.1  
Data: 2025-09-01  
Zakres: Zasady kodowania dla backendu (.NET 9) i frontendu (MAUI Android)

## 1. Ogólne zasady

### Język kodu
- **Kod**: an<PERSON><PERSON><PERSON> (nazwy klas, metod, zmiennych, komentarze techniczne)
- **Komunikaty użytkownika**: polski (błędy, etykiety UI, komunikaty API)
- **Dokumentacja techniczna**: polski (README, komentarze domenowe)

### Nazewnictwo
- **PascalCase**: klasy, metody, właściwości publiczne, enumeracje
- **camelCase**: parametry, zmienne lokalne, pola prywatne
- **UPPER_SNAKE_CASE**: stałe, zmienne środowiskowe
- **kebab-case**: pliki konfiguracyjne, endpointy URL

## 2. Backend (.NET 9 / ASP.NET Core)

### Struktura projektów
```
src/backend/
├── Wms.Api/           # Kontrolery, middleware, konfiguracja
├── Wms.Application/   # Use cases, DTO, walidacje
├── Wms.Domain/        # Encje, reguły biznesowe
└── Wms.Infrastructure/ # EF Core, repozytoria, zewnętrzne serwisy
```

### Kodowanie
- **Nullable reference types**: włączone (`<Nullable>enable</Nullable>`)
- **Implicit usings**: włączone dla czytelności
- **File scoped namespaces**: preferowane
- **Primary constructors**: stosuj gdy to zwiększa czytelność

### Kontrolery
```csharp
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class PalletsController : ControllerBase
{
    private readonly IMovementService _movementService;

    public PalletsController(IMovementService movementService)
    {
        _movementService = movementService;
    }

    [HttpPost("{palletCode}/move")]
    public async Task<ActionResult<MovePalletResponse>> MovePallet(
        string palletCode, 
        MovePalletRequest request)
    {
        // implementacja
    }
}
```

### Serwisy i repozytoria
- Używaj interfejsów dla wszystkich serwisów
- Repository pattern przez interfejsy
- Async/await dla operacji I/O
- CancellationToken dla długotrwałych operacji

### Obsługa błędów
```csharp
// Własne wyjątki domenowe
public class PalletNotFoundException : DomainException
{
    public PalletNotFoundException(string palletCode) 
        : base($"Paleta {palletCode} nie została znaleziona") { }
}

// Global exception handler
public class GlobalExceptionMiddleware
{
    // Zwraca ProblemDetails z komunikatami w języku polskim
}
```

## 3. Frontend (MAUI Android)

### 3.1 Modalne selektory (best practices)
- Preferuj CollectionView z SelectionMode=Single i SelectedItem zbindowanym do właściwości w ViewModelu (zamiast TapGestureRecognizer w DataTemplate).
- W ViewModelu obsługuj partial metody OnSelected…Changed i uruchamiaj logikę na głównym wątku przez BeginInvokeOnMainThread z krótkim opóźnieniem (50–100 ms), aby uniknąć konfliktów cyklu życia UI.
- Nie resetuj SelectedItem z poziomu ViewModelu (częsty powód błędu „VirtualView cannot be null here”).
- Dodaj flagę anty-reentrancy (np. _isSelectionProcessing), aby nie przetwarzać wyboru wielokrotnie.
- Zwracaj wynik z modala przez TaskCompletionSource; przy zamykaniu modala sprawdzaj, czy strona nadal jest w Navigation.ModalStack.
- Chroń IQueryAttributable przed ponowną inicjalizacją (gdy LK/parametr się nie zmienia).
- Szczegóły: docs/Guides/MAUI_ModalPicker_BestPractices.md

### Struktura projektu
```
WmsApp/
├── Views/           # Strony XAML
├── ViewModels/      # ViewModels (MVVM)
├── Services/        # Serwisy biznesowe
├── Models/          # DTO, modele danych
├── Platforms/       # Kod specyficzny dla platformy
│   └── Android/     # BroadcastReceiver dla DataWedge
└── Resources/       # Zasoby (fonty, obrazy, style)
```

### MVVM i binding
```csharp
// ViewModel z CommunityToolkit.Mvvm
public partial class LoginViewModel : ObservableObject
{
    [ObservableProperty]
    private string userName = string.Empty;

    [RelayCommand]
    private async Task ProcessScan(string scannedValue)
    {
        // implementacja
    }
}
```

### XAML Style
- Używaj `x:Name` zamiast `Name` 
- Preferencja dla `Binding` nad `x:Bind` (MAUI compatibility)
- Style i zasoby w `Resources/Styles/`
- Duże kontrolki (minimum 44px touch target)

```xml
<!-- Przykład stylu dla magazynierów -->
<Style x:Key="ScanButtonStyle" TargetType="Button">
    <Setter Property="HeightRequest" Value="60" />
    <Setter Property="FontSize" Value="18" />
    <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
</Style>
```

### Lokalizacja (i18n)
- Wszystkie nowe strony i elementy UI muszą wykorzystywać mechanizm tłumaczeń przez TranslateExtension i pliki .resx.
- Szczegółowy przewodnik: [LOCALIZATION_GUIDE.md](../src/mobile/WmsApp/docs/LOCALIZATION_GUIDE.md)
- Skrót:
  - Dodawaj klucze do `Resources/Strings/AppResources.resx` (PL) i `Resources/Strings/AppResources.en.resx` (EN)
  - W XAML używaj `{loc:Translate Key=...}` (pamiętaj o `xmlns:loc="clr-namespace:WmsApp.Localization"`)
  - W C# pobieraj teksty przez `LocalizationResourceManager.Current["Klucz"]`

## 4. Baza danych (MySQL + EF Core)

### Konwencje tabel
- **Nazwy tabel**: PascalCase, liczba pojedyncza (User, Movement)
- **Kolumny**: PascalCase (UserId, CreatedAt)
- **Klucze obce**: końcówka `Id` (UserId, PalletCode)
- **Indeksy**: prefix `IX_` + nazwa tabeli + kolumny

### Migracje
```csharp
// Nazwa migracji: YYYYMMDD_HHmm_DescriptiveName
// Przykład: 20250901_1400_InitialCreate
public partial class InitialCreate : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // zawsze sprawdź czy tabela nie istnieje
    }
}
```

### Encje
```csharp
public class User
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string CardNumber { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // Navigation properties
    public ICollection<Movement> Movements { get; set; } = new List<Movement>();
}
```

### EF Core Tracking
- **GET/Search queries**: Używaj `AsNoTracking()` lub `AsNoTrackingWithIdentityResolution()` (z Include)
- **Command handlers**: Używaj tracked entities dla modyfikacji
- **Projekcje do DTO**: Zawsze `AsNoTracking()`
- **Szczegółowy przewodnik**: [ef-tracking-checklist.md](ef-tracking-checklist.md)

## 5. Logowanie i obsługa błędów

### Backend - Serilog
```csharp
Log.Information("Pallet {PalletCode} moved from {FromLocation} to {ToLocation} by {UserId}", 
    palletCode, fromLocation, toLocation, userId);

Log.Warning("Invalid pallet code scanned: {PalletCode} by user {UserId}", 
    palletCode, userId);

Log.Error(ex, "Database error during pallet movement {PalletCode}", palletCode);
```

### Frontend - Microsoft.Extensions.Logging
```csharp
_logger.LogInformation("User {UserId} logged in successfully", userId);
_logger.LogWarning("Invalid scan detected: {ScannedValue}", scannedValue);
_logger.LogError(ex, "Network error during API call to {Endpoint}", endpoint);
```

## 6. Konfiguracja i sekrety

### Backend
- Używaj `IConfiguration` i `IOptions<T>`
- Sekrety w zmiennych środowiskowych, nigdy w kodzie
- Walidacja konfiguracji przy starcie aplikacji

### Frontend

## 7. Kodowanie specyficzne dla domeny magazynowej

### GS1-128 i kody kreskowe

#### Formatowanie Application Identifiers (AI)
- **Format**: `(XX)` gdzie XX to 2-4 cyfry
- **Przykład**: `(00)123456789012345678` - SSCC
- **Separatory**: Użyj znak FNC1 (char)29 pomiędzy elementami zmiennej długości

#### Obsługiwane AI w systemie WMS
```csharp
// Standardowe AI dla magazynu
public static readonly Dictionary<string, string> SupportedAI = new()
{
    ["00"] = "SSCC (Serial Shipping Container Code) - 18 cyfr",
    ["01"] = "GTIN (Global Trade Item Number) - 14 cyfr", 
    ["02"] = "GTIN of trade items contained - 14 cyfr",
    ["10"] = "Batch/Lot Number - max 20 znaków alfanumerycznych",
    ["11"] = "Production Date (YYMMDD) - 6 cyfr",
    ["15"] = "Best Before Date (YYMMDD) - 6 cyfr",
    ["17"] = "Expiry Date (YYMMDD) - 6 cyfr", 
    ["37"] = "Count/Quantity - max 8 cyfr",
    ["310d"] = "Net Weight (kg) - wzorzec z pozycją dziesiętną",
    ["400"] = "Customer PO Number - max 30 znaków",
    ["420"] = "Ship To Postal Code - max 20 znaków"
};
```

#### Prefiks IZ dla dostaw
- **Format**: `IZ` na początku kodu oznacza tryb rejestracji dostaw
- **Przykład**: `IZ(00)123456789012345678(10)LOT123`
- **Obsługa**: Parser automatycznie wykrywa i usuwa prefiks, ustawia flagę `HasIzPrefix=true`

#### Przykłady poprawnych kodów GS1
```
// Podstawowy SSCC
(00)123456789012345678

// GTIN z datą ważności i partią
(02)12345678901234(17)251231(10)BATCH123

// Kompletny kod z ilością (tryb dostaw)
IZ(00)123456789012345678(02)12345678901234(10)LOT456(17)241215(37)100

// Kod z separatorem FNC1 (char 29)
(10)VARIABLE_LOT\u001D(17)251130
```

#### Walidacja i normalizacja
```csharp
public class GS1ScanExample
{
    // ✅ DOBRZE - używaj Value Objects
    public void ProcessScan(string rawScan)
    {
        var result = _gs1Parser.Parse(rawScan);
        if (result.IsSuccess)
        {
            var sscc = result.SSCC?.Value; // string z walidacją
            var gtin = result.Gtin?.Value; // 14 cyfr
            var lot = result.Lot?.Value;   // max 20 znaków
        }
    }
    
    // ❌ ŹLE - bezpośrednie stringi bez walidacji
    public void BadExample(string rawScan)
    {
        var sscc = rawScan.Substring(4, 18); // Ryzykowne!
    }
}
```

#### Komunikaty błędów (polskie)
- Używaj ResourceKeys dla spójności:
```csharp
// ✅ DOBRZE
var message = _localizationService.GetString(ResourceKeys.GS1.InvalidFormat);

// ❌ ŹLE  
throw new Exception("Invalid GS1 format"); // angielski komunikat
```
- Używaj `IPreferences` dla ustawień użytkownika
- `SecureStorage` dla tokenów JWT
- Konfiguracja API endpoint w `appsettings.json`

## 7. Testy

### Nazewnictwo testów
```csharp
// Format: MethodName_StateUnderTest_ExpectedBehavior
[Fact]
public void MovePallet_WhenPalletNotExists_ShouldThrowPalletNotFoundException()
{
    // Arrange
    // Act  
    // Assert
}
```

### Organizacja testów
- Jeden plik testowy na klasę testowaną
- Suffix `Tests` (np. `MovementServiceTests`)
- Grupowanie testów w klasach nested gdy to pomaga

## 8. Formatowanie kodu

### C# EditorConfig
```ini
[*.cs]
indent_style = tab
indent_size = 4
end_of_line = crlf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# Preferencje C#
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
```

### Długość linii
- **Maksimum**: 120 znaków
- **Preferowana**: 80-100 znaków
- Łamanie długich wyrażeń na wiele linii

## 9. Komentarze i dokumentacja

### XML Documentation
```csharp
/// <summary>
/// Przenosi paletę do nowej lokalizacji w magazynie.
/// </summary>
/// <param name="palletCode">Kod palety (SSCC lub DS)</param>
/// <param name="toLocation">Docelowa lokalizacja (format MP-*-*-*-*)</param>
/// <returns>Szczegóły przeprowadzonego ruchu</returns>
/// <exception cref="PalletNotFoundException">Gdy paleta nie istnieje</exception>
public async Task<MovementResult> MovePalletAsync(string palletCode, string toLocation)
```

### Komentarze inline
- Unikaj oczywistych komentarzy
- Wyjaśniaj "dlaczego", nie "co"
- Komentarze biznesowe w języku polskim

## 10. Specific patterns dla WMS

### Walidacja kodów
```csharp
public static class CodeValidators
{
    public static readonly Regex SsccPattern = new(@"^\d{18}$");
    public static readonly Regex DsPattern = new(@"^DS\d{8}$");
    public static readonly Regex LocationPattern = new(@"^MP-\d+-\d+-\d+-\d+$");
    public static readonly Regex CardNumberPattern = new(@"^\d{5,20}$");
}
```

### API Response format
```csharp
// Sukces
public class ApiResponse<T>
{
    public T Data { get; set; }
    public bool Success { get; set; } = true;
    public string Message { get; set; } = string.Empty;
}

// Błąd
public class ApiError
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, string[]>? ValidationErrors { get; set; }
}
```

## 11. Performance Guidelines

### Database
- Zawsze używaj indeksów na kluczach wyszukiwania
- Unikaj N+1 queries (Include/ThenInclude)
- Używaj projection dla read-only queries

### Mobile
- Minimalizuj allocations w UI thread
- Używaj `ConfigureAwait(false)` w serwisach
- Cache statycznych danych (lokalizacje)

## 12. Security Best Practices

### JWT
```csharp
// Zawsze sprawdzaj expired tokens
// Używaj strong signing keys (min. 256 bit)
// Waliduj Issuer i Audience
```

### Input validation
- Zawsze waliduj dane wejściowe
- Sanitizuj dane przed zapisem do bazy
- Używaj parameteryzowanych zapytań (EF Core chroni automatycznie)

---

Te zasady mają zapewnić spójność kodu i wysoką jakość implementacji systemu WMS.
