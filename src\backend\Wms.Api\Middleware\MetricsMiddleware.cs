using System.Diagnostics;
using Wms.Api.Metrics;

namespace Wms.Api.Middleware;

public class MetricsMiddleware
{
    private readonly RequestDelegate _next;
    private readonly WmsMetrics _metrics;
    private readonly ILogger<MetricsMiddleware> _logger;

    public MetricsMiddleware(RequestDelegate next, WmsMetrics metrics, ILogger<MetricsMiddleware> logger)
    {
        _next = next;
        _metrics = metrics;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occurred during request processing");
            context.Response.StatusCode = 500;
        }
        finally
        {
            stopwatch.Stop();
            
            // Record metrics for all requests
            _metrics.RecordApiRequest(
                method: context.Request.Method,
                endpoint: context.Request.Path.Value ?? "unknown",
                statusCode: context.Response.StatusCode,
                duration: stopwatch.Elapsed
            );
        }
    }
}
