# Przewodnik Użytkowania - Aplikacja Mobilna Inwentaryzacji

## Przegląd

Dokument zawiera szczegółowe instrukcje użytkowania aplikacji mobilnej WMS do przeprowadzania inwentaryzacji na urządzeniach Zebra MC3300 z systemem Android.

## Wymagania Systemowe

### Sprzęt
- **Urządzenie**: Zebra MC3300 (Android 10+)
- **Skaner**: Wbudowany skaner laserowy/obrazowy
- **Sieć**: WiFi 802.11 b/g/n
- **Pamięć**: Min. 2GB RAM, 16GB storage

### Oprogramowanie
- **System operacyjny**: Android 10.0 (API level 29) lub wyższy
- **DataWedge**: Wersja 8.0+ (preinstalowana na Zebra)
- **Aplikacja WMS**: Najnowsza wersja z auto-update

## Logowanie i Autoryzacja

### 1. Pierwsze uruchomienie
1. **Włącz aplikację WMS** - tap na ikonę aplikacji
2. **Sprawdź połączenie** - aplikacja automatycznie testuje łączność
3. **Skanuj kartę pracownika** - przyłóż kartę z kodem kreskowym do skanera
4. **Weryfikacja** - system sprawdza uprawnienia i tworzy sesję

```
┌─────────────────────┐
│   🔒 LOGOWANIE      │
│                     │
│  Zeskanuj swoją     │
│  kartę pracownika   │
│                     │
│  ╭─────────────────╮ │
│  │ 📊 |||||||||| │ │
│  ╰─────────────────╯ │
│                     │
│  [ Ustawienia ]     │
└─────────────────────┘
```

### 2. Wybór środowiska (Deweloperzy)
- **Localhost**: Serwer lokalny (127.0.0.1:8080)
- **Android Emulator**: Emulator (********:8080)
- **Serwer Produkcyjny**: Adres IP serwera
- **Mock API**: Tryb testowy bez serwera

## Główne Menu

Po zalogowaniu wyświetla się ekran główny z kafelkami funkcji:

```
┌─────────────────────────────────────┐
│  WMS - Jan Kowalski                 │
│  🔋●●●● 📶●●●● ⏰ 10:30          │
├─────────────────────────────────────┤
│                                     │
│  ┌─────────┐  ┌─────────┐          │
│  │ 📦 RUCHY │  │ 📋 INWE-│          │
│  │  PALET   │  │ NTARYZA-│          │
│  │         │  │   CJA   │          │
│  └─────────┘  └─────────┘          │
│                                     │
│  ┌─────────┐  ┌─────────┐          │
│  │ 📥 DOST-│  │ 📊 RAPO-│          │
│  │   AWY   │  │   RTY   │          │
│  │         │  │         │          │
│  └─────────┘  └─────────┘          │
│                                     │
│  ┌─────────┐  ┌─────────┐          │
│  │ 🎯 ZADA-│  │ ⚙️ USTA-│          │
│  │   NIA   │  │  WIENIA │          │
│  │         │  │         │          │
│  └─────────┘  └─────────┘          │
│                                     │
│  ┌─────────┐  ┌─────────┐          │
│  │ 📞 POMOC│  │ ℹ️ O APP│          │
│  │         │  │         │          │
│  │         │  │         │          │
│  └─────────┘  └─────────┘          │
└─────────────────────────────────────┘
```

## Moduł Inwentaryzacji - Szczegóły

### Uruchomienie Inwentaryzacji

1. **Tap na kafelek "INWENTARYZACJA"**
2. **Wybór typu inwentaryzacji**:

```
┌─────────────────────────────────────┐
│  📋 WYBÓR INWENTARYZACJI           │
├─────────────────────────────────────┤
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 🎯 INWENTARYZACJA PRODUKTOWA    │ │
│  │ Spisywanie konkretnych          │ │
│  │ produktów i etykiet             │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 📊 INWENTARYZACJA OGÓLNA        │ │
│  │ Kompleksowa inwentaryzacja      │ │
│  │ z pełną kontrolą                │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 📍 INWENTARYZACJA MIEJSC        │ │
│  │ Spisywanie według lokalizacji   │ │
│  │ magazynowych                    │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 🔧 INWENTARYZACJA GG            │ │
│  │ Specjalny typ inwentaryzacji    │ │
│  │                                 │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│            [ ← POWRÓT ]             │
└─────────────────────────────────────┘
```

### Inwentaryzacja Produktowa - Przepływ

#### 1. Wybór Sesji
```
┌─────────────────────────────────────┐
│  🎯 INWENTARYZACJA PRODUKTOWA      │
├─────────────────────────────────────┤
│                                     │
│  📅 Aktywne sesje:                 │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ Inwentaryzacja Q4 2024          │ │
│  │ 📊 750/1500 (50%)              │ │
│  │ 👤 Jan Kowalski, Anna Nowak     │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ Inwentaryzacja Hala A           │ │
│  │ 📊 234/800 (29%)               │ │
│  │ 👤 Piotr Wiśniewski            │ │
│  │                    [ WYBIERZ ] │ │
│  └─────────────────────────────────┘ │
│                                     │
│  [ + NOWA SESJA ]  [ ← POWRÓT ]    │
└─────────────────────────────────────┘
```

#### 2. Główny Ekran Inwentaryzacji
```
┌─────────────────────────────────────┐
│  🎯 INWENTARYZACJA - Q4 2024       │
│  📊 750/1500 (50%) | ⏱️ 2h 15min   │
├─────────────────────────────────────┤
│                                     │
│  📱 SKANOWANIE:                     │
│  ┌─────────────────────────────────┐ │
│  │ Zeskanuj kod produktu,          │ │
│  │ etykiety lub palety             │ │
│  │                                 │ │
│  │     🔍 GOTOWY DO SKANU         │ │
│  │                                 │ │
│  │ Ostatni skan: DS12345678        │ │
│  └─────────────────────────────────┘ │
│                                     │
│  📋 AKTUALNY PRODUKT:               │
│  ┌─────────────────────────────────┐ │
│  │ Kod: 5901234123457              │ │
│  │ Nazwa: Mleko UHT 1L             │ │
│  │ Teoretyczna: 100 SZT            │ │
│  │ ┌─────────────────┐             │ │
│  │ │ Spisana: [  98  ]│  [ ✓ OK ] │ │
│  │ └─────────────────┘             │ │
│  │ Lokalizacja: MP-1-A01-15-1      │ │
│  └─────────────────────────────────┘ │
│                                     │
│  [ 📊 STATUS ] [ ⚙️ OPCJE ] [ ← ] │
└─────────────────────────────────────┘
```

#### 3. Wprowadzanie Ilości
```
┌─────────────────────────────────────┐
│  📝 WPROWADŹ ILOŚĆ SPISANĄ          │
├─────────────────────────────────────┤
│                                     │
│  📦 Produkt: Mleko UHT 1L           │
│  📊 Teoretyczna: 100 SZT            │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │              98                 │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌───┬───┬───┐ ┌───┬───┬───┐       │
│  │ 1 │ 2 │ 3 │ │ 4 │ 5 │ 6 │       │
│  ├───┼───┼───┤ ├───┼───┼───┤       │
│  │ 7 │ 8 │ 9 │ │ 0 │ . │ ⌫ │       │
│  └───┴───┴───┘ └───┴───┴───┘       │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 💬 Uwagi (opcjonalnie):         │ │
│  │ ┌─────────────────────────────┐ │ │
│  │ │ Uszkodzone opakowanie - 2   │ │ │
│  │ └─────────────────────────────┘ │ │
│  └─────────────────────────────────┘ │
│                                     │
│    [ ❌ ANULUJ ]    [ ✅ ZAPISZ ]   │
└─────────────────────────────────────┘
```

#### 4. Potwierdzenie i Kontynuacja
```
┌─────────────────────────────────────┐
│  ✅ POZYCJA ZAPISANA                │
├─────────────────────────────────────┤
│                                     │
│  📦 Mleko UHT 1L                    │
│  📊 Teoretyczna: 100 SZT            │
│  📝 Spisana: 98 SZT                 │
│  ⚠️  Różnica: -2 SZT (Niedobór)     │
│                                     │
│  📍 Lokalizacja: MP-1-A01-15-1      │
│  👤 Pracownik: Jan Kowalski         │
│  ⏰ Czas: 10:45:30                  │
│                                     │
│  📈 Postęp sesji: 751/1500 (50.1%) │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 🔍 Gotowy do następnego skanu   │ │
│  └─────────────────────────────────┘ │
│                                     │
│         [ 🏠 MENU GŁÓWNE ]          │
└─────────────────────────────────────┘
```

### Funkcje Pomocnicze

#### Status Sesji
```
┌─────────────────────────────────────┐
│  📊 STATUS INWENTARYZACJI           │
├─────────────────────────────────────┤
│                                     │
│  📋 Sesja: Inwentaryzacja Q4 2024   │
│  📅 Data: 15.12.2024                │
│                                     │
│  📈 POSTĘP:                         │
│  ┌─────────────────────────────────┐ │
│  │ ████████████████████████░░░░░░  │ │
│  │           751 / 1500            │ │
│  │            50.1%                │ │
│  └─────────────────────────────────┘ │
│                                     │
│  📊 RÓŻNICE:                        │
│  • Nadwyżki: 15 pozycji             │
│  • Niedobory: 30 pozycji            │
│  • Zgodne: 706 pozycji              │
│                                     │
│  👥 PRACOWNICY:                     │
│  • Jan Kowalski: 250 poz.          │
│  • Anna Nowak: 301 poz.            │
│  • Piotr Wiśniewski: 200 poz.      │
│                                     │
│  ⏱️  Czas trwania: 2h 15min         │
│  🎯 Szacowany koniec: 16:30         │
│                                     │
│            [ ← POWRÓT ]             │
└─────────────────────────────────────┘
```

## Obsługa Skanera (DataWedge)

### Konfiguracja Profilu DataWedge

#### 1. Uruchomienie DataWedge
- Otwórz aplikację DataWedge (preinstalowana)
- Wybierz profil "WmsApp" lub utwórz nowy

#### 2. Ustawienia Profilu
```
DataWedge Profile: WmsApp
├── Associated apps: com.wms.app
├── Barcode input:
│   ├── Enabled: True
│   ├── Decoders: All enabled
│   └── Scanner selection: Auto
├── Intent output:
│   ├── Enabled: True
│   ├── Intent action: com.wms.INVENTORY_SCAN
│   └── Intent delivery: Broadcast Intent
└── Data formatting:
    ├── Prefix: (empty)
    └── Suffix: (empty)
```

#### 3. Obsługiwane Typy Kodów
- **EAN-13/8**: Kody produktów
- **Code 128**: Etykiety wewnętrzne
- **GS1-128**: Kody SSCC z Application Identifiers
- **Code 39**: Palety DS, lokalizacje MP

### Sygnały Dźwiękowe i Wibracje

#### Skany Pomyślne
- **🎵 Beep**: Pojedynczy dźwięk "beep"
- **📳 Wibracja**: Krótka wibracja (100ms)
- **🟢 LED**: Zielona lampka (2 sekundy)

#### Błędy Skanowania
- **🎵 Error**: Podwójny dźwięk "beep-beep"
- **📳 Wibracja**: Dłuższa wibracja (300ms)
- **🔴 LED**: Czerwona lampka (2 sekundy)

## Rozwiązywanie Problemów

### Problemy z Połączeniem

#### Błąd: "Brak połączenia z serwerem"
**Objawy**: Aplikacja nie może połączyć się z API
**Rozwiązanie**:
1. Sprawdź połączenie WiFi
2. Ping serwera: `ping ********` (emulator) lub `ping [IP_SERWERA]`
3. Sprawdź czy serwer nasłuchuje na porcie 8080
4. Zweryfikuj ustawienia proxy/firewall

#### Błąd: "Token wygasł"
**Objawy**: Komunikat o błędzie autoryzacji po pewnym czasie
**Rozwiązanie**:
1. Aplikacja automatycznie przekieruje do logowania
2. Zeskanuj kartę pracownika ponownie
3. System wygeneruje nowy token JWT

### Problemy ze Skanerem

#### Skaner nie reaguje
**Rozwiązanie**:
1. Sprawdź czy DataWedge jest uruchomiony
2. Zweryfikuj profil DataWedge dla aplikacji WMS
3. Restart aplikacji DataWedge
4. Restart urządzenia jeśli problem persystuje

#### Skanuje niepoprawne kody
**Rozwiązanie**:
1. Sprawdź ustawienia dekoderów w DataWedge
2. Wyczyść obiektyw skanera
3. Sprawdź odległość i kąt skanowania
4. Włącz więcej typów dekodowania w DataWedge

### Problemy z Danymi

#### Komunikat: "Kod nie znaleziony w inwentaryzacji"
**Możliwe przyczyny**:
- Produkt nie został dodany do bieżącej sesji inwentaryzacji
- Błędny kod produktu
- Sesja inwentaryzacji nie jest aktywna

**Rozwiązanie**:
1. Sprawdź czy skan jest poprawny
2. Spróbuj zeskanować kod ponownie
3. Skontaktuj się z kierownikiem w sprawie dodania produktu

#### Dane nie zapisują się
**Rozwiązanie**:
1. Sprawdź połączenie z internetem
2. Sprawdź czy sesja inwentaryzacji jest nadal aktywna
3. Restart aplikacji
4. Sprawdź dostępne miejsce na urządzeniu

## Wskazówki Najlepszych Praktyk

### Efektywne Skanowanie
1. **Trzymaj urządzenie stabilnie** - unikaj drżenia podczas skanowania
2. **Odpowiednia odległość** - 15-30cm od kodu kreskowego
3. **Dobry kąt** - skieruj skaner prostopadle do kodu
4. **Czyste kody** - usuwaj brud i zadrapania z kodów kreskowych

### Organizacja Pracy
1. **Systematyczne przechodzenie** - inwentaryzuj regał po regale
2. **Zapisuj uwagi** - notuj anomalie i problemy
3. **Sprawdzaj postęp** - regularnie sprawdzaj status sesji
4. **Koordynacja z zespołem** - komunikuj się z innymi pracownikami

### Zarządzanie Baterią
1. **Regularne ładowanie** - nie czekaj aż bateria się wyczerpie
2. **Tryb oszczędzania energii** - włączaj gdy praca kończy się
3. **Dodatkowa bateria** - zawsze miej zapasową baterię w gotowości
4. **Wyłączanie nieużywanych funkcji** - Bluetooth, GPS gdy nie używane

## Aktualizacje Aplikacji

### Automatyczne Aktualizacje
1. **Sprawdzanie przy starcie** - aplikacja automatycznie sprawdza dostępność aktualizacji
2. **Pobieranie w tle** - aktualizacja pobiera się automatycznie przez WiFi
3. **Weryfikacja SHA-256** - sprawdzenie integralności pliku przed instalacją
4. **Instalacja** - użytkownik otrzymuje powiadomienie o gotowej aktualizacji

### Ręczne Sprawdzenie Aktualizacji
```
Ustawienia > O aplikacji > Sprawdź aktualizacje
```

### Troubleshooting Aktualizacji
- **Błąd pobierania**: Sprawdź połączenie WiFi
- **Błąd weryfikacji**: Usuń pobraną aktualizację i pobierz ponownie
- **Błąd instalacji**: Sprawdź czy masz włączone "Nieznane źródła" w ustawieniach Android

---

*Przewodnik zaktualizowany: 2025-01-14 - Kompleksowa instrukcja użytkowania aplikacji mobilnej* 📱✅