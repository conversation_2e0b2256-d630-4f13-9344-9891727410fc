# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Warehouse Management System (WMS) consisting of a .NET MAUI mobile application for Zebra MC3300 devices (Android) and an ASP.NET Core (.NET 9) backend with MySQL database. The system focuses on two key operations: card-based user login and pallet location changes using a "scan → scan" workflow.

## Technology Stack

### Backend (.NET 9)
- **Wms.Api** - REST API controllers, middleware, authentication
- **Wms.Application** - Use cases, DTOs, validations 
- **Wms.Domain** - Domain entities, business rules
- **Wms.Infrastructure** - EF Core, repositories, MySQL integration

### Frontend (MAUI Android)
- **WmsApp** - Mobile application targeting Zebra MC3300 devices
- Zebra DataWedge integration for barcode scanning
- CommunityToolkit.Mvvm for MVVM pattern
- Refit for HTTP client API calls

### Database
- MySQL 8.0 with Entity Framework Core (Pomelo provider)
- Legacy database mapping with Clean Architecture principles

## Common Commands

### Build entire solution
```bash
dotnet build
```

### Run backend API (Development)
```bash
DISABLE_FLUENT_VALIDATION=1 ASPNETCORE_URLS=http://127.0.0.1:8081 ASPNETCORE_ENVIRONMENT=Development dotnet run --project src/backend/Wms.Api/Wms.Api.csproj --no-launch-profile
```

### Run API proxy (port 8080)
```bash
python api-proxy-local.py
```

### Build mobile app (Debug configuration)
```bash
cd src/mobile/WmsApp
dotnet build --configuration Debug
```

### Run mobile app (Android - requires device/emulator)
```bash
cd src/mobile/WmsApp
dotnet build -t:Run -f net9.0-android --configuration Debug
```

### Run tests
```bash
dotnet test
```

## Testing Configuration

### Test Card Number
Use card number: **1234567**

### Test Login Endpoint
```bash
# Test through proxy (recommended)
curl -v -H "Content-Type: application/json" -d '{"cardNumber":"1234567","deviceId":"Emulator"}' http://127.0.0.1:8080/api/v1/auth/login-scan

# Test health endpoint
curl -v http://127.0.0.1:8080/api/v1/health
```

### Test JSON Payload
```json
{
  "cardNumber": "1234567",
  "deviceId": "Emulator"
}
```

**Important:** Always test through the proxy on port 8080, not directly against the backend. Do not use mock services for testing.

## Architecture

### Clean Architecture Pattern
The backend follows Clean Architecture with clear separation of concerns:
- **Domain**: Core business entities and rules
- **Application**: Use cases and business logic
- **Infrastructure**: Data access and external services  
- **API**: Controllers and presentation layer

### Mobile Architecture
- **MVVM pattern** using CommunityToolkit.Mvvm
- **Services layer** for API communication and business logic
- **Platform-specific code** in Platforms/Android for DataWedge integration
- **Localization** support with .resx files and TranslateExtension

### Database Integration
- Legacy MySQL database mapping with EF Core
- Clean separation between domain entities and database models
- Transaction management using Unit of Work pattern
- AsNoTracking strategy for read-only operations to avoid EF tracking conflicts

## Key Patterns and Conventions

### Code Validation
Centralized validation for warehouse-specific codes:
- **SSCC**: 18 digits (Serial Shipping Container Code)
- **DS**: Format `DS\d{8}` (internal pallet codes) 
- **Location**: Format `MP-\d+-\d+-\d+-\d+` (warehouse locations)
- **Card Numbers**: 5-20 digits for user authentication

### GS1-128 Barcode Support  
The system supports GS1-128 barcodes with Application Identifiers (AI):
- Prefix `IZ` for delivery registration mode
- Supported AIs: 00 (SSCC), 01/02 (GTIN), 10 (Batch), 17 (Expiry), 37 (Quantity)
- FNC1 separator handling for variable-length fields

### Error Handling
- Global exception middleware with ProblemDetails responses
- Domain-specific exceptions (e.g., PalletNotFoundException)
- Polish error messages for user-facing content
- Structured logging with Serilog

### API Patterns
- RESTful API with versioning (`/api/v1/`)
- JWT authentication (1-hour expiry, no refresh tokens)
- Consistent response formats with proper HTTP status codes
- OpenAPI/Swagger documentation

### Mobile Patterns
- Modal selectors using CollectionView with proper MVVM binding
- DataWedge integration via BroadcastReceiver
- TaskCompletionSource for async modal results
- SecureStorage for JWT token persistence

## Testing Strategy

### Test Organization  
- Unit tests in `tests/Wms.UnitTests/`
- Integration tests in `tests/Wms.IntegrationTests/`
- API tests in `tests/Wms.Api.Tests/`
- Application tests in `tests/Wms.Application.Tests/`

### Naming Convention
Format: `MethodName_StateUnderTest_ExpectedBehavior`
```csharp
[Fact]
public void MovePallet_WhenPalletNotExists_ShouldThrowPalletNotFoundException()
```

## Development Environment

### Local Backend + Proxy Setup
1. **Backend**: Runs on port 8081 with FluentValidation disabled for development
   ```bash
   DISABLE_FLUENT_VALIDATION=1 ASPNETCORE_URLS=http://127.0.0.1:8081 ASPNETCORE_ENVIRONMENT=Development dotnet run --project src/backend/Wms.Api/Wms.Api.csproj --no-launch-profile
   ```

2. **Proxy**: Runs on port 8080 and forwards requests to backend
   ```bash
   python api-proxy-local.py
   ```

3. **Mobile app**: Connects to 127.0.0.1:8080 (localhost) or ********:8080 (Android emulator)

4. **Testing**: Always use proxy port 8080, test card number: 1234567

### Common Issues
- **WinError 10061**: Backend not running on port 8081
- **503 Service Unavailable**: Remote server issues, use local setup
- **TypeLoadException**: FluentValidation conflicts, use `DISABLE_FLUENT_VALIDATION=1`
- **Port conflicts**: Check running processes with `netstat -ano | findstr :8081`
- **Proxy connection refused**: Port 8080 may be occupied, kill Python processes first

## Observability

### Logging
- Serilog with structured logging
- Log files in `src/backend/Wms.Api/logs/`
- Promtail → Loki → Grafana for centralized logging

### Monitoring  
- OpenTelemetry → Prometheus → Grafana for metrics
- Health checks on `/api/v1/health`
- Custom metrics for business operations

## Security

### Authentication
- JWT tokens with card-based login (no passwords)
- 1-hour token expiry with session management
- DeviceId tracking for audit purposes

### Transport Security
- HTTPS only through Apache reverse proxy
- Certificate validation in production
- CORS configuration for API access

## Documentation

Key documentation files in `docs/`:
- `PRD.md` - Product requirements
- `ARCHITECTURE.md` - Detailed system architecture  
- `STYLE_GUIDE.md` - Coding standards and conventions
- `LOCAL_TESTING.md` - Local development and testing
- `GS1_PARSING.md` - GS1 barcode parsing details
- `TROUBLESHOOTING.md` - Common issues and solutions

## Performance Considerations

### Database
- Indexed queries on palletCode, locationCode, occurredAt
- AsNoTracking() for read-only operations
- Proper Include() usage to avoid N+1 queries
- Transaction scope for consistency

### Mobile
- ConfigureAwait(false) in service methods
- Minimal UI thread allocations
- Cached static data (locations)
- Proper disposal of resources