using Wms.Domain.Common;

namespace Wms.Domain.Entities;

/// <summary>
/// Encja reprezentująca typ palety w systemie
/// Mapowana na tabelę typypalet
/// </summary>
public class TypyPalet : BaseEntity
{
    public int Id { get; set; }
    public string? Kod { get; set; }
    public string Opis { get; set; } = string.Empty;
    public decimal UdzialSkladowania { get; set; } = 1.00m;
    public int KolejnoscPal { get; set; } = 0;
    public string PalDlugosc { get; set; } = string.Empty;
    public string PalSzerokosc { get; set; } = string.Empty;
    
    // Navigation properties
    public ICollection<Pallet> Pallets { get; set; } = new List<Pallet>();
    
    // Business logic properties
    public bool HasDimensions => !string.IsNullOrEmpty(PalDlugosc) && !string.IsNullOrEmpty(PalSzerokosc);
    
    /// <summary>
    /// Zwraca pełny opis typu palety dla UI
    /// </summary>
    public string GetDisplayName()
    {
        var parts = new List<string> { Opis };
        
        if (!string.IsNullOrEmpty(Kod))
            parts.Add($"({Kod})");
            
        if (HasDimensions)
            parts.Add($"{PalDlugosc}x{PalSzerokosc}");
            
        return string.Join(" ", parts);
    }
    
    /// <summary>
    /// Sprawdza czy typ palety może być używany do generowania DS
    /// </summary>
    public bool IsValidForDSGeneration()
    {
        return !string.IsNullOrEmpty(Opis) && UdzialSkladowania > 0;
    }
}
