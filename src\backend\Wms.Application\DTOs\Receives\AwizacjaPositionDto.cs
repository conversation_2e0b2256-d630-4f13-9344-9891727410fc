namespace Wms.Application.DTOs.Receives;

public class AwizacjaPositionDto
{
    public int Id { get; set; }
    public int SystemId { get; set; }
    public string TowarKod { get; set; } = string.Empty;
    public string TowarNazwa { get; set; } = string.Empty;
    public decimal IloscAwizowana { get; set; }
    public decimal IloscPrzyjeta { get; set; }
    public string? EtykietaKlient { get; set; } // SSCC z awizacji
    public string? Lot { get; set; }
    public string? Blloc { get; set; }
    public string? SSCC { get; set; }
}

