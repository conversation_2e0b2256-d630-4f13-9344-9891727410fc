using Wms.Domain.Common;

namespace Wms.Domain.Entities.Receives;

/// <summary>
/// Encja reprezentująca powiązanie palety DS z dostawą
/// Mapowana na tabelę listcontrol_palety
/// </summary>
public class ListControlPallet : BaseEntity
{
    public int Id { get; set; }
    public int ListcontrolId { get; set; }
    public int PaletaId { get; set; } // UWAGA: To jest numer DS z docnumber, nie palety.id!
    public int Wydruk { get; set; } = 1;
    
    // Navigation properties
    public ListControl ListControl { get; set; } = null!;
    
    // Business logic helpers
    public bool IsPrinted => Wydruk == 1;
    
    /// <summary>
    /// Formatuje numer palety jako DS{PaletaId}
    /// </summary>
    public string GetDSCode() => $"DS{PaletaId}";
    
    /// <summary>
    /// Sprawdza czy paleta jest gotowa do druku
    /// </summary>
    public bool IsReadyForPrint() => !IsPrinted;
}
