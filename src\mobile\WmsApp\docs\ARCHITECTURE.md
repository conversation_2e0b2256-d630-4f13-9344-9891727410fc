# Architektura Aplikacji WMS (MAUI)

## Przegląd Architektury

Aplikacja WMS (Warehouse Management System) jest zbudowana w technologii .NET MAUI wykorzystując architekturę MVVM (Model-View-ViewModel) z dependency injection oraz wzorcem Repository.

## Struktura Katalogów

```
WmsApp/
├── Models/                    # Modele danych i DTO
│   ├── Receives/             # Modele dla modułu dostaw
│   ├── Locations/            # Modele lokalizacji
│   └── Pallets/              # Modele palet
├── ViewModels/               # ViewModels dla logiki prezentacji
│   ├── Receives/             # ViewModels dostaw
│   └── ...                   
├── Views/                    # Widoki XAML
│   ├── Receives/             # Strony dostaw
│   └── ...
├── Services/                 # Serwisy biznesowe
│   ├── Interfaces/           # Interfejsy serwisów
│   ├── Receives/             # Serwisy dostaw
│   └── ...
├── Converters/               # Konwertery XAML
├── Platforms/                # Kod specyficzny dla platform
│   └── Android/              # Receivery DataWedge dla Zebra
└── docs/                     # Dokumentacja projektu
```

## Wzorce Architektoniczne

### MVVM (Model-View-ViewModel)
- **Model**: DTO i modele danych transferowane z API
- **View**: Pliki XAML definiujące interfejs użytkownika  
- **ViewModel**: Logika prezentacji, binding properties, komendy

### Dependency Injection
- Wszystkie serwisy rejestrowane w `MauiProgram.cs`
- Lifetime management: Singleton dla serwisów, Transient dla ViewModels
- Automatyczne wstrzykiwanie zależności przez konstruktor

### Repository Pattern (Implied)
- Serwisy API acting jako repositories
- Separacja logiki dostępu do danych od logiki biznesowej
- Mock implementations dla testów

## Moduł Dostaw - Szczegóły Architektoniczne

### Warstwa Modeli (Models/Receives/)

#### DTO (Data Transfer Objects)
- `AwizacjaDto` - Dane dostawy/awizacji
- `AwizacjaPositionDto` - Pozycje w dostawie  
- `NosnikDto`, `NosnikPositionDto` - Nośniki i ich pozycje
- `KodDto`, `TypPaletyDto` - Kody towarów i typy palet

#### Request/Response Objects
- `GetAwizacjaRequest/Response` - Pobieranie dostaw
- `ClaimAwizacjaRequest/Response` - Zajmowanie dostawy
- `RegisterPositionRequest/Response` - Rejestracja pozycji

#### Enumeratory
```csharp
public enum ReceiveScanCodeType
{
    Unknown,
    GS1,      // Kody GS1 towarów
    SSCC,     // Serial Shipping Container Code
    DS        // Kod Dokumentu Spedycyjnego  
}

public enum RegistrationStatus
{
    Pending,    // Oczekuje
    Registered, // Zarejestrowana
    Error       // Błąd
}
```

### Warstwa Serwisów (Services/Receives/)

#### IReceiveApiClient
Interfejs komunikacji z backendem:
```csharp
public interface IReceiveApiClient
{
    Task<GetAwizacjaResponse> GetAwizacjeAsync(GetAwizacjaRequest request);
    Task<ClaimAwizacjaResponse> ClaimAwizacjaAsync(ClaimAwizacjaRequest request);
    Task<RegisterPositionResponse> RegisterPositionAsync(RegisterPositionRequest request);
    // ...
}
```

#### ReceiveService
Główny serwis biznesowy abstrakcyjny od API:
- Orchestracja wywołań API
- Mapowanie między DTO a ViewModels
- Obsługa błędów i retry logic
- Cache management

#### ReceiveCodeValidationService
Specjalistyczny serwis walidacji kodów:
- Parsowanie kodów GS1 (GTIN, LOT, EXP)  
- Walidacja SSCC (20-cyfrowy kod z checksum)
- Rozpoznawanie kodów DS (dokumentów spedycyjnych)
- Return `ReceiveScanResult` z typem kodu i sparsowanymi danymi

#### MockReceiveService  
Implementacja testowa bez backendu:
- Symulacja opóźnień sieciowych
- Generowanie realistycznych danych testowych
- Używana w trybie debug (#if DEBUG)

### Warstwa ViewModels (ViewModels/Receives/)

#### ReceivesSelectionViewModel
Odpowiedzialność:
- Lista dostępnych dostaw (`ObservableCollection<AwizacjaDto>`)
- Filtrowanie i wyszukiwanie dostaw
- Claim/unclaim dostaw
- Generowanie kodów DS i obsługa drukarek
- Nawigacja do rejestracji

Kluczowe właściwości:
```csharp
[ObservableProperty] private ObservableCollection<AwizacjaDto> awizacje;
[ObservableProperty] private AwizacjaDto? selectedAwizacja;
[ObservableProperty] private bool isGenerateDsModalVisible;
[ObservableProperty] private string[] generatedDsCodes;
```

#### ReceivesRegistrationViewModel  
Odpowiedzialność:
- Rejestracja pozycji w dostawie
- Obsługa skanowania kodów (GS1, SSCC, DS)
- Walidacja danych pozycji
- Preview zarejestrowanych pozycji
- Zarządzanie nośnikami i paletami

Kluczowe właściwości:
```csharp
[ObservableProperty] private string currentLK;        // Current Liść Księgowy
[ObservableProperty] private string currentNosnik;    // Current nośnik
[ObservableProperty] private string scanInput;       // Input ze skanera
[ObservableProperty] private string towarKod;        // Kod towaru
[ObservableProperty] private decimal iloscSztuk;     // Ilość sztuk
```

### Warstwa Views (Views/Receives/)

#### ReceivesSelectionPage.xaml
Funkcjonalności UI:
- Lista dostaw z filtrowaniem i sortowaniem
- Detail view wybranej dostawy  
- Modal generowania kodów DS z konfiguracją drukarki
- Progress indicators dla długotrwałych operacji

#### ReceivesRegistrationPage.xaml
Funkcjonalności UI:  
- Header z informacjami o dostawie i nośniku
- Input field dla skanera z automatycznym focus
- Formularz rejestracji pozycji z walidacją
- Lista zarejestrowanych pozycji z możliwością edycji
- Modali konfirmacji i podglądu

### Integracja z Zebra DataWedge (Platforms/Android/)

#### ReceivesDataWedgeReceiver
BroadcastReceiver obsługujący skany:
```csharp
[BroadcastReceiver(Enabled = true)]
public class ReceivesDataWedgeReceiver : BroadcastReceiver
{
    public override void OnReceive(Context context, Intent intent)
    {
        string scanData = intent.GetStringExtra("com.symbol.datawedge.data_string");
        string scanType = intent.GetStringExtra("com.symbol.datawedge.label_type");
        
        // Routing based on scan type and current context
        if (IsReceiveContext())
        {
            WeakReferenceMessenger.Default.Send(new LkScannedMessage(scanData));
        }
    }
}
```

### Konwertery XAML (Converters/ReceiveConverters.cs)

Specjalne konwertery dla modułu dostaw:
- `BoolToOccupiedConverter` - Status zajętości dostawy
- `RegistrationStatusToColorConverter` - Kolory statusów rejestracji  
- `SsccDisplayConverter` - Formatowanie SSCC codes
- `IdToTypPaletyConverter` - Mapowanie ID na nazwy typów palet
- `NumberWithUnitConverter` - Formatowanie liczb z jednostkami

## Wzorce Komunikacji

### WeakReferenceMessenger (CommunityToolkit.Mvvm)
Używany do komunikacji między komponentami:
```csharp
// Rejestracja odbiorcy w ViewModelu
WeakReferenceMessenger.Default.Register<LkScannedMessage>(this, OnLkScanned);

// Wysyłanie wiadomości
WeakReferenceMessenger.Default.Send(new LkScannedMessage(scanData));

// Handler w ViewModelu
private void OnLkScanned(object recipient, LkScannedMessage message)
{
    MainThread.BeginInvokeOnMainThread(() => {
        ProcessScannedLk(message.Value);
    });
}
```

#### Typy Wiadomości (Messages/)
- `LkScannedMessage` - Zeskanowany kod LK
- `GS1ScannedMessage` - Zeskanowany kod GS1 towaru
- `SsccScannedMessage` - Zeskanowany kod SSCC
- `DsScannedMessage` - Zeskanowany kod DS
- `PrinterIpScannedMessage` - IP drukarki ze skanu
- `ScanProcessedMessage` - Potwierdzenie przetworzenia skanu
- `ScanErrorMessage` - Błąd podczas przetwarzania skanu

### Command Pattern
Wszystkie akcje UI używają RelayCommand:
```csharp
[RelayCommand]
private async Task ProcessScanAsync()
{
    // Implementation
}
```

## Obsługa Błędów

### Strategia Error Handling
1. **API Level**: HttpClient exceptions handled w serwisach
2. **Service Level**: Business logic exceptions z retry logic  
3. **ViewModel Level**: User-friendly error messages
4. **UI Level**: Error states i progress indicators

### Logging
Używamy `ILogger<T>` z dependency injection:
```csharp
_logger.LogError(ex, "Błąd podczas przetwarzania skanu: {ScanInput}", ScanInput);
```

## Konfiguracja DI (MauiProgram.cs)

```csharp
// Services
builder.Services.AddSingleton<IReceiveApiClient, ReceiveApiClient>();
builder.Services.AddScoped<IReceiveService, ReceiveService>();
builder.Services.AddSingleton<ReceiveCodeValidationService>();

// ViewModels  
builder.Services.AddTransient<ReceivesSelectionViewModel>();
builder.Services.AddTransient<ReceivesRegistrationViewModel>();

// Views
builder.Services.AddTransient<ReceivesSelectionPage>();
builder.Services.AddTransient<ReceivesRegistrationPage>();
```

## Routing i Nawigacja (AppShell.xaml)

```xml
<TabBar Title="Główne">
    <Tab Title="Przyjęcia" Icon="truck_icon.png">
        <ShellContent Route="receives/selection" 
                      ContentTemplate="{DataTemplate receives:ReceivesSelectionPage}" />
        <ShellContent Route="receives/registration" 
                      ContentTemplate="{DataTemplate receives:ReceivesRegistrationPage}" />
    </Tab>
</TabBar>
```

## Performance Considerations

### Optimizations Applied
- ObservableCollection dla reaktywnych UI updates
- Nullable reference types dla lepszej type safety  
- Async/await pattern dla non-blocking UI
- Scoped ViewModels dla proper memory management

### Applied Optimizations ✅
- XAML Compiled Bindings (x:DataType) - zaimplementowane
- WeakReferenceMessenger zamiast MessagingCenter - zaimplementowane
- Frame → Border w .NET 9 - zaimplementowane
- Threading safety dla UI updates - zaimplementowane

### Planned Optimizations
- CollectionView virtualization dla dużych list
- Image caching dla ikon i grafik
- Background task management
- ObservableRecipient pattern dla ViewModels

---

**Data ostatniej aktualizacji:** 10.09.2024
