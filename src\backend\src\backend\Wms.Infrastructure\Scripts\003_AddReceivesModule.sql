-- =================================================================
-- 003_AddReceivesModule.sql
-- Migracja dla modułu dostaw - dodanie obsługi claim/release dostaw
-- Data: 2025-09-09
-- Autor: WMS System Development Team
-- =================================================================

-- ==========================================
-- 1. DODANIE KOLUMNY realizujacy_pracownik_id
-- ==========================================

-- Sprawd<PERSON>ie czy kolumna już istnieje
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND COLUMN_NAME = 'realizujacy_pracownik_id'
);

-- Dodanie kolumny tylko gdy nie istnieje
SET @sql = CASE 
    WHEN @column_exists = 0 THEN 
        'ALTER TABLE list_control ADD COLUMN realizujacy_pracownik_id INT UNSIGNED NULL AFTER wd_przyjecie_head_id'
    ELSE 'SELECT "Kolumna realizujacy_pracownik_id już istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 2. DODANIE INDEKSU dla wydajności queries
-- ==========================================

-- Sprawdzenie czy indeks już istnieje
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND INDEX_NAME = 'idx_realizujacy_pracownik'
);

-- Dodanie indeksu tylko gdy nie istnieje
SET @sql = CASE 
    WHEN @index_exists = 0 THEN 
        'CREATE INDEX idx_realizujacy_pracownik ON list_control(realizujacy_pracownik_id)'
    ELSE 'SELECT "Indeks idx_realizujacy_pracownik już istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 3. FOREIGN KEY dla spójności danych
-- ==========================================

-- Sprawdzenie czy FK już istnieje
SET @fk_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'list_control' 
    AND CONSTRAINT_NAME = 'fk_list_control_realizujacy_pracownik'
);

-- Dodanie FK tylko gdy nie istnieje
SET @sql = CASE 
    WHEN @fk_exists = 0 THEN 
        'ALTER TABLE list_control ADD CONSTRAINT fk_list_control_realizujacy_pracownik 
         FOREIGN KEY (realizujacy_pracownik_id) REFERENCES pracownicy(id) ON DELETE SET NULL'
    ELSE 'SELECT "Foreign key fk_list_control_realizujacy_pracownik już istnieje" as info'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 4. UPEWNIENIE SIĘ ŻE docnumber dla 'nrpalety' istnieje
-- ==========================================

-- Insert tylko gdy nie istnieje
INSERT IGNORE INTO docnumber (name, last) 
VALUES ('nrpalety', 650000);

-- ==========================================
-- 5. WERYFIKACJA POPRAWNOŚCI MIGRACJI
-- ==========================================

-- Sprawdzenie struktury po migracji
SELECT 
    'VERIFICATION' as step,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'list_control' 
            AND COLUMN_NAME = 'realizujacy_pracownik_id'
        ) THEN 'OK - Kolumna realizujacy_pracownik_id została dodana'
        ELSE 'ERROR - Kolumna realizujacy_pracownik_id nie istnieje'
    END as column_status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'list_control' 
            AND INDEX_NAME = 'idx_realizujacy_pracownik'
        ) THEN 'OK - Indeks został utworzony'
        ELSE 'ERROR - Indeks nie istnieje'
    END as index_status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM docnumber 
            WHERE name = 'nrpalety'
        ) THEN CONCAT('OK - docnumber nrpalety = ', (SELECT last FROM docnumber WHERE name = 'nrpalety'))
        ELSE 'ERROR - docnumber nrpalety nie istnieje'
    END as docnumber_status;

-- ==========================================
-- 6. INFORMACJA O ZAKOŃCZENIU
-- ==========================================

SELECT 
    'MIGRACJA 003 ZAKOŃCZONA POMYŚLNIE' as status,
    NOW() as completed_at,
    'Moduł dostaw gotowy do użycia' as message;
