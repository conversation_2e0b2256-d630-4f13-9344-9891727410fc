using Wms.Domain.Common;

namespace Wms.Domain.Entities;

public class Pallet : BaseEntity
{
    public int Id { get; set; }
    public int? TypypaletId { get; set; }
    public int? Ilosc { get; set; } = 0;
    public int? JSkladowaniaId { get; set; }
    public string? PalKlient { get; set; }
    public DateTime? TsInwentaryzacja { get; set; }
    public string? ResultInwentaryzacja { get; set; }
    public DateTime TsUtworzenia { get; set; } = DateTime.UtcNow;
    public int? PalDocinId { get; set; } = 0;
    
    // Navigation properties
    public ICollection<Label> Labels { get; set; } = new List<Label>(); // etykiety
    
    // Helper methods
    public Location? GetCurrentLocation()
    {
        // Lokalizacja jest określona przez etykiety, nie bezpośrednio przez paletę
        return Labels.FirstOrDefault(l => l.Active == 1)?.Location;
    }
    
    public string? GetMainSSCC()
    {
        // Główny kod SSCC z pierwszej aktywnej etykiety
        return Labels.FirstOrDefault(l => l.Active == 1 && !string.IsNullOrEmpty(l.Sscc))?.Sscc;
    }
}
