using FluentAssertions;
using Moq;
using Wms.Application.DTOs.Pallets;
using Wms.Application.Services;
using Wms.Application.Validators;

namespace Wms.UnitTests.Application.Validators;

public class MovePalletRequestValidatorTests
{
    private readonly Mock<ICodeValidationService> _mockCodeValidationService;
    private readonly MovePalletRequestValidator _validator;

    public MovePalletRequestValidatorTests()
    {
        _mockCodeValidationService = new Mock<ICodeValidationService>();
        _validator = new MovePalletRequestValidator(_mockCodeValidationService.Object);
    }

    [Fact]
    public void Validate_WithValidSSCCRequest_ShouldReturnValid()
    {
        // Arrange
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-1-A-1-1",
            Notes = "Test move operation"
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateDS(request.PalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithValidDSRequest_ShouldReturnValid()
    {
        // Arrange
        var request = new MovePalletRequest
        {
            ToLocationCode = "MP-2-RMP-010-4",
            Notes = null // Notes are optional
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Validate_WithEmptyToLocationCode_ShouldReturnInvalid(string toLocationCode)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            ToLocationCode = toLocationCode
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.ToLocationCode) 
            && e.ErrorMessage == "Target location code is required");
    }

    

    [Theory]
    [InlineData("INVALID")]
    [InlineData("MP-1-A-1")] // Missing level
    [InlineData("1-A-1-1")] // Missing MP prefix
    [InlineData("MP-1-A-1-10")] // Invalid level format (too long)
    [InlineData("MP-1234-A-1-1")] // Invalid hall format (too long)
    public void Validate_WithInvalidLocationCode_ShouldReturnInvalid(string invalidLocationCode)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = "123456789012345678",
            ToLocationCode = invalidLocationCode
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(invalidLocationCode)).Returns(false);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.ToLocationCode) 
            && e.ErrorMessage == "Location code must be in format MP-H-R-M-P (e.g., MP-1-A-1-1)");
    }

    [Fact]
    public void Validate_WithTooLongNotes_ShouldReturnInvalid()
    {
        // Arrange
        var longNotes = new string('A', 501); // 501 characters - too long
        var request = new MovePalletRequest
        {
            PalletCode = "123456789012345678",
            ToLocationCode = "MP-1-A-1-1",
            Notes = longNotes
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.Notes) 
            && e.ErrorMessage == "Notes cannot exceed 500 characters");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("Short note")]
    [InlineData("This is a longer note with more details about the move operation but still within limits")]
    public void Validate_WithValidNotes_ShouldReturnValid(string notes)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = "123456789012345678",
            ToLocationCode = "MP-1-A-1-1",
            Notes = notes
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithMaximumLengthNotes_ShouldReturnValid()
    {
        // Arrange
        var maxLengthNotes = new string('A', 500); // 500 characters - maximum valid
        var request = new MovePalletRequest
        {
            PalletCode = "DS12345",
            ToLocationCode = "MP-2-B-5-3",
            Notes = maxLengthNotes
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithMultipleErrors_ShouldReturnAllErrors()
    {
        // Arrange
        var longNotes = new string('A', 501); // Too long
        var request = new MovePalletRequest
        {
            PalletCode = "", // Empty pallet code
            ToLocationCode = "INVALID", // Invalid location format
            Notes = longNotes
        };

        _mockCodeValidationService.Setup(x => x.ValidateLocationCode("INVALID")).Returns(false);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(4); // Empty string causes 2 PalletCode errors + 1 LocationCode + 1 Notes = 4
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.PalletCode));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.ToLocationCode));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.Notes));
    }

    

    [Theory]
    [InlineData("MP-1-A-1-1")]
    [InlineData("MP-123-RMP-010-4")]
    [InlineData("MP-2-B-99-6")]
    public void Validate_WithDifferentValidLocationCodes_ShouldReturnValid(string locationCode)
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = "123456789012345678",
            ToLocationCode = locationCode
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(true);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(locationCode)).Returns(true);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithBothInvalidPalletAndLocationCodes_ShouldReturnBothErrors()
    {
        // Arrange
        var request = new MovePalletRequest
        {
            PalletCode = "invalid-pallet",
            ToLocationCode = "invalid-location"
        };

        _mockCodeValidationService.Setup(x => x.ValidateSSCC(request.PalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateDS(request.PalletCode)).Returns(false);
        _mockCodeValidationService.Setup(x => x.ValidateLocationCode(request.ToLocationCode)).Returns(false);

        // Act
        var result = _validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.PalletCode) 
            && e.ErrorMessage.Contains("valid SSCC") && e.ErrorMessage.Contains("DS code"));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(MovePalletRequest.ToLocationCode) 
            && e.ErrorMessage.Contains("MP-H-R-M-P"));
    }
}
