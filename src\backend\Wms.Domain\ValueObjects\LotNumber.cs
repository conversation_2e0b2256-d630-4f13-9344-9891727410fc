using System.Text.RegularExpressions;
using Wms.Domain.Exceptions;

namespace Wms.Domain.ValueObjects;

/// <summary>
/// Value Object reprezentujący numer partii/lotu
/// Format: alfanumeryczny do 20 znaków zgodnie ze standardem GS1 (AI 10)
/// </summary>
public record LotNumber : IComparable<LotNumber>
{
    // GS1 AI 10 allows alphanumeric up to 20 characters
    private static readonly Regex LotPattern = new(@"^[A-Za-z0-9\-_/\\.]{1,20}$", RegexOptions.Compiled);
    
    public string Value { get; private init; }
    public int Length => Value.Length;
    public bool IsNumeric => Regex.IsMatch(Value, @"^\d+$");
    public bool HasSpecialChars => Regex.IsMatch(Value, @"[\-_/\\.]");
    
    public string FormattedValue => $"(10) {Value}";

    private LotNumber(string value)
    {
        Value = value;
    }

    /// <summary>
    /// Tworzy LotNumber z ciągu znaków
    /// </summary>
    public static LotNumber Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new InvalidGS1FormatException(value ?? "null", "Numer partii/lotu nie może być pusty");

        // Clean up the value - remove control characters but keep allowed special chars
        var cleanValue = Regex.Replace(value.Trim(), @"[\x00-\x1F\x7F]", "");
        
        if (!LotPattern.IsMatch(cleanValue))
            throw new InvalidGS1FormatException(value, $"Nieprawidłowy format numeru partii. Dozwolone: alfanumeryczne znaki, '-', '_', '/', '\\', '.', maksymalnie 20 znaków. Otrzymano: '{cleanValue}'");

        return new LotNumber(cleanValue);
    }

    /// <summary>
    /// Próbuje utworzyć LotNumber z stringa
    /// </summary>
    public static bool TryCreate(string value, out LotNumber? lotNumber)
    {
        lotNumber = null;
        
        if (!IsValid(value))
            return false;

        var cleanValue = Regex.Replace(value.Trim(), @"[\x00-\x1F\x7F]", "");
        lotNumber = new LotNumber(cleanValue);
        return true;
    }

    /// <summary>
    /// Waliduje czy string może być numerem partii
    /// </summary>
    public static bool IsValid(string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        var cleanValue = Regex.Replace(value.Trim(), @"[\x00-\x1F\x7F]", "");
        return LotPattern.IsMatch(cleanValue);
    }

    /// <summary>
    /// Sprawdza czy numer partii jest w formacie daty (YYMMDD lub podobnym)
    /// </summary>
    public bool IsDateFormat()
    {
        // Common date formats in lot numbers: YYMMDD, YYYYMMDD, DDMMYY
        return Regex.IsMatch(Value, @"^\d{6}$|^\d{8}$") && TryParseAsDate() != null;
    }

    /// <summary>
    /// Próbuje sparsować numer partii jako datę
    /// </summary>
    public DateOnly? TryParseAsDate()
    {
        if (!IsNumeric) return null;

        // Try YYMMDD format (most common in lot numbers)
        if (Value.Length == 6 && 
            int.TryParse(Value[0..2], out var yy) &&
            int.TryParse(Value[2..4], out var mm) &&
            int.TryParse(Value[4..6], out var dd))
        {
            var year = yy + (yy > 50 ? 1900 : 2000); // Y2K handling
            if (IsValidDate(year, mm, dd))
                return new DateOnly(year, mm, dd);
        }

        // Try YYYYMMDD format
        if (Value.Length == 8 &&
            int.TryParse(Value[0..4], out var yyyy) &&
            int.TryParse(Value[4..6], out var mm2) &&
            int.TryParse(Value[6..8], out var dd2))
        {
            if (IsValidDate(yyyy, mm2, dd2))
                return new DateOnly(yyyy, mm2, dd2);
        }

        return null;
    }

    private static bool IsValidDate(int year, int month, int day)
    {
        if (year < 1900 || year > 2100) return false;
        if (month < 1 || month > 12) return false;
        if (day < 1 || day > DateTime.DaysInMonth(year, month)) return false;
        return true;
    }

    /// <summary>
    /// Porównuje dwa numery partii leksykograficznie
    /// </summary>
    public int CompareTo(LotNumber? other)
    {
        if (other is null) return 1;
        return string.Compare(Value, other.Value, StringComparison.OrdinalIgnoreCase);
    }

    public static bool operator <(LotNumber left, LotNumber right) => left.CompareTo(right) < 0;
    public static bool operator >(LotNumber left, LotNumber right) => left.CompareTo(right) > 0;
    public static bool operator <=(LotNumber left, LotNumber right) => left.CompareTo(right) <= 0;
    public static bool operator >=(LotNumber left, LotNumber right) => left.CompareTo(right) >= 0;

    public override string ToString() => Value;
    
    public static implicit operator string(LotNumber lotNumber) => lotNumber.Value;
    public static explicit operator LotNumber(string value) => Create(value);
}
