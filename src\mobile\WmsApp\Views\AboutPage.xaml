<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WmsApp.Views.AboutPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:WmsApp.ViewModels"
             xmlns:loc="clr-namespace:WmsApp.Localization"
             x:DataType="viewmodels:AboutViewModel"
             Title="{loc:Translate Key=About_Title}">

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="20">
            
            <!-- Informacje o aplikacji -->
            <Frame BackgroundColor="LightGray" Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding AppName}"
                           FontSize="24"
                           FontAttributes="Bold"
                           HorizontalOptions="Center" />
                    
                    <Label Text="{Binding CurrentVersion, StringFormat='Wersja: {0}'}"
                           FontSize="16"
                           HorizontalOptions="Center" />
                    
                    <Label Text="{Binding BuildNumber, StringFormat='Build: {0}'}"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </VerticalStackLayout>
            </Frame>

            <!-- Sekcja aktualizacji -->
            <Frame BackgroundColor="LightGray" Padding="20">
                <VerticalStackLayout Spacing="15">
<Label Text="{loc:Translate Key=About_Updates_Header}"
                           FontSize="18"
                           FontAttributes="Bold" />
                    
<Button Text="{loc:Translate Key=About_CheckUpdates_Button}"
                            Command="{Binding CheckForUpdatesCommand}" />
                    
                    <!-- Status sprawdzania -->
                    <Label Text="{Binding UpdateStatus}"
                           FontSize="14" />
                    
                    <!-- Indicator sprawdzania -->
                    <ActivityIndicator IsVisible="{Binding IsCheckingUpdate}"
                                       IsRunning="{Binding IsCheckingUpdate}" />
                    
                    <!-- Sekcja dostępnej aktualizacji -->
                    <StackLayout IsVisible="{Binding UpdateAvailable}" Spacing="10">
                        <Label Text="{Binding NewVersion, StringFormat='Nowa wersja: {0}'}"
                               FontSize="16"
                               FontAttributes="Bold" />
                        
                        <Label Text="{Binding ReleaseNotes, StringFormat='Zmiany: {0}'}"
                               FontSize="14" />
                        
<Label Text="{loc:Translate Key=About_UpdateRequired_Label}"
                               IsVisible="{Binding UpdateRequired}"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="Red" />
                        
<Button Text="{loc:Translate Key=About_DownloadInstall_Button}"
                                Command="{Binding DownloadAndInstallCommand}"
                                BackgroundColor="Green"
                                TextColor="White" />
                        
                        <!-- Progress pobierania -->
                        <Label Text="{Binding DownloadProgress}"
                               FontSize="14" />
                        
                        <!-- Indicator pobierania -->
                        <ActivityIndicator IsVisible="{Binding IsDownloading}"
                                           IsRunning="{Binding IsDownloading}" />
                    </StackLayout>
                </VerticalStackLayout>
            </Frame>

            <!-- Copyright -->
            <Label Text="© 2025 WMS System"
                   FontSize="12"
                   HorizontalOptions="Center"
                   Margin="0,20,0,0" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
