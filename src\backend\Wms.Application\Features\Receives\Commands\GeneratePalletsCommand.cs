using MediatR;
using Wms.Application.DTOs.Receives;

namespace Wms.Application.Features.Receives.Commands;

public record GeneratePalletsCommand : IRequest<GeneratePalletsResponse>
{
    public int ListControlId { get; init; }
    public int PracownikId { get; init; }
    public int TypPaletyId { get; init; }
    public int Ilosc { get; init; }
    public bool Drukowac { get; init; }
    public string? DrukarkaIp { get; init; }
}
