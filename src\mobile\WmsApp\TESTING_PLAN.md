# 🧪 PLAN TESTOWY - <PERSON><PERSON><PERSON> "Zmiana miejsca"

## 📱 Testowanie z Mock API

### **Mock dane przygotowane:**

#### **<PERSON><PERSON>y testowe:**
- `123456789012345678` (SSCC) - obe<PERSON>na lokalizacja: `MP-1-145-002-1`
- `DS12345678` (DS kod) - obecna lokalizacja: `MP-1-146-003-2`

#### **Lok<PERSON><PERSON>je testowe:**
- `MP-1-145-002-1` (<PERSON><PERSON> 1, <PERSON><PERSON><PERSON> 145)
- `MP-1-146-003-2` (<PERSON><PERSON> 1, <PERSON><PERSON><PERSON> 146)
- `MP-2-200-001-1` (Hal<PERSON> 2, <PERSON><PERSON><PERSON> 200)

---

## 🚀 **SCENARIUSZE TESTOWE:**

### **1. <PERSON><PERSON><PERSON><PERSON> (wymagane przed testami)**
- **<PERSON> testowe:** Karta rozpoczynająca się od `00000` (np. `00000123456`)
- **Oczekiwany wynik:** ✅ Pomyślne logowanie → Dashboard

### **2. <PERSON><PERSON><PERSON><PERSON> do funkcji**
- **<PERSON>rok<PERSON>:** Dash<PERSON> → "<PERSON>c<PERSON>" → "<PERSON>miana miejsca"
- **Oczekiwany wynik:** ✅ Przejście do strony Zmiana miejsca

---

## 🎯 **TESTY FUNKCJONALNOŚCI:**

### **TEST 1: Pomyślne przeniesienie palety**
1. **Wpisz kod palety:** `123456789012345678`
2. **Oczekuj:** ✅ Wyświetlenie info o palecie + obecna lokalizacja `MP-1-145-002-1`
3. **Wpisz lokalizację docelową:** `MP-1-146-003-2`
4. **Oczekuj:** ✅ "Paleta przeniesiona pomyślnie!" + automatyczny reset

### **TEST 2: Nieznana paleta**
1. **Wpisz kod palety:** `999999999999999999`
2. **Oczekuj:** ❌ "Paleta nie została znaleziona w systemie"

### **TEST 3: Nieprawidłowy kod palety**
1. **Wpisz kod palety:** `123` (za krótki)
2. **Oczekuj:** ❌ "Nieprawidłowy kod palety..."

### **TEST 4: Nieprawidłowa lokalizacja**
1. **Wpisz kod palety:** `DS12345678`
2. **Wpisz lokalizację:** `INVALID-LOCATION`
3. **Oczekuj:** ❌ "Nieprawidłowy kod lokalizacji..."

### **TEST 5: Nieistniejąca lokalizacja**
1. **Wpisz kod palety:** `DS12345678`
2. **Wpisz lokalizację:** `MP-9-999-999-9`
3. **Oczekuj:** ❌ "Lokalizacja nie istnieje lub jest nieaktywna"

### **TEST 6: Paleta już w tej lokalizacji**
1. **Wpisz kod palety:** `123456789012345678`
2. **Wpisz tę samą lokalizację:** `MP-1-145-002-1`
3. **Oczekuj:** ⚠️ "Paleta już znajduje się w tej lokalizacji..." + reset po 3 sekundach

### **TEST 7: Anulowanie operacji**
1. **Wpisz kod palety:** `DS12345678`
2. **Kliknij "Anuluj"**
3. **Oczekuj:** 🔙 Powrót do strony Opcje

### **TEST 8: Test DS kodów**
1. **Wpisz kod palety:** `DS12345678`
2. **Oczekuj:** ✅ Wyświetlenie info o palecie + obecna lokalizacja `MP-1-146-003-2`
3. **Wpisz lokalizację:** `MP-2-200-001-1`
4. **Oczekuj:** ✅ Pomyślne przeniesienie

---

## 📊 **KRYTERIA AKCEPTACJI:**

### ✅ **Musi działać:**
- [ ] Walidacja kodów (SSCC 18 cyfr, DS prefiksy, MP lokalizacje)
- [ ] Pobieranie informacji o palecie
- [ ] Wyświetlanie obecnej lokalizacji palety
- [ ] Wykonywanie ruchu palety
- [ ] Obsługa wszystkich błędów zgodnie z specyfikacją
- [ ] Komunikaty w języku polskim
- [ ] Loading states podczas operacji API
- [ ] Anulowanie w dowolnym momencie
- [ ] Reset do stanu początkowego po operacji

### 🎨 **UI/UX:**
- [ ] Duże, czytelne przyciski
- [ ] Kolorowe komunikaty (zielony/czerwony/pomarańczowy)
- [ ] Płynne przejścia między krokami
- [ ] Responsive design na różnych ekranach

---

## 🏃‍♂️ **JAK URUCHOMIĆ TESTY:**

```bash
# Kompilacja z mock API (tryb DEBUG)
dotnet build -c Debug

# Ewentualnie uruchomienie na emulatorze (jeśli dostępny)
dotnet build -t:Run -f net9.0-android
```

### **Alternatywnie - testowanie jednostkowe:**
Można napisać testy jednostkowe dla MovePalletViewModel z użyciem MockWmsApiService.

---

## 📝 **NOTATKI TESTOWE:**

- **Mock API** symuluje opóźnienia sieciowe (200ms-1s)
- **Wszystkie operacje** działają lokalnie bez rzeczywistego backendu
- **Stan palet** jest aktualizowany w mock danych (trwały podczas sesji)
- **Komunikaty błędów** dokładnie jak w rzeczywistym API

---

## 🎯 **NASTĘPNE KROKI PO TESTACH:**

1. **Naprawy błędów** znalezionych podczas testów
2. **Integracja DataWedge** dla rzeczywistego skanowania
3. **Konfiguracja połączenia** z rzeczywistym backendem
4. **Testy na urządzeniu Zebra MC3300**
