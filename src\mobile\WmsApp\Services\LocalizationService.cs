using System.Globalization;
using WmsApp.Localization;

namespace WmsApp.Services;

public interface ILocalizationService
{
    CultureInfo CurrentCulture { get; }
    void SetCulture(string cultureCode);
}

public sealed class LocalizationService : ILocalizationService
{
    public CultureInfo CurrentCulture => CultureInfo.CurrentUICulture;

    public void SetCulture(string cultureCode)
    {
        var culture = CultureInfo.GetCultureInfo(cultureCode);

        CultureInfo.DefaultThreadCurrentUICulture = culture;
        CultureInfo.DefaultThreadCurrentCulture = culture;

        LocalizationResourceManager.Current.SetCulture(culture);

        Preferences.Default.Set("AppLanguage", cultureCode);
    }
}

