using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Wms.Application.DTOs.Inventory;
using Wms.Application.Features.Inventory.Commands;
using Wms.Application.Features.Inventory.Queries;

namespace Wms.Api.Controllers;

/// <summary>
/// Kontroler inwentaryzacji z obsługą skanowania SSCC i DS
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class InventoryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<InventoryController> _logger;

    public InventoryController(IMediator mediator, ILogger<InventoryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Pobiera listę dostępnych sesji inwentaryzacji
    /// </summary>
    /// <param name="type">Typ inwentaryzacji (opcjonalny)</param>
    /// <param name="limit">Limit wyników (default: 40)</param>
    /// <returns>Lista dostępnych inwentaryzacji</returns>
    [HttpGet("sessions")]
    public async Task<ActionResult<IEnumerable<InventoryDto>>> GetAvailableInventories(
        [FromQuery] InventoryType? type = null,
        [FromQuery] int limit = 40)
    {
        try
        {
            var query = new GetActiveInventoriesQuery
            {
                Type = type,
                Limit = limit
            };

            var result = await _mediator.Send(query);

            return Ok(new
            {
                data = result,
                success = true,
                message = "Inwentaryzacje pobrane pomyślnie"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available inventories");
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_LIST_ERROR",
                    message = "Błąd pobierania listy inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Rozpoczyna sesję inwentaryzacji dla użytkownika
    /// </summary>
    /// <param name="request">Dane do rozpoczęcia sesji</param>
    /// <returns>Informacje o sesji inwentaryzacji</returns>
    [HttpPost("sessions/start")]
    public async Task<ActionResult<InventorySessionDto>> StartInventorySession(
        [FromBody] StartInventoryRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var deviceId = request.DeviceId;

            var command = new StartInventoryCommand
            {
                InventoryId = request.InventoryId,
                PracownikId = userId,
                DeviceId = deviceId,
                Type = InventoryType.Product // Domyślnie produktowa
            };

            var result = await _mediator.Send(command);

            return Ok(new
            {
                data = result,
                success = true,
                message = "Sesja inwentaryzacji rozpoczęta"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid inventory session request");
            return BadRequest(new
            {
                success = false,
                error = new
                {
                    code = "INVALID_REQUEST",
                    message = ex.Message
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting inventory session for inventory {InventoryId}", request.InventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "SESSION_START_ERROR",
                    message = "Błąd rozpoczynania sesji inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Przetwarza zeskanowany kod (SSCC lub DS) w inwentaryzacji
    /// </summary>
    /// <param name="request">Dane skanu</param>
    /// <returns>Rezultat przetwarzania skanu</returns>
    [HttpPost("scan")]
    public async Task<ActionResult<InventoryScanResponse>> ProcessInventoryScan(
        [FromBody] InventoryScanRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new ProcessInventoryScanCommand
            {
                InventorySessionId = request.InventorySessionId,
                ScanData = request.ScanData,
                PracownikId = userId,
                DeviceId = request.DeviceId
            };

            var result = await _mediator.Send(command);

            return Ok(new
            {
                data = result,
                success = result.IsSuccess,
                message = result.Message
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid scan data: {ScanData}", request.ScanData);
            return BadRequest(new
            {
                success = false,
                error = new
                {
                    code = "INVALID_SCAN_FORMAT",
                    message = ex.Message,
                    details = new { scannedCode = request.ScanData }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing inventory scan: {ScanData}", request.ScanData);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "SCAN_PROCESSING_ERROR",
                    message = "Błąd przetwarzania skanu. Spróbuj ponownie."
                }
            });
        }
    }

    /// <summary>
    /// Dodaje lub aktualizuje pozycję inwentaryzacji
    /// </summary>
    /// <param name="inventoryId">ID inwentaryzacji</param>
    /// <param name="request">Dane pozycji</param>
    /// <returns>Zaktualizowana pozycja inwentaryzacji</returns>
    [HttpPost("{inventoryId}/items")]
    public async Task<ActionResult<InventoryItemDto>> CreateInventoryItem(
        int inventoryId,
        [FromBody] InventoryItemRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new CreateInventoryItemCommand
            {
                InventoryId = inventoryId,
                PracownikId = userId,
                EtykietaId = request.EtykietaId,
                PaletaId = request.PaletaId,
                Kod = request.Kod,
                IloscSpisana = request.IloscSpisana,
                Hala = request.Hala,
                Regal = request.Regal,
                Miejsce = request.Miejsce,
                Poziom = request.Poziom,
                Podkod = request.Podkod,
                Skan = request.Skan,
                NrSap = request.NrSap
            };

            var result = await _mediator.Send(command);

            return Created($"api/v1/inventory/{inventoryId}/items/{result.Id}", new
            {
                data = result,
                success = true,
                message = "Pozycja inwentaryzacji została dodana"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid inventory item request for inventory {InventoryId}", inventoryId);
            return BadRequest(new
            {
                success = false,
                error = new
                {
                    code = "INVALID_INVENTORY_ITEM",
                    message = ex.Message
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inventory item for inventory {InventoryId}", inventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_ITEM_CREATE_ERROR",
                    message = "Błąd dodawania pozycji inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Pobiera szczegóły inwentaryzacji wraz z postępem
    /// </summary>
    /// <param name="inventoryId">ID inwentaryzacji</param>
    /// <returns>Szczegóły inwentaryzacji</returns>
    [HttpGet("{inventoryId}")]
    public async Task<ActionResult<InventoryDetailsDto>> GetInventoryDetails(int inventoryId)
    {
        try
        {
            var query = new GetInventoryDetailsQuery { InventoryId = inventoryId };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound(new
                {
                    success = false,
                    error = new
                    {
                        code = "INVENTORY_NOT_FOUND",
                        message = $"Inwentaryzacja {inventoryId} nie została znaleziona"
                    }
                });
            }

            return Ok(new
            {
                data = result,
                success = true,
                message = "Szczegóły inwentaryzacji pobrane pomyślnie"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inventory details for {InventoryId}", inventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_DETAILS_ERROR",
                    message = "Błąd pobierania szczegółów inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Pobiera postęp inwentaryzacji
    /// </summary>
    /// <param name="inventoryId">ID inwentaryzacji</param>
    /// <returns>Informacje o postępie</returns>
    [HttpGet("{inventoryId}/progress")]
    public async Task<ActionResult<InventoryProgressDto>> GetInventoryProgress(int inventoryId)
    {
        try
        {
            var query = new GetInventoryProgressQuery { InventoryId = inventoryId };
            var result = await _mediator.Send(query);

            return Ok(new
            {
                data = result,
                success = true,
                message = "Postęp inwentaryzacji pobrany pomyślnie"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inventory progress for {InventoryId}", inventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_PROGRESS_ERROR",
                    message = "Błąd pobierania postępu inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Pobiera pozycje inwentaryzacji
    /// </summary>
    /// <param name="inventoryId">ID inwentaryzacji</param>
    /// <param name="onlyCompleted">Tylko ukończone pozycje</param>
    /// <returns>Lista pozycji inwentaryzacji</returns>
    [HttpGet("{inventoryId}/items")]
    public async Task<ActionResult<IEnumerable<InventoryItemDto>>> GetInventoryItems(
        int inventoryId,
        [FromQuery] bool onlyCompleted = false)
    {
        try
        {
            var query = new GetInventoryItemsQuery
            {
                InventoryId = inventoryId,
                OnlyCompleted = onlyCompleted
            };

            var result = await _mediator.Send(query);

            return Ok(new
            {
                data = result,
                success = true,
                message = "Pozycje inwentaryzacji pobrane pomyślnie"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inventory items for {InventoryId}", inventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_ITEMS_ERROR",
                    message = "Błąd pobierania pozycji inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Kończy sesję inwentaryzacji
    /// </summary>
    /// <param name="sessionId">ID sesji</param>
    /// <returns>Status zakończenia</returns>
    [HttpPost("sessions/{sessionId}/end")]
    public async Task<ActionResult> EndInventorySession(int sessionId)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new EndInventorySessionCommand
            {
                InventorySessionId = sessionId,
                PracownikId = userId
            };

            var result = await _mediator.Send(command);

            if (!result)
            {
                return BadRequest(new
                {
                    success = false,
                    error = new
                    {
                        code = "SESSION_END_FAILED",
                        message = "Nie można zakończyć sesji inwentaryzacji"
                    }
                });
            }

            return Ok(new
            {
                success = true,
                message = "Sesja inwentaryzacji została zakończona"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending inventory session {SessionId}", sessionId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "SESSION_END_ERROR",
                    message = "Błąd kończenia sesji inwentaryzacji"
                }
            });
        }
    }

    /// <summary>
    /// Wyszukuje pozycję inwentaryzacji po kodzie (SSCC, DS, produkt)
    /// </summary>
    /// <param name="inventoryId">ID inwentaryzacji</param>
    /// <param name="code">Kod do wyszukania</param>
    /// <returns>Znaleziona pozycja lub informacja o braku</returns>
    [HttpGet("{inventoryId}/search")]
    public async Task<ActionResult<InventoryItemDto>> SearchInventoryItem(
        int inventoryId,
        [FromQuery] string code)
    {
        try
        {
            var query = new SearchInventoryLabelQuery
            {
                InventoryId = inventoryId,
                EtykietaId = code,
                NrSap = code,
                PaletaId = code
            };

            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound(new
                {
                    success = false,
                    error = new
                    {
                        code = "INVENTORY_ITEM_NOT_FOUND",
                        message = $"Pozycja z kodem {code} nie została znaleziona w inwentaryzacji {inventoryId}"
                    }
                });
            }

            return Ok(new
            {
                data = result,
                success = true,
                message = "Pozycja inwentaryzacji znaleziona"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching inventory item with code {Code} in inventory {InventoryId}",
                code, inventoryId);
            return StatusCode(500, new
            {
                success = false,
                error = new
                {
                    code = "INVENTORY_SEARCH_ERROR",
                    message = "Błąd wyszukiwania pozycji inwentaryzacji"
                }
            });
        }
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
        {
            throw new UnauthorizedAccessException("Nie można określić ID użytkownika");
        }
        return userId;
    }
}