using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Receives.Handlers;
using Wms.Application.Features.Receives.Queries;
using Wms.Application.Interfaces;
using Wms.Domain.Entities.Receives;
using Wms.Domain.Exceptions;

namespace Wms.UnitTests.Application.Handlers;

public class GetReceiveDetailsQueryHandlerTests
{
    private readonly Mock<IReceiveRepository> _mockReceiveRepository;
    private readonly Mock<IPalletRepository> _mockPalletRepository;
    private readonly Mock<ILabelRepository> _mockLabelRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<GetReceiveDetailsHandler>> _mockLogger;
    private readonly GetReceiveDetailsHandler _handler;

    public GetReceiveDetailsQueryHandlerTests()
    {
        _mockReceiveRepository = new Mock<IReceiveRepository>();
        _mockPalletRepository = new Mock<IPalletRepository>();
        _mockLabelRepository = new Mock<ILabelRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<GetReceiveDetailsHandler>>();
        
        _handler = new GetReceiveDetailsHandler(
            _mockReceiveRepository.Object,
            _mockPalletRepository.Object,
            _mockLabelRepository.Object,
            _mockMapper.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Handle_WithValidReceiveId_ShouldReturnReceiveDetailsWithItems()
    {
        // Arrange
        var query = new GetReceiveDetailsQuery { ReceiveId = 1 };

        var receive = new ListControl
        {
            Id = 1,
            DataDodania = DateTime.UtcNow.Date,
            DataRealizacji = DateTime.UtcNow.Date.AddDays(1),
            RealizujacyPracownikId = 100,
            IsAssigned = true,
            IsCompleted = false
        };

        var expectedItems = new List<AwizacjaDane>
        {
            new()
            {
                Id = 1,
                ListControlId = 1,
                EtykietaKlient = "123456789012345678",
                Kod = "12345678901234",
                Lot = "LOT123",
                Dataprod = new DateOnly(2024, 12, 31),
                Ilosc = 50
            }
        };

        var carriers = new List<NosnikDostaw>
        {
            new()
            {
                PaletaId = 1,
                ListControlId = 1,
                DataUtworzenia = DateTime.UtcNow.Date
            }
        };

        var expectedDto = new ReceiveDetailsDto
        {
            Id = 1,
            Data = DateTime.UtcNow.Date.AddDays(1),
            RealizujacyPracownikId = 100,
            IsAssigned = true,
            IsComplete = false,
            ExpectedItems = new List<ExpectedItemDto>(),
            Carriers = new List<CarrierDto>()
        };

        _mockReceiveRepository
            .Setup(x => x.GetWithDetailsAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);

        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedItems);

        _mockPalletRepository
            .Setup(x => x.GetCarriersForReceiveAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(carriers);

        _mockPalletRepository
            .Setup(x => x.GetCarrierItemsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Domain.Entities.Label>());

        _mockMapper
            .Setup(x => x.Map<ReceiveDetailsDto>(It.IsAny<ListControl>()))
            .Returns(expectedDto);

        _mockMapper
            .Setup(x => x.Map<List<ExpectedItemDto>>(It.IsAny<List<AwizacjaDane>>()))
            .Returns(new List<ExpectedItemDto>());

        _mockMapper
            .Setup(x => x.Map<CarrierDto>(It.IsAny<NosnikDostaw>()))
            .Returns(new CarrierDto { PaletaId = 1, ListControlId = 1 });

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);

        // Verify repository calls
        _mockReceiveRepository.Verify(
            x => x.GetWithDetailsAsync(query.ReceiveId, It.IsAny<CancellationToken>()),
            Times.Once);

        _mockReceiveRepository.Verify(
            x => x.GetExpectedItemsAsync(query.ReceiveId, It.IsAny<CancellationToken>()),
            Times.Once);

        _mockPalletRepository.Verify(
            x => x.GetCarriersForReceiveAsync(query.ReceiveId, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithReceiveNotFound_ShouldReturnNull()
    {
        // Arrange
        var query = new GetReceiveDetailsQuery { ReceiveId = 999 };

        _mockReceiveRepository
            .Setup(x => x.GetWithDetailsAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ListControl)null!);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldLogDebugForSuccessfulRetrieval()
    {
        // Arrange
        var query = new GetReceiveDetailsQuery { ReceiveId = 1 };
        var receive = new ListControl { Id = 1 };
        var expectedDto = new ReceiveDetailsDto { Id = 1, ExpectedItems = [], Carriers = [] };

        _mockReceiveRepository
            .Setup(x => x.GetWithDetailsAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(receive);
            
        _mockReceiveRepository
            .Setup(x => x.GetExpectedItemsAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AwizacjaDane>());
            
        _mockPalletRepository
            .Setup(x => x.GetCarriersForReceiveAsync(query.ReceiveId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<NosnikDostaw>());

        _mockMapper
            .Setup(x => x.Map<ReceiveDetailsDto>(It.IsAny<ListControl>()))
            .Returns(expectedDto);
            
        _mockMapper
            .Setup(x => x.Map<List<ExpectedItemDto>>(It.IsAny<List<AwizacjaDane>>()))
            .Returns(new List<ExpectedItemDto>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
    }
}
