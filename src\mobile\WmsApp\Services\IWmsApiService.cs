using Refit;
using WmsApp.Models.Auth;
using WmsApp.Models.Pallets;

namespace WmsApp.Services;

public interface IWmsApiService
{
    [Post("/auth/login-scan")]
    Task<IApiResponse<LoginScanResponse>> LoginScanAsync([Body] LoginScanRequest request);

    [Post("/auth/logout")]
    Task<IApiResponse<object>> LogoutAsync();

    [Post("/pallets/{palletCode}/move")]
    Task<IApiResponse<MovePalletResponse>> MovePalletAsync(string palletCode, [Body] MovePalletRequest request);

    [Get("/pallets/{palletCode}")]
    Task<IApiResponse<PalletInfo>> GetPalletAsync(string palletCode);

    [Get("/locations/{locationCode}")]
    Task<IApiResponse<LocationInfo>> GetLocationAsync(string locationCode);
}
