using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

public interface ILocationRepository
{
    Task<Location?> GetByCodeAsync(string locationCode);
    Task<Location?> GetByCoordinatesAsync(int hala, string regal, int miejsce, string? poziom);
    Task<Location?> GetByIdAsync(int id);
    Task<IEnumerable<Location>> GetVisibleLocationsAsync();
    Task<int> GetCurrentPalletCountAsync(int locationId);
}
