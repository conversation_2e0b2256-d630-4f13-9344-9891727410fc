using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WmsApp.ViewModels;

public partial class OptionsViewModel : ObservableObject
{
    [RelayCommand]
    private async Task NavigateToMovePallet()
    {
        await Shell.Current.GoToAsync("///movepalletpage");
    }

    [RelayCommand]
    private async Task NavigateToAbout()
    {
        await Shell.Current.GoToAsync("///aboutpage");
    }
}
