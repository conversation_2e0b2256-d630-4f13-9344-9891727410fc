# ARCHITECTURE – WMS Mobile (MAUI Android) + .NET 9 Backend

Wersja dokumentu: 0.2  
Data: 2025-09-01  
Referencja: PRD.md v0.2, ADR records, Database Structure docs

## 1. Przegląd architektury
System składa się z aplikacji mobilnej (.NET MAUI na Android) oraz backendu (ASP.NET Core .NET 9) uruchomionego w sieci lokalnej na Debianie, za reverse proxy Apache (HTTPS). <PERSON> są przechowywane w MySQL 8. Observability obejmuje: Serilog + Promtail + Grafana Loki (logi), Prometheus + Grafana (metryki), <PERSON><PERSON> (self-host) dla crashy aplikacji mobilnej.

Kluczowe cechy MVP:
- Mobilnie: logowanie skanem karty i zmiana lokalizacji palety (flow „scan → scan”, bez potwierdzenia), brak offline.
- Backend: REST API v1, JWT (1h), transakcyjny zapis ruchów.
- Sieć: komunikacja wyłącznie przez HTTPS w LAN.
- Auto-aktualizacja APK z serwera (manifest app.json + app.apk przez Apache, z walidacją SHA-256).

## 2. Komponenty
- Aplikacja mobilna (MAUI Android)
  - Wersja .NET: 9 (MAUI zgodna z .NET 9).  
  - Integracja skanera: Zebra DataWedge (Intent).  
  - MinSdk: 29 (Android 10).  
  - Warstwy: UI (Shell, strony Logowanie/Przeniesienie), MVVM (CommunityToolkit.Mvvm – opcjonalnie), Services (Auth, Movement, Update, DataWedge), HttpClient (API), Storage (SecureStorage na token).  
- Backend (ASP.NET Core)
  - Wms.Api (prezentacja, kontrolery, walidacje, autoryzacja).  
  - Wms.Application (use-case’y, DTO, walidacje).  
  - Wms.Domain (encje i reguły domenowe).  
  - Wms.Infrastructure (EF Core + Pomelo MySQL, migracje, repozytoria, konfiguracja).  
- Baza danych: MySQL 8 (InnoDB; transakcje, FK, indeksy).  
- Reverse proxy: Apache (HTTPS, ProxyPass do Kestrela; przygotowany WebSocket dla przyszłego SignalR).  
- Observability: Promtail → Loki, Prometheus, Grafana, Sentry self-host.  
- Hosting auto-aktualizacji: katalog /wms w Apache (app.json, app.apk).

## 3. Przepływy (sequence)
### 3.1 Logowanie
1) Użytkownik skanuje kartę (numer np. 00000123453).  
2) Aplikacja wywołuje POST /api/v1/auth/login-scan z cardNumber.  
3) Backend weryfikuje użytkownika (aktywny), generuje JWT (1h) i zwraca token.  
4) Aplikacja zapisuje token w SecureStorage, rozpoczyna licznik bezczynności; po 1h braku aktywności wymusza ponowne logowanie skanem.

### 3.2 Zmiana lokalizacji palety (bez potwierdzenia)
1) Skan palety (SSCC/DS).  
2) Skan lokalizacji docelowej (MP-…-…-…-…).  
3) Aplikacja wywołuje POST /api/v1/pallets/{code}/move z toLocation, deviceId, occurredAt.  
4) Backend waliduje istnienie palety i lokalizacji, zapisuje ruch w transakcji, uaktualnia bieżącą lokalizację palety, zwraca MovementId i dane ruchu.

### 3.3 Auto-aktualizacja APK
1) Aplikacja pobiera manifest https://***********/wms/app.json.  
2) Porównuje wersję zainstalowaną z manifestem; jeśli nowsza, proponuje aktualizację.  
3) Pobiera https://***********/wms/app.apk, weryfikuje SHA-256, uruchamia instalator (pół-automatycznie).

## 4. Model danych (MVP)
- User(id, name, cardNumber, isActive, createdAt, updatedAt)
- Session(id, userId, issuedAt, expiresAt, deviceId)
- Location(code, area, hall, rack, slot, level, isActive)
- Pallet(code, currentLocationCode, createdAt, updatedAt)
- Movement(id, palletCode, fromLocationCode, toLocationCode, userId, deviceId, occurredAt)

Uwagi:
- W MVP nieznana paleta → błąd (brak auto-tworzenia).  
- Lista lokalizacji ładowana i utrzymywana w backendzie (import inicjalny poza zakresem MVP).

## 5. Kontrakt API (MVP) – szkic
- POST /api/v1/auth/login-scan  
  Body: { cardNumber } → 200: { token, user: { id, name } }  
  401: niepoprawny/nieaktywny użytkownik.
- POST /api/v1/pallets/{palletCode}/move  
  Body: { toLocation, deviceId, occurredAt } → 200: { movementId, palletCode, fromLocation, toLocation, userId, occurredAt }  
  400: walidacja (lokalizacja/paleta)  
  404: paleta/lokalizacja nie istnieje.  
- GET /api/v1/locations/{locationCode} → 200: { code, isActive, … } / 404.

Konwencje: JSON, kody błędów spójne, komunikaty PL/klucz techniczny. Wersjonowanie w ścieżce (/api/v1/...).

## 6. Integracja Zebra DataWedge (Android)
- Profil DataWedge przypisany do pakietu aplikacji (np. com.example.wms).  
- Output: Broadcast Intent (zalecane) lub StartActivity; Action np. com.example.wms.SCAN.  
- Najważniejsze extras DataWedge:  
  - com.symbol.datawedge.data_string – zeskanowany tekst.  
  - com.symbol.datawedge.label_type – typ etykiety (np. EAN128).  
  - com.symbol.datawedge.source – źródło skanera.  
- W MAUI (Android): BroadcastReceiver w Platforms/Android nasłuchuje akcji i przekazuje wartość do warstwy ViewModel (np. przez MessagingCenter/EventAggregator).  
- Walidacje kodów:  
  - SSCC: 18 cyfr; DS: ^DS\d{8}$; Lokalizacja: ^MP-\d+-\d+-\d+-\d+$.

Przykładowa definicja akcji (umowne nazwy):
```json path=null start=null
{
  "intentAction": "com.example.wms.SCAN",
  "intentDelivery": "BROADCAST",
  "extras": [
    "com.symbol.datawedge.data_string",
    "com.symbol.datawedge.label_type",
    "com.symbol.datawedge.source"
  ]
}
```

## 7. Bezpieczeństwo
- Transport: wyłącznie HTTPS przez Apache (reverse proxy → Kestrel).  
- Certyfikat: wewnętrzny CA lub self-signed wdrożony na urządzeniach.  
- JWT: ważny 1h; brak refresh tokenów (ponowne logowanie po idle 1h).  
- CORS: dozwolone pochodzenia tylko z aplikacji mobilnej (schemat intent/webview – brak, więc ograniczenia głównie dla paneli przyszłościowych).  
- Nagłówki forwardowane: X-Forwarded-For, X-Forwarded-Proto.  
- Audyt: pełny zapis w Movement; opcjonalny AuditLog dla zdarzeń biznesowych.

## 8. Konfiguracja środowisk
- Środowiska: dev, test, prod (oddzielne bazy, endpointy, certyfikaty).  
- Backend: appsettings.{Environment}.json; sekrety poza repo (zmienne środowiskowe).  
- Kluczowe ustawienia:  
  - ConnectionStrings:MySql  
  - Jwt:Issuer, Audience, SigningKey  
  - Security:RequireHttps = true  
  - Logging:Serilog (sink do Promtail)  
  - Update:ManifestUrl = https://***********/wms/app.json

Przykładowy fragment konfiguracji backendu:
```json path=null start=null
{
  "ConnectionStrings": {
    "MySql": "Server=127.0.0.1;Database=wms;User Id={{DB_USER}};Password={{DB_PASSWORD}};SslMode=None;"
  },
  "Jwt": {
    "Issuer": "wms.local",
    "Audience": "wms.mobile",
    "SigningKey": "{{JWT_SIGNING_KEY}}",
    "AccessTokenMinutes": 60
  },
  "Security": {
    "RequireHttps": true
  },
  "Update": {
    "ManifestUrl": "https://***********/wms/app.json"
  }
}
```

## 9. Wdrożenie i sieć
- Apache na Debianie jako reverse proxy do Kestrela (ASP.NET Core).  
- Ścieżki:  
  - API: https://***********/api/v1  
  - Auto-update: https://***********/wms (statyczne pliki)  
  - Przyszły SignalR (WebSocket), np. https://***********/hub

Przykładowa konfiguracja Apache (VirtualHost HTTPS + proxy do Kestrela):
```conf path=null start=null
<VirtualHost *:443>
  ServerName ***********

  SSLEngine on
  SSLCertificateFile /etc/ssl/certs/wms.crt
  SSLCertificateKeyFile /etc/ssl/private/wms.key

  ProxyPreserveHost On
  RequestHeader set X-Forwarded-Proto "https"
  RequestHeader set X-Forwarded-For expr=%{REMOTE_ADDR}

  # API -> Kestrel na localhost:5000
  ProxyPass        "/api"  "http://127.0.0.1:5000/api"
  ProxyPassReverse "/api"  "http://127.0.0.1:5000/api"

  # WebSocket (SignalR – w przyszłości)
  ProxyPass        "/hub"  "ws://127.0.0.1:5000/hub"
  ProxyPassReverse "/hub"  "ws://127.0.0.1:5000/hub"

  # Statyczne pliki aktualizacji APK
  Alias "/wms" "/var/www/wms"
  <Directory "/var/www/wms">
    Options -Indexes
    Require all granted
    Header set Access-Control-Allow-Origin "https://***********"
  </Directory>
</VirtualHost>
```
Wymagane moduły Apache: ssl, headers, proxy, proxy_http, proxy_wstunnel, alias.

## 10. Observability
- Logi backendu: Serilog → pliki/konsola; Promtail tailuje i wysyła do Loki.  
- Metryki: OpenTelemetry → Prometheus; dashboardy w Grafanie.  
- Crashe mobile: Sentry self-host (DSN w konfiguracji aplikacji).  
- Retencja logów technicznych poza DB; logi audytowe (ruchy) w MySQL.

## 11. Wydajność i skalowanie
- Docelowo do ~30 równoległych operatorów.  
- p95 < 200 ms w LAN dla operacji MVP.  
- Indeksacja MySQL po kluczach wyszukiwania (palletCode, locationCode, occurredAt).  
- Możliwość poziomego skalowania backendu (Kestrel w systemd / kontener), z sticky sessions niewymaganymi (JWT stateless).

## 12. Struktura repozytorium (propozycja)
- src/backend/Wms.Api  
- src/backend/Wms.Application  
- src/backend/Wms.Domain  
- src/backend/Wms.Infrastructure  
- src/mobile/Wms.Mobile  
- infra/observability/{prometheus,grafana,loki,promtail,sentry}/  
- docs/{PRD.md, ARCHITECTURE.md, TODO.md, STYLE_GUIDE.md}

## 12.1 UI patterns – DataTemplate i komendy (MAUI)

Problem: w DataTemplate kontekst danych to element (np. NosnikPositionDto), co utrudnia wiązanie komend do ViewModelu strony. Dodatkowo `x:DataType` + kompilowane bindingi potrafią generować ostrzeżenia typu „BindingContext not found on ...”.

Zalecane wzorce:
- Prosty przypadek: użyj CommunityToolkit `EventToCommandBehavior` i `x:Reference` do strony.
- Przypadki problematyczne (ostrzeżenia XamlC lub brak wywołań Command): mostkuj przez code-behind handler i wywołuj komendę VM ręcznie.

Przykład zastosowany w ReceivesRegistrationPage (podgląd nośnika – usuwanie pozycji):
```xml path=/c/Users/<USER>/source/repos/WMS-System/src/mobile/WmsApp/Views/Receives/ReceivesRegistrationPage.xaml start=390
<Frame Grid.Column="3" WidthRequest="30" HeightRequest="30" CornerRadius="15" Padding="0" BackgroundColor="Red">
  <Label Text="🗑️" FontSize="12" TextColor="White" HorizontalOptions="Center" VerticalOptions="Center" />
  <Frame.GestureRecognizers>
    <TapGestureRecognizer Tapped="OnDeleteTapped" CommandParameter="{Binding Id}" />
  </Frame.GestureRecognizers>
</Frame>
```
```csharp path=/c/Users/<USER>/source/repos/WMS-System/src/mobile/WmsApp/Views/Receives/ReceivesRegistrationPage.xaml.cs start=24
private async void OnDeleteTapped(object sender, TappedEventArgs e)
{
    if (e.Parameter is int id && VM.DeletePositionCommand.CanExecute(id))
        VM.DeletePositionCommand.Execute(id);
}
```

Korzyści: brak konfliktów kompilowanych bindingów, jednoznaczny przepływ zdarzeń do VM, czytelne logowanie.

## 13. Decyzje architektoniczne (ADR – skrót)
- ADR-001: Brak trybu offline (online-only w LAN).  
- ADR-002: Zebra DataWedge (Intent) zamiast EMDK (mniejszy narzut).  
- ADR-003: Nieznana paleta – odrzucenie w MVP (bez auto-tworzenia).  
- ADR-004: JWT bez refresh (idle 1h wymusza ponowne logowanie).  
- ADR-005: Wymagany HTTPS w LAN (Apache reverse proxy).  
- ADR-006: Observability: Serilog+Promtail+Loki; Prometheus+Grafana; Sentry self-host.  
- ADR-007: MySQL 8 + EF Core Pomelo jako ORM.

## 14. Implementacja Backend (Status: ✅ Ukończone)

### Zaimplementowane komponenty:
- **Clean Architecture** - pełne rozdzielenie warstw (ADR-001)
- **Database Integration** - mapowanie legacy MySQL z Clean Architecture (ADR-002)  
- **Code Validation** - centralizowana walidacja SSCC/DS/Location (ADR-003)
- **JWT Authentication** - card-based login z session tracking (ADR-004)
- **API Endpoints** - wszystkie endpointy v1 zaimplementowane
- **Transaction Management** - Unit of Work pattern dla spójności danych
- **Error Handling** - globalne middleware z ProblemDetails
- **Logging** - Serilog z structured logging
- **Validation** - FluentValidation dla wszystkich DTOs

### Fazy implementacji backendu:
1. ✅ **Struktura rozwiązania** - DI, konwencje, walidacja, error handling
2. ✅ **Encje domenowe i EF Core** - mapowanie legacy DB, migracje
3. ✅ **Uwierzytelnianie JWT** - login-scan, session management
4. ✅ **Logika ruchów palet** - transakcyjne operacje, walidacja kodów
5. ✅ **Endpointy API v1** - REST API z OpenAPI documentation
6. 🔄 **Observability** - rozszerzone metryki i monitoring (w toku)

## 15. Dokumentacja szczegółowa

### Architecture Decision Records:
- [ADR Index](adr/README.md) - przegląd wszystkich decyzji architektonicznych
- [ADR-001: Clean Architecture](adr/001-clean-architecture.md) - uzasadnienie wyboru architektury
- [ADR-002: Database Mapping](adr/002-database-mapping-strategy.md) - strategia mapowania legacy DB
- [ADR-003: Code Validation](adr/003-code-validation-strategy.md) - centralizowana walidacja kodów
- [ADR-004: JWT Authentication](adr/004-jwt-authentication-strategy.md) - authentication bez haseł

### Szczegółowe specyfikacje:
- [Database Structure](database-structure.md) - kompletna dokumentacja struktury bazy danych
- [API Documentation](../src/backend/Wms.Api/) - OpenAPI/Swagger dostępne po uruchomieniu

## 16. Otwarte punkty

### 16.1 Wzorzec modalnych selektorów (MAUI)
- W aplikacji mobilnej stosujemy spójny wzorzec dla modalnych selektorów (np. wybór towaru):
  - UI: CollectionView (SelectionMode=Single, SelectedItem → VM), bez TapGestureRecognizer w DataTemplate.
  - VM: OnSelected…Changed → BeginInvokeOnMainThread (+ krótki Delay), flaga anty-reentrancy, wynik przez TaskCompletionSource.
  - Nawigacja: PushModalAsync/PopModalAsync z kontrolą ModalStack i blokadą wyjątków.
  - Inicjalizacja: IQueryAttributable z guardem dla identycznych parametrów.
- Uzasadnienie i szczegóły implementacyjne: docs/Guides/MAUI_ModalPicker_BestPractices.md

### 16.2 Refaktoryzacja API mobilnego (2025-01-10)

**Problem:** Aplikacja mobilna wywoływała nieistniejący endpoint `/api/v1/receives/register-position` co powodowało HTTP 405 Method Not Allowed.

**Rozwiązanie:** Przepisanie warstwy mobilnej `ReceiveService` do użycia istniejących endpointów backendu:
- `/api/v1/dostawy/{id}/nosnik` (POST) - tworzenie nośników (palet DS)
- `/api/v1/dostawy/{id}/pozycje` (POST) - dodawanie pozycji do dostaw
- `/api/v1/dostawy/{id}/nosniki` (GET) - pobieranie istniejących nośników

**Implementacja:**
1. **IReceiveService** - dodano nowe metody zgodne z backendem:
   - `CreateCarrierAsync(deliveryId, typPaletyId, drukowac, drukarkaIp)`
   - `CreateReceiveItemAsync(deliveryId, request)`
   - `GetDeliveryCarriersAsync(deliveryId)`
   - `CompleteCarrierAsync(deliveryId, paletaId)`

2. **RegisterPositionAsync refactoring** - nowa logika:
   - Parsuje LK na delivery ID (`ParseLkToId("LK123") -> 123`)
   - W trybie manualnym: szuka istniejącego nośnika przez `GetDeliveryCarriersAsync()`
   - W trybie Auto: tworzy nowy nośnik przez `CreateCarrierAsync()`
   - Dodaje pozycję przez `CreateReceiveItemAsync()` z konwersją DateTime -> DateOnly
   - Zwraca `RegisterPositionResponse` zachowując kompatybilność z UI

3. **Modele DTO** - dodano zgodne z backendem:
   - `CreateCarrierRequest`, `CreateReceiveItemRequest`
   - `CarrierDto`, `ReceiveItemDto`

4. **MockReceiveService** - zaktualizowano implementację mocków dla testów

5. **Markowanie przestarzałych API** - stare endpointy oznaczone jako `[Obsolete]`

**Korzyści:**
- Eliminacja HTTP 405 błędów
- Pełna kompatybilność z istniejącym backendem
- Zachowana kompatybilność wsteczna UI
- Lepsze mapowanie logiki biznesowej (delivery -> carrier -> items)
- Przygotowanie do deprecation starych endpointów

### 16.3 Pozostałe punkty
- Import i utrzymanie słownika lokalizacji (proces i format).  
- Nazwy/prefixy pakietu aplikacji i ostateczne ścieżki w Apache.  
- Wybór narzędzia CI/CD (GH Actions/GitLab CI) i pipeline dla APK oraz backendu.  
- Polityki rotacji certyfikatów HTTPS i zarządzanie kluczami JWT.  
- **Implementacja aplikacji mobilnej MAUI** - następny etap rozwoju


## 17. EF Core – strategia śledzenia i AsNoTracking

W celu uniknięcia konfliktów śledzenia (tzw. „podwójnego śledzenia” tej samej encji w jednym `DbContext`, objawiających się błędami 409 lub wyjątkami stanu) przyjęliśmy spójną strategię korzystania z wariantów zapytań bez śledzenia (`AsNoTracking`) dla operacji tylko-do-odczytu oraz świadomego dołączania encji przy aktualizacji.

Kluczowe założenia i implementacja:

- AsNoTracking dla odczytów tylko-do-odczytu:
  - `ILabelRepository.GetBySSCCAsNoTrackingAsync(sscc)` – wyszukiwanie etykiety po SSCC.
  - `ILabelRepository.GetByClientCodeAsNoTrackingAsync(clientCode)` – wyszukiwanie etykiety po kodzie klienta (DS fallback).
  - `ILabelRepository.GetActiveByPalletIdAsNoTrackingAsync(palletId)` – pobieranie aktywnych etykiet palety do operacji ruchu (lista etykiet do przeniesienia).

- Aktualizacje z ochroną przed podwójnym śledzeniem:
  - `ILabelRepository.UpdateLocationAsync(labelId, systemId, newLocationId)` – metoda najpierw sprawdza `ChangeTracker` pod kątem już śledzonej encji; jeśli encja nie jest śledzona, ładuje ją przez `AsNoTracking()` i dołącza do kontekstu przed modyfikacją właściwości. Dzięki temu unikamy konfliktów śledzenia przy równoczesnym ładowaniu i aktualizacji.

- Serwis `PalletService`:
  - `MovePalletAsync()` wykorzystuje `GetActiveByPalletIdAsNoTrackingAsync()` do pobrania aktywnych etykiet oraz następnie wywołuje `UpdateLocationAsync()` dla każdej z nich w ramach transakcji `IUnitOfWork.ExecuteInTransactionAsync(...)`.
  - `FindLabelByCodeAsync()` szuka najpierw po SSCC (`AsNoTracking`), a dla kodów DS próbuje odczytać po `paleta_id` przez `GetByPalletIdDirectAsync(palletId)` (najnowsza etykieta dla palety). Jeśli nie znajdzie, wykonuje fallback do `GetByClientCodeAsNoTrackingAsync()`.
  - Weryfikacja „post-commit” korzysta z wariantu śledzonego `GetActiveByPalletIdAsync()` wyłącznie do sprawdzenia zgodności stanu po zapisie (read-after-write w tym samym żądaniu).

- Testy jednostkowe:
  - Testy `PalletServiceTests` zostały zaktualizowane tak, aby mockować metody `AsNoTracking` dla odczytów tylko-do-odczytu i wykorzystywać `GetByPalletIdDirectAsync()` w ścieżce DS (z fallbackiem do `GetByClientCodeAsNoTrackingAsync`).
  - Emulacja `IUnitOfWork.ExecuteInTransactionAsync` wywołuje przekazaną operację i w zależności od powodzenia wykonuje `CommitAsync()` lub `RollbackAsync()` na zamockowanej transakcji.

Efekt: redukcja konfliktów śledzenia EF Core oraz większa przewidywalność kontekstu w operacjach „read-then-update” wykonywanych w ramach jednej transakcji.

## 18. Moduł Inwentaryzacji (2025-01-14)

### Implementacja struktury modułu Inventory

**Cel:** Stworzenie spójnego modułu inwentaryzacji analogicznego do modułu Receives, obsługującego 4 typy inwentaryzacji zgodnie z dokumentacją systemu.

**Struktura Backend:**
```
src/backend/Wms.Application/Features/Inventory/
├── Commands/
│   └── InventoryCommands.cs          # Komendy CQRS dla inwentaryzacji
├── Queries/
│   └── InventoryQueries.cs           # Zapytania CQRS dla inwentaryzacji
└── Handlers/
    └── InventoryQueryHandlers.cs     # Handlery zapytań

src/backend/Wms.Application/DTOs/Inventory/
└── InventoryDtos.cs                  # DTOs dla inwentaryzacji

src/backend/Wms.Domain/Entities/Inventory/
└── InventoryEntity.cs                # Encje domenowe inwentaryzacji

src/backend/Wms.Application/Interfaces/
└── IInventoryRepository.cs           # Interfejs repozytorium
```

**Struktura Frontend:**
```
src/mobile/WmsApp/Models/Inventory/
└── InventoryModels.cs                # Modele i DTOs dla aplikacji mobilnej

src/mobile/WmsApp/Services/Inventory/
└── IInventoryService.cs              # Serwis biznesowy inwentaryzacji

src/mobile/WmsApp/ViewModels/Inventory/
├── InventorySelectionViewModel.cs    # ViewModel wyboru typu inwentaryzacji
└── InventoryProductViewModel.cs      # ViewModel inwentaryzacji produktowej

src/mobile/WmsApp/Views/Inventory/
├── InventorySelectionPage.xaml       # Strona wyboru typu inwentaryzacji
├── InventorySelectionPage.xaml.cs
├── InventoryProductPage.xaml         # Strona inwentaryzacji produktowej
└── InventoryProductPage.xaml.cs
```

**Typy inwentaryzacji:**
1. **Inwentaryzacja Produktowa** (InventoryType.Product) - ✅ Zaimplementowana
   - Pierwsza implementacja z pełnym UI
   - Obsługa sesji inwentaryzacji
   - Skanowanie etykiet, palet i kodów produktów

2. **Inwentaryzacja Ogólna** (InventoryType.General) - 🔄 Planowana
   - Kompleksowa inwentaryzacja z pełną kontrolą
   - Obsługa pól `nr_wspolny` i `proba`
   - Może obsługiwać palety

3. **Inwentaryzacja Miejsc** (InventoryType.Location) - 🔄 Planowana
   - Inwentaryzacja lokalizacji magazynowych

4. **Inwentaryzacja GG** (InventoryType.GG) - 🔄 Planowana
   - Specjalny typ inwentaryzacji

**Routing i nawigacja:**
- `//inventoryselection` - strona wyboru typu inwentaryzacji
- `//inventoryproduct` - strona inwentaryzacji produktowej
- MainViewModel.NavigateToInventoryAsync() → nawigacja do wyboru inwentaryzacji

**Integracja z systemem:**
- Rejestracja serwisów w MauiProgram.cs
- Dodanie tłumaczeń w AppResources.resx (PL/EN)
- Spójność stylistyczna z istniejącymi modułami
- Wzorzec MVVM z CommunityToolkit.Mvvm

## 19. Lokalizacja (i18n)
- Mechanizm aplikacji mobilnej: ResourceManager + pliki .resx + TranslateExtension (binding przez LocalizationResourceManager.Current)
- Zmiana języka w runtime (bez restartu) przez ILocalizationService.SetCulture(...), preferencje zapisywane w Preferences (AppLanguage)
- Inicjalizacja kultury przy starcie w MauiProgram (odczyt z Preferences)
- Przewodnik implementacyjny: [LOCALIZATION_GUIDE.md](../src/mobile/WmsApp/docs/LOCALIZATION_GUIDE.md)

