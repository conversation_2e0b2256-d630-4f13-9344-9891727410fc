using Microsoft.AspNetCore.Mvc;

namespace Wms.Api.Controllers;

[ApiVersion("1.0")]
public class HealthController : BaseApiController
{
    /// <summary>
    /// Sprawdzenie stanu aplikacji
    /// </summary>
    /// <returns>Status zdravia aplikacji</returns>
    [HttpGet]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult<object> Get()
    {
        return Ok(new 
        { 
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        });
    }

    /// <summary>
    /// Szczegółowy health check
    /// </summary>
    /// <returns>Szczegółowe informacje o stanie</returns>
    [HttpGet("detailed")]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult<object> GetDetailed()
    {
        return Ok(new 
        { 
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            Uptime = Environment.TickCount64 / 1000.0,
            MachineName = Environment.MachineName,
            ProcessorCount = Environment.ProcessorCount,
            WorkingSet = Environment.WorkingSet
        });
    }
}
