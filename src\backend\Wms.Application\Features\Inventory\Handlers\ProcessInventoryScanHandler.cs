using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs;
using Wms.Application.DTOs.Inventory;
using Wms.Application.Features.Inventory.Commands;
using Wms.Application.Interfaces;
using Wms.Application.Services;
using Wms.Domain.Entities;
using InventoryScanType = Wms.Application.DTOs.Inventory.ScanType;

namespace Wms.Application.Features.Inventory.Handlers;

/// <summary>
/// Handler dla przetwarzania skanów w inwentaryzacji
/// Obsługuje kody SSCC i DS zgodnie z dokumentacją
/// </summary>
public class ProcessInventoryScanHandler : IRequestHandler<ProcessInventoryScanCommand, InventoryScanResponse>
{
    private readonly Wms.Application.Interfaces.IInventoryRepository _inventoryRepository;
    private readonly ILabelRepository _labelRepository;
    private readonly IPalletRepository _palletRepository;
    private readonly Wms.Application.Interfaces.IKodRepository _kodyRepository;
    private readonly ICodeValidationService _codeValidationService;
    private readonly ILogger<ProcessInventoryScanHandler> _logger;

    public ProcessInventoryScanHandler(
        Wms.Application.Interfaces.IInventoryRepository inventoryRepository,
        ILabelRepository labelRepository,
        IPalletRepository palletRepository,
        Wms.Application.Interfaces.IKodRepository kodyRepository,
        ICodeValidationService codeValidationService,
        ILogger<ProcessInventoryScanHandler> logger)
    {
        _inventoryRepository = inventoryRepository;
        _labelRepository = labelRepository;
        _palletRepository = palletRepository;
        _kodyRepository = kodyRepository;
        _codeValidationService = codeValidationService;
        _logger = logger;
    }

    public async Task<InventoryScanResponse> Handle(ProcessInventoryScanCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing inventory scan: {ScanData} for session {SessionId}",
                request.ScanData, request.InventorySessionId);

            // 1. Walidacja kodu
            var validationResult = _codeValidationService.ValidateAndParseScan(request.ScanData);
            if (!validationResult.IsValid)
            {
                return new InventoryScanResponse
                {
                    IsSuccess = false,
                    Message = $"Nieprawidłowy format kodu: {validationResult.ErrorMessage}",
                    ScanType = InventoryScanType.Unknown
                };
            }

            // 2. Identyfikacja typu skanu na podstawie kodu
            var scanType = DetermineScanType(request.ScanData, validationResult);

            // 3. Przetwarzanie na podstawie typu
            return scanType switch
            {
                InventoryScanType.Label => await ProcessLabelScanAsync(request, validationResult, cancellationToken),
                InventoryScanType.Pallet => await ProcessPalletScanAsync(request, validationResult, cancellationToken),
                InventoryScanType.ProductCode => await ProcessProductCodeScanAsync(request, validationResult, cancellationToken),
                _ => new InventoryScanResponse
                {
                    IsSuccess = false,
                    Message = $"Nierozpoznany typ kodu: {request.ScanData}",
                    ScanType = InventoryScanType.Unknown
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing inventory scan: {ScanData}", request.ScanData);
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = "Błąd przetwarzania skanu. Spróbuj ponownie.",
                ScanType = InventoryScanType.Unknown
            };
        }
    }

    private InventoryScanType DetermineScanType(string scanData, ScanValidationResult validationResult)
    {
        // SSCC: 18 cyfr
        if (validationResult.ScanType == DTOs.ScanType.SSCC || validationResult.SSCC != null)
            return InventoryScanType.Label; // SSCC traktujemy jako etykietę

        // DS Code: DS + 4-9 cyfr
        if (validationResult.ScanType == DTOs.ScanType.DS)
            return InventoryScanType.Pallet;

        // Kod produktu: inne formaty EAN/PLU
        if (validationResult.ScanType == DTOs.ScanType.Manual || validationResult.ScanType == DTOs.ScanType.Unknown)
            return InventoryScanType.ProductCode;

        return InventoryScanType.Unknown;
    }

    private async Task<InventoryScanResponse> ProcessLabelScanAsync(
        ProcessInventoryScanCommand request,
        ScanValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        // Wyszukiwanie etykiety po SSCC
        var label = await _labelRepository.GetBySSCCAsNoTrackingAsync(request.ScanData);
        if (label == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Etykieta SSCC {request.ScanData} nie została znaleziona w systemie",
                ScanType = InventoryScanType.Label
            };
        }

        // Sprawdzenie czy etykieta już istnieje w tej inwentaryzacji
        var sessionInfo = await _inventoryRepository.GetSessionAsync(request.InventorySessionId);
        if (sessionInfo == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = "Sesja inwentaryzacji nie została znaleziona",
                ScanType = InventoryScanType.Label
            };
        }

        var existingItem = await _inventoryRepository.SearchLabelAsync(
            sessionInfo.InventoryId,
            label.Id.ToString(),
            null,
            null);

        if (existingItem != null)
        {
            // Etykieta już istnieje w inwentaryzacji
            var itemDto = MapToInventoryItemDto(existingItem);
            return new InventoryScanResponse
            {
                IsSuccess = true,
                Message = "Etykieta znaleziona w inwentaryzacji",
                ScanType = InventoryScanType.Label,
                FoundItem = itemDto,
                RequiresNewEntry = false
            };
        }

        // Pobierz informacje o produkcie dla etykiety
        ProductCodeDto? productInfo = null;
        if (label.KodId.HasValue)
        {
            var kod = await _kodyRepository.GetByIdAsync(label.KodId.Value);
            if (kod != null)
            {
                productInfo = new ProductCodeDto
                {
                    Id = kod.Id,
                    Kod = kod.KodValue,
                    Kod2 = kod.Kod2,
                    KodNazwa = kod.KodNazwa,
                    SystemId = kod.SystemId,
                    Jm = kod.Jm,
                    EanJednostki = kod.EanJednostki,
                    Ean = kod.Ean,
                    IsActive = kod.Active > 0
                };
            }
        }

        return new InventoryScanResponse
        {
            IsSuccess = true,
            Message = "Nowa etykieta - wprowadź ilość spisaną",
            ScanType = InventoryScanType.Label,
            ProductInfo = productInfo,
            RequiresNewEntry = true
        };
    }

    private async Task<InventoryScanResponse> ProcessPalletScanAsync(
        ProcessInventoryScanCommand request,
        ScanValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        // Wyciągnięcie ID palety z kodu DS
        var dsCodeMatch = System.Text.RegularExpressions.Regex.Match(request.ScanData, @"^DS(\d+)$");
        if (!dsCodeMatch.Success)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Nieprawidłowy format kodu DS: {request.ScanData}",
                ScanType = InventoryScanType.Pallet
            };
        }

        var paletaId = int.Parse(dsCodeMatch.Groups[1].Value);

        // Sprawdzenie czy paleta istnieje
        var pallet = await _palletRepository.GetByIdAsync(paletaId);
        if (pallet == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Paleta DS{paletaId} nie została znaleziona w systemie",
                ScanType = InventoryScanType.Pallet
            };
        }

        // Pobranie aktywnych etykiet na palecie
        var labels = await _labelRepository.GetActiveByPalletIdAsNoTrackingAsync(paletaId);
        if (!labels.Any())
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Paleta DS{paletaId} nie zawiera aktywnych etykiet",
                ScanType = InventoryScanType.Pallet
            };
        }

        // Sprawdzenie czy któraś z etykiet już istnieje w inwentaryzacji
        var sessionInfo = await _inventoryRepository.GetSessionAsync(request.InventorySessionId);
        if (sessionInfo == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = "Sesja inwentaryzacji nie została znaleziona",
                ScanType = InventoryScanType.Pallet
            };
        }

        foreach (var label in labels)
        {
            var existingItem = await _inventoryRepository.FindByEtykietaIdAsync(
                sessionInfo.InventoryId,
                label.Id.ToString(),
                sessionInfo.SystemId);

            if (existingItem != null)
            {
                // Znaleziono etykietę z palety w inwentaryzacji
                var itemDto = MapToInventoryItemDto(existingItem);
                return new InventoryScanResponse
                {
                    IsSuccess = true,
                    Message = $"Znaleziono etykietę z palety DS{paletaId} w inwentaryzacji",
                    ScanType = InventoryScanType.Pallet,
                    FoundItem = itemDto,
                    RequiresNewEntry = false
                };
            }
        }

        // Paleta nie została jeszcze spisana - zwróć informacje o pierwszej etykiecie
        var firstLabel = labels.First();
        ProductCodeDto? productInfo = null;

        if (firstLabel.KodId.HasValue)
        {
            var kod = await _kodyRepository.GetByIdAsync(firstLabel.KodId.Value);
            if (kod != null)
            {
                productInfo = new ProductCodeDto
                {
                    Id = kod.Id,
                    Kod = kod.KodValue,
                    Kod2 = kod.Kod2,
                    KodNazwa = kod.KodNazwa,
                    SystemId = kod.SystemId,
                    Jm = kod.Jm,
                    EanJednostki = kod.EanJednostki,
                    Ean = kod.Ean,
                    IsActive = kod.Active > 0
                };
            }
        }

        return new InventoryScanResponse
        {
            IsSuccess = true,
            Message = $"Nowa paleta DS{paletaId} - wprowadź ilość spisaną",
            ScanType = InventoryScanType.Pallet,
            ProductInfo = productInfo,
            RequiresNewEntry = true
        };
    }

    private async Task<InventoryScanResponse> ProcessProductCodeScanAsync(
        ProcessInventoryScanCommand request,
        ScanValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        var sessionInfo = await _inventoryRepository.GetSessionAsync(request.InventorySessionId);
        if (sessionInfo == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = "Sesja inwentaryzacji nie została znaleziona",
                ScanType = InventoryScanType.ProductCode
            };
        }

        // Wyszukiwanie kodu produktu
        var kod = await _kodyRepository.GetByKodValueAsync(request.ScanData, sessionInfo.SystemId);
        if (kod == null)
        {
            return new InventoryScanResponse
            {
                IsSuccess = false,
                Message = $"Kod produktu {request.ScanData} nie został znaleziony w kartotece",
                ScanType = InventoryScanType.ProductCode
            };
        }

        var productInfo = new ProductCodeDto
        {
            Id = kod.Id,
            Kod = kod.KodValue,
            Kod2 = kod.Kod2,
            KodNazwa = kod.KodNazwa,
            SystemId = kod.SystemId,
            Jm = kod.Jm,
            EanJednostki = kod.EanJednostki,
            Ean = kod.Ean,
            IsActive = kod.Active > 0
        };

        // Sprawdzenie czy kod już istnieje w inwentaryzacji
        // Sprawdzenie czy produkt już istnieje w inwentaryzacji – uproszczone: szukamy po etykiecie/kodzie w SearchLabelAsync
        var existingItems = await _inventoryRepository.GetInventoryItemsAsync(sessionInfo.InventoryId, sessionInfo.SystemId);
        var matched = existingItems.FirstOrDefault(i => i.Kod == request.ScanData);

        if (matched != null)
        {
            // Kod już istnieje - zwróć pierwszy znaleziony
            var itemDto = MapToInventoryItemDto(matched);
            return new InventoryScanResponse
            {
                IsSuccess = true,
                Message = $"Produkt {kod.KodNazwa} znaleziony w inwentaryzacji",
                ScanType = InventoryScanType.ProductCode,
                FoundItem = itemDto,
                ProductInfo = productInfo,
                RequiresNewEntry = false
            };
        }

        return new InventoryScanResponse
        {
            IsSuccess = true,
            Message = $"Nowy produkt {kod.KodNazwa} - wprowadź ilość spisaną",
            ScanType = InventoryScanType.ProductCode,
            ProductInfo = productInfo,
            RequiresNewEntry = true
        };
    }

    private InventoryItemDto MapToInventoryItemDto(Domain.Entities.Inventory.InventoryEntity entity)
    {
        return new InventoryItemDto
        {
            Id = entity.Id,
            InwentaryzacjaId = entity.InwentaryzacjaId ?? 0,
            Data = entity.Data ?? DateTime.Today,
            Opis = entity.Opis,
            Ilosc = entity.Ilosc ?? 0,
            IloscSpisana = entity.IloscSpisana,
            Kod = entity.Kod,
            Hala = entity.Hala ?? 0,
            Regal = int.TryParse(entity.Regal, out var r) ? r : 0,
            Miejsce = entity.Miejsce ?? 0,
            Poziom = int.TryParse(entity.Poziom, out var p) ? p : 0,
            Ts = entity.Timestamp,
            Podkod = entity.Podkod,
            Skan = entity.Skan,
            EtykietaId = entity.EtykietaId,
            PaletaId = entity.PaletaId.ToString(),
            NrSap = entity.NumerSap,
            SystemId = entity.SystemId,
            Jm = entity.JednostkaMiary
        };
    }
}

