using WmsApp.ViewModels.Inventory;

namespace WmsApp.Views.Inventory;

public partial class InventorySelectionPage : ContentPage
{
    public InventorySelectionPage(InventorySelectionViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (BindingContext is InventorySelectionViewModel vm)
        {
            await vm.InitializeCommand.ExecuteAsync(null);
        }
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public InventorySelectionViewModel VM => (InventorySelectionViewModel)BindingContext;
}
