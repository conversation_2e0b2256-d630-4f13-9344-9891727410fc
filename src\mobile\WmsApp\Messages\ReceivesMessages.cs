using CommunityToolkit.Mvvm.Messaging.Messages;
using WmsApp.Models.Receives;

namespace WmsApp.Messages;

public sealed class LkScannedMessage : ValueChangedMessage<string>
{
    public LkScannedMessage(string value) : base(value) { }
}

public sealed class PrinterIpScannedMessage : ValueChangedMessage<string>
{
    public PrinterIpScannedMessage(string value) : base(value) { }
}

public sealed class Gs1ScannedMessage : ValueChangedMessage<string>
{
    public Gs1ScannedMessage(string value) : base(value) { }
}

public sealed class SsccScannedMessage : ValueChangedMessage<string>
{
    public SsccScannedMessage(string value) : base(value) { }
}

public sealed class DsScannedMessage : ValueChangedMessage<string>
{
    public DsScannedMessage(string value) : base(value) { }
}

public sealed class ScanProcessedMessage : ValueChangedMessage<ReceiveScanResult>
{
    public ScanProcessedMessage(ReceiveScanResult value) : base(value) { }
}

public sealed class ScanErrorMessage : ValueChangedMessage<string>
{
    public ScanErrorMessage(string value) : base(value) { }
}
