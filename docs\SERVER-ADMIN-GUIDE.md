# Szybka Instrukcja Administracji Serwera WMS

## 🔧 Najważniejsze Komendy Administratora

### Zarządzanie Usługą WMS API
```bash
# Status usługi
systemctl status wms-api

# Uruchamianie
systemctl start wms-api

# Zatrzymywanie  
systemctl stop wms-api

# Restart
systemctl restart wms-api

# Włączenie automatycznego startu
systemctl enable wms-api

# Wyłączenie automatycznego startu
systemctl disable wms-api

# Logi na żywo
journalctl -u wms-api -f

# Ostatnie 50 linii logów
journalctl -u wms-api -n 50

# Logi z ostatniej godziny
journalctl -u wms-api --since "1 hour ago"
```

### Zarządzanie Apache
```bash
# Status Apache
systemctl status apache2

# Restart Apache
systemctl restart apache2

# Reload konfiguracji (bez przerwy w działaniu)
systemctl reload apache2

# Test konfiguracji
apache2ctl configtest

# Włączenie strony/modułu
a2ensite nazwa-strony
a2enmod nazwa-modulu

# Wyłączenie strony/modułu  
a2dissite nazwa-strony
a2dismod nazwa-modulu

# Logi Apache
tail -f /var/log/apache2/error.log
tail -f /var/log/apache2/access.log
```

### Diagnostyka Sieci
```bash
# Sprawdzenie portów nasłuchujących
ss -tlnp | grep -E ':(80|443|5000)'

# Alternatywnie
netstat -tlnp | grep -E ':(80|443|5000)'

# Test lokalnych endpointów
curl http://127.0.0.1:5000/health
curl http://127.0.0.1/health  
curl -k https://127.0.0.1/health

# Test z zewnątrz
curl -k https://***********/health
curl -k https://***********/api/v1.0/health
```

### Monitoring Zasobów
```bash
# Użycie CPU i RAM
htop

# Pamięć
free -h

# Dysk
df -h

# Procesy WMS
ps aux | grep Wms.Api

# Szczegóły procesu WMS
systemctl show wms-api --property=MainPID
```

### Backup i Przywracanie
```bash
# Utworzenie backupu
tar -czf /tmp/wms-backup-$(date +%Y%m%d_%H%M%S).tar.gz -C /var/www/wms-backend .

# Lista backupów
ls -la /tmp/wms-backup-*

# Przywracanie z backupu
systemctl stop wms-api
cd /var/www/wms-backend
tar -xzf /tmp/wms-backup-YYYYMMDD_HHMMSS.tar.gz
systemctl start wms-api
```

---

## 🚨 Procedury Awaryjne

### Problem: API nie odpowiada
```bash
# 1. Sprawdź status usługi
systemctl status wms-api

# 2. Sprawdź logi
journalctl -u wms-api -n 20

# 3. Restart usługi  
systemctl restart wms-api

# 4. Sprawdź czy nasłuchuje na porcie
ss -tlnp | grep :5000

# 5. Test ręczny
cd /var/www/wms-backend
./Wms.Api --urls http://127.0.0.1:5000
```

### Problem: Apache zwraca 503
```bash
# 1. Sprawdź czy WMS API działa
curl http://127.0.0.1:5000/health

# 2. Jeśli nie - restart WMS API
systemctl restart wms-api

# 3. Sprawdź konfigurację Apache
apache2ctl configtest

# 4. Restart Apache
systemctl restart apache2
```

### Problem: Certyfikat SSL
```bash
# Sprawdzenie certyfikatu
openssl x509 -in /etc/ssl/certs/wms-selfsigned.crt -noout -dates -subject

# Regeneracja certyfikatu (jeśli wygasł)
cd /root
openssl req -new -x509 -days 365 -nodes \
  -config /tmp/cert.conf \
  -keyout /etc/ssl/private/wms-selfsigned.key \
  -out /etc/ssl/certs/wms-selfsigned.crt
systemctl reload apache2
```

---

## 📊 Ważne Ścieżki i Pliki

### Konfiguracja
- **Apache HTTP:** `/etc/apache2/sites-available/000-default.conf`
- **Apache HTTPS:** `/etc/apache2/sites-available/default-ssl.conf`  
- **WMS Service:** `/etc/systemd/system/wms-api.service`
- **Certyfikat SSL:** `/etc/ssl/certs/wms-selfsigned.crt`
- **Klucz SSL:** `/etc/ssl/private/wms-selfsigned.key`

### Aplikacja
- **Katalog aplikacji:** `/var/www/wms-backend/`
- **Plik wykonywalny:** `/var/www/wms-backend/Wms.Api`
- **Konfiguracja app:** `/var/www/wms-backend/appsettings.json`

### Logi
- **WMS API:** `journalctl -u wms-api`
- **Apache Error:** `/var/log/apache2/error.log`
- **Apache Access:** `/var/log/apache2/access.log`
- **System:** `journalctl -f`

---

## 🌐 Endpointy do Testowania

### Health Checks
- ✅ `https://***********/health`
- ✅ `https://***********/api/v1.0/health`
- ✅ `https://***********/api/v1.0/health/detailed`

### API
- ✅ `https://***********/api/v1.0/auth/login-scan` (POST)
- ✅ `https://***********/api/v1.0/pallets`
- ✅ `https://***********/api/v1.0/locations`

### Development
- ✅ `https://***********/openapi/v1.0.json`

---

## 🔄 Procedura Wdrażania

### Automatyczne (z Windows)
```powershell
# Z katalogu głównego projektu
echo "y" | pwsh -File ./scripts/publish-and-deploy.ps1 dev
```

### Ręczne (na serwerze)
```bash
# 1. Backup bieżącej wersji
tar -czf /tmp/wms-backup-$(date +%Y%m%d_%H%M%S).tar.gz -C /var/www/wms-backend .

# 2. Zatrzymaj usługę
systemctl stop wms-api

# 3. Skopiuj nowe pliki do /var/www/wms-backend/

# 4. Ustaw uprawnienia
chmod +x /var/www/wms-backend/Wms.Api
chown -R wms-dev:users /var/www/wms-backend

# 5. Uruchom usługę
systemctl start wms-api

# 6. Sprawdź status
systemctl status wms-api
curl -k https://***********/health
```

---

## 📞 Kontakt Awaryjny

### Szybka Diagnostyka
```bash
# Wszystko w jednej komendzie
echo "=== SYSTEM STATUS ===" && \
systemctl status apache2 --no-pager && \
echo -e "\n=== WMS API STATUS ===" && \
systemctl status wms-api --no-pager && \
echo -e "\n=== PORTS ===" && \
ss -tlnp | grep -E ':(80|443|5000)' && \
echo -e "\n=== HEALTH TEST ===" && \
curl -k -m 5 https://***********/health 2>/dev/null || echo "API NOT RESPONDING"
```

### Restart Wszystkiego
```bash
# Awaryjny restart (ostatnia deska ratunku)
systemctl restart wms-api && \
systemctl restart apache2 && \
sleep 5 && \
systemctl status wms-api && \
curl -k https://***********/health
```

---

## 📝 Changelog

**2025-09-03:**
- ✅ Zainstalowano .NET 9.0
- ✅ Skonfigurowano Apache z reverse proxy
- ✅ Utworzono usługę systemd
- ✅ Skonfigurowano SSL (samopodpisany)
- ✅ Dodano HealthController dla API v1.0

**Stan aktualny:**
- WMS API działa na porcie 5000
- Apache proxy na portach 80/443  
- Endpointy API dostępne pod `/api/v1.0/`
- Health check pod `/health` i `/api/v1.0/health`
