using Wms.Domain.Entities;

namespace Wms.Application.Interfaces;

public interface IKodRepository
{
    Task<Kod?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<Kod?> GetByKodValueAsync(string kodValue, int? systemId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Kod>> SearchAsync(string query, int limit = 10, int? systemId = null, CancellationToken cancellationToken = default);
}
