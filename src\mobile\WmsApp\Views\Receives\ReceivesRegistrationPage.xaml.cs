using WmsApp.ViewModels.Receives;

namespace WmsApp.Views.Receives;

public partial class ReceivesRegistrationPage : ContentPage
{
    public ReceivesRegistrationPage(ReceivesRegistrationViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // Ułatwia kompilowane wiązania w DataTemplate
    public ReceivesRegistrationViewModel VM => (ReceivesRegistrationViewModel)BindingContext;

    protected override void OnAppearing()
    {
        base.OnAppearing();
        
        // Focus scan entry for immediate scanning
        ScanEntry.Focus();
    }

    private async void OnDeleteTapped(object sender, TappedEventArgs e)
    {
        try
        {
            if (e.Parameter is int id)
            {
                System.Diagnostics.Debug.WriteLine($"[UI] Tap on delete for position: {id}");
                if (VM.DeletePositionCommand.CanExecute(id))
                {
                    VM.DeletePositionCommand.Execute(id);
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[UI] Tap without valid id parameter");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[UI] Error in OnDeleteTapped: {ex.Message}");
        }
    }
}
