#!/usr/bin/env python3
"""
Lokalny HTTP proxy do przekierowania zapytań z (localhost:8080) do lokalnego backendu (127.0.0.1:8081)
Uruchom: python api-proxy-local.py
Test:   curl http://127.0.0.1:8080/api/v1/health
Naciśnij 'c', aby w<PERSON> ekran
"""

import http.server
import socketserver
import urllib.request
import urllib.error
import json
import os
import sys
import threading
import msvcrt  # Tylko dla Windows

TARGET_HOST = "127.0.0.1"
TARGET_PORT = 8081
PROXY_PORT = 8080

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request('GET')
    
    def do_POST(self):
        self.proxy_request('POST')
    
    def do_PUT(self):
        self.proxy_request('PUT')
    
    def do_DELETE(self):
        self.proxy_request('DELETE')

    def proxy_request(self, method):
        try:
            # Zbuduj URL docelowy
            target_url = f"http://{TARGET_HOST}:{TARGET_PORT}{self.path}"
            
            print(f"\n[{method}] Proxying: {self.path} -> {target_url}")
            
            # Przygotuj dane dla POST/PUT
            content_length = int(self.headers.get('Content-Length', 0))
            transfer_encoding = self.headers.get('Transfer-Encoding', '')
            post_data = None
            
            print(f"Content-Length: {content_length}")
            print(f"Transfer-Encoding: {transfer_encoding}")
            
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                print(f"📦 Read {len(post_data) if post_data else 0} bytes via Content-Length")
            elif 'chunked' in transfer_encoding.lower():
                # Obsługa chunked encoding
                print(f"🔄 Reading chunked data...")
                post_data = self.read_chunked_data()
                print(f"Read {len(post_data) if post_data else 0} bytes via chunked encoding")
                print("Chunked data reading completed successfully")
            
            # Loguj nagłówki zapytania
            print("Request Headers:")
            for header_name, header_value in self.headers.items():
                print(f"   {header_name}: {header_value}")
            
            # Loguj dane POST/PUT jeśli istnieją
            if post_data:
                print(f"📦 Request Body ({len(post_data)} bytes):")
                try:
                    # Spróbuj zdekodować jako JSON
                    decoded_data = post_data.decode('utf-8')
                    if self.headers.get('Content-Type', '').startswith('application/json'):
                        json_data = json.loads(decoded_data)
                        print(f"   JSON: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"   Raw: {decoded_data}")
                except (UnicodeDecodeError, json.JSONDecodeError):
                    print(f"   Binary data: {len(post_data)} bytes")
            
            # Stwórz zapytanie
            headers = {}
            for header_name, header_value in self.headers.items():
                if header_name.lower() not in ['host', 'connection', 'transfer-encoding']:
                    headers[header_name] = header_value
            
            # Jeśli mamy dane POST/PUT, dodaj Content-Length
            if post_data:
                headers['Content-Length'] = str(len(post_data))
            
            # Wykonaj zapytanie
            req = urllib.request.Request(
                target_url,
                data=post_data,
                headers=headers,
                method=method
            )

            with urllib.request.urlopen(req) as response:
                response_data = response.read()
                
                # Loguj odpowiedź
                print(f"Response Status: {response.status}")
                print("Response Headers:")
                for header_name, header_value in response.headers.items():
                    print(f"   {header_name}: {header_value}")
                
                print(f"Response Body ({len(response_data)} bytes):")
                try:
                    decoded_response = response_data.decode('utf-8')
                    if response.headers.get('Content-Type', '').startswith('application/json'):
                        json_response = json.loads(decoded_response)
                        print(f"   JSON: {json.dumps(json_response, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"   Raw: {decoded_response}")
                except (UnicodeDecodeError, json.JSONDecodeError):
                    print(f"   Binary data: {len(response_data)} bytes")
                
                # Wyślij status i nagłówki
                self.send_response(response.status)
                for header_name, header_value in response.headers.items():
                    if header_name.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header_name, header_value)
                self.end_headers()
                
                # Wyślij zawartość
                self.wfile.write(response_data)

        except urllib.error.HTTPError as e:
            # Loguj błąd HTTP
            error_data = e.read()
            print(f"HTTP Error: {e.status} {e.reason}")
            print("Error Headers:")
            for header_name, header_value in e.headers.items():
                print(f"   {header_name}: {header_value}")
            
            print(f"Error Body ({len(error_data)} bytes):")
            try:
                decoded_error = error_data.decode('utf-8')
                if e.headers.get('Content-Type', '').startswith('application/json'):
                    json_error = json.loads(decoded_error)
                    print(f"   JSON: {json.dumps(json_error, indent=2, ensure_ascii=False)}")
                else:
                    print(f"   Raw: {decoded_error}")
            except (UnicodeDecodeError, json.JSONDecodeError):
                print(f"   Binary data: {len(error_data)} bytes")
            
            # Przekaż błędy HTTP
            self.send_response(e.status)
            for header_name, header_value in e.headers.items():
                if header_name.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header_name, header_value)
            self.end_headers()
            self.wfile.write(error_data)
        except Exception as e:
            print(f"Proxy error: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = json.dumps({
                "error": "Proxy error",
                "message": str(e)
            }).encode()
            self.wfile.write(error_response)

    def read_chunked_data(self):
        """Czyta dane w formacie chunked encoding"""
        data = b''
        while True:
            # Czytaj długość chunk'a (hex + \r\n)
            line = self.rfile.readline()
            if not line:
                break
            
            try:
                chunk_size = int(line.strip(), 16)
                print(f"🔢 Chunk size: {chunk_size}")
            except ValueError:
                print(f"❌ Invalid chunk size: {line}")
                break
                
            if chunk_size == 0:
                # Ostatni chunk, czytaj trailing headers
                while True:
                    trailer = self.rfile.readline()
                    if trailer in (b'\r\n', b'\n', b''):
                        break
                break
                
            # Czytaj dane chunk'a + \r\n
            chunk_data = self.rfile.read(chunk_size + 2)  # +2 for \r\n
            if len(chunk_data) >= 2:
                data += chunk_data[:-2]  # usuń \r\n na końcu
                print(f"📥 Received chunk: {len(chunk_data)-2} bytes")
            else:
                print(f"❌ Short chunk read: {len(chunk_data)} bytes")
                break
                
        print(f"📦 Total chunked data: {len(data)} bytes")
        return data if data else None

    def log_message(self, format, *args):
        # Logowanie zapytań
        print(f"[PROXY] {format % args}")

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def keyboard_listener():
    print("Naciśnij 'c', aby wyczyścić ekran...")
    while True:
        if msvcrt.kbhit():
            key = msvcrt.getch().decode('utf-8').lower()
            if key == 'c':
                clear_screen()
                print("Ekran wyczyszczony. Naciśnij 'c' ponownie, aby wyczyścić ekran...")

if __name__ == "__main__":
    clear_screen()
    print("Starting Local API Proxy Server...")
    print(f"   Target API: http://{TARGET_HOST}:{TARGET_PORT}")
    print(f"   Proxy Port: {PROXY_PORT}")
    print("Naciśnij 'c', aby wyczyścić ekran")
    
    # Uruchom wątek nasłuchujący klawisza 'c'
    keyboard_thread = threading.Thread(target=keyboard_listener, daemon=True)
    keyboard_thread.start()
    print(f"   Local URL:  http://127.0.0.1:{PROXY_PORT}")
    print("")

    with socketserver.TCPServer(("0.0.0.0", PROXY_PORT), ProxyHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️  Proxy server stopped")

