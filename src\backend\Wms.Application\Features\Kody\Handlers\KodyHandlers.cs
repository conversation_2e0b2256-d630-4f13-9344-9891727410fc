using MediatR;
using Microsoft.Extensions.Logging;
using Wms.Application.DTOs.Receives;
using Wms.Application.Features.Kody.Queries;
using Wms.Application.Interfaces;

namespace Wms.Application.Features.Kody.Handlers;

public class GetKodByIdHandler : IRequestHandler<GetKodByIdQuery, KodDto?>
{
    private readonly IKodRepository _kodRepository;
    private readonly ILogger<GetKodByIdHandler> _logger;

    public GetKodByIdHandler(IKodRepository kodRepository, ILogger<GetKodByIdHandler> logger)
    {
        _kodRepository = kodRepository;
        _logger = logger;
    }

    public async Task<KodDto?> Handle(GetKodByIdQuery request, CancellationToken cancellationToken)
    {
        var kod = await _kodRepository.GetByIdAsync(request.Id, cancellationToken);
        if (kod == null) return null;

        return new KodDto
        {
            Id = kod.Id,
            Kod = kod.KodValue,
            Nazwa = kod.<PERSON>dNazwa,
            IloscWOpakowaniu = (kod.Il<PERSON>cWOpakowaniu ?? 0),
            CzyWymaganaPartia = kod.WymaganaPartia == 1,
            CzyWymaganaDataWaznosci = kod.WymaganaDataWaznosci == 1
        };
    }
}

public class SearchKodyHandler : IRequestHandler<SearchKodyQuery, List<KodDto>>
{
    private readonly IKodRepository _kodRepository;
    private readonly ILogger<SearchKodyHandler> _logger;

    public SearchKodyHandler(IKodRepository kodRepository, ILogger<SearchKodyHandler> logger)
    {
        _kodRepository = kodRepository;
        _logger = logger;
    }

    public async Task<List<KodDto>> Handle(SearchKodyQuery request, CancellationToken cancellationToken)
    {
        var results = await _kodRepository.SearchAsync(request.Query, request.Limit, request.SystemId, cancellationToken);
        return results.Select(k => new KodDto
        {
            Id = k.Id,
            Kod = k.KodValue,
            Nazwa = k.KodNazwa,
            IloscWOpakowaniu = (k.IloscWOpakowaniu ?? 0),
            CzyWymaganaPartia = k.WymaganaPartia == 1,
            CzyWymaganaDataWaznosci = k.WymaganaDataWaznosci == 1
        }).ToList();
    }
}

public class GetKodByCodeHandler : IRequestHandler<GetKodByCodeQuery, KodDto?>
{
    private readonly IKodRepository _kodRepository;
    private readonly ILogger<GetKodByCodeHandler> _logger;

    public GetKodByCodeHandler(IKodRepository kodRepository, ILogger<GetKodByCodeHandler> logger)
    {
        _kodRepository = kodRepository;
        _logger = logger;
    }

    public async Task<KodDto?> Handle(GetKodByCodeQuery request, CancellationToken cancellationToken)
    {
        var kod = await _kodRepository.GetByKodValueAsync(request.Kod, request.SystemId, cancellationToken);
        if (kod == null) return null;

        return new KodDto
        {
            Id = kod.Id,
            Kod = kod.KodValue,
            Nazwa = kod.KodNazwa,
            IloscWOpakowaniu = (kod.IloscWOpakowaniu ?? 0),
            CzyWymaganaPartia = kod.WymaganaPartia == 1,
            CzyWymaganaDataWaznosci = kod.WymaganaDataWaznosci == 1
        };
    }
}

