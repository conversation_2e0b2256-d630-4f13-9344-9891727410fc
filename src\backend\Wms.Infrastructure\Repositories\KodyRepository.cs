using Microsoft.EntityFrameworkCore;
using Wms.Application.Features.Inventory.Handlers;
using Wms.Domain.Entities;
using Wms.Infrastructure.Data;

namespace Wms.Infrastructure.Repositories;

/// <summary>
/// Repository dla kodów produktów
/// </summary>
public class KodyRepository : IKodyRepository
{
    private readonly WmsDbContext _context;

    public KodyRepository(WmsDbContext context)
    {
        _context = context;
    }

    public async Task<Kod?> GetByIdAsync(int id)
    {
        return await _context.Kody
            .AsNoTracking()
            .FirstOrDefaultAsync(k => k.Id == id);
    }

    public async Task<Kod?> GetByCodeAsync(string code, int systemId)
    {
        return await _context.Kody
            .AsNoTracking()
            .FirstOrDefaultAsync(k =>
                k.KodValue == code &&
                k.SystemId == systemId &&
                k.Active > 0);
    }
}