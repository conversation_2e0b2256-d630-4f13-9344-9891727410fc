

1. Okno Dostawa wybór
 - umożliwia wprowadzenie do pola tekstowego nr LK lub zeskanowanie 
 - dodatkowo posiada przycisk "Lista dostaw" który umożliwia wybranie dostawy z pośród już istniejących w systemie i oczekujących na przyjęcie; Lista dostaw jest pobierana z systemu "awizacje_dostaw_head" i pokazuje "numer_zamowienia | system_nazwa" i przyciskiem wybierz. 
 - Posiada też przycisk "Generuj DS"; Po jego naciśnięciu pojawia się nowy ekran z polami: Typ palety, <PERSON><PERSON><PERSON><PERSON> palet; Skan drukarki i "Generuj DS" dzięki czemu można tworzyć palety w tabeli 'listcontrol_palety'.



CREATE TABLE `awizacje_dostaw_head` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `system_id` int unsigned NOT NULL DEFAULT '0',
  `magazyn` int unsigned NOT NULL DEFAULT '0',
  `data` varchar(12) NOT NULL DEFAULT '',
  `godzina` varchar(10) NOT NULL DEFAULT '',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `pracownik_id` int unsigned NOT NULL DEFAULT '0',
  `transport` varchar(45) NOT NULL DEFAULT '',
  `kierowca` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `uwagi` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `typ` int unsigned NOT NULL DEFAULT '0',
  `nazwa_pliku` varchar(45) NOT NULL DEFAULT '',
  `kontrah_id` int unsigned DEFAULT NULL,
  `numer_zamowienia` varchar(155) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `listcontrol_id` int unsigned DEFAULT NULL,
  `status` int unsigned NOT NULL DEFAULT '0',
  `zleceniodawca` int unsigned DEFAULT NULL,
  `docin_id_man` int unsigned DEFAULT NULL,
  `numer_zamowienia_klienta` varchar(2000) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `system_id` (`system_id`)
) ENGINE=MyISAM AUTO_INCREMENT=16368 DEFAULT CHARSET=latin1;





CREATE TABLE `list_control` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `listcontrol_system_id` int DEFAULT NULL,
  `numer` int unsigned NOT NULL DEFAULT '0',
  `data` date DEFAULT NULL,
  `dokument_dostawy` varchar(160) CHARACTER SET utf8 COLLATE utf8_polish_ci DEFAULT NULL,
  `transport` varchar(145) CHARACTER SET utf8 COLLATE utf8_polish_ci DEFAULT NULL,
  `uwagi` varchar(145) CHARACTER SET utf8 COLLATE utf8_polish_ci DEFAULT NULL,
  `list_internal` int NOT NULL DEFAULT '1',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pracownik_id` int unsigned DEFAULT NULL,
  `listcontrol_type_id` int unsigned NOT NULL DEFAULT '0',
  `awizacje_id` int unsigned NOT NULL DEFAULT '0',
  `ilosc_etykiet` int unsigned NOT NULL DEFAULT '0',
  `miejsce_id` int unsigned NOT NULL DEFAULT '0',
  `wd_przyjecie_head_id` int unsigned NOT NULL DEFAULT '0',
  `list_controlcol` varchar(45) COLLATE utf8_polish_ci DEFAULT NULL,
  `palety_opis_wydruk` varchar(45) COLLATE utf8_polish_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `listcontrol_system_id` (`listcontrol_system_id`),
  KEY `Index_3` (`numer`),
  KEY `Index_4` (`listcontrol_type_id`)
) ENGINE=MyISAM AUTO_INCREMENT=29627 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_polish_ci;



CREATE TABLE `listcontrol_palety` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `listcontrol_id` int unsigned NOT NULL DEFAULT '0',
  `paleta_id` int unsigned NOT NULL DEFAULT '0',
  `wydruk` int unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique` (`listcontrol_id`,`paleta_id`),
  KEY `listcontrol_id` (`listcontrol_id`),
  KEY `paleta_id` (`paleta_id`)
) ENGINE=MyISAM AUTO_INCREMENT=649995 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_polish_ci;



CREATE TABLE `palety` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `typypalet_id` int unsigned DEFAULT NULL,
  `ilosc` int unsigned DEFAULT '0',
  `j_skladowania_id` int unsigned DEFAULT NULL,
  `pal_klient` varchar(45) DEFAULT NULL,
  `ts_inwentaryzacja` datetime DEFAULT NULL,
  `result_inwentaryzacja` varchar(45) DEFAULT NULL,
  `ts_utworzenia` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `pal_docin_id` int unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `Index_2` (`typypalet_id`) USING BTREE,
  KEY `Index_3` (`j_skladowania_id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=32296837 DEFAULT CHARSET=latin1 ROW_FORMAT=DYNAMIC;



CREATE TABLE `typypalet` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `kod` varchar(45) DEFAULT NULL,
  `opis` varchar(45) CHARACTER SET utf8 COLLATE utf8_polish_ci NOT NULL,
  `udzial_skladowania` decimal(4,2) NOT NULL DEFAULT '1.00',
  `kolejnosc_pal` int unsigned NOT NULL DEFAULT '0',
  `pal_dlugosc` varchar(45) NOT NULL DEFAULT '',
  `pal_szerokosc` varchar(45) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unikalne` (`opis`)
) ENGINE=MyISAM AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;








2. Okno Dostawa Rejestracja

 Posiada pola: list_control.dokument_dostawy i "Miejscu dostawy" (list_control.miejsce_id=miejsca. ) 
 Informacja o nr 
 - Skanuj w którym jest autofocus po każdym skanowaniu; Umożliwia skanowanie Etykiety GS1 SSCC. PO zeskanowaniu system szuka  
 - Nr nośnika z domyślną wartością "(Auto)"
 - Towar z przyciskiem "Szukaj"
 - Data produkcji i data ważności
 - Partia  etykiety.lot, awizacje_dostaw_dane.lot
 - Certyfikat etykiety.lot
 - St. Jakości i Typ Palety
 - Ilość opakowań i ilość sztuk
 - Przyciski "Koniec", "Podgląd", "Nośnik kompletny"



CREATE TABLE `awizacje_dostaw_head` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `system_id` int unsigned NOT NULL DEFAULT '0',
  `magazyn` int unsigned NOT NULL DEFAULT '0',
  `data` varchar(12) NOT NULL DEFAULT '',
  `godzina` varchar(10) NOT NULL DEFAULT '',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `pracownik_id` int unsigned NOT NULL DEFAULT '0',
  `transport` varchar(45) NOT NULL DEFAULT '',
  `kierowca` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `uwagi` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `typ` int unsigned NOT NULL DEFAULT '0',
  `nazwa_pliku` varchar(45) NOT NULL DEFAULT '',
  `kontrah_id` int unsigned DEFAULT NULL,
  `numer_zamowienia` varchar(155) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `listcontrol_id` int unsigned DEFAULT NULL,
  `status` int unsigned NOT NULL DEFAULT '0',
  `zleceniodawca` int unsigned DEFAULT NULL,
  `docin_id_man` int unsigned DEFAULT NULL,
  `numer_zamowienia_klienta` varchar(2000) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `system_id` (`system_id`)
) ENGINE=MyISAM AUTO_INCREMENT=16368 DEFAULT CHARSET=latin1;



CREATE TABLE `awizacje_dostaw_dane` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `awizacje_dostaw_id` int unsigned NOT NULL DEFAULT '0',
  `etykieta_klient` varchar(45) NOT NULL DEFAULT '',
  `paletyzacja` varchar(45) NOT NULL DEFAULT '',
  `grupa` int unsigned NOT NULL DEFAULT '0',
  `kod` varchar(90) NOT NULL DEFAULT '',
  `lot` varchar(45) NOT NULL DEFAULT '',
  `blloc` varchar(45) DEFAULT NULL,
  `ilosc` decimal(10,3) DEFAULT NULL,
  `pozycja_zamowienia` int unsigned NOT NULL DEFAULT '0',
  `data_waznosci` date DEFAULT NULL,
  `dataprod` date DEFAULT NULL,
  `krajprod` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `awizacje_dostaw_id` (`awizacje_dostaw_id`),
  KEY `etykieta_klient` (`etykieta_klient`),
  KEY `kod` (`kod`)
) ENGINE=MyISAM AUTO_INCREMENT=537440 DEFAULT CHARSET=utf8mb3;




CREATE TABLE `etykiety` (
  `id` int NOT NULL AUTO_INCREMENT,
  `system_id` int NOT NULL DEFAULT '0',
  `etykieta_klient` varchar(45) DEFAULT NULL,
  `magazyn` int unsigned NOT NULL DEFAULT '0',
  `active` int unsigned DEFAULT NULL,
  `miejscep` int NOT NULL DEFAULT '0',
  `kod_id` int unsigned DEFAULT NULL,
  `status_id_old` int unsigned NOT NULL DEFAULT '1',
  `status_id` int NOT NULL DEFAULT '1',
  `stat` varchar(17) DEFAULT NULL,
  `paleta_id` int unsigned DEFAULT NULL,
  `kartony` int unsigned DEFAULT NULL,
  `dataprod` date DEFAULT NULL,
  `data_waznosci` date DEFAULT NULL,
  `ilosc` decimal(10,3) DEFAULT NULL,
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(30) DEFAULT NULL,
  `blloc` varchar(45) DEFAULT NULL,
  `akcja_id` int DEFAULT NULL,
  `status_prism` varchar(45) DEFAULT NULL,
  `lot` varchar(300) DEFAULT NULL,
  `sscc` varchar(35) DEFAULT NULL,
  `gtin` varchar(24) DEFAULT NULL,
  `przeznaczenie_id` int NOT NULL DEFAULT '1',
  `nretykiety` int NOT NULL DEFAULT '0',
  `docin_id` int DEFAULT NULL,
  `docout_id` int DEFAULT NULL,
  `delivery_id` int unsigned DEFAULT NULL,
  `listcontrol_id` int unsigned DEFAULT NULL,
  `status_id2` int unsigned DEFAULT NULL,
  `edycja_et` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`,`system_id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `paleta` (`paleta_id`),
  KEY `magazyn` (`magazyn`) USING BTREE,
  KEY `active` (`active`) USING BTREE,
  KEY `miejsce` (`miejscep`),
  KEY `docin_id` (`docin_id`),
  KEY `docout_id` (`docout_id`),
  KEY `delivery_id` (`delivery_id`),
  KEY `kod_id` (`kod_id`),
  KEY `system_id` (`system_id`),
  KEY `listcontrol_id` (`listcontrol_id`),
  KEY `nretykiety` (`nretykiety`),
  KEY `status_id` (`status_id`) USING BTREE,
  KEY `etykieta_klient` (`etykieta_klient`),
  KEY `active_system_id` (`system_id`,`active`),
  KEY `status_id2` (`status_id2`),
  KEY `idx_etykiety_docout_system` (`docout_id`,`system_id`),
  CONSTRAINT `chk_paleta_id` CHECK (((`paleta_id` <> 0) and (`paleta_id` is not null)))
) ENGINE=InnoDB AUTO_INCREMENT=7640280 DEFAULT CHARSET=utf8mb3 PACK_KEYS=1 ROW_FORMAT=DYNAMIC;





CREATE TABLE `kody` (
  `id` int NOT NULL AUTO_INCREMENT,
  `kod` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `kod2` varchar(100) DEFAULT NULL,
  `kod_nazwa` varchar(200) DEFAULT '',
  `system_id` int NOT NULL DEFAULT '0',
  `jm` varchar(8) DEFAULT NULL,
  `ean_jednostki` varchar(240) NOT NULL DEFAULT '',
  `waga_szt_kg` decimal(9,3) NOT NULL DEFAULT '0.000',
  `objetosc_szt_cm` int unsigned NOT NULL DEFAULT '0',
  `ean` varchar(100) DEFAULT NULL,
  `opakowanie_jm` varchar(8) DEFAULT NULL,
  `ilosc_w_opakowaniu` int unsigned DEFAULT '0',
  `ean_opakowanie_zbiorcze` varchar(45) NOT NULL,
  `ilosc_szt_w_zbiorczym` int NOT NULL,
  `kod_producent` varchar(75) DEFAULT NULL,
  `ilosc_opak_w_zborczym` int unsigned NOT NULL DEFAULT '1',
  `kod_kategoria` varchar(45) DEFAULT NULL,
  `kod_plec` varchar(45) DEFAULT NULL,
  `active` int unsigned NOT NULL DEFAULT '1',
  `gln` varchar(45) NOT NULL DEFAULT '',
  `wlasciciel` varchar(45) NOT NULL DEFAULT '',
  `brand_id` varchar(45) NOT NULL DEFAULT '1',
  `kody_grupy_id` int unsigned DEFAULT NULL,
  `wysokosc` varchar(25) DEFAULT NULL,
  `jednostka_wagi` varchar(10) NOT NULL DEFAULT '',
  `ilosc_szt_palecie` int unsigned NOT NULL DEFAULT '0',
  `ilosc_dni_przydatnosci` int unsigned NOT NULL DEFAULT '0',
  `wymagana_partia` int unsigned NOT NULL DEFAULT '0',
  `wymagana_data_waznosci` int unsigned NOT NULL DEFAULT '0',
  `wymagana_dataprod` int unsigned NOT NULL DEFAULT '0',
  `status_jakosci_domyslny` int unsigned DEFAULT NULL,
  `dlugosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `szerokosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `wysokosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `jednostka_skladowania_domyslna` int unsigned NOT NULL DEFAULT '0',
  `kraj_pochodzenia_id` int unsigned NOT NULL DEFAULT '0',
  `nowy_kod_mail` int unsigned NOT NULL DEFAULT '0',
  `ts_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `adr_towary` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `kod_system_id` (`kod`,`system_id`),
  KEY `kod` (`kod`) USING BTREE,
  KEY `system_id` (`system_id`),
  KEY `active` (`active`),
  KEY `ilosc_w_opakowaniu` (`ilosc_w_opakowaniu`)
) ENGINE=InnoDB AUTO_INCREMENT=78649 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;


CREATE TABLE `status_system` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `system_id` int unsigned NOT NULL DEFAULT '0',
  `nazwa` varchar(45) CHARACTER SET utf8 COLLATE utf8_polish_ci DEFAULT NULL,
  `funkcja_stat` varchar(45) CHARACTER SET utf8 COLLATE utf8_polish_ci DEFAULT NULL,
  `nadwyzka` int unsigned NOT NULL DEFAULT '0',
  `porzadek` int unsigned NOT NULL DEFAULT '0',
  `status_nazwa_klient` varchar(45) COLLATE utf8_polish_ci DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `system_id` (`system_id`)
) ENGINE=MyISAM AUTO_INCREMENT=996 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_polish_ci;


CREATE TABLE `miejsca` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hala` int NOT NULL DEFAULT '0',
  `regal` char(10) NOT NULL DEFAULT '',
  `miejsce` int NOT NULL DEFAULT '0',
  `poziom` varchar(4) DEFAULT NULL,
  `wysokosc` varchar(3) NOT NULL DEFAULT '',
  `widoczne` int unsigned NOT NULL DEFAULT '1',
  `zbiorka` int unsigned NOT NULL DEFAULT '0',
  `max_pojemnosc` int unsigned NOT NULL DEFAULT '0',
  `stanowisko_pracy_id` int unsigned NOT NULL DEFAULT '0',
  `picking_fakturowany` int unsigned NOT NULL DEFAULT '0',
  `producent` varchar(45) NOT NULL DEFAULT '0',
  `max_udzwig_kg` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `regal` (`regal`),
  KEY `miejsce` (`miejsce`),
  KEY `poziom` (`poziom`),
  KEY `hala` (`hala`),
  KEY `zbiorka` (`zbiorka`),
  KEY `widoczne` (`widoczne`)
) ENGINE=MyISAM AUTO_INCREMENT=96350 DEFAULT CHARSET=utf8mb3;


Przepływ pracy i scenariusze. 
Realizacja poza MAUI i skanerem:
Podczas tworzenia awizacje_dostaw_head tworzy się też list_control gdzie awizacje_dostaw_head.listcontrol_id=list_control.id.


Na skanerze: 
Magazynier po wybraniu "Rejestracja dostawy" ukazuje mu się okno "Dostawa wybór"
 - umożliwia wprowadzenie do pola tekstowego list_control.id lub zeskanowanie barcode w postaci "LK123" czyli prefix "LK" + liczba gdzie "liczba może być do 8 cyfr".
 - kliknięcie przycisku "Lista dostaw" umożliwia wyświetlenie dostaw oczekujących ba przyjęcie; Lista dostaw jest pobierana z systemu "awizacje_dostaw_head" i pokazuje "numer_zamowienia | system_nazwa "
	Dane Listy dostaw będą widoczne w postaci z możliwością wyboru w postaci :
SELECT l.id,dokument_dostawy,s.nazwa,  concat('H:',m.hala," ",m.regal, " ",m.miejsce) as miejsce_dostawy, l.data  FROM wmsgg.list_control l
left join systemy s on listcontrol_system_id=s.wartosc
left join miejsca m on m.id=l.miejsce_id
order by l.id desc limit 100

Jeśli jakaś dostawa zostanie wybrana, do pola LK zostanie wstawiona wartość "LK1234" jako list_control.id  dla id=1234.

 - Posiada też przycisk "Generuj etykiety"; Po jego naciśnięciu pojawia się nowy ekran z polami: 
  a) Typ palety wybieralny z listy z zapytania: SELECT id, opis FROM wmsgg.typypalet;
  b) Ilość palet, pole z możliwością wpisania ilości od 1 do 100;
  c) Skan drukarki: pole z możliwością zeskanowane IP drukarki z prefixem "IP" przykładowo: IP172.7.1.44 i zaznaczoym checkbox "Czy drukować?"
  Przycisk "Generuj DS" dzięki czemu można tworzyć palety w tabeli 'listcontrol_palety'; Kliknięcie przycisku Generuj powoduje insert do tabeli listcontrol_palety ilości palet/nośników:



CREATE TABLE `listcontrol_palety` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `listcontrol_id` int unsigned NOT NULL DEFAULT '0',
  `paleta_id` int unsigned NOT NULL DEFAULT '0',
  `wydruk` int unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique` (`listcontrol_id`,`paleta_id`),
  KEY `listcontrol_id` (`listcontrol_id`),
  KEY `paleta_id` (`paleta_id`)
) ENGINE=MyISAM AUTO_INCREMENT=650024 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_polish_ci;

listcontrol_palety.paleta_id jest pobierany z tabeli: SELECT last FROM wmsgg.docnumber where name='nrpalety'; po każdym pobraniu nr nośnika, pole last jest robione increment.

Jeśli zostało naciśnięte pole "Czy drukować?" to skaner wyśle do API informację o by wygeneował kod ZPL z barcode 128 w postaci "DS123456" gdzie jest to prefix "DS" + listcontrol_palety.paleta_id gdzie paleta_id = 123456 oznacza numer nośnika. 




 Po wybraniu listy dostawy wartość z Pola "LK123456" wartość "LK123456" jako listcontrol_id zostanie przekazana do Następnego widoku : 2. Okno Dostawa Rejestracja i wyświetlone gdzieś u góry.
Poniżej opisano  przepływ pracy z Widokiem 2. Okno Dostawa Rejestracja.


Kursor/wskaźnik wstawia się w pole(focus) Skanuj gdzie można zeskanować nośnik: SSCC lub DS. 
Jeśli będzie to SSCC. system szuka czy taka paleta jest awizowana
i jeśli ją znajdzie to wstawia dane jak kod, ilość, lot(partia), data ważności  w odpowiednie pola formularza. po czym Focus jest wstawiany ma pole ilość
System ma umożlwiać przyjęcie wielu pozycji na jednej nośnik oraz jedną pozycję na nośnik. Ma to polegać tym że:
Jeśli w polu ilość magazynier przyciska Enter to następne przyjmoweanie ma być na ten sam nośnik (w polu nośnik będzie SSCC  lub DS z numerem)
natomiast jeśli Magazynier kliknie przycisk "Nośnik kompletny" to następnie rejestowanie towaru/nośnika będzie miało na nośnik (AUTO). 

W polu "Skanuj" system ma umożliwiać ekstrakcję danych ze skanowanych etykiety i barcodów wg IZ z GS1, i po ekstrakcji wstawia w odpowiednie pola. 
Ponadto w polu kod/Towar system ma umożlwiać wstawianie kodu ze skanowanego nr EAN. 
Jeśli po zeskanowaniu towaru jest więcej nież jeden wiersz system wyświetla dane użytkownikowi aby on wskazał który towar chodzi:

SELECT k.id, k.kod FROM wmslow.kody k
where (ean="" or ean_opakowanie_zbiorcze="" or ean_jednostki="")
order by id desc


System ma umożliwiać też  ręczny wybór towaru z pozycji awizacji lub kartoteki kodów (tabela 'kody' dla danego system_id zgodnego z awizacja_head.system_id).


















Podziel do na PRD i  TODO backend i PRD TODO frontend.

w backend i fontend tam gdzie uważasz to słuszne stwórz podkatalogi 'dostawy' w celu lepszego zorganizowania wszelkich widoków, kontrolerów i innych składowych frontend i backend w których będą tworzone powyższe funkcjonalności.


 













