using System.Diagnostics;
using System.Diagnostics.Metrics;
using System.Text.RegularExpressions;

namespace Wms.Api.Metrics;

public class WmsMetrics
{
    public const string MeterName = "WMS.Operations";
    public const string Version = "1.0.0";
    
    private readonly Meter _meter;

    // Counters for business operations
    public readonly Counter<long> LoginAttempts;
    public readonly Counter<long> LoginSuccesses;
    public readonly Counter<long> LoginFailures;
    public readonly Counter<long> PalletMoveAttempts;
    public readonly Counter<long> PalletMoveSuccesses;
    public readonly Counter<long> PalletMoveFailures;
    public readonly Counter<long> ApiRequestsTotal;
    public readonly Counter<long> ValidationErrors;

    // Histograms for timing
    public readonly Histogram<double> PalletMoveDuration;
    public readonly Histogram<double> AuthenticationDuration;
    public readonly Histogram<double> DatabaseQueryDuration;
    public readonly Histogram<double> ApiRequestDuration;

    // Gauges (using UpDownCounters)
    public readonly UpDownCounter<long> ActiveSessions;
    public readonly UpDownCounter<long> ActiveUsers;

    public WmsMetrics()
    {
        _meter = new Meter(MeterName, Version);

        // Initialize counters
        LoginAttempts = _meter.CreateCounter<long>(
            "wms_login_attempts_total",
            "total", 
            "Total number of login attempts");

        LoginSuccesses = _meter.CreateCounter<long>(
            "wms_login_successes_total", 
            "total",
            "Total number of successful logins");

        LoginFailures = _meter.CreateCounter<long>(
            "wms_login_failures_total",
            "total", 
            "Total number of failed logins");

        PalletMoveAttempts = _meter.CreateCounter<long>(
            "wms_pallet_move_attempts_total",
            "total",
            "Total number of pallet move attempts");

        PalletMoveSuccesses = _meter.CreateCounter<long>(
            "wms_pallet_move_successes_total",
            "total", 
            "Total number of successful pallet moves");

        PalletMoveFailures = _meter.CreateCounter<long>(
            "wms_pallet_move_failures_total",
            "total",
            "Total number of failed pallet moves");

        ApiRequestsTotal = _meter.CreateCounter<long>(
            "wms_api_requests_total",
            "total",
            "Total number of API requests");

        ValidationErrors = _meter.CreateCounter<long>(
            "wms_validation_errors_total",
            "total",
            "Total number of validation errors");

        // Initialize histograms
        PalletMoveDuration = _meter.CreateHistogram<double>(
            "wms_pallet_move_duration_seconds",
            "seconds",
            "Duration of pallet move operations");

        AuthenticationDuration = _meter.CreateHistogram<double>(
            "wms_authentication_duration_seconds", 
            "seconds",
            "Duration of authentication operations");

        DatabaseQueryDuration = _meter.CreateHistogram<double>(
            "wms_database_query_duration_seconds",
            "seconds",
            "Duration of database queries");

        ApiRequestDuration = _meter.CreateHistogram<double>(
            "wms_api_request_duration_seconds",
            "seconds", 
            "Duration of API requests");

        // Initialize gauges
        ActiveSessions = _meter.CreateUpDownCounter<long>(
            "wms_active_sessions",
            "sessions",
            "Number of currently active sessions");

        ActiveUsers = _meter.CreateUpDownCounter<long>(
            "wms_active_users", 
            "users",
            "Number of currently active users");
    }

    public void RecordLoginAttempt(string cardNumber, bool success, string? failureReason = null)
    {
        var tags = new TagList();
        tags.Add("success", success.ToString().ToLower());

        if (!success && !string.IsNullOrEmpty(failureReason))
        {
            tags.Add("failure_reason", failureReason);
        }

        LoginAttempts.Add(1, tags);
        
        if (success)
        {
            LoginSuccesses.Add(1);
        }
        else
        {
            LoginFailures.Add(1, tags);
        }
    }

    public void RecordPalletMove(string palletCode, string fromLocation, string toLocation, 
        bool success, TimeSpan duration, string? failureReason = null)
    {
        var tags = new TagList();
        tags.Add("success", success.ToString().ToLower());
        tags.Add("from_location_type", GetLocationTypeFromCode(fromLocation));
        tags.Add("to_location_type", GetLocationTypeFromCode(toLocation));

        if (!success && !string.IsNullOrEmpty(failureReason))
        {
            tags.Add("failure_reason", failureReason);
        }

        PalletMoveAttempts.Add(1, tags);
        PalletMoveDuration.Record(duration.TotalSeconds, tags);

        if (success)
        {
            PalletMoveSuccesses.Add(1, tags);
        }
        else
        {
            PalletMoveFailures.Add(1, tags);
        }
    }

    public void RecordApiRequest(string method, string endpoint, int statusCode, TimeSpan duration)
    {
        var tags = new TagList();
        tags.Add("method", method.ToUpper());
        tags.Add("endpoint", SanitizeEndpoint(endpoint));
        tags.Add("status_code", statusCode.ToString());
        tags.Add("status_class", GetStatusClass(statusCode));

        ApiRequestsTotal.Add(1, tags);
        ApiRequestDuration.Record(duration.TotalSeconds, tags);
    }

    public void RecordValidationError(string errorType, string field)
    {
        var tags = new TagList();
        tags.Add("error_type", errorType);
        tags.Add("field", field);

        ValidationErrors.Add(1, tags);
    }

    public void RecordDatabaseQuery(string operation, TimeSpan duration, bool success)
    {
        var tags = new TagList();
        tags.Add("operation", operation);
        tags.Add("success", success.ToString().ToLower());

        DatabaseQueryDuration.Record(duration.TotalSeconds, tags);
    }

    private static string GetLocationTypeFromCode(string locationCode)
    {
        if (locationCode.StartsWith("MP-"))
            return "storage";
        if (locationCode.StartsWith("DOCK-"))
            return "dock";
        if (locationCode.StartsWith("PICK-"))
            return "picking";
        return "unknown";
    }

    private static string SanitizeEndpoint(string endpoint)
    {
        // Remove specific IDs from endpoint for better grouping
        // e.g., /api/v1/pallets/123456789012345678/move -> /api/v1/pallets/{id}/move
        var result = endpoint;
        result = Regex.Replace(result, @"/\d{18}(/|$)", "/{sscc}$1");  // SSCC codes
        result = Regex.Replace(result, @"/DS\d{4,9}(/|$)", "/{ds_code}$1");  // DS codes
        result = Regex.Replace(result, @"/MP-[\w\d-]+(/|$)", "/{location_code}$1");  // Location codes
        return result;
    }

    private static string GetStatusClass(int statusCode)
    {
        return statusCode switch
        {
            >= 200 and < 300 => "2xx",
            >= 300 and < 400 => "3xx", 
            >= 400 and < 500 => "4xx",
            >= 500 => "5xx",
            _ => "unknown"
        };
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}
