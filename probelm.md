
Skub się na module rejestracji dostaw aplikacji mobilnej WMS. 

**Problem:**
- Po kliknięciu przycisku usuwania (🗑️) w podglądzie nośnika pozycje nie są usuwane
- Może występować błąd w komunikacji z API lub w logice MockReceiveService
- Pozycje nadal wyświetlają się po próbie usunięcia

**Wymagane działania:**
1. Zdiagnozuj dlaczego usuwanie nie działa - sprawdź logi aplikacji mobilnej
4. Przetestuj funkcjonalność usuwania dla przykładowych nośników (**********)
5. <PERSON><PERSON><PERSON><PERSON> się, że podgląd nośnika odświeża się automatycznie po usunięciu pozycji
6. Jeśli problem dotyczy prawdziwego API, przetestuj ejuj
ndpoint `DELETE /api/v1/nosniki/labels/{labelId}` przez proxy

**Kontekst:**
Funkcjonalność została niedawno zaimplementowana w ramach naprawy "Podglądu nośnika" - sprawdź czy wszystkie komponenty (UI, ViewModel, Service, API) są poprawnie połączone.