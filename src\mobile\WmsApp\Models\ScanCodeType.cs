namespace WmsApp.Models;

public enum ScanCodeType
{
    Unknown,
    EmployeeCard,    // Karta pracownika (10+ cyfr)
    SSCC,           // SSCC paleta (18 cyfr)
    DS,             // DS kod (DS + 4-9 cyfr)
    Location        // Lokalizacja (MP-H-R-M-P format)
}

public record ScanResult
{
    public string Code { get; init; } = null!;
    public ScanCodeType Type { get; init; }
    public bool IsValid { get; init; }
    public string? ErrorMessage { get; init; }
    public DateTime ScanTime { get; init; } = DateTime.Now;
}
