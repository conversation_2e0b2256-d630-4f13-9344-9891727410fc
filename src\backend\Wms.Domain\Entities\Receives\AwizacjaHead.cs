using Wms.Domain.Common;
using Wms.Domain.Entities; // For User entity

namespace Wms.Domain.Entities.Receives;

/// <summary>
/// Encja reprezentująca nagłówek awizacji dostawy
/// Mapowana na tabelę awizacje_dostaw_head
/// </summary>
public class AwizacjaHead : BaseEntity
{
    public int Id { get; set; }
    public int SystemId { get; set; } = 0;
    public int Magazyn { get; set; } = 0;
    public string Data { get; set; } = string.Empty;
    public string Godzina { get; set; } = string.Empty;
    public DateTime Ts { get; set; } = DateTime.UtcNow;
    public int PracownikId { get; set; } = 0;
    public string Transport { get; set; } = string.Empty;
    public string Kierowca { get; set; } = string.Empty;
    public string Uwagi { get; set; } = string.Empty;
    public int Typ { get; set; } = 0;
    public string NazwaPliku { get; set; } = string.Empty;
    public int? KontrahId { get; set; }
    public string NumerZamowienia { get; set; } = string.Empty;
    public int? ListcontrolId { get; set; }
    public int Status { get; set; } = 0;
    public int? Zleceniodawca { get; set; }
    public int? DocinIdMan { get; set; }
    public string NumerZamowieniaKlienta { get; set; } = string.Empty;
    
    // Navigation properties
    public ListControl? ListControl { get; set; }
    public User? Pracownik { get; set; }
    public ICollection<AwizacjaDane> AwizacjaDane { get; set; } = new List<AwizacjaDane>();
    
    // Business logic properties
    public bool IsProcessed => DocinIdMan.HasValue;
    public bool IsAssignedToListControl => ListcontrolId.HasValue;
    
    /// <summary>
    /// Sprawdza czy awizacja może być powiązana z dostawą
    /// </summary>
    public bool CanBeAssignedToListControl() => !IsAssignedToListControl && !IsProcessed;
}
