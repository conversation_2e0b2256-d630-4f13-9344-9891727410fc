# Lokalne testowanie WMS (Backend + Proxy + Aplikacja mobilna)

Poniższy przewodnik opisuje jak uruchomić lokalnie backend WMS API, skonfi<PERSON><PERSON><PERSON><PERSON> lekki proxy HTTP oraz przetestować logowanie zarówno narzędziami CLI (curl), jak i z aplikacji mobilnej .NET MAUI.

## Wymagania wstępne

- Windows 11 (bash, cmd lub PowerShell)
- .NET SDK 9
- Python 3 (dla prostego serwera proxy)
- curl (do testów HTTP)
- Dostęp sieciowy do serwera MySQL 25.21.88.159:3306 (używany przez lokalnie uruchomione API)

## Struktura (lokalizacja istotnych plików)

- Backend API: `src/backend/Wms.Api`
- Skrypt lokalnego proxy: `api-proxy-local.py`

## 1. Uruchomienie backendu WMS API na porcie 8081

Backend w trybie Development łączy się z bazą MySQL pod adresem 25.21.88.159 zgodnie z connection stringiem w `src/backend/Wms.Api/appsettings.json`.

Najpierw można (opcjonalnie) sprawdzić łączność z MySQL:

```powershell path=null start=null
# PowerShell
Test-NetConnection -ComputerName 25.21.88.159 -Port 3306 | Format-List
```

Jeśli TCP jest osiągalny (TcpTestSucceeded: True), uruchom lokalnie API na porcie 8081 w osobnym oknie konsoli:

```bat path=null start=null
:: cmd (otwiera nowe okno, proces pozostaje uruchomiony)
start "WMS API 8081" cmd /k "set DISABLE_FLUENT_VALIDATION=1 && set ASPNETCORE_URLS=http://127.0.0.1:8081 && set ASPNETCORE_ENVIRONMENT=Development && dotnet run --project src\backend\Wms.Api\Wms.Api.csproj --no-launch-profile"
```

lub (PowerShell):

```powershell path=null start=null
$env:DISABLE_FLUENT_VALIDATION='1';
$env:ASPNETCORE_URLS='http://127.0.0.1:8081';
$env:ASPNETCORE_ENVIRONMENT='Development';
Start-Process -WindowStyle Normal -FilePath dotnet -ArgumentList "run --project 'src\backend\Wms.Api\Wms.Api.csproj' --no-launch-profile"
```

Weryfikacja nasłuchiwania i zdrowia:

```bash path=null start=null
# Sprawdź, czy port 8081 nasłuchuje
netstat -ano | findstr LISTENING | findstr ":8081"

# Sprawdź endpoint zdrowia
curl -v http://127.0.0.1:8081/api/v1/health
```

Powinieneś otrzymać `HTTP/1.1 200 OK` z krótkim JSONem.

### Uwaga dot. walidacji FluentValidation
Na potrzeby szybkich testów lokalnych, włączone jest wyłączenie auto-walidacji przez ustawienie zmiennej `DISABLE_FLUENT_VALIDATION=1`. Pozwala to uniknąć konfliktów wersji bibliotek walidacji przy testach. W buildach docelowych możesz usunąć tę zmienną po pełnym zestrojeniu wersji FV w solution.

## 2. Uruchomienie lokalnego Proxy (8080 → 8081)

Skrypt proxy wystawia lokalnie port 8080 i przekazuje żądania do backendu na 127.0.0.1:8081. Umożliwia to testowanie klientów, które spodziewają się endpointu pod 8080 (np. emulator Android przez ********:8080 lub aplikacja ustawiona na Localhost).

Uruchom proxy w osobnym oknie:

```bat path=null start=null
:: cmd
start "API Proxy Local" python api-proxy-local.py
```

Weryfikacja nasłuchiwania:

```bash path=null start=null
netstat -ano | findstr LISTENING | findstr ":8080"
```

Test health przez proxy:

```bash path=null start=null
curl -v http://127.0.0.1:8080/api/v1/health
```

## 3. Test logowania (curl → proxy → backend)

Przykładowe wywołanie logowania przez proxy z JSON payloadem (zmień cardNumber na właściwy, jeśli potrzebujesz):

```bash path=null start=null
curl -v -m 10 --connect-timeout 5 \
  http://127.0.0.1:8080/api/v1/auth/login-scan \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "User-Agent: WmsApp/1.0 (Localhost)" \
  -d '{"cardNumber":"1234567","deviceId":"curl-localhost"}'
```

Oczekiwany rezultat: `HTTP/1.0 200 OK` z odpowiedzią zawierającą token, czas wygaśnięcia i dane użytkownika.

## 4. Test z aplikacji mobilnej (.NET MAUI)

- Na Windows: wybierz w ekranie logowania środowisko `Localhost (127.0.0.1:8080)`.
- Na emulatorze Android: wybierz `Android Emulator (********:8080)` — ruch z emulowanego urządzenia na port hosta 8080 zostanie przekazany przez proxy do backendu 8081.

Upewnij się, że oba okna (Backend 8081 i Proxy 8080) są uruchomione przed testami z UI.

## Rozwiązywanie problemów (Troubleshooting)

- Błąd proxy: `WinError 10061 ... komputer docelowy aktywnie odmawia`  
  Przyczyna: Backend na 8081 nie działa.  
  Działanie: uruchom ponownie krok 1 i upewnij się, że 8081 nasłuchuje (`netstat`), a `/api/v1/health` zwraca 200.

- Błąd `503 Service Unavailable` z serwera zdalnego  
  Jeśli używasz proxy wskazującego na adres zdalny (np. `api-proxy-remote.py`), a backend zdalny jest w konserwacji, przełącz się na lokalny scenariusz (ten przewodnik) i testuj przez `api-proxy-local.py`.

- Błąd `500` związany z FluentValidation (TypeLoadException)  
  Upewnij się, że podczas testów lokalnych ustawiasz `DISABLE_FLUENT_VALIDATION=1` przy uruchamianiu API lub ujednolić wersje FV w solution.

- Port 8081/8080 zajęty lub build zablokowany  
  Jeśli build zgłasza, że plik exe/dll jest zajęty, zamknij okno procesu API lub użyj:
  
  ```bat path=null start=null
  taskkill /IM Wms.Api.exe /F
  ```

- Weryfikacja portów:

  ```bash path=null start=null
  netstat -ano | findstr LISTENING | findstr ":8081"
  netstat -ano | findstr LISTENING | findstr ":8080"
  ```

## Dobre praktyki i uwagi

- Nie commituj danych uwierzytelniających (sekretów) do repozytorium.
- Utrzymuj osobne okna dla Backend (8081) i Proxy (8080), aby mieć podgląd logów w czasie rzeczywistym.
- Jeśli testujesz na emulatorze Android, pamiętaj, że `********` to alias hosta, więc aplikacja mobilna powinna łączyć się na `http://********:8080/api/v1`.

---

Opracowanie: zespół WMS  
Data: 2025-09-09

